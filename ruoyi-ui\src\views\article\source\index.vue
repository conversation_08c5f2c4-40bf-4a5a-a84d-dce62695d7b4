<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="110px"
      class="queryForm"
    >
      <el-row type="flex" justify="space-between">
        <el-col :span="5">
          <el-form-item label="数据源名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入数据源名称"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="数据源中文名" prop="cnName">
            <el-input
              v-model="queryParams.cnName"
              placeholder="请输入数据源中文名"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="分类" prop="type">
            <el-select
              v-model="queryParams.type"
              placeholder="请选择分类"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in classList"
                :key="index"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="创建日期" prop="createdTime">
            <el-date-picker
              v-model="queryParams.createdTime"
              size="mini"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="稳定性" prop="isStability">
            <el-select
              size="mini"
              v-model="queryParams.isStability"
              placeholder="请选择稳定性"
              style="width: 100%"
              clearable
            >
              <el-option label="全部" :value="''"></el-option>
              <el-option label="稳定" :value="1"></el-option>
              <el-option label="不稳定" :value="0"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" justify="space-between">
        <el-col :span="5">
          <el-form-item label="url地址" prop="url">
            <el-input
              v-model="queryParams.url"
              placeholder="请输入url地址"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="所属国家" prop="countryOfOrigin">
            <el-select
              :popper-append-to-body="false"
              v-model="queryParams.countryOfOrigin"
              placeholder="请选择所属国家"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dict in dict.type.country"
                :label="dict.label"
                :key="'country' + dict.value"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="时区" prop="timeZoneId">
            <el-select
              :popper-append-to-body="false"
              v-model="queryParams.timeZoneId"
              placeholder="请选择时区"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="(dict, index) in dict.type.time_zone"
                :label="dict.label"
                :key="'time_zone' + dict.value"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="类型" prop="thinkTankClassification">
            <el-select
              :popper-append-to-body="false"
              v-model="queryParams.thinkTankClassification"
              placeholder="请选择类型"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="(dict, index) in dict.type.think_tank_class"
                :label="dict.label"
                :key="'think_tank_class' + dict.value"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="领域分类" prop="domainCode">
            <el-select
              :popper-append-to-body="false"
              v-model="queryParams.domainCode"
              placeholder="请选择领域分类"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="(dict, index) in dict.type.relation_domain_code"
                :label="dict.label"
                :key="'relation_domain_code' + dict.value"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" justify="space-between">
        <el-col :span="5">
          <el-form-item label="状态" prop="status">
            <el-select
              size="mini"
              v-model="queryParams.status"
              placeholder="请选择状态"
              style="width: 100%"
              clearable
            >
              <el-option label="全部" :value="''"></el-option>
              <el-option label="启用" :value="0"></el-option>
              <el-option label="停用" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="自动审核通过" prop="isAutomaticReviewed">
            <el-select
              size="mini"
              v-model="queryParams.isAutomaticReviewed"
              placeholder="请选择"
              style="width: 100%"
              clearable
            >
              <el-option label="否" :value="0"></el-option>
              <el-option label="是" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5" class="isAutomaticTechnology">
          <el-form-item
            label="自动选中小信优选"
            prop="isAutomaticTechnology"
            label-width="130px"
          >
            <el-select
              size="mini"
              v-model="queryParams.isAutomaticTechnology"
              placeholder="请选择"
              style="width: 100%"
              clearable
            >
              <el-option label="否" :value="0"></el-option>
              <el-option label="是" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10" style="padding-left: 40px">
          <el-form-item class="form-item-btn">
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['article:source:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['article:source:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['article:source:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain size="mini" @click="openNew"
          >查询ES文章数据是否存在</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['article:source:export']"
        >导出</el-button>
      </el-col>-->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      border
      :data="sourceList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 330px)"
      ref="tableRef"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column fixed="left" label="序号" align="center" type="index" />
      <el-table-column
        fixed="left"
        label="数据源中文名"
        align="left"
        prop="cnName"
        width="250"
        show-overflow-tooltip
      />
      <el-table-column
        label="数据源名称"
        align="left"
        prop="name"
        width="250"
        show-overflow-tooltip
      />
      <el-table-column
        label="来源URL"
        align="left"
        prop="url"
        min-width="300"
        show-overflow-tooltip
      />
      <el-table-column label="分类" align="center" prop="type" width="150">
        <template slot-scope="{ row }">
          {{
            classList.filter((item) => {
              return item.id == row.type;
            }).length
              ? classList.filter((item) => {
                  return item.id == row.type;
                })[0].name
              : ""
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="所属国家"
        align="left"
        prop="countryOfOrigin"
        min-width="150"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{
            scope.row.countryOfOrigin
              ? dict.type.country.filter(
                  (item) => item.value == scope.row.countryOfOrigin
                )[0]
                ? dict.type.country.filter(
                    (item) => item.value == scope.row.countryOfOrigin
                  )[0].label
                : ""
              : ""
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="类型"
        align="left"
        prop="thinkTankClassification"
        min-width="150"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{
            scope.row.thinkTankClassification
              ? dict.type.think_tank_class.filter(
                  (item) => item.value == scope.row.thinkTankClassification
                )[0]
                ? dict.type.think_tank_class.filter(
                    (item) => item.value == scope.row.thinkTankClassification
                  )[0].label
                : ""
              : ""
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="时区"
        align="left"
        prop="timeZoneId"
        min-width="150"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{
            scope.row.timeZoneId
              ? dict.type.time_zone.filter(
                  (item) => item.value == scope.row.timeZoneId
                )[0]
                ? dict.type.time_zone.filter(
                    (item) => item.value == scope.row.timeZoneId
                  )[0].label
                : ""
              : ""
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="语种"
        align="left"
        prop="languages"
        min-width="150"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{
            scope.row.languages
              ? dict.type.language.filter(
                  (item) => item.value == scope.row.languages
                )[0]
                ? dict.type.language.filter(
                    (item) => item.value == scope.row.languages
                  )[0].label
                : ""
              : ""
          }}
        </template>
      </el-table-column>
      <el-table-column
        prop="orderNum"
        label="排序"
        width="80"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="dataSourceFeasibilityIndex"
        label="DSI指数"
        width="100"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <!-- <el-table-column prop="industry" label="行业" width="150" align="center" show-overflow-tooltip></el-table-column> -->
      <el-table-column
        prop="domainList"
        label="领域"
        width="150"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          {{ formatDomain(row.domainList) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="signature"
        label="标签"
        width="150"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="创建日期"
        align="center"
        width="160"
      ></el-table-column>
      <!-- <el-table-column fixed="right" label="自动翻译" align="center" prop="test5" width="80">
        <template slot-scope="{row}">
          <el-switch v-model="row.test5" active-value="0" inactive-value="1" active-color="#13ce66"
            inactive-color="#ff4949" @change="handletest5Change(row)"></el-switch>
        </template>
      </el-table-column> -->
      <el-table-column
        label="稳定性"
        align="center"
        prop="isStability"
        width="80"
      >
        <template slot-scope="{ row }">
          <el-switch
            v-model="row.isStability"
            active-value="1"
            inactive-value="0"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="handleisStabilityChange(row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="{ row }">
          <el-switch
            v-model="row.status"
            active-value="0"
            inactive-value="1"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="handleStatusChange(row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        label="自动审核通过"
        align="center"
        prop="isAutomaticReviewed"
        width="100"
      >
        <template slot-scope="{ row }">
          <el-switch
            v-model="row.isAutomaticReviewed"
            active-value="1"
            inactive-value="0"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="handleSwitchChange(row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        label="自动选中小信优选"
        align="center"
        prop="isAutomaticTechnology"
        width="130"
      >
        <template slot-scope="{ row }">
          <el-switch
            v-model="row.isAutomaticTechnology"
            active-value="1"
            inactive-value="0"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="handleSwitchChange(row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="120"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['article:source:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['article:source:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改文章数据源对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="分类" prop="type">
              <el-select
                v-model="form.type"
                placeholder="请输入来源大分类"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, index) in classList"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="来源名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入来源名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="来源中文名称" prop="cnName">
              <el-input
                v-model="form.cnName"
                placeholder="请输入来源中文名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="来源URL" prop="url">
              <el-input v-model="form.url" placeholder="请输入来源URL" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="唯一标识(sn)" prop="sn">
              <el-input
                v-model="form.sn"
                placeholder="请输入来源唯一标识"
                :disabled="form.type != 1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="orderNum">
              <el-input v-model="form.orderNum" placeholder="请输入排序" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属国家" prop="countryOfOrigin">
              <el-select
                style="width: 100%"
                :popper-append-to-body="false"
                v-model="form.countryOfOrigin"
                placeholder="请选择所属国家"
              >
                <el-option
                  v-for="dict in dict.type.country"
                  :label="dict.label"
                  :key="'country' + dict.value"
                  :value="dict.value"
                  clearable
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="时区" prop="timeZoneId">
              <el-select
                :popper-append-to-body="false"
                v-model="form.timeZoneId"
                placeholder="请选择时区"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="(dict, index) in dict.type.time_zone"
                  :label="dict.label"
                  :key="'time_zone' + dict.value"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="语种" prop="languages">
              <el-select
                :popper-append-to-body="false"
                v-model="form.languages"
                placeholder="请选择语种"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="(dict, index) in dict.type.language"
                  :label="dict.label"
                  :key="'language' + dict.value"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="行业" prop="industry">
              <el-select clearable multiple placeholder="请选择行业" :filterable="true" default-first-option
                v-model="form.industry" style="width:100%">
                <el-option v-for="(item, index) in industry" :key="index" :label="item.industryName"
                  :value="item.industryName"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="领域" prop="domain">
              <el-select clearable multiple placeholder="请选择领域" :filterable="true" default-first-option
                v-model="form.domain" style="width:100%">
                <el-option v-for="(item, index) in domain" :key="index" :label="item.fieldName"
                  :value="item.fieldName"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="领域分类" prop="domainList">
              <el-select
                clearable
                multiple
                placeholder="请选择领域分类"
                :filterable="true"
                default-first-option
                v-model="form.domainList"
                style="width: 100%"
              >
                <el-option
                  v-for="(dict, index) in dict.type.relation_domain_code"
                  :label="dict.label"
                  :key="'relation_domain_code' + dict.value"
                  :value="String(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="DSI指数"
              prop="dataSourceFeasibilityIndex"
              :rules="[
                // { required: true, message: '请输入DSI指数', trigger: 'blur' },
                {
                  required: true,
                  validator: this.validatePublishArea,
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                v-model="form.dataSourceFeasibilityIndex"
                placeholder="请输入DSI指数"
              >
                <template slot="append">
                  <el-button type="primary" @click="getDSI">生成DSI</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型" prop="thinkTankClassification">
              <el-select
                :popper-append-to-body="false"
                v-model="form.thinkTankClassification"
                placeholder="请选择类型"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="(dict, index) in dict.type.think_tank_class"
                  :label="dict.label"
                  :key="'think_tank_class' + dict.value"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="标签" prop="signature">
              <el-tag
                :key="tag"
                v-for="tag in form.signature"
                closable
                :disable-transitions="false"
                @close="handleClose(tag)"
              >
                {{ tag }}
              </el-tag>
              <el-input
                class="input-new-tag"
                v-if="inputVisible"
                v-model="inputValue"
                ref="saveTagInput"
                size="small"
                @keyup.enter.native="handleInputConfirm"
                @blur="handleInputConfirm"
              >
              </el-input>
              <el-button
                v-else
                class="button-new-tag"
                size="small"
                @click="showInput"
                >+添加新标签</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="稳定性"
              prop="isStability"
              v-if="title == '添加文章数据源'"
            >
              <el-switch
                active-text="稳定"
                inactive-text="不稳定"
                v-model="form.isStability"
                active-value="1"
                inactive-value="0"
                active-color="#13ce66"
                inactive-color="#ff4949"
              ></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="状态"
              prop="status"
              v-if="title == '添加文章数据源'"
            >
              <el-switch
                active-text="启用"
                inactive-text="禁用"
                v-model="form.status"
                active-value="0"
                inactive-value="1"
                active-color="#13ce66"
                inactive-color="#ff4949"
              ></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- <el-form-item label="自动翻译" prop="test5" v-if="title == '添加文章数据源'">
              <el-switch active-text="启用" inactive-text="禁用" v-model="form.test5" active-value="0" inactive-value="1"
                active-color="#13ce66" inactive-color="#ff4949"></el-switch>
            </el-form-item> -->
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="生成DSI"
      :visible.sync="openDSI"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="form1"
        :model="formDSI"
        :rules="rulesDSI"
        label-width="180px"
      >
        <el-form-item label="权威性与公信力" prop="w1">
          <el-input v-model="formDSI.w1" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="数据准确性" prop="w2">
          <el-input v-model="formDSI.w2" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="数据覆盖范围与粒度" prop="w3">
          <el-input v-model="formDSI.w3" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="数据更新频率与稳定性" prop="w4">
          <el-input v-model="formDSI.w4" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="数据透明度与合规性" prop="w5">
          <el-input v-model="formDSI.w5" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="用户反馈与社区支持" prop="w6">
          <el-input v-model="formDSI.w6" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="数据源定制化与灵活性" prop="w7">
          <el-input v-model="formDSI.w7" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDSI">确 定</el-button>
        <el-button @click="cancelDSI">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/ScienceApi/index.js";
import {
  listSource,
  getSource,
  delSource,
  addSource,
  updateSource,
  getSourceSelect,
} from "@/api/article/source";
import { listClassify } from "@/api/article/classify";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import { validURL } from "@/utils/validate";
import md5 from "js-md5";
import { getDataSourceIndex } from "@/api/article/newUrl";

export default {
  name: "Source",
  dicts: [
    "time_zone",
    "language",
    "think_tank_class",
    "country",
    "relation_domain_code",
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 文章数据源表格数据
      sourceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        sn: null,
        type: null,
        name: null,
        cnName: null,
        url: null,
        summary: null,
        qrCode: null,
        status: 0,
        userId: null,
        deptId: null,
        createdTime: "",
        isAutomaticReviewed: null,
        isAutomaticTechnology: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        type: [{ required: true, message: "请选择分类", trigger: "blur" }],
        sn: [
          { required: true, message: "来源唯一标识不能为空", trigger: "blur" },
        ],
        status: [
          {
            required: true,
            message: "状态(0正常 1停用)不能为空",
            trigger: "change",
          },
        ],
        name: [
          { required: true, message: "来源名称不能为空", trigger: "blur" },
        ],
        category: [
          { required: true, message: "请选择来源分类", trigger: "change" },
        ],
        url: [{ required: true, message: "不能为空", trigger: "change" }],
      },
      industry: [],
      domain: [],
      tags: [],
      classList: [],
      openDSI: false,
      formDSI: {
        w1: null,
        w2: null,
        w3: null,
        w4: null,
        w5: null,
        w6: null,
        w7: null,
      },
      rulesDSI: {
        w1: [
          { required: true, message: "请输入内容", trigger: "blur" },
          { validator: this.validatePublishArea, trigger: "blur" },
        ],
        w2: [
          { required: true, message: "请输入内容", trigger: "blur" },
          { validator: this.validatePublishArea, trigger: "blur" },
        ],
        w3: [
          { required: true, message: "请输入内容", trigger: "blur" },
          { validator: this.validatePublishArea, trigger: "blur" },
        ],
        w4: [
          { required: true, message: "请输入内容", trigger: "blur" },
          { validator: this.validatePublishArea, trigger: "blur" },
        ],
        w5: [
          { required: true, message: "请输入内容", trigger: "blur" },
          { validator: this.validatePublishArea, trigger: "blur" },
        ],
        w6: [
          { required: true, message: "请输入内容", trigger: "blur" },
          { validator: this.validatePublishArea, trigger: "blur" },
        ],
        w7: [
          { required: true, message: "请输入内容", trigger: "blur" },
          { validator: this.validatePublishArea, trigger: "blur" },
        ],
      },
      inputVisible: false,
      inputValue: "",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    formatDomain(domainList) {
      if (!domainList) return "";
      return domainList
        .map(
          (item) =>
            this.dict.type.relation_domain_code.find((d) => d.value === item)
              ?.label
        )
        .filter(Boolean)
        .join(", ");
    },
    async industryOrDomain() {
      await API.areaList().then((data) => {
        if (data.code == 200) {
          this.domain = data.data;

          this.options = data.data;
          API.industry().then((value) => {
            this.industry = value.data;
            this.options1 = value.data;
            getSourceSelect().then((Data) => {
              this.classList = Data.data;
            });
          });
        }
      });
    },
    /** 查询文章数据源列表 */
    getList() {
      this.loading = true;
      listSource(this.queryParams)
        .then((response) => {
          this.sourceList = response.rows;
          this.total = response.total;
          this.loading = false;
          this.industryOrDomain();
          this.$nextTick(() => {
            this.scrollToTop();
          });
        })
        .catch((err) => {
          this.industryOrDomain();
        });
    },
    // 表格滚动到顶部
    scrollToTop() {
      if (this.$refs.tableRef) {
        const tableEl = this.$refs.tableRef.$el.querySelector(
          ".el-table__body-wrapper"
        );
        if (tableEl) {
          tableEl.scrollTop = 0;
        }
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.resetForm("form");
      this.form = {
        status: `0`,
        isStability: `1`,
        signature: [],
        industry: [],
        domain: [],
        domainList: [],
      };
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      console.log(this.form);
      this.title = "添加文章数据源";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getSource(id).then((response) => {
        this.form = response.data;
        if (this.form.industry) {
          this.form.industry = this.form.industry.split(",");
        } else {
          this.form.industry = [];
        }
        if (this.form.domain) {
          this.form.domain = this.form.domain.split(",");
        } else {
          this.form.domain = [];
        }
        if (this.form.signature) {
          this.form.signature = this.form.signature.split(",");
        } else {
          this.form.signature = [];
        }
        this.form.type = Number(this.form.type);
        this.open = true;
        this.title = "修改文章数据源";
      });
    },
    /** 提交按钮 */
    submitForm() {
      if (this.form.type != 1 && !this.form.id) {
        this.form.sn = md5(this.form.url);
      }
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.form));
          form.industry = form.industry.join(",");
          form.domain = form.domain.join(",");
          form.signature = form.signature.join(",");
          if (form.id != null) {
            updateSource(form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSource(form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      let ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除文章数据源编号为"' + ids + '"的数据项？')
        .then(function () {
          if (!Array.isArray(ids)) {
            ids = [ids];
            console.log(ids);
          }
          return delSource(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch((err) => {
          console.log(err);
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "article/source/export",
        {
          ...this.queryParams,
        },
        `source_${new Date().getTime()}.xlsx`
      );
    },
    handleStatusChange(item) {
      updateSource(item).then((response) => {
        console.log(response);
        this.$modal.msgSuccess("修改成功");
        this.open = false;
        this.getList();
      });
    },
    handleisStabilityChange(item) {
      updateSource(item).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.open = false;
        this.getList();
      });
    },
    handleSwitchChange(item) {
      updateSource(item).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.getList();
      });
    },
    // handletest5Change(item) {
    //   updateSource(item).then(response => {
    //     this.$modal.msgSuccess('修改成功')
    //     this.open = false
    //     this.getList()
    //   })
    // },
    openNew() {
      window.open("/availableData", "_blank");
    },
    validatePublishArea(rule, value, callback) {
      if (isNaN(value)) {
        callback(new Error("请输入有效的数字"));
      } else if (!/^\d+(\.\d{1,2})?$/.test(value)) {
        callback(new Error("小数位数最多为两位"));
      } else {
        callback();
      }
    },
    getDSI() {
      this.openDSI = true;
    },
    submitDSI() {
      this.$refs.form1.validate((valid) => {
        if (valid) {
          getDataSourceIndex({
            sn: "58af1a76a3c1e7f5222b071beb03d04f",
            is_sync_db: true,
            json_str: this.formDSI,
          }).then((response) => {
            this.form.dataSourceFeasibilityIndex = response.data;
            this.cancelDSI();
          });
        }
      });
    },
    cancelDSI() {
      this.$refs.form1.resetFields();
      this.openDSI = false;
    },
    handleClose(tag) {
      this.form.signature.splice(this.form.signature.indexOf(tag), 1);
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.form.signature.push(inputValue);
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
  },
};
</script>
<style>
.el-tag + .el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>

<style lang="scss" scoped>
::v-deep .queryForm {
  .el-col-5 {
    max-width: 20%;
    flex: 0 0 20%;
  }
  .el-form-item {
    width: 100%;
    margin-right: 0;
  }
  .el-form-item__content {
    width: calc(100% - 110px);
  }
  .form-item-btn {
    .el-form-item__content {
      width: 100%;
    }
  }

  .isAutomaticTechnology {
    .el-form-item__content {
      width: calc(100% - 130px);
    }
  }
}
</style>
