import Cookies from "js-cookie";

const TokenKey = "Dpx-Company-Admin-Token";
const TokenKey2 = "xtyzx-Admin-Token";

export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token) {
  return Cookies.set(Token<PERSON><PERSON>, token);
}

export function removeToken() {
  return Cookies.remove(TokenKey);
}

export function getToken2() {
  return Cookies.get(TokenKey2);
}

export function setToken2(token) {
  return Cookies.set(TokenKey2, token);
}

export function removeToken2() {
  return Cookies.remove(TokenKey2);
}
