<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="98px">
      <el-form-item label="文章唯一标识" prop="sourceSn">
        <el-input v-model="queryParams.sourceSn" placeholder="请输入文章唯一标识" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="关键字" prop="keywords">
        <el-input v-model="queryParams.keywords" placeholder="请输入关键字，多个关键字可用逗号分隔。" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="顺序序号" prop="orderNum">
        <el-input v-model="queryParams.orderNum" placeholder="请输入顺序序号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="发布地区" prop="publishArea">
        <el-input v-model="queryParams.publishArea" placeholder="请输入发布地区" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['policy:source:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['policy:source:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['policy:source:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['policy:source:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="sourceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column width="120" label="文章唯一标识" align="center" prop="sourceSn" />
      <el-table-column label="政策源码" align="center" prop="sourceName" min-width="150" show-overflow-tooltip />
      <el-table-column label="政策源网站地址" align="center" min-width="180" prop="sourceUrl" show-overflow-tooltip />
      <el-table-column label="关键字" align="center" prop="keywords" min-width="80" show-overflow-tooltip />
      <el-table-column label="政策概要或主要内容摘要" align="center" prop="summary" min-width="200" show-overflow-tooltip />
      <el-table-column label="顺序序号" align="center" prop="orderNum" width="80" />
      <el-table-column label="封面图片" align="center" prop="cover" width="100" />
      <el-table-column label="发布地区" align="center" prop="publishArea" min-width="120" show-overflow-tooltip />
      <el-table-column label="状态" align="center" key="status" width="120">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
            @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['policy:source:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['policy:source:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改政策库采集源对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="文章唯一标识" prop="sourceSn">
          <el-input v-model="form.sourceSn" placeholder="请输入文章唯一标识" />
        </el-form-item>
        <el-form-item label="政策源码" prop="sourceName">
          <el-input v-model="form.sourceName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="政策源网站地址" prop="sourceUrl">
          <el-input v-model="form.sourceUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="关键字" prop="keywords">
          <el-input v-model="form.keywords" placeholder="请输入关键字，多个关键字可用逗号分隔。" />
        </el-form-item>
        <el-form-item label="政策概要或主要内容摘要" prop="summary">
          <el-input v-model="form.summary" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="顺序序号" prop="orderNum">
          <el-input v-model="form.orderNum" placeholder="请输入顺序序号" />
        </el-form-item>
        <el-form-item label="封面图片" prop="cover">
          <el-input v-model="form.cover" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="发布地区" prop="publishArea">
          <el-input v-model="form.publishArea" placeholder="请输入发布地区" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSource, getSource, delSource, addSource, updateSource } from "@/api/policy/source";

export default {
  name: "Source",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 政策库采集源表格数据
      sourceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        sourceSn: null,
        sourceName: null,
        sourceUrl: null,
        keywords: null,
        summary: null,
        orderNum: null,
        cover: null,
        publishCode: null,
        publishArea: null,
        status: null,
        userId: null,
        deptId: null,
        deleteBy: null,
        deleteTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        // orderNum: [
        //   { required: true, message: "顺序序号不能为空", trigger: "blur" }
        // ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询政策库采集源列表 */
    getList() {
      this.loading = true;
      listSource(this.queryParams).then(response => {
        this.sourceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        sourceSn: null,
        sourceName: null,
        sourceUrl: null,
        keywords: null,
        summary: null,
        orderNum: null,
        cover: null,
        publishCode: null,
        publishArea: null,
        status: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null,
        delFlag: null,
        deleteBy: null,
        deleteTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加政策库采集源";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getSource(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改政策库采集源";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateSource(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSource(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids;
      this.$modal.confirm('是否确认删除政策库采集源编号为"' + ids + '"的数据项？').then(function () {
        return delSource(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('policy/source/export', {
        ...this.queryParams
      }, `source_${new Date().getTime()}.xlsx`)
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === '0' ? '启用' : '停用'
      this.$modal.confirm('确认要"' + text + '吗？').then(() => {
        return updateSource(row)
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
      }).catch(function () {
        row.status = row.status === '0' ? '1' : '0'
      })
    },
  }
};
</script>
