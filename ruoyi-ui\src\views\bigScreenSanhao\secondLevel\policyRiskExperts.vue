<template>
  <div
    v-if="visible"
    class="custom-dialog-mask"
    @click="handleMaskClick"
    :style="{ zIndex: currentZIndex }"
  >
    <div
      class="custom-dialog"
      :class="{ 'policy-risk-experts-fullscreen': isFullscreen }"
      :style="isFullscreen ? {} : { width: width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{
          tianExpertsDetail && tianExpertsDetail.proposalsExperts
        }}</span>
        <div style="display: flex; align-items: center">
          <div
            @click="handleScreen"
            :title="isFullscreen ? '退出全屏' : '全屏'"
            style="
              margin-right: 20px;
              cursor: pointer;
              color: #ffffff;
              font-size: 20px;
            "
          >
            <i
              :class="isFullscreen ? 'el-icon-rank' : 'el-icon-full-screen'"
              style="width: 20px; height: 20px"
            ></i>
          </div>
          <div class="custom-dialog-close" @click="closeDialog"></div>
        </div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="bg-box-title">人物简介</div>
          <div class="bg-box-content">
            <div class="jianjie">
              <div class="touxiang">
                <img
                  v-if="tianExpertsDetail.proposalsExpertsCover"
                  :src="getImageUrl(tianExpertsDetail.proposalsExpertsCover)"
                  alt=""
                />
                <el-empty v-else :image-size="106" style="padding: 0" />
              </div>
              <div class="text">
                <div class="jianjie-text">
                  <div v-if="tianExpertsDetail.proposalsExperts">
                    {{ tianExpertsDetail.proposalsExperts }}
                  </div>
                  <div
                    class="jianjie-text-item"
                    v-if="tianExpertsDetail.belongToGroup"
                  >
                    {{ tianExpertsDetail.belongToGroup }}
                  </div>
                  <div
                    class="jianjie-text-item"
                    v-if="tianExpertsDetail.belongToArea"
                  >
                    {{ tianExpertsDetail.belongToArea }}
                  </div>
                </div>
                <div>{{ tianExpertsDetail.expertSummary }}</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 议员关系桑葚图 -->
        <sankeyChart2
          title="人物关系"
          param-type="proposalsExperts"
          :param-value="tianExpertsDetail.proposalsExperts"
          style="width: 100%"
        />
        <div class="bg-box">
          <div class="bg-box-title">相关新闻</div>
          <div
            class="bg-box-content"
            v-loading="newsLoading"
            element-loading-background="rgba(27, 40, 59, 0.8)"
            style="min-height: 100px"
          >
            <ul class="article-list" v-if="newsArticles.length > 0">
              <li
                v-for="article in newsArticles"
                :key="article.id"
                class="article-item"
              >
                <div class="article-title" @click="openNewsDetail(article)">
                  {{ article.cnTitle }}
                </div>
                <div class="article-publishTime">
                  {{ formatPublishTime(article.publishTime) }}
                </div>
              </li>
            </ul>
            <div v-else-if="!newsLoading">
              <div class="no-data">暂无数据</div>
            </div>
            <pagination
              v-show="newsTotal > 0"
              :total="newsTotal"
              :page.sync="newsQueryParams.pageNum"
              :limit.sync="newsQueryParams.pageSize"
              @pagination="getNewsList"
            />
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">相关提案</div>
          <div class="bg-box-content">
            <!-- 仪表盘容器 -->
            <div class="dial-container">
              <div class="dial-item">
                <div
                  id="left-dial-chart"
                  style="width: 100%; height: 200px"
                ></div>
              </div>
              <div class="dial-item">
                <div
                  id="right-dial-chart"
                  style="width: 100%; height: 200px"
                ></div>
              </div>
            </div>
            <el-table
              :data="tianExpertsDetail.proposals"
              border
              style="width: 100%"
            >
              <el-table-column
                prop="proposalsTitle"
                align="center"
                label="提案名称"
                width="300"
              >
                <template slot-scope="scope">
                  <div
                    @click="openpOlicyRiskContent(scope.row.proposalsSn)"
                    style="color: #0ec2f4e6; cursor: pointer; font-weight: bold"
                  >
                    {{ scope.row.proposalsTitle }}
                  </div>
                  <div class="jianjie-text" style="justify-content: center">
                    <div
                      class="jianjie-text-item"
                      v-if="scope.row.proposalsToChina"
                    >
                      {{ formatProposalsToChina(scope.row.proposalsToChina) }}
                    </div>
                    <div class="jianjie-text-item" v-if="scope.row.labelName">
                      {{ scope.row.labelName }}
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="summary" align="center" label="提案摘要" />
              <el-table-column
                prop="publishTime"
                align="center"
                label="提案时间"
                width="120"
              />
            </el-table>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">合作关系</div>
          <div class="bg-box-content">
            <div
              v-if="hasCooperationData"
              id="cooperation-chart"
              style="width: 100%; height: 400px"
            ></div>
            <div
              v-else
              class="no-cooperation-data"
              style="
                width: 100%;
                height: 100px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #ffffff;
                font-size: 16px;
              "
            >
              暂无合作关系
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getNewZIndex, getBaseZIndex } from "@/utils/zIndexManager";
import * as echarts from "echarts";
import {
  proposalsLeftDial,
  proposalsRightDial,
  proposalsCooperation,
  getXyArticleListByEsRetrieval,
} from "@/api/bigScreen/sanhao";
import sankeyChart2 from "../components/sankeyChart2.vue";

export default {
  name: "PolicyRiskExperts",
  components: {
    sankeyChart2,
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      currentZIndex: getBaseZIndex(),
      // 和弦图最大显示数量常量
      MAX_COOPERATION_ITEMS: 50,
      activeContent: "proposalsContent",
      cooperationChart: null,
      leftDialChart: null,
      rightDialChart: null,
      hasCooperationData: false,
      // 人物相关新闻数据
      newsArticles: [],
      newsQueryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      newsTotal: 0,
      newsLoading: false,
      // 全屏状态
      isFullscreen: false,
      // ResizeObserver 实例
      resizeObserver: null,
      // 防抖定时器
      resizeTimer: null,
    };
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    closeOnClickMask: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 1000,
    },
    tianExpertsDetail: {
      type: Object,
      default: () => {},
    },
  },

  mounted() {
    // 添加ESC键监听
    document.addEventListener("keydown", this.handleKeydown);
    // 添加窗口大小变化监听
    window.addEventListener("resize", this.handleWindowResize);
  },

  beforeDestroy() {
    // 销毁图表实例
    if (this.cooperationChart) {
      this.cooperationChart.destroy();
      this.cooperationChart = null;
    }
    if (this.leftDialChart) {
      this.leftDialChart.destroy();
      this.leftDialChart = null;
    }
    if (this.rightDialChart) {
      this.rightDialChart.destroy();
      this.rightDialChart = null;
    }
    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    // 清理定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    }
    // 移除ESC键监听
    document.removeEventListener("keydown", this.handleKeydown);
    // 移除窗口大小变化监听
    window.removeEventListener("resize", this.handleWindowResize);
  },

  watch: {
    visible: {
      handler(value) {
        this.activeContent = "proposalsContent";
        // 当对话框变为可见时，更新 z-index
        if (value) {
          this.bringToFront();
          // 重置全屏状态
          this.isFullscreen = false;
          // 重置合作关系数据状态
          this.hasCooperationData = false;
          this.newsQueryParams = {
            pageNum: 1,
            pageSize: 10,
          };
          this.newsArticles = [];
          this.newsTotal = 0;
          // 延迟初始化图表，确保DOM已渲染
          this.$nextTick(() => {
            try {
              this.initCooperationChart();
              this.initDialCharts();
              this.getNewsList(); // 初始化人物相关新闻
              this.setupResizeListeners(); // 设置尺寸监听
            } catch (error) {
              console.error("Failed to initialize charts:", error);
            }
          });
        }
      },
    },
  },
  methods: {
    // 判断图片地址是否需要拼接baseUrl
    getImageUrl(imageUrl) {
      if (!imageUrl) return "";

      // 判断是否为完整的线上地址（包含http://或https://）
      if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
        return imageUrl;
      }

      // 如果不是完整地址，则拼接baseUrl
      return this.baseUrl + imageUrl;
    },

    // 提升组件到最上层的方法
    bringToFront() {
      this.currentZIndex = getNewZIndex();
    },

    closeDialog() {
      this.$emit("update:visible", false);
    },

    handleMaskClick() {
      if (this.closeOnClickMask) {
        this.closeDialog();
      }
    },

    openpOlicyRiskContent(id) {
      this.$emit("openpOlicyRiskContent", id);
    },

    // 动态加载脚本
    loadScript(src) {
      return new Promise((resolve, reject) => {
        if (document.querySelector(`script[src="${src}"]`)) {
          resolve();
          return;
        }
        const script = document.createElement("script");
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
      });
    },

    // 初始化合作关系和弦图
    async initCooperationChart() {
      console.log("tianExpertsDetail:", this.tianExpertsDetail);
      if (!this.tianExpertsDetail) {
        console.log(
          "tianExpertsDetail not found, setting hasCooperationData to false"
        );
        this.hasCooperationData = false;
        return;
      }

      // 检查专家标识字段（可能是expertsSn或其他字段）
      const expertId = this.tianExpertsDetail.expertsSn;
      if (!expertId) {
        console.log("Expert ID not found, setting hasCooperationData to false");
        this.hasCooperationData = false;
        return;
      }
      console.log("Using expert ID:", expertId);

      try {
        // 获取合作关系数据
        console.log(
          "Calling proposalsCooperation API with expertsSn:",
          expertId
        );
        const response = await proposalsCooperation({
          expertsSn: expertId,
        });
        console.log("API response:", response);
        const cooperationData = response.data || [];
        console.log("Cooperation data:", cooperationData);

        // 检查是否有合作关系数据
        if (!cooperationData || cooperationData.length === 0) {
          this.hasCooperationData = false;
          return;
        }

        // 数据量优化：如果数据过多，只显示前N个最重要的合作关系
        let processedData = cooperationData;

        if (cooperationData.length > this.MAX_COOPERATION_ITEMS) {
          console.log(
            `数据量过大(${cooperationData.length}条)，只显示前${this.MAX_COOPERATION_ITEMS}条最重要的合作关系`
          );
          // 按合作次数降序排序，取前N个
          processedData = cooperationData
            .sort((a, b) => (b.count || 0) - (a.count || 0))
            .slice(0, this.MAX_COOPERATION_ITEMS);
        }

        // 设置有数据状态，等待DOM更新
        this.hasCooperationData = true;

        // 等待DOM更新后再获取容器
        await this.$nextTick();

        const container = document.getElementById("cooperation-chart");
        if (!container) {
          console.log("cooperation-chart container not found after DOM update");
          this.hasCooperationData = false;
          return;
        }

        // 动态加载Highcharts脚本
        await this.loadScript("/highcharts.js");
        await this.loadScript("/sankey.js");
        await this.loadScript("/dependency-wheel.js");

        // 确保Highcharts已加载
        if (typeof window.Highcharts === "undefined") {
          throw new Error("Highcharts failed to load");
        }

        const Highcharts = window.Highcharts;

        // 处理合作关系数据
        // 接口返回的都是与当前专家有关系的其他专家
        // 当前专家信息从 tianExpertsDetail 获取
        const currentExpertName = this.tianExpertsDetail.proposalsExperts;

        // 构建和弦图数据：当前专家与其他专家的关系
        const data = processedData.map((item) => [
          currentExpertName, // 当前专家名称
          item.proposalsExperts, // 其他专家名称
          item.count || 1, // 合作次数/权重，确保有默认值
        ]);
        console.log(`渲染和弦图数据，共${data.length}条关系`);

        // 销毁之前的图表实例以释放内存
        if (this.cooperationChart) {
          this.cooperationChart.destroy();
          this.cooperationChart = null;
        }

        this.cooperationChart = Highcharts.chart("cooperation-chart", {
          chart: {
            type: "dependencywheel",
            backgroundColor: "transparent",
            animation: data.length > 30 ? false : true, // 数据量大时禁用动画提升性能
          },
          title: {
            // text: cooperationData.length > this.MAX_COOPERATION_ITEMS ?
            //   `显示前${this.MAX_COOPERATION_ITEMS}个最重要的合作关系（共${cooperationData.length}个）` : "",
            text: "",
            style: {
              color: "#ffffff",
            },
          },
          credits: {
            enabled: false, // 隐藏右下角的Highcharts水印
          },
          accessibility: {
            enabled: false, // 禁用无障碍功能提升性能
          },
          series: [
            {
              keys: ["from", "to", "weight"],
              data: data,
              name: "人员关系",
              dataLabels: {
                color: "#ffffff",
                style: {
                  textOutline: "none",
                  fontSize: data.length > 30 ? "10px" : "12px", // 数据量大时使用更小字体
                },
                textPath: {
                  enabled: true, // 启用文本路径让文字沿圆弧显示，避免重叠
                },
                distance: 10, // 适中的距离，既不重叠又不太远
                enabled: data.length <= 30, // 数据量大时禁用普通节点的标签显示
              },
              size: "95%",
              nodes: [
                {
                  id: currentExpertName,
                  color: "#ff0000", // 当前专家固定为红色
                  dataLabels: {
                    enabled: true, // 确保当前专家标签始终显示
                    color: "#ffffff",
                    style: {
                      fontWeight: "bold",
                      fontSize: "14px",
                      textOutline: "none",
                    },
                    distance: 10,
                    format: "{point.id}", // 确保显示节点ID
                    textPath: {
                      enabled: true, // 启用文本路径，让文字沿着圆弧显示
                    },
                  },
                },
              ],
              animation: data.length > 30 ? false : true, // 数据量大时禁用动画
            },
          ],
          plotOptions: {
            dependencywheel: {
              dataLabels: {
                color: "#ffffff",
                style: {
                  fontSize: data.length > 30 ? "10px" : "12px",
                  fontWeight: "normal",
                },
              },
              animation: data.length > 30 ? false : true,
            },
          },
          tooltip: {
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            style: {
              color: "#ffffff",
            },
            pointFormat:
              "<b>{point.from}</b> → <b>{point.to}</b>: 合作密切度 {point.weight}",
            nodeFormat: "<b>{point.id}</b><br/>合作密切度: {point.sum}",
          },
        });
      } catch (error) {
        console.error("Failed to create dependency wheel chart:", error);
        // 如果API调用或脚本加载失败，设置为无数据状态
        this.hasCooperationData = false;
      }
    },

    // 初始化仪表盘
    async initDialCharts() {
      if (!this.tianExpertsDetail) {
        return;
      }

      const expertsSn = this.tianExpertsDetail.expertsSn;
      if (!expertsSn) {
        return;
      }

      try {
        // 初始化左侧仪表盘
        await this.initLeftDial(expertsSn);
        // 初始化右侧仪表盘
        await this.initRightDial(expertsSn);
      } catch (error) {
        console.error("Failed to initialize dial charts:", error);
      }
    },

    // 初始化左侧半圆环形图
    async initLeftDial(expertsSn) {
      const container = document.getElementById("left-dial-chart");
      if (!container) return;

      try {
        const response = await proposalsLeftDial({ expertsSn });
        const data = response.data || [];

        this.leftDialChart = echarts.init(container);
        const option = this.getDialOption(data);
        this.leftDialChart.setOption(option);
      } catch (error) {
        console.error("Failed to initialize left dial:", error);
        container.innerHTML =
          '<div style="color: #ffffff; text-align: center; padding: 50px;">左侧图表加载失败</div>';
      }
    },

    // 初始化右侧半圆环形图
    async initRightDial(expertsSn) {
      const container = document.getElementById("right-dial-chart");
      if (!container) return;

      try {
        const response = await proposalsRightDial({ expertsSn });
        const data = response.data || [];

        this.rightDialChart = echarts.init(container);
        const option = this.getDialOption(data, true);
        this.rightDialChart.setOption(option);
      } catch (error) {
        console.error("Failed to initialize right dial:", error);
        container.innerHTML =
          '<div style="color: #ffffff; text-align: center; padding: 50px;">右侧图表加载失败</div>';
      }
    },

    // 获取半圆环形图配置
    getDialOption(data, isRightChart = false) {
      // 处理数据，转换为饼图数据格式
      const pieData = data.map((item) => ({
        value: item.count,
        name: isRightChart
          ? item.labelName
          : this.formatProposalsToChina2(item.proposalsToChina),
        percentage: item.percentage,
      }));

      // 定义颜色
      const colors = ["#4992ff", "#7cffb2", "#fddd60"];

      return {
        color: colors,
        series: [
          {
            type: "pie",
            radius: ["45%", "90%"],
            center: ["50%", "70%"],
            startAngle: 180,
            endAngle: 360,
            data: pieData,
            label: {
              show: true,
              position: "outside",
              formatter: "{b}: {d}%",
              color: "#ffffff",
              fontSize: 16,
              distanceToLabelLine: 8,
            },
            labelLine: {
              show: true,
              length: 20,
              length2: 30,
              lineStyle: {
                color: "#fff",
              },
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
    },

    // 在methods中添加格式化方法
    formatProposalsToChina(value) {
      const map = {
        1: "提及了中国",
        2: "对中国产生影响",
        3: "其他",
      };
      return map[value] || value;
    },
    formatProposalsToChina2(value) {
      const map = {
        1: "提及中国",
        2: "影响中国",
        3: "其他",
      };
      return map[value] || value;
    },

    // 获取人物相关新闻列表
    getNewsList() {
      if (!this.tianExpertsDetail || !this.tianExpertsDetail.proposalsExperts) {
        return;
      }

      this.newsLoading = true;
      getXyArticleListByEsRetrieval({
        // type: "0",
        pageNum: this.newsQueryParams.pageNum,
        pageSize: this.newsQueryParams.pageSize,
        keywords: this.tianExpertsDetail.proposalsExperts,
        dateType: 10,
        isSort: "0",
      })
        .then((res) => {
          const originalArticles = res.list || [];
          this.newsArticles = this.deduplicateArticles(originalArticles);
          this.newsTotal = res.total || 0;
          this.newsLoading = false;
        })
        .catch((error) => {
          console.error("获取人物相关新闻失败:", error);
          this.newsArticles = [];
          this.newsTotal = 0;
          this.newsLoading = false;
        });
    },

    // 打开新闻详情
    openNewsDetail(item) {
      this.$emit("openArticleDetail", item);
    },

    // 格式化发布时间，去掉时分秒
    formatPublishTime(publishTime) {
      if (!publishTime) return "";
      // 如果是日期字符串，提取年月日部分
      if (typeof publishTime === "string") {
        return publishTime.split(" ")[0];
      }
      // 如果是Date对象，格式化为YYYY-MM-DD
      if (publishTime instanceof Date) {
        return publishTime.toISOString().split("T")[0];
      }
      return publishTime;
    },

    // 去重文章，相同标题只保留一条并标记数量
    deduplicateArticles(articles) {
      if (!articles || articles.length === 0) return [];

      const titleMap = new Map();

      // 统计相同标题的文章
      articles.forEach((article) => {
        if (!article.cnTitle) return;

        // 去掉标题中的所有空格进行比较
        const normalizedTitle = article.cnTitle.replace(/\s+/g, "");

        if (titleMap.has(normalizedTitle)) {
          titleMap.get(normalizedTitle).count++;
        } else {
          titleMap.set(normalizedTitle, {
            article: { ...article },
            count: 1,
          });
        }
      });

      // 生成去重后的文章列表
      const deduplicatedArticles = [];
      titleMap.forEach(({ article, count }) => {
        if (count > 1) {
          // 如果有重复，在标题后添加数量标识
          article.cnTitle = `${article.cnTitle}(${count})`;
        }
        deduplicatedArticles.push(article);
      });

      return deduplicatedArticles;
    },

    // 设置尺寸变化监听
    setupResizeListeners() {
      // 创建 ResizeObserver 监听容器尺寸变化
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(() => {
          // 使用防抖处理，避免频繁触发
          clearTimeout(this.resizeTimer);
          this.resizeTimer = setTimeout(() => {
            this.handleResize();
          }, 100);
        });

        // 监听图表容器的尺寸变化
        const cooperationContainer =
          document.getElementById("cooperation-chart");
        const leftDialContainer = document.getElementById("left-dial-chart");
        const rightDialContainer = document.getElementById("right-dial-chart");

        if (cooperationContainer) {
          this.resizeObserver.observe(cooperationContainer);
        }
        if (leftDialContainer) {
          this.resizeObserver.observe(leftDialContainer);
        }
        if (rightDialContainer) {
          this.resizeObserver.observe(rightDialContainer);
        }
      }
    },

    // 处理尺寸变化
    handleResize() {
      this.$nextTick(() => {
        if (this.cooperationChart) {
          this.cooperationChart.reflow();
        }
        if (this.leftDialChart) {
          this.leftDialChart.resize();
        }
        if (this.rightDialChart) {
          this.rightDialChart.resize();
        }
      });
    },

    // 全屏切换
    handleScreen() {
      this.isFullscreen = !this.isFullscreen;

      // 延迟调整图表大小，确保DOM更新完成
      this.$nextTick(() => {
        setTimeout(() => {
          this.handleResize();
        }, 300); // 等待CSS动画完成
      });
    },

    // 处理键盘事件
    handleKeydown(event) {
      // 按ESC键退出全屏
      if (event.key === "Escape" && this.isFullscreen && this.visible) {
        this.isFullscreen = false;
      }
    },

    // 处理窗口大小变化
    handleWindowResize() {
      if (this.isFullscreen) {
        // 重新调整图表大小
        this.handleResize();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  // z-index: 1000; // 移除固定 z-index，使用动态值

  // 确保在所有分辨率下都能正确覆盖
  min-width: 100%;
  min-height: 100%;
  overflow: hidden;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("../../../assets/bigScreenSanhao/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;
    transition: all 0.3s ease;

    &.policy-risk-experts-fullscreen {
      width: calc(100vw - 40px) !important;
      height: calc(100vh - 40px) !important;
      max-width: none !important;
      max-height: none !important;
      margin: 0 !important;
      // 确保在所有分辨率下都能正确显示
      min-width: calc(100% - 40px) !important;
      min-height: calc(100% - 40px) !important;

      .custom-dialog-body {
        height: calc(100% - 80px); // 减去header高度和padding
        max-height: calc(100% - 80px);
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("../../../assets/bigScreenSanhao/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("../../../assets/bigScreenSanhao/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      height: 800px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0px 20px 0px;

      .bg-box {
        position: relative;
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .toggle-divs {
          position: absolute;
          display: flex;
          top: 5px;
          right: 10px;

          div {
            width: 48px;
            height: 30px;
            background-color: #3a4d68ff;
            color: white;
            padding: 5px 10px;
            cursor: pointer;
            user-select: none; // 禁止文本选中

            &:hover {
              background-color: #1d3046;
            }

            // 选中状态的高亮样式
            &.active {
              background-color: #ff9d00ff;
              color: white;
            }
          }
        }

        .bg-box-content {
          // display: flex;
          // justify-content: center;
          font-size: 16px;

          .dial-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;

            .dial-item {
              flex: 1;
              display: flex;
              justify-content: center;
              align-items: center;
              padding: 10px;
            }
          }

          ::v-deep .el-table__header th {
            background-color: #1f3850 !important;
            color: rgba(255, 255, 255);
            font-size: 16px;
          }

          ::v-deep .el-table__body td {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
          }

          ::v-deep .el-descriptions__label {
            background-color: #1f3850;
            color: rgba(255, 255, 255);
            font-size: 16px;
            text-align: center;
          }

          ::v-deep .el-descriptions__content {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            text-align: center;
          }

          .jianjie {
            display: flex;

            .touxiang {
              width: 106px;
              height: 143px;
              display: flex;
              align-items: center;
              justify-content: center;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }

            .text {
              flex: 1;
              padding-left: 20px;
            }
          }

          // 新闻列表样式
          .article-list {
            list-style: none;
            padding: 0;
            margin: 0;

            .article-item {
              display: flex;
              height: 40px;
              line-height: 40px;
              margin-bottom: 10px;
              font-size: 16px;

              &:last-child {
                margin-bottom: 0;
              }

              .article-title {
                cursor: pointer;
                width: calc(100% - 100px);
                font-size: 18px;
                // font-weight: 600;
                color: rgba(255, 255, 255, 0.9);
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .article-publishTime {
                width: 100px;
                color: rgba(255, 255, 255, 0.5);
              }
            }
          }

          .no-data {
            text-align: center;
            color: #fff;
            padding: 20px 0;
          }
        }
      }
    }
  }
}
::v-deep(.el-empty__description) {
  display: none;
}

.jianjie-text {
  display: flex;
  align-items: center;

  & > div:first-child {
    margin-right: 10px;
  }

  .jianjie-text-item {
    margin-right: 10px;
    background: #233c5c;
    padding: 4px 8px;
    border-radius: 4px;
    color: #dd8e0d;
  }
}

::v-deep .pagination-container {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 20px 0 0;
  padding-bottom: 0px !important;

  .el-select__wrapper,
  .el-input__wrapper {
    .el-select__placeholder {
      color: #fff;
    }

    background: #2a304000;
    border-color: #ffffff;
  }

  .el-input__inner {
    color: #fff;
  }
}

::v-deep .el-pagination__total,
::v-deep .el-pagination__jump {
  color: #f2f2f2;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination button:disabled {
  background-color: #ffffff00 !important;
  color: #fff !important;
}

::v-deep .el-pager li {
  background: #ffffff00 !important;
  color: #fff !important;

  &.active {
    color: #1890ff !important;
  }
}
</style>
