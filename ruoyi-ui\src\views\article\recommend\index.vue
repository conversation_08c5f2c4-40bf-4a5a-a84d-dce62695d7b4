<template>
  <div class="app-container">
    <!-- <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="标题" prop="cnTitle">
        <el-input v-model="queryParams.cnTitle" placeholder="请输入用户ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="留言内容" prop="content">
        <el-input v-model="queryParams.content" placeholder="请输入单位ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="留言人" prop="createBy">
        <el-input v-model="queryParams.createBy" placeholder="请输入单位ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form> -->

    <el-table v-loading="loading" :data="workList" height="calc(100vh - 150px)" ref="tableRef">
      <el-table-column width="80" label="序号" align="center">
        <template v-slot="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="来源" show-overflow-tooltip width="200" prop="sourceName" />
      <el-table-column label="文章标题" show-overflow-tooltip min-width="300" prop="title">
        <template slot-scope="scope">
          <span class="title" @click="openNewView(scope.row)">{{ scope.row.title }}</span>
        </template>
      </el-table-column>
      <el-table-column label="推荐数量" show-overflow-tooltip width="100" prop="total" align="center">
        <template slot-scope="scope">
          <span class="title" @click="openUserList(scope.row)">{{ scope.row.total }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body :close-on-click-modal="false">
      <userList v-if="open" :id="articleId"></userList>
    </el-dialog>
  </div>
</template>
  
<script>
import API from '@/api/ScienceApi/index.js'
import userList from "./userList.vue";

export default {
  components: { userList },
  data() {
    return {
      loading: true,
      total: 0,
      workList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      title: "",
      open: false,
      articleId: null,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询文章工作列表 */
    getList() {
      this.loading = true;
      API.recommendManageList(this.queryParams).then(response => {
        this.workList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.$nextTick(() => {
          this.scrollToTop();
        });
      });
    },
    // 表格滚动到顶部
    scrollToTop() {
      if (this.$refs.tableRef) {
        const tableEl = this.$refs.tableRef.$el.querySelector(
          ".el-table__body-wrapper"
        );
        if (tableEl) {
          tableEl.scrollTop = 0;
        }
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    openNewView(item) {
      window.open(`/expressDetails?id=${item.articleId}&docId=${item.articleId}`, '_blank')
    },
    openUserList(row) {
      this.title = row.title;
      this.articleId = row.articleId;
      this.open = true;
    },
  }
};
</script>
<style lang="scss" scoped>
.title:hover {
  color: #1d8af0;
  border-bottom: solid 1px #1d8af0;
  cursor: pointer;
}
</style>