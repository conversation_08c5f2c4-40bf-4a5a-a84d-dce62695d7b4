<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="产品名称" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属产品" prop="productId">
        <el-select
          v-model="queryParams.productId"
          placeholder="选择产品"
          clearable="true"
          style="width: 100%"
          @keyup.enter.native="handleQuery"
        >
          <el-option
            v-for="(item, index) in productList"
            :key="index"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="标签" prop="tags">
        <el-input
          v-model="queryParams.tags"
          placeholder="请输入标签"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['large:productData:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['large:productData:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['large:productData:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['large:productData:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="productDataList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 230px)"
      ref="tableRef"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index" />

      <el-table-column
        label="产品名称"
        align="center"
        prop="title"
        width="120px"
      />
      <el-table-column
        label="所属产品"
        align="center"
        prop="productId"
        width="100px"
      >
        <template slot-scope="scope">
          {{ getProductDict(scope.row.productId) }}
        </template>
      </el-table-column>

      <el-table-column
        label="封面图片"
        align="center"
        prop="cover"
        width="100px"
      >
        <template slot-scope="scope">
          <ImagePreview
            v-if="scope.row.cover"
            :src="scope.row.cover"
            :width="'100px'"
            :height="'60px'"
          />
        </template>
      </el-table-column>
      <el-table-column label="标签" align="center" prop="tags" width="120px" />
      <el-table-column
        label="附件名称"
        align="center"
        prop="fileName"
        show-overflow-tooltip="true"
      />
      <el-table-column
        label="附件类型"
        align="center"
        prop="fileType"
        width="100px"
      />
      <el-table-column
        label="附件大小"
        align="center"
        prop="fileSize"
        width="100px"
      />
      <el-table-column label="状态" align="center" prop="status" width="100px">
        <template slot-scope="scope">
          {{ scope.row.status == "1" ? "停用" : "正常" }}
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        align="center"
        prop="remark"
        show-overflow-tooltip="true"
      />
      <el-table-column
        label="发布时间"
        align="center"
        prop="publishTime"
        show-overflow-tooltip="true"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['large:productData:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['large:productData:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改产品数据列对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="50%" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="产品名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="选择产品" prop="productId">
          <el-select
            v-model="form.productId"
            placeholder="选择产品"
            style="width: 100%"
          >
            <el-option
              v-for="(item, index) in productList"
              :key="index"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发布时间" prop="publishTime">
          <el-date-picker
            clearable
            v-model="form.publishTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择发布时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="封面图片" prop="cover">
          <ImageUpload :limit="1" :value="form.cover" @input="imageFun" />
        </el-form-item>
        <el-form-item label="摘要" prop="summary">
          <editor v-model="form.summary" :min-height="192" />
        </el-form-item>
        <el-form-item label="标签" prop="tags">
          <el-input v-model="form.tags" placeholder="请输入标签" />
        </el-form-item>
        <el-form-item label="附件上传" prop="articleUrl">
          <fileUpload
            :limit="1"
            :fileType="['doc', 'pdf', 'docx']"
            :value="form.articleUrl"
            @input="(data) => articleFun(data)"
          />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import fileUpload from "./fileUpload.vue";
import {
  listProductData,
  getProductData,
  delProductData,
  addProductData,
  updateProductData,
} from "@/api/large/productData";
import { listProduct } from "@/api/large/product";

export default {
  name: "ProductData",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 产品数据列
      productList: [],
      //表格数据
      productDataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        title: null,
        productId: null,
        tags: null,
      },
      // 表单参数
      form: {
        cover: null,
      },
      // 表单校验
      rules: {
        productId: [
          { required: true, message: "产品ID不能为空", trigger: "blur" },
        ],
        title: [
          { required: true, message: "产品名称不能为空", trigger: "blur" },
        ],
        status: [
          {
            required: true,
            message: "状态不能为空",
            trigger: "change",
          },
        ],
        publishTime: [
          {
            required: true,
            message: "时间不能为空",
            trigger: "change",
          },
        ],
      },
      isReset: false,
    };
  },
  components: { fileUpload },
  async created() {
    await this.getProduct();
    this.getList();
  },
  methods: {
    getProductDict(id) {
      return (
        this.productList.filter((item) => {
          return item.id === id;
        })?.[0]?.name ?? ""
      );
    },
    getProduct() {
      listProduct({
        pageNum: 1,
        pageSize: 100000,
      }).then((response) => {
        this.productList = response.rows;
      });
    },
    /** 查询产品数据列列表 */
    getList() {
      this.loading = true;
      listProductData(this.queryParams).then((response) => {
        this.productDataList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.$nextTick(() => {
          this.scrollToTop();
        });
      });
    },
    // 表格滚动到顶部
    scrollToTop() {
      if (this.$refs.tableRef) {
        const tableEl = this.$refs.tableRef.$el.querySelector(
          ".el-table__body-wrapper"
        );
        if (tableEl) {
          tableEl.scrollTop = 0;
        }
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        title: "",
        cover: "",
        summary: "",
        tags: "",
        status: "0",
        remark: "",
        publishTime: this.parseTime(new Date(), "{y}-{m}-{d} {h}:{i}:{s}"),
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.id = null;
      this.open = true;
      this.title = "添加产品数据列";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getProductData(id).then((response) => {
        this.isReset = false;
        const { data } = response;
        this.form = {
          title: data.title,
          status: data.status,
          cover: data.cover,
          summary: data.summary,
          tags: data.tags,
          remark: data.remark,
          id: data.id,
          productId: data.productId,
          publishTime: data.publishTime,
        };
        if (data.fileUrl) {
          this.form.articleUrl = {
            name: data.fileName,
            url: data.fileUrl,
          };
        }
        this.open = true;
        this.title = "修改产品数据列";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let formData = new FormData();
          const param = {
            ...this.form,
          };
          delete param["articleUrl"];
          if (this.isReset) {
            param.fileUpdateFlag = 1;
            formData.append("file", this.form.articleUrl?.raw);
          } else {
            param.fileUpdateFlag = 0;
          }

          for (let item in param) {
            formData.append(item, param[item] ?? "");
          }
          // formData.append("xyLargeScreenProductList", JSON.stringify(param));

          if (this.form.id != null) {
            updateProductData(formData).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProductData(formData).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids;
      this.$modal
        .confirm('是否确认删除产品数据列编号为"' + ids + '"的数据项？')
        .then(function () {
          return delProductData(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "large/productData/export",
        {
          ...this.queryParams,
        },
        `productData_${new Date().getTime()}.xlsx`
      );
    },
    imageFun(data) {
      this.form.cover = data;
    },
    //附件上传回传
    articleFun(file) {
      this.isReset = true;
      this.form.articleUrl = file;
    },
  },
};
</script>
