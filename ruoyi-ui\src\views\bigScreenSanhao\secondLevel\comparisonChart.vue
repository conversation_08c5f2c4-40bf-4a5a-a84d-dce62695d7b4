<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div
      v-show="show"
      class="custom-dialog"
      :class="{ 'comparison-chart-fullscreen': isFullscreen }"
      :style="isFullscreen ? {} : { width: width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div style="display: flex; align-items: center">
          <div
            @click="handleScreen"
            :title="isFullscreen ? '退出全屏' : '全屏'"
            style="
              margin-right: 20px;
              cursor: pointer;
              color: #ffffff;
              font-size: 20px;
            "
          >
            <i
              :class="isFullscreen ? 'el-icon-rank' : 'el-icon-full-screen'"
              style="width: 20px; height: 20px"
            ></i>
          </div>
          <div class="custom-dialog-close" @click="closeDialog"></div>
        </div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box" v-loading="loading">
          <div class="bg-box-title">国内技术报告</div>
          <div class="bg-box-content">
            <el-table
              :data="reportData"
              style="
                width: calc(100% - 20px);
                margin: 0 10px;
                background-color: #00000000;
              "
            >
              <el-table-column
                prop="reportSn"
                label="序号"
                width="60"
                align="center"
              >
                <template slot-scope="scope">
                  {{
                    scope.$index +
                    1 +
                    (queryParams.pageNum - 1) * queryParams.pageSize
                  }}
                </template>
              </el-table-column>
              <el-table-column
                prop="publishTime"
                label="发布时间"
                width="110"
                align="center"
              />
              <el-table-column
                prop="reportName"
                label="报告名称"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <div
                    style="color: #0ec2f4ff; cursor: pointer"
                    @click="
                      openHotTechnologyDetails({
                        ...scope.row,
                        title: scope.row.reportName,
                      })
                    "
                  >
                    {{ scope.row.reportName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="publishUnit"
                label="发布机构"
                width="240"
                show-overflow-tooltip
              />
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
            />
          </div>
        </div>
        <div class="bg-box" v-loading="loading1">
          <div class="bg-box-title">国外技术报告</div>
          <div class="bg-box-content">
            <el-table
              :data="reportData1"
              style="
                width: calc(100% - 20px);
                margin: 0 10px;
                background-color: #00000000;
              "
            >
              <el-table-column
                prop="reportSn"
                label="序号"
                width="60"
                align="center"
              >
                <template slot-scope="scope">
                  {{
                    scope.$index +
                    1 +
                    (queryParams1.pageNum - 1) * queryParams1.pageSize
                  }}
                </template>
              </el-table-column>
              <el-table-column
                prop="publishTime"
                label="发布时间"
                width="110"
                align="center"
              />
              <el-table-column
                prop="reportName"
                label="报告名称"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <div
                    style="color: #0ec2f4ff; cursor: pointer"
                    @click="
                      openHotTechnologyDetails({
                        ...scope.row,
                        title: scope.row.reportName,
                      })
                    "
                  >
                    {{ scope.row.reportName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="publishUnit"
                label="发布机构"
                width="240"
                show-overflow-tooltip
              />
            </el-table>
            <pagination
              v-show="total1 > 0"
              :total="total1"
              :page.sync="queryParams1.pageNum"
              :limit.sync="queryParams1.pageSize"
              @pagination="getList1"
            />
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">国内热点技术</div>
          <div class="bg-box-content content-flex">
            <baar3DEcharts
              :show="show"
              :sccenId="sccenId"
              :type="1"
              style="width: 1200px; height: 500px"
            ></baar3DEcharts>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">国外热点技术</div>
          <div class="bg-box-content content-flex">
            <baar3DEcharts
              :show="show"
              :sccenId="sccenId"
              :type="0"
              style="width: 1200px; height: 500px"
            ></baar3DEcharts>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import baar3DEcharts from "../components/baar3DEcharts";
import { technicalReportList } from "@/api/bigScreen/sanhao.js";

export default {
  name: "ComparisonChart",
  components: {
    baar3DEcharts,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "自定义弹窗",
    },
    closeOnClickMask: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 1280,
    },
    sccenId: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      reportData: [{ name: "1" }],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      reportData1: [{ name: "1" }],
      queryParams1: {
        pageNum: 1,
        pageSize: 10,
      },
      total1: 0,
      show: false,
      loading: true,
      loading1: true,
      // 全屏状态
      isFullscreen: false,
    };
  },

  mounted() {
    // 添加ESC键监听
    document.addEventListener("keydown", this.handleKeydown);
    // 添加窗口大小变化监听
    window.addEventListener("resize", this.handleWindowResize);
  },

  beforeDestroy() {
    // 移除ESC键监听
    document.removeEventListener("keydown", this.handleKeydown);
    // 移除窗口大小变化监听
    window.removeEventListener("resize", this.handleWindowResize);
  },

  watch: {
    visible: {
      handler(newVal) {
        if (newVal) {
          // 重置全屏状态
          this.isFullscreen = false;
          this.queryParams = {
            pageNum: 1,
            pageSize: 10,
          };
          this.queryParams1 = {
            pageNum: 1,
            pageSize: 10,
          };
          this.show = false;
          this.getList();
          this.getList1();
        }
      },
    },
  },
  methods: {
    getList() {
      this.loading = true;
      technicalReportList({
        ...this.queryParams,
        projectSn: "1",
        screenSn: this.sccenId,
        columnSn: "1",
        isForeign: 1,
      }).then((res) => {
        this.reportData = res.rows;
        this.total = res.total;
        this.show = true;
        this.loading = false;
      });
    },

    getList1() {
      this.loading1 = true;
      technicalReportList({
        ...this.queryParams1,
        projectSn: "1",
        screenSn: this.sccenId,
        columnSn: "1",
        isForeign: 0,
      }).then((res) => {
        this.reportData1 = res.rows;
        this.total1 = res.total;
        this.show = true;
        this.loading1 = false;
      });
    },

    openHotTechnologyDetails(data) {
      this.$emit("openHotTechnology", data);
    },

    // 关闭弹窗的方法
    closeDialog() {
      this.$emit("update:visible", false);
    },

    // 处理遮罩层点击事件
    handleMaskClick() {
      if (this.closeOnClickMask) {
        this.closeDialog();
      }
    },

    // 全屏切换
    handleScreen() {
      this.isFullscreen = !this.isFullscreen;

      // 延迟调整图表大小，确保DOM更新完成
      this.$nextTick(() => {
        setTimeout(() => {
          // 如果有图表组件，调整其大小
          // 这里可以根据实际的图表组件引用进行调整
        }, 300); // 等待CSS动画完成
      });
    },

    // 处理键盘事件
    handleKeydown(event) {
      // 按ESC键退出全屏
      if (event.key === "Escape" && this.isFullscreen && this.visible) {
        this.isFullscreen = false;
      }
    },

    // 处理窗口大小变化
    handleWindowResize() {
      if (this.isFullscreen) {
        // 重新调整图表大小
        // 这里可以根据实际的图表组件引用进行调整
      }
    },
  },
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  // 确保在所有分辨率下都能正确覆盖
  min-width: 100%;
  min-height: 100%;
  overflow: hidden;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("../../../assets/bigScreenSanhao/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;
    transition: all 0.3s ease;

    &.comparison-chart-fullscreen {
      width: calc(100vw - 40px) !important;
      height: calc(100vh - 40px) !important;
      max-width: none !important;
      max-height: none !important;
      margin: 0 !important;
      // 确保在所有分辨率下都能正确显示
      min-width: calc(100% - 40px) !important;
      min-height: calc(100% - 40px) !important;

      .custom-dialog-body {
        height: calc(100% - 80px); // 减去header高度和padding
        max-height: calc(100% - 80px);
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("../../../assets/bigScreenSanhao/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("../../../assets/bigScreenSanhao/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      height: 800px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .bg-box-content {
          font-size: 16px;

          .bg-box-content-list {
            padding: 0 0 10px 15px;
            font-size: 14px;
            color: #ffffffc6;

            span {
              font-weight: 600;
              color: #ffffff;
              margin-right: 10px;
            }
          }

          ::v-deep .el-table tr {
            background-color: #1f3850ff !important;
          }

          ::v-deep .el-table__inner-wrapper:before {
            height: 0px;
            border-color: #00000000;
          }

          ::v-deep .el-table__header th {
            background-color: #1f3850 !important;
            color: rgba(255, 255, 255);
            font-size: 16px;
            border-bottom-width: 0px;
          }

          ::v-deep .el-table__body td {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            border-bottom-width: 0px;
          }

          ::v-deep .el-table__body tr:hover > td {
            background-color: #132f56 !important;
          }
        }

        .content-flex {
          display: flex;
          justify-content: center;
        }
      }
    }
  }
}

::v-deep .pagination-container {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 0;

  .el-select .el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }

  .el-pagination__editor.el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }
}

::v-deep .el-pagination__total,
::v-deep .el-pagination__jump {
  color: #f2f2f2;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination button:disabled {
  background-color: #ffffff00;
  color: #fff;
}

::v-deep .el-pager li {
  background: #ffffff00;
  color: #fff;

  &.active {
    color: #1890ff;
  }
}
</style>
