<template>
  <div class="table-three">
    <div class="table_main">
      <div :class="{ 'scoll-Table': hotList.length > 8 }">
        <div
          class="table_col"
          v-for="(item, index) in hotList"
          :key="index"
          @click="expertFun"
        >
          <div class="item-bottom">
            <el-tooltip
              class="item"
              effect="dark"
              :content="item.reportName"
              placement="top-start"
            >
              <div class="last-child" @click="jumpFun(item.reportUrl)">
                {{ item.reportName }}
              </div>
            </el-tooltip>

            <div style="width: 150px; text-align: right">
              {{ item.reportDate }}
            </div>
          </div>
          <img
            class="bottom-border"
            :src="require('@/assets/bigScreenTwo/bottom-border.png')"
            alt=""
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      hotList: [
        {
          reportName: "网络安全攻防技术在实际业务系统中的应用",
          reportDate: "2024-02",
        },
        {
          reportName: "基于虚拟化容器技术的网络安全攻防实验教学与实训...",
          reportDate: " 2023-12",
        },
        {
          reportName: "基于虚拟技术的网络安全攻防模拟平台设计",
          reportDate: " 2023-08",
        },
        {
          reportName: "ARP协议分析及攻防技术研究",
          reportDate: " 2023-06",
        },
        {
          reportName: "网络安全攻防演练中攻防双方技术概述",
          reportDate: " 2023-06",
        },
        {
          reportName: "基于网络安全技术的攻防一体化教学设计与探究",
          reportDate: " 2023-05",
        },
        {
          reportName: "网络攻防靶场实战核心平台应用研究",
          reportDate: " 2023-05",
        },
        {
          reportName: "基于攻防博弈模型的网络安全测评与防御技术",
          reportDate: " 2022-12",
        },
        {
          reportName: "电力关键信息基础设施网络安全攻防演练研究",
          reportDate: " 2022-07",
        },

        {
          reportName: "基于网络攻防理论的美国网络安全战略评析",
          reportDate: " 2022-06",
        },
        {
          reportName: "网络安全智能化与自动化技术应用研究",
          reportDate: " 2022-04",
        },
        {
          reportName: "CCF计算机安全专业委员会2022年网络安全十大发展...",
          reportDate: " 2022-04",
        },
        {
          reportName: "网络大数据背景下计算机信息安全防护措施探究——...",
          reportDate: " 2022-02",
        },
        {
          reportName: "中国资产普遍上涨 四大现象折射A股投资新机遇",
          reportDate: " 2023-01 ",
        },
        {
          reportName: "华脉科技替收购方打掩护 信息披露不到位 被监管...",
          reportDate: " 2021-12 ",
        },
        {
          reportName: "“研学游”有助于拉动消费",
          reportDate: "  2024-01",
        },
        {
          reportName: "中国资产普遍上涨 四大现象折射A股投资新机遇",
          reportDate: " 2023-01 ",
        },
        {
          reportName: "推动改革举措尽快落地 增强A股吸引力",
          reportDate: " 2023-01 ",
        },
        {
          reportName: "房地产供求关系发生重大变化 政策调整优化恰逢...",
          reportDate: " 2023-01 ",
        },
      ], //滚动
    };
  },
  mounted() {},

  props: {},
  watch: {},

  components: {},
  methods: {
    /**
     *
     */
    expertFun(item) {
      this.$emit("expertFun", item);
    },
  },
  beforeDestroy() {
    /* 页面组件销毁的时候，别忘了移除绑定的监听resize事件，否则的话，多渲染几次
            容易导致内存泄漏和额外CPU或GPU占用哦*/
  },
};
</script>

<style lang="scss">
.table-three {
  padding: 10px 33px 10px 40px;
  width: 100%;
  margin: 0 auto;
  height: 620px;

  .table_main {
    width: 100%;
    height: calc(100% - 25px);
    overflow: hidden;
    z-index: 0;

    .table_col {
      position: relative;
      width: 100%;
      height: 65px;
      position: relative;
      padding: 0 5px 0 20px;
      font-size: 14px;

      line-height: 65px;

      .last-child {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        font-size: 16px;
        font-weight: bold;
        font-weight: 500;
        color: #e6f7ff;
        font-style: normal;
        flex: 1;
      }
      .item-bottom {
        margin-top: 8px;
        display: flex;
        font-family: "pingFangMedium";
        font-weight: 300;
        font-size: 14px;
        color: #e6f7ff;
        font-style: normal;
        justify-content: space-between;
      }
    }
    .bottom-border {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 800px;
      display: inline-block;
    }

    @keyframes scoll-Table {
      from {
        transform: translate(0, 0px);
      }

      to {
        transform: translate(0, calc(((85vh - 20px) / 3 - 90px) - 400px));
      }
    }

    .scoll-Table {
      animation: scoll-Table 7s linear infinite;
      transition: all 1s ease-out;
      //  animation-timing-function: linear;
      // animation-fill-mode: forwards;
      /* 在动画结束后保持最后一个关键帧的状态 */
    }

    /* 鼠标进入 */
    .scoll-Table:hover {
      animation-play-state: paused;
    }

    /* 鼠标离开 */
    .scoll-Table:not(:hover) {
      animation-play-state: running;
    }
  }
}
</style>
