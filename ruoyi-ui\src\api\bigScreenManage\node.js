import request from "@/utils/request";

// 查询图谱节点列表
export function listNode(query) {
  return request({
    url: "/screen/node/list",
    method: "get",
    params: query,
  });
}

// 查询图谱节点详细
export function getNode(id) {
  return request({
    url: "/screen/node/" + id,
    method: "get",
  });
}

// 新增图谱节点
export function addNode(data) {
  return request({
    url: "/screen/node",
    method: "post",
    data: data,
  });
}

// 修改图谱节点
export function updateNode(data) {
  return request({
    url: "/screen/node/edit",
    method: "post",
    data: data,
  });
}

// 删除图谱节点
export function delNode(id) {
  return request({
    url: "/screen/node/remove",
    method: "post",
    data: id,
  });
}
