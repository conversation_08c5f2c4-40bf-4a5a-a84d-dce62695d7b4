import request from '@/utils/request'

// 查询政策库采集源列表
export function listSource(query) {
  return request({
    url: '/policy/source/list',
    method: 'get',
    params: query
  })
}

// 查询政策库采集源详细
export function getSource(id) {
  return request({
    url: '/policy/source/' + id,
    method: 'get'
  })
}

// 新增政策库采集源
export function addSource(data) {
  return request({
    url: '/policy/source',
    method: 'post',
    data: data
  })
}

// 修改政策库采集源
export function updateSource(data) {
  return request({
    url: '/policy/source/edit',
    method: 'post',
    data: data
  })
}

// 删除政策库采集源
export function delSource(data) {
  return request({
    url: '/policy/source/remove',
    method: 'post',
    data: data
  })
}
