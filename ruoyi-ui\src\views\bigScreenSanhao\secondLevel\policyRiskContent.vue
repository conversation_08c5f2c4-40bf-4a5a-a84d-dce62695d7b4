<template>
  <div
    v-if="visible"
    class="custom-dialog-mask"
    @click="handleMaskClick"
    :style="{ zIndex: currentZIndex }"
  >
    <div
      class="custom-dialog"
      :class="{ 'policy-risk-content-fullscreen': isFullscreen }"
      :style="isFullscreen ? {} : { width: width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ tianDetail && tianDetail.proposalsTitle }}</span>
        <div style="display: flex; align-items: center">
          <div
            @click="handleScreen"
            :title="isFullscreen ? '退出全屏' : '全屏'"
            style="
              margin-right: 20px;
              cursor: pointer;
              color: #ffffff;
              font-size: 20px;
            "
          >
            <i
              :class="isFullscreen ? 'el-icon-rank' : 'el-icon-full-screen'"
              style="width: 20px; height: 20px"
            ></i>
          </div>
          <div class="custom-dialog-close" @click="closeDialog"></div>
        </div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="bg-box-title">提案摘要</div>
          <div class="bg-box-content" v-html="tianDetail.summary"></div>
        </div>
        <!-- 提案关系桑葚图 -->
        <sankeyChart2
          title="提案关系"
          param-type="proposalsTitle"
          :param-value="tianDetail.proposalsTitle"
          style="width: 100%"
        />
        <div class="bg-box">
          <div class="bg-box-title">提案详情</div>
          <div class="toggle-divs">
            <div
              @click="activeContent = 'proposalsEnContent'"
              :class="{ active: activeContent === 'proposalsEnContent' }"
            >
              原文
            </div>
            <div
              @click="activeContent = 'proposalsContent'"
              :class="{ active: activeContent === 'proposalsContent' }"
            >
              中文
            </div>
          </div>
          <div class="bg-box-content" v-html="tianDetail[activeContent]"></div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">提案人员</div>
          <div class="bg-box-content">
            <el-table :data="tianDetail.experts" border style="width: 100%">
              <el-table-column
                prop="proposalsExperts"
                align="center"
                label="提案人员"
              >
                <template slot-scope="scope">
                  <div
                    style="color: #0ec2f4e6; cursor: pointer; font-weight: bold"
                    @click="openExpertsContent(scope.row.expertsSn)"
                  >
                    {{ scope.row.proposalsExperts }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="belongToGroup"
                align="center"
                label="所属党派"
              />
              <el-table-column
                prop="belongToArea"
                align="center"
                label="所属州"
              />
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getNewZIndex, getBaseZIndex } from "@/utils/zIndexManager";
import sankeyChart2 from "../components/sankeyChart2.vue";

export default {
  name: "PolicyRiskContent",
  components: {
    sankeyChart2,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    closeOnClickMask: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 1000,
    },
    tianDetail: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      currentZIndex: getBaseZIndex(),
      activeContent: "proposalsContent",
      // 全屏状态
      isFullscreen: false,
    };
  },

  mounted() {
    // 添加ESC键监听
    document.addEventListener("keydown", this.handleKeydown);
    // 添加窗口大小变化监听
    window.addEventListener("resize", this.handleWindowResize);
  },

  beforeDestroy() {
    // 移除ESC键监听
    document.removeEventListener("keydown", this.handleKeydown);
    // 移除窗口大小变化监听
    window.removeEventListener("resize", this.handleWindowResize);
  },

  watch: {
    visible: {
      handler(value) {
        this.activeContent = "proposalsContent";
        // 当对话框变为可见时，更新 z-index
        if (value) {
          this.bringToFront();
          // 重置全屏状态
          this.isFullscreen = false;
        }
      },
    },
  },
  methods: {
    // 提升组件到最上层的方法
    bringToFront() {
      this.currentZIndex = getNewZIndex();
    },

    closeDialog() {
      this.$emit("update:visible", false);
    },

    handleMaskClick() {
      if (this.closeOnClickMask) {
        this.closeDialog();
      }
    },

    openExpertsContent(id) {
      this.$emit("openExpertsContent", id);
    },

    // 全屏切换
    handleScreen() {
      this.isFullscreen = !this.isFullscreen;

      // 延迟调整图表大小，确保DOM更新完成
      this.$nextTick(() => {
        setTimeout(() => {
          // 如果有图表组件，调整其大小
          // 这里可以根据实际的图表组件引用进行调整
        }, 300); // 等待CSS动画完成
      });
    },

    // 处理键盘事件
    handleKeydown(event) {
      // 按ESC键退出全屏
      if (event.key === "Escape" && this.isFullscreen && this.visible) {
        this.isFullscreen = false;
      }
    },

    // 处理窗口大小变化
    handleWindowResize() {
      if (this.isFullscreen) {
        // 重新调整图表大小
        // 这里可以根据实际的图表组件引用进行调整
      }
    },
  },
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  // z-index: 1000; // 移除固定 z-index，使用动态值

  // 确保在所有分辨率下都能正确覆盖
  min-width: 100%;
  min-height: 100%;
  overflow: hidden;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("../../../assets/bigScreenSanhao/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;
    transition: all 0.3s ease;

    &.policy-risk-content-fullscreen {
      width: calc(100vw - 40px) !important;
      height: calc(100vh - 40px) !important;
      max-width: none !important;
      max-height: none !important;
      margin: 0 !important;
      // 确保在所有分辨率下都能正确显示
      min-width: calc(100% - 40px) !important;
      min-height: calc(100% - 40px) !important;

      .custom-dialog-body {
        height: calc(100% - 80px); // 减去header高度和padding
        max-height: calc(100% - 80px);
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("../../../assets/bigScreenSanhao/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("../../../assets/bigScreenSanhao/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      height: 800px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0px 20px 0px;

      .bg-box {
        position: relative;
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .toggle-divs {
          position: absolute;
          display: flex;
          top: 16px;
          right: 16px;

          div {
            width: 48px;
            height: 30px;
            background-color: #3a4d68ff;
            color: white;
            padding: 5px 10px;
            cursor: pointer;
            user-select: none; // 禁止文本选中

            &:hover {
              background-color: #1d3046;
            }

            // 选中状态的高亮样式
            &.active {
              background-color: #ff9d00ff;
              color: white;
            }
          }
        }

        .bg-box-content {
          // display: flex;
          // justify-content: center;
          font-size: 16px;
          color: #ffffff;
          white-space: pre-wrap;

          ::v-deep .el-table__header th {
            background-color: #1f3850 !important;
            color: rgba(255, 255, 255);
            font-size: 16px;
          }

          ::v-deep .el-table__body td {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
          }

          ::v-deep .el-descriptions__label {
            background-color: #1f3850;
            color: rgba(255, 255, 255);
            font-size: 16px;
            text-align: center;
          }

          ::v-deep .el-descriptions__content {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            text-align: center;
          }
        }
      }
    }
  }
}
</style>
