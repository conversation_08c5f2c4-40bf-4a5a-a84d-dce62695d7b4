﻿body {
  margin:0px;
  background-color:rgba(8, 31, 70, 1);
  background-image:none;
  position:static;
  left:auto;
  width:806px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u0_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:806px;
  height:1166px;
  background:inherit;
  background-color:rgba(29, 35, 52, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:806px;
  height:1166px;
  display:flex;
}
#u0 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u0_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:37px;
  background:inherit;
  background-color:rgba(42, 48, 64, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u1 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:27px;
  width:750px;
  height:37px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u1 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:37px;
  background:inherit;
  background-color:rgba(42, 48, 64, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u2 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:344px;
  width:750px;
  height:37px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u2 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:272px;
  height:72px;
  background:inherit;
  background-color:rgba(241, 242, 242, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:justify;
  text-justify:inter-word;
}
#u3 {
  border-width:0px;
  position:absolute;
  left:80px;
  top:80px;
  width:272px;
  height:72px;
  display:flex;
  text-align:justify;
  text-justify:inter-word;
}
#u3 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:277px;
  height:76px;
  background:inherit;
  background-color:rgba(251, 251, 251, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:justify;
  text-justify:inter-word;
}
#u4 {
  border-width:0px;
  position:absolute;
  left:254px;
  top:233px;
  width:277px;
  height:76px;
  display:flex;
  text-align:justify;
  text-justify:inter-word;
}
#u4 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:64px;
  background:inherit;
  background-color:rgba(241, 242, 242, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5 {
  border-width:0px;
  position:absolute;
  left:440px;
  top:87px;
  width:281px;
  height:64px;
  display:flex;
}
#u5 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:63px;
  background:inherit;
  background-color:rgba(251, 251, 251, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6 {
  border-width:0px;
  position:absolute;
  left:596px;
  top:239px;
  width:182px;
  height:63px;
  display:flex;
}
#u6 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u7 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:396px;
  width:56px;
  height:16px;
  display:flex;
  color:#FFFFFF;
}
#u7 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u8_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u8 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:707px;
  width:56px;
  height:16px;
  display:flex;
  color:#FFFFFF;
}
#u8 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u8_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u9 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:733px;
  width:756px;
  height:96px;
}
#u10_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:30px;
}
#u10 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:30px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u10 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u10_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u11_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:628px;
  height:30px;
}
#u11 {
  border-width:0px;
  position:absolute;
  left:121px;
  top:0px;
  width:628px;
  height:30px;
  display:flex;
  font-size:14px;
  text-align:left;
}
#u11 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 15px;
  box-sizing:border-box;
  width:100%;
}
#u11_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:30px;
}
#u12 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:121px;
  height:30px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u12 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:628px;
  height:30px;
}
#u13 {
  border-width:0px;
  position:absolute;
  left:121px;
  top:30px;
  width:628px;
  height:30px;
  display:flex;
  font-size:14px;
  text-align:left;
}
#u13 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 15px;
  box-sizing:border-box;
  width:100%;
}
#u13_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u14_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:36px;
}
#u14 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:121px;
  height:36px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u14 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u14_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u15_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:628px;
  height:36px;
}
#u15 {
  border-width:0px;
  position:absolute;
  left:121px;
  top:60px;
  width:628px;
  height:36px;
  display:flex;
  font-size:14px;
}
#u15 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 15px;
  box-sizing:border-box;
  width:100%;
}
#u15_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u16_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u16 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:849px;
  width:56px;
  height:16px;
  display:flex;
  color:#FFFFFF;
}
#u16 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u16_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u17 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:875px;
  width:752px;
  height:264px;
}
#u18_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:52px;
}
#u18 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:52px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u18 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u18_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u19_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:636px;
  height:52px;
}
#u19 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:0px;
  width:636px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:justify;
  text-justify:inter-word;
}
#u19 .text {
  position:absolute;
  align-self:center;
  padding:2px 10px 2px 15px;
  box-sizing:border-box;
  width:100%;
}
#u19_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u20_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:52px;
}
#u20 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:52px;
  width:116px;
  height:52px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u20 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u20_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u21_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:636px;
  height:52px;
}
#u21 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:52px;
  width:636px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:justify;
  text-justify:inter-word;
}
#u21 .text {
  position:absolute;
  align-self:center;
  padding:2px 10px 2px 15px;
  box-sizing:border-box;
  width:100%;
}
#u21_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u22_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:36px;
}
#u22 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:104px;
  width:116px;
  height:36px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u22 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u22_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u23_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:636px;
  height:36px;
}
#u23 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:104px;
  width:636px;
  height:36px;
  display:flex;
  font-size:14px;
  text-align:justify;
  text-justify:inter-word;
}
#u23 .text {
  position:absolute;
  align-self:center;
  padding:2px 10px 2px 15px;
  box-sizing:border-box;
  width:100%;
}
#u23_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u24_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:52px;
}
#u24 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:140px;
  width:116px;
  height:52px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u24 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u24_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u25_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:636px;
  height:52px;
}
#u25 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:140px;
  width:636px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:justify;
  text-justify:inter-word;
}
#u25 .text {
  position:absolute;
  align-self:center;
  padding:2px 10px 2px 15px;
  box-sizing:border-box;
  width:100%;
}
#u25_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u26_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:36px;
}
#u26 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:192px;
  width:116px;
  height:36px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u26 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u26_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u27_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:636px;
  height:36px;
}
#u27 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:192px;
  width:636px;
  height:36px;
  display:flex;
  font-size:14px;
  text-align:justify;
  text-justify:inter-word;
}
#u27 .text {
  position:absolute;
  align-self:center;
  padding:2px 10px 2px 15px;
  box-sizing:border-box;
  width:100%;
}
#u27_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u28_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:36px;
}
#u28 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:228px;
  width:116px;
  height:36px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u28 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u28_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u29_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:636px;
  height:36px;
}
#u29 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:228px;
  width:636px;
  height:36px;
  display:flex;
  font-size:14px;
  text-align:justify;
  text-justify:inter-word;
}
#u29 .text {
  position:absolute;
  align-self:center;
  padding:2px 10px 2px 15px;
  box-sizing:border-box;
  width:100%;
}
#u29_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u30_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:57px;
}
#u30 {
  border-width:0px;
  position:absolute;
  left:528px;
  top:162px;
  width:193px;
  height:57px;
  display:flex;
}
#u30 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u30_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:57px;
}
#u31 {
  border-width:0px;
  position:absolute;
  left:362px;
  top:162px;
  width:193px;
  height:57px;
  display:flex;
}
#u31 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u32_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:57px;
}
#u32 {
  border-width:0px;
  position:absolute;
  left:194px;
  top:162px;
  width:193px;
  height:57px;
  display:flex;
}
#u32 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u32_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u33_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:57px;
}
#u33 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:162px;
  width:193px;
  height:57px;
  display:flex;
}
#u33 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u33_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u34_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'宋体 Bold', '宋体 常规', '宋体', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
}
#u34 {
  border-width:0px;
  position:absolute;
  left:72px;
  top:179px;
  width:72px;
  height:23px;
  display:flex;
  font-family:'宋体 Bold', '宋体 常规', '宋体', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
}
#u34 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u34_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u35 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u36_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:7px;
}
#u36 {
  border-width:0px;
  position:absolute;
  left:68px;
  top:80px;
  width:7px;
  height:7px;
  display:flex;
}
#u36 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u36_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u37_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:76px;
}
#u37 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:87px;
  width:1px;
  height:75px;
  display:flex;
}
#u37 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u37_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u38 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u39_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:7px;
}
#u39 {
  border-width:0px;
  position:absolute;
  left:422px;
  top:92px;
  width:7px;
  height:7px;
  display:flex;
}
#u39 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u39_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u40_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:64px;
}
#u40 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:99px;
  width:1px;
  height:63px;
  display:flex;
}
#u40 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u40_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u41 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u42_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:7px;
}
#u42 {
  border-width:0px;
  position:absolute;
  left:242px;
  top:295px;
  width:7px;
  height:7px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u42 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u42_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u43_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:76px;
}
#u43 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:220px;
  width:1px;
  height:75px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u43 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u43_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u44 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u45_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:7px;
}
#u45 {
  border-width:0px;
  position:absolute;
  left:581px;
  top:295px;
  width:7px;
  height:7px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u45 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u45_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u46_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:76px;
}
#u46 {
  border-width:0px;
  position:absolute;
  left:584px;
  top:220px;
  width:1px;
  height:75px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u46 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u46_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u47_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'宋体 Bold', '宋体 常规', '宋体', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
}
#u47 {
  border-width:0px;
  position:absolute;
  left:581px;
  top:178px;
  width:82px;
  height:23px;
  display:flex;
  font-family:'宋体 Bold', '宋体 常规', '宋体', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
}
#u47 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u47_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u48_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'宋体 Bold', '宋体 常规', '宋体', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
}
#u48 {
  border-width:0px;
  position:absolute;
  left:396px;
  top:178px;
  width:144px;
  height:23px;
  display:flex;
  font-family:'宋体 Bold', '宋体 常规', '宋体', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
}
#u48 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u48_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u49_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'宋体 Bold', '宋体 常规', '宋体', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
}
#u49 {
  border-width:0px;
  position:absolute;
  left:221px;
  top:179px;
  width:155px;
  height:23px;
  display:flex;
  font-family:'宋体 Bold', '宋体 常规', '宋体', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
}
#u49 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u49_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u50 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-2 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-3 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-4 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-5 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-6 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-7 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-8 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-9 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-10 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-11 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-12 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-13 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-14 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-15 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-16 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-17 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-18 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-19 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-20 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-21 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-22 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-23 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-24 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-25 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-26 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-27 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-28 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-29 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-30 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-31 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-32 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-33 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-34 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51-35 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51 {
  border-width:0px;
  position:absolute;
  left:778px;
  top:579px;
  width:0px;
  height:0px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
}
#u52_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:113px;
}
#u52 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:579px;
  width:750px;
  height:113px;
  display:flex;
}
#u52 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u52_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u53_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u53 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:413px;
  width:112px;
  height:16px;
  display:flex;
  color:#FFFFFF;
}
#u53 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u53_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u54_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u54 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:557px;
  width:112px;
  height:16px;
  display:flex;
  color:#FFFFFF;
}
#u54 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u54_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u55 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-2 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-3 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-4 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-5 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-6 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-7 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-8 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-9 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-10 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-11 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-12 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-13 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-14 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-15 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-16 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-17 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-18 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-19 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-20 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-21 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-22 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-23 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-24 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-25 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-26 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-27 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-28 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-29 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56-30 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56 {
  border-width:0px;
  position:absolute;
  left:779px;
  top:432px;
  width:0px;
  height:0px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
}
#u57_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:113px;
}
#u57 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:432px;
  width:750px;
  height:113px;
  display:flex;
}
#u57 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u57_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
