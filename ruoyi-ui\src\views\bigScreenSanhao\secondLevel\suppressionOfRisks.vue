<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div
      class="custom-dialog"
      :class="{ 'suppression-risks-fullscreen': isFullscreen }"
      :style="isFullscreen ? {} : { width: width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div style="display: flex; align-items: center">
          <div
            @click="handleScreen"
            :title="isFullscreen ? '退出全屏' : '全屏'"
            style="
              margin-right: 20px;
              cursor: pointer;
              color: #ffffff;
              font-size: 20px;
            "
          >
            <i
              :class="isFullscreen ? 'el-icon-rank' : 'el-icon-full-screen'"
              style="width: 20px; height: 20px"
            ></i>
          </div>
          <div class="custom-dialog-close" @click="closeDialog"></div>
        </div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="bg-box-title">受打压情况</div>
          <div class="bg-box-content">
            <div ref="chartKeyDetails" style="width: 100%; height: 300px"></div>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">受打压企业</div>
          <div class="bg-box-content">
            <el-table
              :row-style="getRowStyle"
              :data="enterpriseList"
              border
              style="width: 100%"
            >
              <el-table-column
                prop="publishTime"
                align="center"
                label="时间"
                width="180"
              />
              <el-table-column
                prop="enterpriseName"
                align="center"
                label="企业名称"
                min-width="180"
              >
                <template slot-scope="scope">
                  <div @click="openDetail(scope.row)" style="cursor: pointer">
                    {{ scope.row.enterpriseName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="domain"
                align="center"
                label="领域"
                width="200"
              />
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "SuppressionOfRisks",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    closeOnClickMask: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 1200,
    },
    item: {
      type: Object,
      default: () => ({}),
    },
    guanjianType: {
      type: Number,
      default: 1,
    },
    levelCount: {
      type: Array,
      default: () => [],
    },
    enterpriseList: {
      type: Array,
      default: () => [],
    },
    total: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      myChart: null,
      option: {},
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 全屏状态
      isFullscreen: false,
      // ResizeObserver 实例
      resizeObserver: null,
      // 防抖定时器
      resizeTimer: null,
    };
  },
  mounted() {
    // 添加ESC键监听
    document.addEventListener("keydown", this.handleKeydown);
    // 添加窗口大小变化监听
    window.addEventListener("resize", this.handleWindowResize);
  },

  beforeDestroy() {
    // 销毁图表实例
    if (this.myChart) {
      this.myChart.dispose();
      this.myChart = null;
    }
    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    // 清理定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    }
    // 移除ESC键监听
    document.removeEventListener("keydown", this.handleKeydown);
    // 移除窗口大小变化监听
    window.removeEventListener("resize", this.handleWindowResize);
  },

  watch: {
    visible: {
      handler(newVisible) {
        if (newVisible) {
          // 重置全屏状态
          this.isFullscreen = false;
          this.$nextTick(() => {
            if (this.$refs.chartKeyDetails) {
              this.myChart = echarts.init(this.$refs.chartKeyDetails);
              this.initChart();
              this.setupResizeListeners();
            }
          });
        }
      },
    },
  },
  methods: {
    initChart() {
      this.option = {
        legend: {
          show: true,
          textStyle: { color: "#fff" },
          padding: [10, 10],
          x: "center",
        },
        tooltip: {
          show: true,
          trigger: "axis",
          showContent: true,
          triggerOn: "mousemove",
          // axisPointer: {
          //   type: "shadow",
          // },
        },
        dataset: {
          dimensions: ["product", "严重", "一般", "较轻"],
          source: this.levelCount,
        },
        xAxis: {
          type: "category",
          axisLabel: {
            fontSize: "14px",
            color: "#fff",
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            lineStyle: {
              color: "#ffffff70",
              type: "dotted",
            },
          },
          axisLabel: {
            interval: 0,
            fontSize: "14px",
            color: "#fff",
          },
        },
        series: [
          { type: "bar", itemStyle: { color: "#A82B2D" }, barMaxWidth: 40 },
          { type: "bar", itemStyle: { color: "#AC6628" }, barMaxWidth: 40 },
          { type: "bar", itemStyle: { color: "#345CA4" }, barMaxWidth: 40 },
        ],
      };

      setTimeout(() => {
        this.myChart?.resize();
      }, 1);
      this.myChart.setOption(this.option);
    },

    getRowStyle(rowInfo) {
      const { row } = rowInfo;
      console.log(row);
      switch (row.riskLevel) {
        case "严重":
          return { backgroundColor: "#A82B2D", color: "#ffffff" };
        case "一般":
          return { backgroundColor: "#AC6628", color: "#ffffff" };
        case "较轻":
          return { backgroundColor: "#345CA4", color: "#ffffff" };
        default:
          return {};
      }
    },

    openDetail(item) {
      this.$emit("openEnterpriseInformation", item);
    },

    getList() {
      this.$emit("pagination", this.queryParams);
    },

    // 关闭弹窗的方法
    closeDialog() {
      this.$emit("update:visible", false);
    },

    // 处理遮罩层点击事件
    handleMaskClick() {
      if (this.closeOnClickMask) {
        this.closeDialog();
      }
    },

    // 设置尺寸变化监听
    setupResizeListeners() {
      // 创建 ResizeObserver 监听容器尺寸变化
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(() => {
          // 使用防抖处理，避免频繁触发
          clearTimeout(this.resizeTimer);
          this.resizeTimer = setTimeout(() => {
            this.handleResize();
          }, 100);
        });

        // 监听图表容器的尺寸变化
        if (this.$refs.chartKeyDetails) {
          this.resizeObserver.observe(this.$refs.chartKeyDetails);
        }
      }
    },

    // 处理尺寸变化
    handleResize() {
      if (this.myChart) {
        this.$nextTick(() => {
          this.myChart.resize();
        });
      }
    },

    // 全屏切换
    handleScreen() {
      this.isFullscreen = !this.isFullscreen;

      // 延迟调整图表大小，确保DOM更新完成
      this.$nextTick(() => {
        setTimeout(() => {
          this.handleResize();
        }, 300); // 等待CSS动画完成
      });
    },

    // 处理键盘事件
    handleKeydown(event) {
      // 按ESC键退出全屏
      if (event.key === "Escape" && this.isFullscreen && this.visible) {
        this.isFullscreen = false;
      }
    },

    // 处理窗口大小变化
    handleWindowResize() {
      if (this.isFullscreen) {
        // 重新调整图表大小
        this.handleResize();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  // 确保在所有分辨率下都能正确覆盖
  min-width: 100%;
  min-height: 100%;
  overflow: hidden;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("../../../assets/bigScreenSanhao/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;
    transition: all 0.3s ease;

    &.suppression-risks-fullscreen {
      width: calc(100vw - 40px) !important;
      height: calc(100vh - 40px) !important;
      max-width: none !important;
      max-height: none !important;
      margin: 0 !important;
      // 确保在所有分辨率下都能正确显示
      min-width: calc(100% - 40px) !important;
      min-height: calc(100% - 40px) !important;

      .custom-dialog-body {
        height: calc(100% - 80px); // 减去header高度和padding
        max-height: calc(100% - 80px);
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("../../../assets/bigScreenSanhao/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("../../../assets/bigScreenSanhao/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      height: 800px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .bg-box-content {
          .bg-box-img {
            width: 120px;
            height: 120px;
            background: #fff;
            vertical-align: middle;
            position: relative;

            img {
              width: 120px;
              height: 120px;
              display: inline-block;
            }
          }

          .bg-box-summary {
            margin-left: 20px;
          }
        }

        .flex-box {
          display: flex;
        }
      }
    }
  }
}

::v-deep .el-table__header th {
  background-color: #1f3850 !important;
  color: rgba(255, 255, 255);
  font-size: 16px;
}

::v-deep .el-table__body td {
  font-size: 14px;
}

::v-deep .el-table__body tr:hover > td {
  background-color: #132f5600 !important;
}

::v-deep .pagination-container {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 0;

  .el-select .el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }

  .el-pagination__editor.el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }
}

::v-deep .el-pagination__total,
::v-deep .el-pagination__jump {
  color: #f2f2f2;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination button:disabled {
  background-color: #ffffff00;
  color: #fff;
}

::v-deep .el-pager li {
  background: #ffffff00;
  color: #fff;

  &.active {
    color: #1890ff;
  }
}
</style>
