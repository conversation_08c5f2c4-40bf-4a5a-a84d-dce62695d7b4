<template>
  <div id="mainLine" style="width: 100%; height: 100%"></div>
</template>
 
<script>
import request from "@/utils/request";
import * as echarts from "echarts";
import { largeTrendQueryByTime } from "@/api/bigScreen/index1";

export default {
  data() {
    return {
      myChart: null,
      option: {},
    };
  },
  mounted() {
    this.initChart();
  },
  components: {},
  methods: {
    async initChart() {
      let list
      await largeTrendQueryByTime({}).then(res => {
        list = res.data
      })
      const chineseMonths = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二']
      let data = list.map(item => {
        return chineseMonths[new Date(item.gatherTime).getMonth()] + '月'
      })
      let chartDom = document.getElementById("mainLine");
      this.myChart = echarts.init(chartDom);
      this.myChart.resize();
      this.option = {
        tooltip: {
          showContent: true,
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          show: true, //是否显示
          textStyle: { color: "#fff" },
          padding: [15, 10],
          x: "right",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "5%",
          top: "14%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: data,
          axisLabel: {
            fontSize: "14px",
            color: "#fff",
            formatter: function (value) {
              if (value.length > 4) {
                return `${value.slice(0, 4)}...`;
              }
              return value;
            },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            lineStyle: {
              color: "#1BDCFF40",
            },
          },
          axisLabel: {
            interval: 0,
            fontSize: "14px",
            color: "#fff",
            formatter: function (value) {
              if (value.length > 4) {
                return `${value.slice(0, 4)}...`;
              }
              return value;
            },
          },
        },
        series: [
          {
            name: '国内信息源',
            type: 'line',
            data: list.map(item => {
              return item.gatherData[1]
            })
          }, {
            name: '国外信息源',
            type: 'line',
            data: list.map(item => {
              return item.gatherData[2]
            })
          }
        ],
      };
      window.addEventListener("resize", () => {
        this.myChart.resize();
      });

      this.myChart.setOption(this.option);
    },
  },
  beforeDestroy() {
    window.removeEventListener("resize", () => {
      this.myChart.resize();
    });
  },
};
</script>

<style lang='scss'>
.el-table .warning-row {
  background-color: #13436d;
}

.el-table .success-row {
  background-color: #113a65;
}
</style>