import request from '@/utils/request'

// 查询采集趋势折线图数据统计列表
export function listTrend(query) {
  return request({
    url: '/large/trend/list',
    method: 'get',
    params: query
  })
}

// 查询采集趋势折线图数据统计详细
export function getTrend(id) {
  return request({
    url: '/large/trend/' + id,
    method: 'get'
  })
}

// 新增采集趋势折线图数据统计
export function addTrend(data) {
  return request({
    url: '/large/trend',
    method: 'post',
    data: data
  })
}

// 修改采集趋势折线图数据统计
export function updateTrend(data) {
  return request({
    url: '/large/trend',
    method: 'put',
    data: data
  })
}

// 删除采集趋势折线图数据统计
export function delTrend(id) {
  return request({
    url: '/large/trend/' + id,
    method: 'delete'
  })
}
