<!-- 科情页面 -->
<template>
  <div class="emptyFather">
    <div class="empty" v-if="isEmpty">
      <i class="el-icon-office-building" style="font-size: 180px; color: #9b9b9b"></i>
      <p>还没有监测方案</p>
      <el-button icon="el-icon-plus" type="primary" @click="drawer = true">新建专题</el-button>
    </div>
    <div v-else v-loading="loadingOpen" element-loading-text="数据加载中" element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)" style="height: 100%; overflow: hidden">
      <leftLink :type="'KeQin'" :isActive="isActive" :ActiveData="ActiveData" :drawer="drawer" :KeList="KeList"
        @activeLink="activeKeData" @AddKeEvent="AddKeEvent" @deleteEvetn="deleteEvetn" />
      <div class="rightMain" style="height: calc(100vh - 58px);overflow-y: auto;">
        <component :is="compName" :buttonDisabled="buttonDisabled" :SeachData="SeachData" :ActiveData="ActiveData"
          @EmitInfo="SwitchInfo" @SeachEvent="(resetPage) => EsSeach(resetPage)" :seniorSerchFlag="seniorSerchFlag" @seniorSerch="seniorSerch"
          :areaList="areaList" :countryList="countryList"></component>
        <!-- <topSeach :ActiveData="ActiveData" @EmitInfo="SwitchInfo"></topSeach> -->
        <div v-if="isInfo === '信息模式'">
          <MainArtivle v-loading="buttonDisabled" :flag="'Monitor'" :currentPage="currentPage" :pageSize="pageSize" :total="total"
            :ArticleList="ArticleList" :keywords="keywords" @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange" @Refresh="refreshData" :SeachData="SeachData" ref="mainArticle"></MainArtivle>
        </div>
        <div v-if="isInfo === '统计模式'">
          <statistics></statistics>
        </div>
      </div>
    </div>
    <!-- 新建科情 -->
    <KeDrawer :title="DrawerTitle" :visble="drawer" :editData="editData" @beforCloe="handleClose" @submitKe="saveEvent"
      :areaList="areaList" :countryList="countryList" />
  </div>
</template>

<script>
import topSeach from '@/views/components/topSeach.vue'
import MainArtivle from '@/views/components/MainArticle.vue'
import statistics from '@/views/components/statistics.vue'
import leftLink from '@/views/components/leftLink.vue'
import KeDrawer from '@/views/components/KeDrawer.vue'
import api from '@/api/ScienceApi/index.js'

export default {
  components: {
    topSeach,
    MainArtivle,
    statistics,
    leftLink,
    KeDrawer
  },
  data() {
    return {
      seniorSerchFlag: false /* 普通检索或高级检索 */,
      DrawerTitle: '新增科情',
      ArticleList: [],
      SeachData: {
        metaMode: '' /* 匹配模式 */,
        keyword: '' /* 关键词 */,
        sortMode: false /* 排序模式 */,
        releaseArea: '' /* 发布地区 */,
        timeRange: '4' /* 时间范围 */,
        country: [] /* 国家或地区 */,
        customDay: '' /* 自定义天 */,
        radio: '' /* 平台类型 */,
        area: [] /* 领域 */,
        industry: [] /* 行业 */
      },
      currentPage: 1 /* 当前页数  */,
      pageSize: 50 /* 每页条数 */,
      total: 0 /* 总条数 */,
      compName: 'topSeach',
      drawer: false,
      isActive: 0,
      ActiveData: {},
      isInfo: '信息模式' /* 当前是否是信息模式 */,
      loadingOpen: false /* loading */,
      timer: null /* 定时器 */,
      isEmpty: false,
      KeList: [] /* 科情列表 */,
      editData: {} /* 修改科情暂存 */,
      areaList: [] /* 国内地区 */,
      countryList: [] /* 国外地区 */,
      buttonDisabled: false, /* 按钮防抖 */
      keywords: ''
    }
  },

  watch: {
    isActive(newVaslue, old) {
      /* 防止出现未选中的情况 */
      if (newVaslue == null) {
        this.isActive = old
      }
    },
    KeList: function (newVal, oldVal) {
      if (newVal.length == 0) {
        this.isEmpty = true
      }
    },
    'SeachData.timeRange': {
      handler(newVal, oldVal) {
        this.SeachData.customDay = ''
      },
      deep: true
    }
  },
  filters: {},
  created() {
    this.getList()
    // window.addEventListener('wheel', event => {
    //   if (event.deltaY > 0) {
    //     this.seniorSerchFlag = false
    //   } else if (event.deltaY < 0) {
    //   }
    // })
  },
  methods: {
    handleClose(cloe) {
      this.drawer = cloe
      this.DrawerTitle = '新增科情'
    },
    /* 提交新建科情 */
    async saveEvent(data) {
      let params = {
        areaType: data.region,
        exclusions: data.exclude,
        keywords: data.mainBody,
        title: data.KeName,
        type: String(data.platformType),
        publishArea: String(data.countryOrCrea)
      }
      if (this.DrawerTitle == '修改科情') {
        params.id = data.id
        let res = await api.editMonitoring(params)
        if (res.code == 200) {
          this.$message({ message: '修改科情成功', type: 'success' })
        } else {
          this.$message({
            message: '修改科情失败,请联系管理员',
            type: 'error'
          })
        }
        this.DrawerTitle = '新增科情'
      } else {
        let res = await api.Addmonitoring(params)
        if (res.code == 200) {
          this.$message({ message: '新增科情成功', type: 'success' })
        } else {
          this.$message({
            message: '新增科情失败,请联系管理员',
            type: 'error'
          })
        }
      }

      this.drawer = false
      this.getList()
    },
    async getList() {
      await api.monitoringList({ pageSize: 50, pageNum: 1 }).then(Data => {
        if (Data.code == 200) {
          this.KeList = Data.rows
          /* 科情列表为空 */
          if (Data.rows.length == 0) {
            this.isEmpty = true
            this.$message({ message: '请先添加科情', type: 'warning' })
            return
          }
          this.ActiveData = this.KeList[this.isActive]
          this.isEmpty = false
          // 在获取科情列表成功后，先获取地区列表，再搜索
          this.getArea().then(() => {
            // 只在这里调用一次EsSeach
            this.EsSeach(true)
          })
        } else {
          this.isEmpty = true
          this.$message({
            message: '科情列表获取失败，请联系管理员',
            type: 'error'
          })
        }
      }).catch(() => {
        this.isEmpty = true
        this.$message({
          message: '科情列表获取失败，请联系管理员',
          type: 'error'
        })
      })
    },
    async EsSeach(resetPage = false) {
      // 如果已经在执行中，则不重复调用
      if (this.buttonDisabled) {
        console.log('正在搜索中，请稍候...')
        return
      }
      
      this.buttonDisabled = true
      // 如果需要重置页码（比如筛选条件变更时），则将currentPage设为1
      if (resetPage) {
        this.currentPage = 1
      }
      // this.loadingOpen = true
      
      let keywords = params => {
        if (this.SeachData.keyword && this.KeList[this.isActive].keywords) {
          return this.SeachData.keyword + ',' + this.KeList[this.isActive].keywords
        } else if (this.SeachData.keyword) {
          return this.SeachData.keyword
        } else if (this.KeList[this.isActive].keywords) {
          return this.KeList[this.isActive].keywords
        }
      }
      let industry = this.SeachData.industry.map(item => String(item)),
        area = this.SeachData.area.map(item => String(item))
      let params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        id: this.KeList[this.isActive].id,
        dateType: this.SeachData.timeRange != 6 ? this.SeachData.timeRange : '',
        startTime: this.SeachData.customDay[0],
        endTime: this.SeachData.customDay[1],
        publishType: this.SeachData.releaseArea,
        publishArea: String(this.SeachData.country),
        isSort: this.SeachData.sortMode,
        sourceType: this.SeachData.radio,
        matchObject: '',
        keywords: keywords(),
        matchType: this.SeachData.metaMode,
        industryList: industry,
        fieldList: area,
        isTechnology: this.SeachData.isTechnology
      }
      this.keywords = keywords()

      await api.EsSeach(params).then(data => {
        if (data.code == 200) {
          this.ArticleList = data.data.list
          this.total = data.data.total
        } else {
          this.$message({
            message: '文章获取失败，请联系管理员',
            type: 'error'
          })
        }
      })
      setTimeout(() => {
        this.buttonDisabled = false
        this.scrollToTop()
      }, 1000)
      // this.loadingOpen = false
    },
    async getArea() {
      return await api.getAreaList().then(Response => {
        if (Response.code == 200) {
          this.areaList = Response.data[0]
          this.countryList = Response.data[1]
          // 移除这里的EsSeach调用
        } else {
          this.$message({ message: '地区数据获取失败', type: 'error' })
        }
      }).catch(err => {
        this.$message({ message: '地区数据获取失败', type: 'error' })
      })
    },
    /* 页面条数变化 */
    handleSizeChange(num) {
      this.pageSize = num
      this.EsSeach()
      this.scrollToTop()
    },
    /* 页码变化 */
    handleCurrentChange(current) {
      this.currentPage = current
      this.EsSeach()
      this.scrollToTop()
    },
    SwitchInfo(data) {
      this.isInfo = data
    },
    /* 切换科情 */
    activeKeData(activeIndex) {
      this.isActive = activeIndex
      if (!this.isActive) return
      this.ActiveData = this.KeList[this.isActive]
      setTimeout(()=>{
        this.currentPage = 1
        this.EsSeach(true)
        this.scrollToTop()
      },100)
    },
    /* 修改科情 */
    AddKeEvent(flag, item) {
      this.editData = JSON.parse(JSON.stringify(item))
      this.drawer = flag
      if (item) {
        this.DrawerTitle = '修改科情'
      }
    },
    /* 删除科情 */
    deleteEvetn(id) {
      this.$confirm('您确定要删除这条数据吗', '删除科情', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let res = await api.deleteMonitoring([id])
          if (res.code == 200) {
            this.$message({ message: '删除成功', type: 'success' })
            this.isActive = 0
            this.getList()
          } else {
            this.$message({ message: '删除失败，请联系管理员', type: 'error' })
          }
        })
        .catch(() => { })
    },
    seniorSerch() {
      this.seniorSerchFlag = !this.seniorSerchFlag
    },
    /* 滚动到顶部 */
    scrollToTop() {
      this.$nextTick(() => {
        // 获取右侧内容区域
        const rightMain = this.$el.querySelector('.rightMain')
        if (rightMain) {
          rightMain.scrollTop = 0
        }
        
        // 获取文章列表滚动区域
        if (this.$el.querySelector('.scollBox')) {
          this.$el.querySelector('.scollBox').scrollTop = 0
        }
        
        // 使用 MainArticle 组件中的 mainScorll 方法
        const mainArticleRef = this.$refs.mainArticle
        if (mainArticleRef && typeof mainArticleRef.mainScorll === 'function') {
          mainArticleRef.mainScorll()
        }
      })
    },
    // 添加刷新数据的方法，使用防抖控制
    refreshData() {
      if (!this.buttonDisabled) {
        this.EsSeach(false)
      } else {
        console.log('请等待当前搜索完成')
      }
    }
  },
  /* 清除定时器 */
  beforeDestroy() {
    clearTimeout(this.timer)
  }
}
</script>

<style lang="scss" scoped></style>
