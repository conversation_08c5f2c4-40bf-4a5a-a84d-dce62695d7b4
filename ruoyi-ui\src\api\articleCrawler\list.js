import request from '@/utils/request'

// 查询元数据文章列（清洗后的数据）列表
export function listList(query) {
  return request({
    url: '/articleCrawler/list/list',
    method: 'get',
    params: query
  })
}

// 查询元数据文章列（清洗后的数据）详细
export function getList(id) {
  return request({
    url: '/articleCrawler/list/' + id,
    method: 'get'
  })
}

// 新增元数据文章列（清洗后的数据）
export function addList(data) {
  return request({
    url: '/articleCrawler/list',
    method: 'post',
    data: data
  })
}

// 修改元数据文章列（清洗后的数据）
export function updateList(data) {
  return request({
    url: '/articleCrawler/list/edit',
    method: 'post',
    data: data
  })
}
// 修改元数据文章列（清洗后的数据）
export function articleListEdit(data) {
  return request({
    url: '/article/articleList/edit',
    method: 'post',
    data: data
  })
}

// 删除元数据文章列（清洗后的数据）
export function delList(id) {
  return request({
    url: '/articleCrawler/list/remove',
    method: 'post',
    data: id
  })
}

// 同步数据
export function synchronousList(data) {
  return request({
    url: '/articleCrawler/list/synchronous',
    method: 'get',
    params: data,
  })
}

// 上传附件
export function uploadCover(data) {
  return request({
    url: '/scanning/list/cover',
    method: 'post',
    data: data,
  })
}

// 附件下载 
export function downloadFile(params) {
  return request({
    url: "/articleCrawler/list/download/file",
    method: "post",
    data: params,
    responseType: "blob",
    headers: {
      "Content-Type": "multipart/form-data; boundary=something",
      Accept: "*/*",
    },
  });
};