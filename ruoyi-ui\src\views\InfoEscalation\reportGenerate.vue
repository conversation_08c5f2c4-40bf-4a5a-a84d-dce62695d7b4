<!-- 报告生成 -->
<template>
  <div v-loading="loadingOpen" class="brieFing" element-loading-text="数据加载中" element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)">
    <div class="topSeach" v-if="!preview">
      <div class="formStyle">
        <el-row :gutter="20">
          <el-col :span="1.5">简报名称</el-col>
          <el-col :span="4">
            <el-input size="mini" v-model="BriefingName" placeholder="请输入简报名称" clearable />
          </el-col>
          <el-col :span="1.5">创建时间</el-col>
          <el-col :span="3">
            <el-select size="mini" placeholder v-model="timeRange">
              <el-option value="all" label="全部">全部</el-option>
              <el-option value="time" label="时间选择器">时间选择器</el-option>
            </el-select>
          </el-col>
          <el-col :span="9">
            <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" unlink-panels v-if="timeRange == 'time'"
              v-model="TImeInterval" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期"></el-date-picker>
          </el-col>
          <el-col :span="2.5">
            <el-button type="primary" size="mini" @click="getList" :loading="buttonDisabled">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetFields">重置</el-button>
          </el-col>
        </el-row>
        <el-button type="primary" size="mini" @click="visble = true">新建简报</el-button>
      </div>
      <div class="btnGroup">
        <div class="iconTop">
          <el-tooltip class="item" effect="dark" content="图片视图" placement="bottom">
            <p class="el-icon-menu" :style="{ color: switchView == '图片视图' ? '#1890ff' : '' }"
              @click="switchView = '图片视图'"></p>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="列表视图" placement="bottom">
            <p class="el-icon-s-operation" :style="{ color: switchView == '列表视图' ? '#1890ff' : '' }"
              @click="switchView = '列表视图'"></p>
          </el-tooltip>
        </div>
      </div>
    </div>
    <mainBriefingVue :preview="preview" :pageCurrent="pageNum" :total="total" :pageSize="pageSize"
      :switchView="switchView" :tableData="tableData" @deleteBrie="deleteEvent" @handleCurrentChange="handleCurrentChange"
      @handleSizeChange="handleSizeChange" />
    <briefingDialog :visble="visble" @onsubmit="onsubmit" :distinguish="distinguish" :switchView="switchView"
      :switchSourceType="false" @beforCloe="cloeEvent" :editorUsers="editorUsers" :chiefEditorUsers="chiefEditorUsers"></briefingDialog>
  </div>
</template>

<script>
import mainBriefingVue from '../components/mainBriefing.vue'
import API from '@/api/ScienceApi/briefing.js'
import briefingDialog from '@/views/components/briefingDialog.vue'
import { listRole, allocatedUserList } from '@/api/system/role'
export default {
  components: {
    mainBriefingVue,
    briefingDialog
  },
  data() {
    return {
      preview: false,
      visble: false,
      distinguish: '新建简报',
      total: 0,
      pageSize: 10,
      pageNum: 1,
      tableData: [],
      loadingOpen: false,
      TImeInterval: [],
      BriefingName: '' /* 报告名称 */,
      timeRange: 'all',
      switchView: '图片视图',
      buttonDisabled: false,
      editorUsers: [], // 编辑角色用户列表
      chiefEditorUsers: [] // 主编角色用户列表
    }
  },
  created() {
    this.getList()
    this.getRolesAndUsers()
  },
  methods: {
    async getList() {
      this.loadingOpen = true
      this.buttonDisabled = true
      if (this.buttonDisabled == false) return
      if (!Array.isArray(this.TImeInterval)) {
        this.TImeInterval = []
      }
      let params = {
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        title: this.BriefingName,
        startTime: this.TImeInterval[0],
        endTime: this.TImeInterval[1],
        sourceType: '1'
      }
      let res = await API.queryReportList(params)
      if (res.code == 200) {
        this.tableData = res.rows
        this.total = res.total
      } else {
        this.$message({ message: '简报列表获取失败', type: 'error' })
      }
      this.loadingOpen = false
      setTimeout(() => {
        this.buttonDisabled = false
      }, 1000)
    },
    resetFields() {
      this.BriefingName = ''
      this.TImeInterval = []
      this.timeRange = 'all'
    },
    async deleteEvent(dataId) {
      this.$confirm('您确定要删除这条数据吗', '删除科情', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let res = await API.removeBriefing([dataId])
          if (res.code == 200) {
            this.$message({ message: '删除成功', type: 'success' })
            this.getList()
          } else {
            this.$message({ message: '删除失败,请联系管理员', type: 'error' })
          }
        })
        .catch(() => { })
    },
    cloeEvent() {
      this.visble = false
    },
    /* 新增简报 */
    async onsubmit(model) {
      this.visble = false
      let params
      let res
      /* 修改定时任务 */
      if (model.id) {
        this.editTimeOut(model)
        return
      }
      /* 新增的情况 */
      if (this.distinguish == '生成简报') {
        params = {
          title: model.briefingName,
          startTime: model.TImeInterval[0],
          endTime: model.TImeInterval[1],
          sourceType: String(model.platformType),
          keywords: model.keyWords,
          exclusions: model.excludeWords,
          type: 4
        }
      } else if (this.distinguish == '新建简报') {
        params = {
          title: model.briefingName /* 简报名称 */,
          type: 5,
          templateId: model.report,
          sourceType: '1'
        }
      }
      if (this.distinguish !== '定时任务') {
        res = await API.addReport(params)
        if (res.code == 200) {
          this.$message({
            message: '新增成功',
            type: 'success'
          })
          this.getList()
        } else {
          this.$message({
            message: '新增失败,请联系管理员',
            type: 'error'
          })
        }
        return
      }
      /* 新增定时任务方法 */
      this.AddTimeOut(model)
    },
    handleCurrentChange(current) {
      this.pageNum = current
      this.getList()
    },
    handleSizeChange(size) {
      this.pageSize = size
      this.getList()
    },
    /* 获取角色和用户列表 */
    async getRolesAndUsers() {
      try {
        // 获取所有角色
        const roleResponse = await listRole({})
        const roles = roleResponse.rows || []

        // 查找编辑和主编角色
        const editorRole = roles.find(role => role.roleName === '编辑')
        const chiefEditorRole = roles.find(role => role.roleName === '主编')

        if (editorRole) {
          // 获取编辑角色的用户列表
          const editorUsersResponse = await allocatedUserList({
            roleId: editorRole.roleId,
            pageNum: 1,
            pageSize: 1000
          })
          this.editorUsers = editorUsersResponse.rows || []
        }

        if (chiefEditorRole) {
          // 获取主编角色的用户列表
          const chiefEditorUsersResponse = await allocatedUserList({
            roleId: chiefEditorRole.roleId,
            pageNum: 1,
            pageSize: 1000
          })
          this.chiefEditorUsers = chiefEditorUsersResponse.rows || []
        }
      } catch (error) {
        console.error('获取角色和用户列表失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.brieFing {
  height: calc(100vh - 90px);
}

.topSeach {
  width: 100%;
  height: 100px;
  line-height: 40px;
  box-shadow: 1px 1px 16px 6px #efefef;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .formStyle {
    padding: 0 0 0 20px;
    flex: 3;
  }

  .btnGroup {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 15px;
    flex: 1;
  }
}

.iconTop {
  margin-right: 20px;

  p {
    font-size: 28px;
    margin: 0 5px;
    color: rgb(90, 89, 89);
  }
}
</style>