import request from "@/utils/request";

// 留言分页列表-管理员
export function feedbackList(query) {
  return request({
    url: "/article/feedback/list",
    method: "get",
    params: query,
  });
}

// 新增留言
export function feedbackAdd(data) {
  return request({
    url: "/article/feedback/add",
    method: "post",
    data: data,
  });
}

// 删除留言
export function feedbackRemove(id) {
  return request({
    url: "/article/feedback/remove",
    method: "post",
    data: id,
  });
}

// 留言分页列表-用户
export function feedbackOwnList(query) {
  return request({
    url: "/article/feedback/own/list",
    method: "get",
    params: query,
  });
}