<template>
  <div class="app-container">
    <el-form style="margin-bottom: 10px; padding-top: 20px; border: 2px solid #eeeeee;" :model="queryParams"
      ref="queryForm" size="mini" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="标题" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入标题" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="类型" prop="sourceType">
        <el-select v-model="queryParams.sourceType" placeholder="请选择类型" clearable>
          <el-option v-for="(item, index) in sourceTypeList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据源" prop="sourceName">
        <el-select v-model="queryParams.sourceName" placeholder="请选择数据源" clearable>
          <el-option v-for="(item, index) in sourceLists" :key="index" :label="item.cnName"
            :value="item.cnName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="作者" prop="author">
        <el-input v-model="queryParams.author" placeholder="请输入作者" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="发布时间" prop="publishTime">
        <el-date-picker v-model="queryParams.publishTime" size="mini" type="date" placeholder="选择日期"
          value-format="yyyy-MM-dd"></el-date-picker>
      </el-form-item>
      <el-form-item label="采集时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" size="mini" type="date" placeholder="选择日期"
          value-format="yyyy-MM-dd"></el-date-picker>
      </el-form-item>
      <el-form-item label="是否有附件" prop="isFile">
        <el-select size="mini" v-model="queryParams.isFile" placeholder="请选择是否有附件" clearable>
          <el-option label="全部" :value="''"></el-option>
          <el-option label="是" :value="1"></el-option>
          <el-option label="否" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="标签" prop="tagsName">
        <el-input v-model="queryParams.tagsName" placeholder="请输入标签" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="是否处理" prop="isFinish">
        <el-select size="mini" v-model="queryParams.isFinish" placeholder="请选择是否处理完成" clearable>
          <el-option label="全部" :value="''"></el-option>
          <el-option label="是" :value="1"></el-option>
          <el-option label="否" :value="0"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['crawler:list:add']">新增</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['crawler:list:edit']">修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['crawler:list:remove']">删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain size="mini" @click="handleSynchronous">批量同步清洗后的干净数据</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['crawler:list:export']">导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="listList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="标题" prop="title" min-width="300" show-overflow-tooltip />
      <el-table-column label="中文标题" prop="cnTitle" min-width="300" show-overflow-tooltip />
      <el-table-column label="平台类型" align="center" prop="sourceType" width="100">
        <template slot-scope="scope">
          {{ sourceTypeList.filter(item => item.id == scope.row.sourceType)[0].name }}
        </template>
      </el-table-column>
      <el-table-column label="数据源" align="center" prop="sourceName" width="200" />
      <el-table-column label="数据源地址" align="center" prop="originalUrl" width="200" show-overflow-tooltip />
      <el-table-column label="作者" prop="author" width="140" show-overflow-tooltip />
      <el-table-column label="发布时间" align="center" prop="publishTime" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="采集时间" align="center" prop="collectTime" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.collectTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="附件" prop="fileUrl" width="60">
        <template slot-scope="scope">
          <p v-if="scope.row.fileUrl" @click="documentDownload(scope.row)">下载</p>
        </template>
      </el-table-column>
      <el-table-column label="标签" prop="tagsName" width="100" />
      <!-- <el-table-column label="是否处理完成" align="center" prop="isFinish" width="100">
        <template slot-scope="{row}">
          <el-switch v-model="row.isFinish" active-value="0" inactive-value="1" active-color="#13ce66"
            inactive-color="#ff4949" @change="handleIsFinishChange(row)"></el-switch>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="节点标识" prop="appId" width="160" /> -->

      <el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width" width="100">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleUpdate(scope.row)"
            v-hasPermi="['crawler:list:edit']">修改</el-button>
          <el-button size="mini" type="text" @click="handleDelete(scope.row)"
            v-hasPermi="['crawler:list:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改元数据文章列（清洗后的数据）对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body :close-on-click-modal="false">
      <div class="dialog_Box">
        <el-form :model="form" class="form_Style" label-position="top" ref="form" :rules="rules" size="mini">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="文章标题" prop="title">
                <el-input v-model="form.title"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="中文标题" prop="cnTitle">
                <el-input v-model="form.cnTitle"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="平台类型" prop="sourceType">
                <el-select v-model="form.sourceType" style="width: 100%" clearable>
                  <el-option v-for="(item, index) in sourceTypeList" :key="index" :label="item.name"
                    :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="文章作者" prop="author">
                <el-input v-model="form.author" style="width: 100%" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="发布时间" prop="publishTime">
                <el-date-picker v-model="form.publishTime" value-format="yyyy-MM-dd HH:mm:ss" type="date"
                  style="width: 100%" clearable placeholder="选择日期"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="动态标签" prop="tagsName">
                <el-select v-model="form.tagsName" style="width: 100%" multiple filterable allow-create
                  default-first-option placeholder="请添加文章标签"></el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="原文链接" prop="originalUrl">
            <el-input v-model="form.originalUrl"></el-input>
          </el-form-item>
          <el-form-item label="媒体来源" prop="sourceName">
            <el-input v-model="form.sourceName"></el-input>
          </el-form-item>
          <el-form-item label="摘要" prop="summary">
            <el-input v-model="form.summary" type="textarea" :autosize="{ minRows: 3, maxRows: 6 }"></el-input>
          </el-form-item>
          <el-form-item label="中文摘要" prop="cnSummary">
            <el-input v-model="form.cnSummary" type="textarea" :autosize="{ minRows: 3, maxRows: 6 }"></el-input>
          </el-form-item>
          <el-form-item label="中文内容" prop="cnContent">
            <editor v-model="form.cnContent" :minHeight="150"></editor>
          </el-form-item>
          <el-form-item label="文章内容" prop="content">
            <editor v-model="form.content" :minHeight="150"></editor>
          </el-form-item>
          <el-form-item label="封面图片" prop="cover">
            <el-upload action="#" ref="upload" :limit="3" :on-exceed="exceed" list-type="picture-card"
              :auto-upload="false" :headers="vertifyUpload.headers" :file-list="fileList" :on-change="handleChange"
              :http-request="requestLoad">
              <i slot="default" class="el-icon-plus"></i>
              <div slot="file" slot-scope="{ file }">
                <img class="el-upload-list__item-thumbnail" :src="file.url" alt="文件缩略图加载失败" />
                <span class="el-upload-list__item-actions">
                  <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="上传附件" prop="fileUrl">
            <el-upload class="upload-demo" :action="fileUrlurl" :before-upload="beforeUploadUrl" multiple :limit="1"
              :http-request="uploadUrlRequest" :on-success="uploadUrlSuccess" :file-list="fileUrlList"
              :on-exceed="uploadUrlExceed" :on-remove="uploadUrlRemove">
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listList, getList, delList, addList, updateList, synchronousList, uploadCover, downloadFile } from "@/api/articleCrawler/list";
import { getListClassify } from '@/api/article/classify'
import api from '@/api/ScienceApi/index.js'
import API from '@/api/ScienceApi/KeEnter.js'
import { getToken } from '@/utils/auth'
import { saveAs } from "file-saver";
import { blobValidate, tansParams } from "@/utils/ruoyi";

export default {
  name: "List",
  dicts: ['cleaning_content', 'cleaning_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 元数据文章列（清洗后的数据）表格数据
      listList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        articleSn: null,
        title: null,
        cnTitle: null,
        sourceType: null,
        sourceName: null,
        sourceSn: null,
        originalUrl: null,
        shortUrl: null,
        author: null,
        description: null,
        summary: null,
        cnSummary: null,
        cover: null,
        publishType: null,
        publishCode: null,
        publishArea: null,
        publishTime: null,
        numberLikes: null,
        numberReads: null,
        numberCollects: null,
        numberShares: null,
        numberComments: null,
        emotion: null,
        status: null,
        userId: null,
        deptId: null,
        content: null,
        cnContent: null,
        fileUrl: null,
        industry: null,
        domain: null,
        tagsName: null,
        tmpUrl: null,
        isFinish: null,
        groupId: null,
        appId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [{ required: true, message: '文章标题为必填项' }],
        content: [{ required: true, message: '文章详情为必填项' }],
        publishTime: [{ required: true, message: '发布时间为必填项' }],
        cnTitle: [{ required: true, message: '中文名称为必填项' }],
        sourceType: [{ required: true, message: '平台类型为必填项' }],
        originalUrl: [{ required: true, message: '原文为必填项' }],
        summary: [{ required: true, message: '请填写摘要' }],
        cnSummary: [{ required: true, message: '请填写中文摘要' }],
        sn: [{ required: true, message: '请填写文章地址唯一识别号' }]
      },
      sourceTypeList: [], // 数据源分类
      sourceLists: [], // 数据源列表
      inputVisible: false,
      inputValue: '',
      vertifyUpload: {
        isUploading: false,
        // 设置上传的请求头部
        headers: {
          Authorization: 'Bearer ' + getToken(),
          ContentType: 'application/json;charset=utf-8'
        },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/article/articleList/cover'
      },
      fileList: [],
      fileUrlList: [],
      fileUrlurl: process.env.VUE_APP_BASE_API + '/article/articleList/upload/file',
    }
  },
  created() {
    getListClassify().then(res => {
      this.sourceTypeList = res.data
    })
    api.getSourceList().then(data => {
      if (data.code == 200) {
        this.sourceLists = data.data
      }
    })
    this.getList();
  },
  methods: {
    /** 查询元数据文章列（清洗后的数据）列表 */
    getList() {
      this.loading = true;
      listList(this.queryParams).then(response => {
        this.listList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        articleSn: null,
        title: null,
        cnTitle: null,
        sourceType: null,
        sourceName: null,
        sourceSn: null,
        originalUrl: null,
        shortUrl: null,
        author: null,
        description: null,
        summary: null,
        cnSummary: null,
        cover: null,
        publishType: null,
        publishCode: null,
        publishArea: null,
        publishTime: null,
        numberLikes: null,
        numberReads: null,
        numberCollects: null,
        numberShares: null,
        numberComments: null,
        emotion: null,
        status: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null,
        content: null,
        cnContent: null,
        fileUrl: null,
        industry: null,
        domain: null,
        tagsName: null,
        tmpUrl: null,
        isFinish: null,
        groupId: null,
        appId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加元数据文章列";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getList(id).then(response => {
        this.form = response.data;
        this.form.sourceType = Number(this.form.sourceType)
        this.form.tagsName = this.form.tagsName ? this.form.tagsName.split(',') : []
        this.fileUrlList = this.form.fileUrl ? this.form.fileUrl.split(",").map(item => {
          return {
            name: item,
            url: item
          }
        }) : []
        this.open = true;
        this.title = "修改元数据文章列";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          let cover = String(this.fileList.map(item => item.path))
          let queryForm = this.form
          queryForm.cover = cover
          queryForm.tagsName = this.form.tagsName.join(",")
          if (queryForm.id != null) {
            updateList(queryForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addList(queryForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      let ids = null
      if (row && row.id) {
        ids = [row.id]
      } else {
        ids = this.ids;
      }
      this.$modal.confirm('是否确认删除元数据文章列编号为"' + ids + '"的数据项？').then(function () {
        return delList(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('crawler/list/export', {
        ...this.queryParams
      }, `list_${new Date().getTime()}.xlsx`)
    },
    // 是否处理切换
    handleIsFinishChange(item) {
      updateList(item).then(response => {
        this.$modal.msgSuccess('修改成功')
        this.getList()
      })
    },
    // 批量同步
    handleSynchronous() {
      this.$modal.confirm('是否确认批量同步元数据文章列？').then(function () {
        return synchronousList();
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("同步成功");
      }).catch(() => { });
    },
    /* 自定义上传 */
    async requestLoad(file) {
      let data = new FormData()
      data.append('cover', file.file)
      await uploadCover(data).then(response => {
        if (response.code == 200) {
          this.fileList.map(item => {
            if (item.uid == file.file.uid) {
              item.path = response.imgUrl
            }
          })
          this.$message({ message: '上传成功', type: 'success' })
        } else {
          this.$message({ message: '上传失败,请稍候重试', type: 'error' })
        }
      })
    },
    /* 文件超出限制 */
    exceed() {
      this.$message({ message: '文件上传超出限制,最多可以上传三个文件', type: 'info' })
    },
    /* 移除文件 */
    handleRemove(file) {
      this.fileList = this.fileList.filter(item => item !== file)
    },
    // 文件更改
    handleChange(file, fileList) {
      this.fileList = fileList
      this.$refs.upload.submit()
    },
    // 上传附件校验
    beforeUploadUrl(file) {
      // 判断文件是否为excel
      let fileName = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase(),
        condition = fileName == 'pdf' || fileName == 'doc' || fileName == 'xls' || fileName == 'ppt' || fileName == 'xlsx' || fileName == 'pptx' || fileName == 'docx'
      let fileSize = file.size / 1024 / 1024 < 10
      if (!condition) {
        this.$notify({
          title: '警告',
          message: '上传文件必须是pdf,doc,xls,ppt,xlsx,pptx,docx格式',
          type: 'warning'
        })
      }
      /* 文件大小限制 */
      if (!fileSize) {
        this.$notify({
          title: '警告',
          message: '上传文件的大小不能超过 10MB!',
          type: 'warning'
        })
      }
      return condition && fileSize
    },
    // 文件上传成功
    uploadUrlSuccess(res, file) {
      this.$message({ message: '上传成功', type: 'success' })
    },
    // 文件上传超出限制
    uploadUrlExceed() {
      this.$message({ message: '文件上传超出限制,最多可以上传1个文件', type: 'info' })
    },
    // 文件上传方法
    uploadUrlRequest(file) {
      if (this.form.originalUrl != null && this.form.originalUrl != '') {
        if (this.form.originalUrl.match(/(http|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/)) {
          let data = new FormData()
          data.append('file', file.file)
          data.append('originalUrl', this.form.originalUrl)

          API.uploadFile(data).then(response => {
            if (response.code == 200) {
              this.$message({ message: '上传成功', type: 'success' })
              this.form.fileUrl = response.data
            } else {
              this.$message({ message: '上传失败,请稍候重试', type: 'error' })
              this.form.fileUrl = ''
            }
          })
        } else {
          this.$message({ message: '请填写正确的原文链接', type: 'warning' })
          this.fileUrlList = []
        }
      } else {
        this.$message({ message: '请填写原文链接', type: 'warning' })
        this.fileUrlList = []
      }
    },
    // 删除附件
    uploadUrlRemove() {
      API.removeFile({ filePath: this.form.fileUrl }).then(response => {
        if (response.code == 200) {
          this.$message({ message: '删除成功', type: 'success' })
          this.fileUrlList = []
          this.form.fileUrl = ''
        } else {
          this.$message({ message: '删除失败,请稍候重试', type: 'error' })
        }
      })
    },
    /* 附件下载 */
    documentDownload(items) {
      this.loading = true
      let formData = new FormData()
      formData.append('id', items.id)
      downloadFile(formData).then(async (data) => {
        const isBlob = blobValidate(data);
        if (isBlob) {
          const blob = new Blob([data]);
          let list = items.fileUrl.split('.')
          let title = items.cnTitle ? items.cnTitle : items.title
          saveAs(blob, title + '.' + list[list.length - 1]);
        } else {
          const resText = await data.text();
          const rspObj = JSON.parse(resText);
          const errMsg =
            errorCode[rspObj.code] || rspObj.msg || errorCode["default"];
          this.$message({ message: errMsg, type: 'error' })
        }
        this.loading = false
      })
    },
  }
};
</script>
<style>
.el-tag+.el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
