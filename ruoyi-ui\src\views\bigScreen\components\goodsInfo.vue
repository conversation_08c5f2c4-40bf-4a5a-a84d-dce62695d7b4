<template>
  <div class="app-container">
    <div class="modal" :style="{ opacity: `${open ? 1 : 0}` }"></div>
    <!-- 添加或修改证书公示对话框 -->
    <el-dialog
      :modal="false"
      :visible.sync="visible"
      append-to-body
      :show-close="true"
      custom-class="info-dialog"
      width="960px"
      :close-on-click-modal="false"
    >
      <div
        class="info"
        v-loading="loading"
        element-loading-text="加载中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="#12078d80"
      >
        <img
          src="@/assets/bigScreen/close.png"
          alt=""
          class="close"
          @click="visible = false"
        />
        <div class="left">
          <div
            v-for="(item, index) in leftList"
            :key="index"
            :class="[{ active: activeName === item.tag }]"
            @click="infoFun(item, index)"
          >
            {{ item.tag }}
          </div>
        </div>
        <div class="right">
          <div
            class="content"
            v-html="
              leftList[articleIndex] ? leftList[articleIndex].article : null
            "
          ></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { infoScreen } from "@/api/bigScreen/index.js";
export default {
  components: {},
  props: {
    open: {
      required: true,
      default: false,
    },

    id: {
      required: true,
      default: "",
    },
  },
  computed: {
    visible: {
      get() {
        return this.open;
      },
      set(val) {
        this.$emit("update:open", val);
      },
    },
  },
  watch: {
    id(val) {
      if (val) {
        this.loading = true;
        this.getInfo(val);
      }
    },
  },
  data() {
    return {
      activeName: "",
      leftList: [],
      articleIndex: 0,
      loading: false,
    };
  },
  created() {},

  methods: {
    infoFun(item, index) {
      this.activeName = item.tag;
      this.articleIndex = index;
    },

    getInfo(id) {
      infoScreen(id).then((res) => {
        this.loading = false;
        this.leftList = res.data.screenEntityDataList;
        this.activeName = res.data?.screenEntityDataList?.[0]?.tag ?? null;
      });
    },
    /** 提交按钮 */
    submitForm() {},

    cancel() {
      this.visible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog-header {
  width: 100%;
}
.info {
  display: flex;
  padding: 10px 20px;
  .close {
    position: absolute;
    right: 15px;
    top: 15px;
    width: 24px;
    cursor: pointer;
  }
  .left {
    font-size: 14px;
    color: #fff;
    background: #1b5bff;
    width: 120px;
    margin-right: 15px;
    // margin: 0 20px;
    text-align: center;
    height: 476px;
    overflow: auto;
    > div {
      height: 42px;
      line-height: 42px;
      cursor: pointer;
    }
    .active {
      background: linear-gradient(to left, #2988e0, #22b2ef, #1bdcff);
    }
  }
  .right {
    color: #fff;
    // border: 1px solid #1bdcff40;
    flex: 1;
    height: 476px;
    padding: 30px 0;
    background: linear-gradient(to bottom, #1c0bda, #110c9b, #060d5b);
    .content {
      width: 660px;
      height: 416px;
      overflow: auto;
      margin: auto;
    }
  }
}
.modal {
  width: 1980px;
  height: 15000px;
  position: absolute;
  top: -120px;
  left: 0px;
  background: #0034d080 !important;
  pointer-events: none;
}
</style>

<style lang="scss">
.info-dialog {
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    margin-top: 18vh;
    background: linear-gradient(to bottom, #12078d, #0d096f);
  }
  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }

  /* 保持滚动效果 */
  .scrollable {
    overflow-y: scroll;
  }
  /* 隐藏滚动条 */
  .scrollable {
    scrollbar-width: none; /* Firefox */
  }

  /* 隐藏滚动条 */
  .scrollable {
    -ms-scrollbar-face-color: transparent; /* IE and Edge */
    -ms-scrollbar-3dlight-color: transparent; /* IE and Edge */
  }
  /* 保持滚动效果 */
}
</style>
