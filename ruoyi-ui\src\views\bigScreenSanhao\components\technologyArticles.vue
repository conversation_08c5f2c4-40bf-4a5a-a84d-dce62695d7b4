<template>
  <div class="technologyArticlesBox">
    <div class="technologyArticles" v-for="(item, index) in hotTechnologyList">
      <div
        class="technologyArticlesPng"
        @click="openHotTechnologyDetails(item)"
      >
        <img :src="baseUrl + item.cover" alt="" />
      </div>
      <div class="technologyArticlesBg"></div>
    </div>
  </div>
</template>

<script>
import { technicalReportData } from "@/api/bigScreen/sanhao.js";

export default {
  name: "TechnologyArticles",
  props: {
    sccenId: {
      type: Number,
      default: 1,
    },
    screenSn: {
      type: String,
      default: "1",
    },
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      hotTechnologyList: [],
    };
  },
  mounted() {
    this.fetchData();
  },
  watch: {
    screenSn: {
      handler() {
        this.fetchData();
      },
      immediate: false,
    },
  },
  methods: {
    fetchData() {
      console.log("technologyArticles 获取数据，screenSn:", this.screenSn);
      technicalReportData({
        screenSn: this.screenSn,
      })
        .then((res) => {
          this.hotTechnologyList = res.data;
          console.log("technologyArticles 数据获取成功:", res.data);
        })
        .catch((error) => {
          console.error("technologyArticles 数据获取失败:", error);
        });
    },
    openHotTechnologyDetails(data) {
      this.$emit("openHotTechnology", { ...data, title: data.reportName });
    },
  },
};
</script>

<style lang="scss" scoped>
.technologyArticlesBox {
  display: flex;
  justify-content: space-around;
  height: 100%;
  padding: 10px 15px 0px;

  .technologyArticles {
    position: relative;
    width: 134px;
    height: 100%;

    .technologyArticlesPng {
      position: relative;
      height: 100%;
      z-index: 2;
      display: flex;
      justify-content: center;

      img {
        cursor: pointer;
        position: absolute;
        top: 10px;
        height: 120px;
        box-shadow: 0px 0px 20px 4px skyblue;
      }
    }

    .technologyArticlesBg {
      position: absolute;
      bottom: 0;
      width: 134px;
      height: 80px;
      background-image: url("../../../assets/bigScreenSanhao/articleBase.png");
      background-size: 100%;
      z-index: 1;
    }
  }
}
</style>
