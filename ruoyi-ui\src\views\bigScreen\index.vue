<template>
  <v-scale-screen fullScreen width="1920" height="1080">
    <div class="bigMap">
      <!-- 上下布局 -->
      <el-row class="top">
        <el-col :span="8">
          <el-row type="flex" class="top-content">
            <el-col
              v-for="(item, index) in tabList"
              :key="index"
              :class="['tabs', { active: index === activeKey }]"
              @click.native="changeTabsFun(index)"
            >
              <img :src="item.icon" alt="" />
              <span>{{ item.name }}</span>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="8" class="center-content">
          {{ tabList[activeKey].name }}
        </el-col>
        <el-col :span="6" :offset="2">
          <div class="right-content">
            <el-row style="display: flex">
              <div>
                <span class="top-icon">
                  <img src="@/assets/bigScreen/time.png" alt=""
                /></span>
              </div>
              <div style="line-height: 55px">
                <span class="h1">
                  {{ currentTime ? parseTime(currentTime, "{h}:{i}:{s}") : "" }}
                </span>
                <span class="h2" style="margin-right: 30px">
                  {{ currentTime ? parseTime(currentTime, "{y}/{m}/{d}") : "" }}
                </span>
              </div>
            </el-row>
            <el-row style="display: flex">
              <div>
                <span class="top-icon">
                  <img src="@/assets/bigScreen/weather.png" alt=""
                /></span>
              </div>
              <div style="line-height: 55px">
                <span class="h1"> 20~28°C </span>
                <span class="h2" style="margin-right: 45px"> 晴转多云 </span>
              </div>
            </el-row>
          </div>
        </el-col>
      </el-row>
      <!-- 下面部分 -->
      <el-row class="bottom">
        <tabOne v-if="activeKey == 0" />
        <tabTwo v-else-if="activeKey == 1" />
        <tabThree v-else />
      </el-row>
    </div>
  </v-scale-screen>
</template>
<script >
import tabOne from "./tabOne";
import tabTwo from "./tabTwo";
import tabThree from "./tabThree";
export default {
  data() {
    return {
      tabList: [
        {
          name: "数据采集情况",
          index: 1,
          icon: require("@/assets/bigScreen/monitoring.png"),
        },
        {
          name: "科技安全监控",
          index: 2,
          icon: require("@/assets/bigScreen/monitoring2.png"),
        },
        {
          name: "数字图书馆",
          index: 3,
          icon: require("@/assets/bigScreen/dashboard.png"),
        },
      ],
      activeKey: 0,
      currentTime: "",
    };
  },
  components: { tabThree, tabOne, tabTwo },
  created() {
    setInterval(() => {
      this.currentTime = new Date();
      let weekDays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      this.week = weekDays[this.currentTime.getDay()];
    }, 1000);
  },
  beforeDestroy() {},
  methods: {
    //tabs点击事件
    changeTabsFun(index) {
      this.activeKey = index;
    },
  },
};
</script>
<style lang="scss" scoped>
.bigMap {
  background: url("../../assets/bigScreen/bg.png") 0px 0px no-repeat;
  background-size: 100% 100% !important;
  background-size: cover;
  font-size: 14px;
  color: #ffffff;
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0px;
  margin: 0px;
  font-family: "微软雅黑";
  display: flex;
  flex-direction: column;

  .top {
    height: 81px;
    background: url("../../assets/bigScreen/bg-top.png") no-repeat 0px 0px !important;
    background-size: 100% 100% !important;
    //左边
    .top-content {
      margin-top: 7px;
      margin-left: 50px;
      > div:nth-child(2) {
        margin-right: -12px;
        margin-left: -12px;
      }
      .tabs {
        background: url("../../assets/bigScreen/menu.png") 0px 0px no-repeat;
        background-size: 100% 100% !important;
        background-size: cover;
        width: 220px;
        height: 44px;
        cursor: pointer;
        line-height: 44px;
        text-align: center;
        img {
          width: 20px;
          height: 20px;
          display: inline-block;
          vertical-align: middle;
          margin-left: -2px;
          margin-top: -3px;
          margin-right: 1px;
        }
        span {
          margin-left: 3px;
          font-size: 16px;
          font-weight: bold;
        }
      }
      .active {
        background: url("../../assets/bigScreen/menu-active.png") 0px 0px
          no-repeat !important;
        background-size: 100% 100% !important;
        background-size: cover;
      }
    }
    //中间
    .center-content {
      text-align: center;
      font-size: 42px;
      color: linear-gradient(to bottom, #def7ff, #7ceaff, #1bdcff);
      background: linear-gradient(to bottom, #def7ff, #7ceaff, #1bdcff);
      /* 使用渐变背景时，通常需要设置这些值以确保文字可读 */
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-family: "douyinmeihaoti";
      height: 81px;
      line-height: 81px;
      font-weight: bold;
      img {
        width: 224px;
        height: 40px;
        // vertical-align: middle;
        margin-top: 15px;
      }
    }
    //右边
    .right-content {
      display: flex;
      font-family: "Soure Han Sans CN";
      justify-content: end;

      .h1 {
        color: #1bdcff;
        display: inline-block;
        font-size: 16px;
        margin-right: 5px;
        font-weight: bold;
      }
      .h2 {
        color: #1bdcff;
        display: inline-block;
        font-size: 12px;
      }
      .top-icon {
        width: 32px;
        height: 32px;
        margin-right: 5px;
        img {
          display: inline-block;
          margin-top: 10px;
          width: 32px;
          height: 32px;
          // vertical-align: middle;
        }
      }
    }
  }
  .bottom {
    flex: 1;
    height: calc(100% - 100px);
  }
}
</style>