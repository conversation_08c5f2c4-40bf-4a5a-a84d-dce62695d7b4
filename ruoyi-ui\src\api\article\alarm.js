import request from '@/utils/request'

// 查询预警采集列表
export function listAlarm(query) {
  return request({
    url: '/article/alarm/list',
    method: 'get',
    params: query
  })
}

// 查询预警采集详细
export function getAlarm(id) {
  return request({
    url: '/article/alarm/' + id,
    method: 'get'
  })
}

// 新增预警采集
export function addAlarm(data) {
  return request({
    url: '/article/alarm',
    method: 'post',
    data: data
  })
}

// 修改预警采集
export function updateAlarm(data) {
  return request({
    url: '/article/alarm',
    method: 'put',
    data: data
  })
}

// 删除预警采集
export function delAlarm(id) {
  return request({
    url: '/article/alarm/' + id,
    method: 'delete'
  })
}
