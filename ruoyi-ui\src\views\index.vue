<template>
  <div class="home">
    <!-- <div class="index_top">
      <div class="index_card">
        <div class="title">科情总量</div>
        <div class="info_box">
          <p>
            科情总量
            <span>{{thousandth(indexData.monitoringTotal.totalCount)}}</span>
          </p>
          <div class="line"></div>
          <p>
            微信平台
            <span>{{thousandth(indexData.monitoringTotal.wxTotal)}}</span>
          </p>
          <div class="line"></div>
          <p>
            网站
            <span>{{thousandth(indexData.monitoringTotal.wyTotal)}}</span>
          </p>
        </div>
      </div>
      <div class="index_card">
        <div class="title">专题总量</div>
        <div class="info_box">
          <p>
            专题总量
            <span>{{thousandth(indexData.specialTotal.totalCount)}}</span>
          </p>
          <div class="line"></div>
          <p>
            微信平台
            <span>{{thousandth(indexData.specialTotal.wxTotal)}}</span>
          </p>
          <div class="line"></div>
          <p>
            网站
            <span>{{thousandth(indexData.specialTotal.wyTotal)}}</span>
          </p>
        </div>
      </div>
    </div>
    <div class="dayInfo">
      <div class="index_card">
        <div class="title">今日新增科情</div>
        <div class="info_box_day">{{thousandth(indexData.toDayMonitoringCount)}}</div>
      </div>

      <div class="index_card">
        <div class="title">今日新增专题</div>
        <div class="info_box_day">{{thousandth(indexData.toDaySpecialCount)}}</div>
      </div>
      <div class="index_card">
        <div class="title">今日新增报告</div>
        <div class="info_box_day">{{thousandth(indexData.toDayReportCount)}}</div>
      </div>
    </div>-->
    <div class="echarts_top">
      <div class="topBox">
        <div style="max-width: 400px; min-width: 300px; margin-left: 0px">
          <span style="margin-right: 25px; font-size: 16px">
            当前:
            <span style="font-weight: 600; color: #1062ff">{{
              statisticObj.current
            }}</span>
          </span>
          <span style="margin-right: 25px; font-size: 16px">
            总量:
            <span style="font-weight: 600; color: #1062ff">{{
              statisticObj.totalCount
            }}</span>
          </span>
        </div>
        <div class="a">
          <p style="font-weight: 600">科情文章统计</p>
        </div>

        <el-select class="b" v-model="statistic" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <div class="line_Style" id="line"></div>
    </div>
    <div class="bottom_box">
      <div id="ring"></div>
      <div class="table">
        <p class="HotTitle">热门文章</p>
        <el-table
          size="mini"
          :data="indexData.hotListVoList"
          border
          :header-cell-style="{
            background: '#1062ff',
            textAlign: 'center',
            color: '#fff',
            fontSize: '18px',
          }"
          :cell-style="{
            fontSize: '16px',
          }"
          style="width: 97%; margin: 15px auto"
          height="400"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="cnTitle"
            label="标题"
            align="left"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span class="link-type" @click="openNewView(row)">{{
                row.cnTitle
              }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="sourceType" label="平台类型" width="120" align="center">
            <template slot-scope="{ row }">{{
              row.sourceType == 1 ? "国内信息源" : "网站"
            }}</template>
          </el-table-column> -->
          <el-table-column
            prop="sourceName"
            label="发布源"
            width="120"
            align="center"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="publishTime"
            label="发布时间"
            width="120"
            align="center"
          >
            <template slot-scope="{ row }">{{
              parseTime(row.publishTime, "{y}-{m}-{d}")
            }}</template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import api from "@/api/infoEscalation/index";
import { renderCol } from "@/utils/renderLine";
import { getConfigKey } from "@/api/system/config.js";
import { mapGetters } from "vuex";

export default {
  name: "Index",
  data() {
    return {
      // 版本号
      version: "3.8.6",
      statistic: "4",
      statisticObj: {},
      options: [
        { value: "0", label: "最近24小时" },
        // { value: '1', label: '最近24小时' },
        { value: "2", label: "最近7天" },
        { value: "3", label: "最近15天" },
        { value: "4", label: "最近30天" },
      ],
      tableData: [
        {
          title: "成都大运会博物馆开馆",
          sourceType: "国内信息源",
          source: "新华社",
          createdTime: "2023-07-25  13:30:00",
        },
        {
          title: "打起来的Temu和Shein：中式电商企业的困局",
          sourceType: "网站",
          source: "新华社",
          createdTime: "2023-07-25  13:30:00",
        },
        {
          title: "中国双边结算中人民币占比首超美元",
          sourceType: "网站",
          source: "宇宙V空间",
          createdTime: "2023-07-25  13:30:00",
        },
        {
          title: "中日韩纷纷加强与中东产油国的经济合作",
          sourceType: "国内信息源",
          source: "新华社",
          createdTime: "2023-07-25  13:30:00",
        },
        {
          title: "蒋凡的新故事开始了",
          sourceType: "国内信息源",
          source: "闻话人探剧",
          createdTime: "2023-07-25  13:30:00",
        },
        {
          title: "现代贸易格局重构：美国制造业回流时机成熟",
          sourceType: "国内信息源",
          source: "七O后珍姐",
          createdTime: "2023-07-25  13:30:00",
        },
        {
          title: "美中合作对全球卫生事业的意义2019",
          sourceType: "网站",
          source: "新华社",
          createdTime: "2023-07-25  13:30:00",
        },
        {
          title:
            "未到万事俱废时: 美国交通运输和水利设施的融资与财务前景内容摘要 (内容摘要)2017",
          sourceType: "国内信息源",
          source: "东北柳树毛",
          createdTime: "2023-07-25  13:30:00",
        },
        {
          title: "解密一带一路倡议: 澄清其主要特征、目标和影响",
          sourceType: "网站",
          source: "新华社",
          createdTime: "2023-07-25  13:30:00",
        },
        {
          title: "弥合差距（中文翻译摘要）: 评估美国商界对美中竞争的支持 2022",
          sourceType: "国内信息源",
          source: "新华社",
          createdTime: "2023-07-25  13:30:00",
        },
      ],
      charts: null,
      lineChart: null,
      resizeTimer: null, // 用于debounce resize事件
      sidebarElm: null, // 左侧菜单元素
      pieRotationTimer: null, // 饼图轮动定时器
      indexData: {
        monitoringTotal: {
          wyTotal: 0,
          totalCount: 0,
          wxTotal: 0,
        },
        specialTotal: {
          wyTotal: 0,
          totalCount: 0,
          wxTotal: 0,
        },
        toDayMonitoringCount: 0,
        toDaySpecialCount: 0,
        toDayReportCount: 0,
        proportion: {
          wyPercentage: "0",
          wxPercentage: "0",
        },
        hotListVoList: [],
        scrolltimer: "", // 自动滚动的定时任务
        timer: null,
      },
    };
  },
  computed: {
    ...mapGetters(["roles"]),
    // 订阅app模块中的sidebar状态
    isCollapse() {
      return this.$store.state.app.sidebar.opened;
    },
  },
  mounted() {
    // 不在这里初始化图表，而是在数据加载后初始化
    // 使用防抖处理resize事件，避免频繁触发
    window.addEventListener("resize", this.handleResize);

    // 监听菜单折叠状态变化
    this.watchSidebar();
  },
  created() {
    getConfigKey("role_menu").then((data) => {
      if (data.msg != "") {
        let role_menu = JSON.parse(data.msg);
        if (role_menu[this.roles[0]]) {
          this.$router
            .push({ path: `${role_menu[this.roles[0]]}` })
            .catch(() => {});
        }
      }
    });
    // 调用数据加载方法，数据加载成功后会自动渲染图表
    this.getIndexData();
    this.indexStatistic();
  },
  methods: {
    // 监听侧边栏的变化
    watchSidebar() {
      this.sidebarElm = document.getElementsByClassName("sidebar-container")[0];
      if (this.sidebarElm) {
        // 创建一个观察器实例并监听sidebar容器
        const observer = new MutationObserver((mutations) => {
          // 侧边栏变化时，延迟执行图表重绘以确保DOM已完成过渡动画
          setTimeout(() => {
            this.resizeCharts();
          }, 300);
        });

        // 开始观察sidebar的样式变化
        observer.observe(this.sidebarElm, {
          attributes: true,
          attributeFilter: ["style", "class"],
        });

        // 组件销毁时断开观察
        this.$once("hook:beforeDestroy", () => {
          observer.disconnect();
        });
      }
    },

    // 处理resize事件的防抖函数
    handleResize() {
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }
      this.resizeTimer = setTimeout(() => {
        this.resizeCharts();
      }, 100); // 100ms防抖
    },

    // 初始化所有图表
    initCharts() {
      // 不再这里初始化图表，而是在数据加载后初始化
      // 这样图表只会在有数据时才渲染
    },

    // 调整所有图表大小
    resizeCharts() {
      if (this.charts) {
        try {
          // 先获取容器的当前宽高
          const ringDom = document.getElementById("ring");
          if (ringDom) {
            // 强制重新计算容器大小
            this.charts.resize({
              width: "auto",
              height: 460, // 使用固定高度
            });

            // 确保在下一个动画帧中再次调整大小，以解决某些异步布局问题
            requestAnimationFrame(() => {
              this.charts.resize({
                width: "auto",
                height: 460,
              });
            });
          } else {
            this.charts.resize({
              height: 460,
            });
          }
        } catch (e) {
          console.error("Error resizing ring chart:", e);
        }
      }
      if (this.lineChart) {
        try {
          // 先获取容器的当前宽高
          const lineDom = document.getElementById("line");
          if (lineDom) {
            // 强制重新计算容器大小
            this.lineChart.resize({
              width: "auto",
              height: 300, // 使用固定高度
            });

            // 确保在下一个动画帧中再次调整大小，以解决某些异步布局问题
            requestAnimationFrame(() => {
              this.lineChart.resize({
                width: "auto",
                height: 300,
              });
            });
          } else {
            this.lineChart.resize({
              height: 300,
            });
          }
        } catch (e) {
          console.error("Error resizing line chart:", e);
        }
      }
    },
    goTarget(href) {
      window.open(href, "_blank");
    },
    renderRing(responseData = {}) {
      // 如果没有有效数据，则不渲染饼图
      if (!responseData || !responseData.proportion) {
        return;
      }

      var chartDom = document.getElementById("ring");
      // 如果DOM不存在，直接返回
      if (!chartDom) return;

      try {
        // 获取容器的实际尺寸
        chartDom.style.height = "100%";
        chartDom.style.width = "100%";

        // 确保容器可见并有合理的尺寸
        const parentWidth = chartDom.parentElement.clientWidth;
        const parentHeight = 460; // 固定高度

        // 销毁旧实例，确保正确初始化
        if (this.charts) {
          this.charts.dispose();
        }

        // 创建新图表实例
        this.charts = echarts.init(chartDom, null, {
          width: parentWidth || "auto",
          height: parentHeight,
          renderer: "canvas",
        });

        var option;
        let data;
        try {
          data = [
            {
              value: responseData.proportion?.wyPercentage?.slice(0, 4) || "0",
              name: "文章数(境外)",
              itemStyle: {
                color: "#2c58e3",
              },
            },
            {
              value: responseData.proportion?.wxPercentage?.slice(0, 4) || "0",
              name: "文章数(境内)",
              itemStyle: {
                color: "#5dbf93",
              },
            },
          ];

          // 检查数据是否有效
          if (data.every((item) => item.value === "0")) {
            console.log("所有饼图数据为0，不渲染图表");
            return;
          }
        } catch (error) {
          console.error("Error preparing pie chart data:", error);
          return; // 如果数据准备出错，不渲染图表
        }

        option = {
          tooltip: {
            trigger: "item",
          },
          title: {
            text: "发布平台",
            left: "center",
            top: 14,
            textStyle: {
              fontSize: 20,
            },
          },
          animation: true,
          animationDuration: 1000,
          animationEasing: "bounceOut",
          animationDelay: function (idx) {
            return idx * 200;
          },
          legend: {
            show: true,
            top: "50",
            data: ["文章数(境外)", "文章数(境内)"],
            formatter: (name) => {
              if (name == "文章数(境内)") {
                return name + ":" + (responseData.proportion?.wxCount || "0");
              } else {
                return name + ":" + (responseData.proportion?.wzCount || "0");
              }
            },
            textStyle: {
              fontSize: 16,
              color: "#676a6c",
            },
            itemHeight: 16,
          },
          series: [
            {
              name: "发布平台",
              type: "pie",
              center: ["50%", "55%"],
              radius: ["35%", "65%"],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: "#fff",
                borderWidth: 2,
              },
              label: {
                show: false,
                position: "center",
              },
              emphasis: {
                focus: "series",
                label: {
                  show: true,
                  fontSize: 20,
                  fontWeight: "bold",
                  formatter: "{b}\n{c}" + "%",
                },
                itemStyle: {
                  // shadowBlur: 10,
                  // shadowOffsetX: 0,
                  // shadowColor: 'rgba(0, 0, 0, 0.5)'
                },
              },
              labelLine: {
                show: false,
              },
              data: data,
            },
          ],
        };

        // 设置图表选项
        this.charts.setOption(option);

        // 强制执行一次重绘，确保尺寸正确
        this.$nextTick(() => {
          if (this.charts) {
            this.charts.resize();

            // 确保动画在渲染后正确执行
            this.resizeCharts();
          }
        });

        // 重写饼图轮动效果，使用更可靠的计数器方式
        let currentIndex = 0;
        const pieItemCount = data.length;

        // 清除旧的定时器，如果存在的话
        if (this.pieRotationTimer) {
          clearInterval(this.pieRotationTimer);
          this.pieRotationTimer = null;
        }

        // 创建新的轮动效果
        this.pieRotationTimer = setInterval(() => {
          if (!this.charts) {
            clearInterval(this.pieRotationTimer);
            this.pieRotationTimer = null;
            return;
          }

          try {
            // 先取消所有高亮
            this.charts.dispatchAction({
              type: "downplay",
              seriesIndex: 0,
            });

            // 然后高亮当前项
            this.charts.dispatchAction({
              type: "highlight",
              seriesIndex: 0,
              dataIndex: currentIndex,
            });

            // 移动到下一项
            currentIndex = (currentIndex + 1) % pieItemCount;
          } catch (e) {
            console.error("Pie rotation animation error:", e);
            clearInterval(this.pieRotationTimer);
            this.pieRotationTimer = null;
          }
        }, 2000);

        // 确保组件销毁时清除定时器
        this.$once("hook:beforeDestroy", () => {
          if (this.pieRotationTimer) {
            clearInterval(this.pieRotationTimer);
            this.pieRotationTimer = null;
          }
        });
      } catch (e) {
        console.error("Error rendering ring chart:", e);
      }
    },
    async getIndexData() {
      await api.indexPageData().then((response) => {
        if (response.code == 200 && response.data) {
          this.indexData = response.data;
          // 只有当数据加载成功后才渲染饼图
          if (document.getElementById("ring")) {
            this.$nextTick(() => {
              this.renderRing(response.data);

              // 确保渲染后调整大小，这对动画效果很重要
              setTimeout(() => {
                this.resizeCharts();
              }, 100);
            });
          }
        } else {
          this.$message({
            message: response.msg || "获取数据失败",
            type: "error",
          });
        }
        // setTimeout(() => {
        //   this.autoScroll();
        // }, 10);
      });
    },
    async indexStatistic() {
      await api.indexStatistic(this.statistic).then((response) => {
        if (
          response.code == 200 &&
          response.data &&
          response.data.resultCount &&
          response.data.resultCount.length > 0
        ) {
          this.statisticObj = response.data;
          // 只有当数据加载成功后才渲染柱状图
          if (document.getElementById("line")) {
            this.$nextTick(() => {
              this.renderLineCharts();

              // 确保渲染后调整大小，这对动画效果很重要
              setTimeout(() => {
                this.resizeCharts();
              }, 100);
            });
          }
        } else {
          this.$message({
            message: response.msg || "获取统计数据失败",
            type: "error",
          });
        }
      });
    },
    thousandth(data) {
      /* 千分符 */
      if (String(data).length < 4) {
        return data;
      } else {
        return data.toString().replace(/\d{1,3}/g, (m) => {
          return m.replace(/(\d{1,3})/g, "$1,");
        });
      }
    },
    /* 跳转新页面 */
    openNewView(item) {
      // if (item.sourceType == 1) {
      //   if (item.shortUrl) {
      //     window.open(item.shortUrl);
      //     return;
      //   }
      //   this.$message({ message: "该文章没有原文链接" });
      // } else if (item.sourceType == 2) {
      //   if (item.originalUrl) {
      //     window.open(item.originalUrl);
      //     return;
      //   }
      //   this.$message({ message: "该文章没有原文链接" });
      // }
      window.open(
        `/expressDetails?id=${item.id}&docId=${item.docId}`,
        "_blank"
      );
    },
    renderLineCharts() {
      // 如果没有有效数据，则不渲染柱状图
      if (
        !this.statisticObj ||
        !this.statisticObj.resultCount ||
        this.statisticObj.resultCount.length === 0
      ) {
        return;
      }

      var chartDom = document.getElementById("line");
      // 如果DOM不存在，直接返回
      if (!chartDom) return;

      try {
        // 设置容器样式
        chartDom.style.height = "100%";
        chartDom.style.width = "100%";

        // 确保容器可见并有合理的尺寸
        const parentWidth = chartDom.parentElement.clientWidth;
        const parentHeight = 300; // 固定高度

        // 销毁旧实例，确保正确初始化
        if (this.lineChart) {
          this.lineChart.dispose();
        }

        // 创建新图表实例
        this.lineChart = echarts.init(chartDom, null, {
          width: parentWidth || "auto",
          height: parentHeight,
          renderer: "canvas",
        });

        // 基础 ECharts 配置
        let option = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
          },
          legend: {
            width: "100%",
            height: "100%",
            right: "center",
            bottom: "20px",
            data: [
              "每日发布数(境内源)",
              "每日发布数(境外源)",
              "每日采集数(境内源)",
              "每日采集数(境外源)",
            ],
          },
          grid: {
            left: "20px",
            right: "20px",
            top: "30px",
            bottom: "50px",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            data: this.statisticObj.resultCount.map((res) => {
              let time = res.date ? res.date : res.hour;
              return time;
            }),
          },
          yAxis: {
            type: "value",
          },
          series: [
            {
              name: "每日发布数(境外源)",
              type: "bar",
              stack: "total",
              barWidth: "40%",
              data: this.statisticObj.resultCount.map((res) => {
                return { value: res.wzCount, total: res.count };
              }),
              label: {
                show: false,
                position: "top",
                formatter: function (params) {
                  return params.data.total;
                },
                fontSize: 12,
              },
              barGap: "0",
              itemStyle: {
                color: "#2c58e3",
              },
            },
            {
              name: "每日发布数(境内源)",
              type: "bar",
              stack: "total",
              barWidth: "40%",
              label: {
                show: false,
                position: "top",
                formatter: function (params) {
                  return params.data.total;
                },
                fontSize: 12,
              },
              data: this.statisticObj.resultCount.map((res) => {
                return { value: res.wxCount, total: res.count };
              }),
              itemStyle: {
                color: "#5DBF93",
              },
            },
            {
              name: "合计(每日发布数)",
              type: "bar",
              stack: "total",
              barWidth: "40%",
              label: {
                show: true,
                position: "top",
                formatter: function (params) {
                  return params.data.total;
                },
                fontSize: 12,
              },
              data: this.statisticObj.resultCount.map((res) => {
                return { value: 0, total: res.count };
              }),
              tooltip: {
                show: false,
              },
            },
            {
              name: "每日采集数(境外源)",
              type: "bar",
              stack: "total2",
              barWidth: "40%",
              data: this.statisticObj.resultCount.map((res) => {
                return { value: res.wzCount2, total: res.count2 };
              }),
              label: {
                show: false,
                position: "top",
                formatter: function (params) {
                  return params.data.total;
                },
                fontSize: 12,
              },
              itemStyle: {
                color: "#5590f6",
              },
            },
            {
              name: "每日采集数(境内源)",
              type: "bar",
              stack: "total2",
              barWidth: "40%",
              label: {
                show: false,
                position: "top",
                formatter: function (params) {
                  return params.data.total;
                },
                fontSize: 12,
              },
              data: this.statisticObj.resultCount.map((res) => {
                return { value: res.wxCount2, total: res.count2 };
              }),
              itemStyle: {
                color: "#ffb400",
              },
            },
            {
              name: "合计(每日采集数)",
              type: "bar",
              stack: "total2",
              barWidth: "40%",
              label: {
                show: true,
                position: "top",
                formatter: function (params) {
                  return params.data.total;
                },
                fontSize: 12,
              },
              data: this.statisticObj.resultCount.map((res) => {
                return { value: 0, total: res.count2 };
              }),
              tooltip: {
                show: false,
              },
            },
          ],
        };

        // 设置图表选项
        this.lineChart.setOption(option);

        // 强制执行一次重绘，确保尺寸正确
        this.$nextTick(() => {
          if (this.lineChart) {
            this.lineChart.resize();

            // 确保动画在渲染后正确执行
            this.resizeCharts();
          }
        });
      } catch (e) {
        console.error("Error rendering line chart:", e);
      }
    },
    // 设置自动滚动
    autoScroll(stop) {
      const table = this.$refs.scroll_Table;
      // 拿到表格中承载数据的div元素
      const divData = table.$refs.bodyWrapper;

      // 拿到元素后，对元素进行定时增加距离顶部距离，实现滚动效果(此配置为每100毫秒移动1像素)
      if (stop) {
        //再通过事件监听，监听到 组件销毁 后，再执行关闭计时器。
        window.clearInterval(this.scrolltimer);
        clearTimeout(this.timer, 1);
      } else {
        this.scrolltimer = window.setInterval(() => {
          // 元素自增距离顶部1像素
          divData.scrollTop += 1;
          // 判断元素是否滚动到底部(可视高度+距离顶部=整个高度)
          if (
            divData.clientHeight + divData.scrollTop ==
            divData.scrollHeight
          ) {
            // 重置table距离顶部距离
            this.autoScroll(true);
            this.timer = setTimeout(() => {
              divData.scrollTop = 0;
              this.autoScroll(false);
            }, 1000);
            // 重置table距离顶部距离。值=(滚动到底部时，距离顶部的大小) - 整个高度/2
            // divData.scrollTop = divData.scrollTop - divData.scrollHeight / 2
          }
        }, 30); // 滚动速度
      }
    },
  },
  watch: {
    statistic(newVal, oldVal) {
      this.indexStatistic();
    },
    // 监听侧边栏折叠状态变化
    isCollapse(val) {
      // 当侧边栏状态变化时，延迟一段时间再重绘图表
      // 这样可以等待过渡动画完成
      setTimeout(() => {
        this.resizeCharts();
      }, 300);
    },
  },
  beforeDestroy() {
    // 停止表格自动滚动
    // this.autoScroll(true);

    // 清除定时器
    if (this.scrolltimer) {
      clearInterval(this.scrolltimer);
    }

    if (this.timer) {
      clearTimeout(this.timer);
    }

    // 清除饼图轮动定时器
    if (this.pieRotationTimer) {
      clearInterval(this.pieRotationTimer);
      this.pieRotationTimer = null;
    }

    // 清除resize防抖定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    }

    // 移除窗口大小变化监听器
    window.removeEventListener("resize", this.handleResize);

    // 释放图表实例
    if (this.charts) {
      try {
        this.charts.dispose();
      } catch (e) {
        console.error("Error disposing ring chart:", e);
      }
      this.charts = null;
    }

    if (this.lineChart) {
      try {
        this.lineChart.dispose();
      } catch (e) {
        console.error("Error disposing line chart:", e);
      }
      this.lineChart = null;
    }
  },
};
</script>

<style scoped lang="scss">
.HotTitle {
  padding: 0 20px;
  height: 20px;
  line-height: 30px;
  font-size: 20px;
  color: #555;
  text-align: center;
  font-weight: 600;
}

.line_Style {
  width: 100%;
  height: 300px;
  min-height: 300px; /* 确保图表有足够高度 */
}

.home {
  padding: 0 20px;
  height: 100%;
  min-height: calc(100vh - 70px);
  display: flex;
  flex-direction: column;

  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }

  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}

.index_top {
  display: flex;
  gap: 20px;
}

.index_card {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  flex: 1;
  height: 180px;
  // background-color: rgb(255, 255, 255);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .title {
    font-size: 20px;
    font-weight: 450;
    text-align: center;
    height: 80px;
    line-height: 80px;
  }

  .info_box_day {
    // margin-top: 20px;
    font-size: 32px;
    font-weight: 600;
    width: 100%;
    text-align: center;
  }

  .info_box {
    height: 100px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;

    p {
      font-size: 14px;
    }

    .line {
      content: "";
      display: inline-block;
      width: 1px;
      height: 60px;
      background: rgb(207, 207, 207);
    }

    span {
      margin-left: 20px;
      font-size: 32px;
      font-weight: 600;
      vertical-align: middle;
    }
  }
}

.dayInfo {
  margin-top: 20px;
  width: 100%;
  height: 200px;
  display: flex;
  gap: 20px;

  div {
    flex: 1;
    background-color: rgb(255, 255, 255);
  }
}

.bottom_box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-top: 20px;
  min-height: 460px; /* 确保有最小高度 */
  height: 460px; /* 指定固定高度 */
  align-items: stretch; /* 确保子元素拉伸以填充父元素高度 */

  @media (max-width: 992px) {
    flex-direction: column;
    height: auto; /* 在小屏幕上高度自适应 */
  }

  #ring {
    flex: 4;
    min-width: 300px;
    min-height: 460px; /* 确保最小高度 */
    height: 460px; /* 固定高度 */
    background-color: rgb(255, 255, 255);
    box-shadow: -7px 8px 18px 11px #e7e5e5;
    position: relative; /* 确保定位上下文 */
    overflow: hidden; /* 防止内容溢出 */
    display: flex;
    flex-direction: column;

    @media (max-width: 992px) {
      width: 100%;
      margin-bottom: 20px;
      min-height: 460px;
    }
  }

  .table {
    flex: 6;
    min-width: 400px;
    min-height: 460px; /* 确保最小高度 */
    height: 460px; /* 固定高度 */
    background-color: rgb(255, 255, 255);
    box-shadow: 7px 8px 18px 11px #e7e5e5;
    position: relative; /* 确保定位上下文 */
    overflow: hidden; /* 防止内容溢出 */
    display: flex;
    flex-direction: column;

    @media (max-width: 992px) {
      width: 100%;
      min-height: 460px;
    }
  }
}

.echarts_top {
  width: 100%;
  margin: 15px 0;
  box-shadow: 0px 0px 13px 7px #e7e5e5;
  flex-shrink: 0;

  .line_Style {
    width: 100%;
    height: 300px;
    min-height: 250px; /* 确保图表有最小高度 */
  }

  .topBox {
    width: 100%;
    display: flex;
    font-size: 16px;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;

    .a {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      right: 90px;

      p {
        text-align: center;
        font-size: 20px;
        // margin-right: 30px;
        color: #555;
      }
    }

    .b {
      float: right;
    }
  }
}
</style>
