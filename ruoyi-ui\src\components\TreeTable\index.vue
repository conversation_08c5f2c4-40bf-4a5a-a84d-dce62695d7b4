<template>
  <div
    class="tree-table-container"
    v-loading="loading"
    element-loading-text="数据加载中"
  >
    <!-- 搜索框 -->
    <div class="search-container">
      <el-input
        placeholder="输入关键字进行过滤"
        v-model="filterText"
        clearable
        class="input_Fixed"
        style="margin-bottom: 10px"
      >
        <i slot="prefix" class="el-input__icon el-icon-search"></i>
      </el-input>
    </div>

    <!-- 操作按钮行 -->
    <div class="action-container">
      <!-- 数据源分类筛选 -->
      <div class="filter-container">
        <el-select
          v-model="selectedClassify"
          placeholder="数据源分类"
          clearable
          size="mini"
          style="width: 120px"
          @change="handleClassifyChange"
          class="classify-select"
        >
          <el-option
            v-for="dict in dict.type.think_tank_class"
            :label="dict.label"
            :key="'think_tank_class' + dict.value"
            :value="dict.value"
          />
        </el-select>

        <!-- 按国家筛选 - 仅在MonitorUse页面且id=1时显示 -->
        <!-- <el-select
          v-if="showCountryFilter"
          v-model="selectedCountry"
          placeholder="国家"
          clearable
          size="mini"
          style="width: 100px; margin-left: 5px"
          @change="handleCountryChange"
          class="country-select"
        >
          <el-option
            v-for="(dict, index) in dict.type.country"
            :label="dict.label"
            :key="'country' + dict.value"
            :value="dict.value"
          />
        </el-select> -->
      </div>

      <!-- 全选、取消选中和重置按钮 -->
      <div class="button-container">
        <el-tooltip
          class="item"
          effect="dark"
          content="全选当前页"
          placement="top"
        >
          <el-button
            icon="el-icon-check"
            @click="handleSelectAll"
            type="text"
            style="color: #409eff; padding: 0"
          >
            全选
          </el-button>
        </el-tooltip>
        <el-tooltip
          class="item"
          effect="dark"
          content="取消所有选中"
          placement="top"
        >
          <el-button
            icon="el-icon-close"
            @click="handleClearAll"
            type="text"
            style="color: #f56c6c; padding: 0"
          >
            取消选中
          </el-button>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="重置" placement="top">
          <el-button
            icon="el-icon-refresh"
            @click="handleReset"
            type="text"
            style="color: #666; padding: 0"
          >
            重置
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 已勾选数据源显示行 -->
    <div
      class="selected-sources-info"
      v-if="selectedSources && selectedSources.length > 0"
    >
      <span class="selected-text">
        {{ getSelectedSourcesText }}
      </span>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        style="font-size: 16px"
        :height="tableHeight"
        :show-header="false"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
        ref="table"
        :row-key="rowKey"
        size="small"
        border
      >
        <!-- 序号列 -->
        <el-table-column type="index" label="序号" align="center">
          <template slot-scope="scope">
            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>

        <!-- 复选框列 -->
        <el-table-column
          type="selection"
          width="30"
          align="center"
        ></el-table-column>

        <!-- 名称+数量+链接列 -->
        <el-table-column label="名称">
          <template slot-scope="scope">
            <div class="name-cell">
              <span class="name-text">{{ scope.row.label }}</span>
              <div
                class="country-text"
                v-if="scope.row.country && scope.row.country !== '0'"
              >
                <div>[</div>
                <dict-tag
                  :options="dict.type.country"
                  :value="scope.row.country"
                  class="country-tag"
                />
                <div>]</div>
              </div>
              <span class="count-text" v-if="scope.row.count !== undefined">
                ({{ scope.row.count }})
              </span>
              <el-tooltip
                v-if="scope.row.url"
                content="打开数据源链接"
                placement="top-start"
                effect="light"
              >
                <i
                  class="el-icon-connection link-icon"
                  @click.stop="openUrl(scope.row.url)"
                ></i>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="treeTable-pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :pager-count="5"
        :page-sizes="[50, 100, 150, 200]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next"
        :total="total"
        small
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "SimpleTreeTable",
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    rowKey: {
      type: String,
      default: "id",
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 20,
    },
    total: {
      type: Number,
      default: 0,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    // 已勾选的数据源列表，用于显示勾选信息
    selectedSources: {
      type: Array,
      default: () => [],
    },
  },
  dicts: ["country", "think_tank_class"],
  data() {
    return {
      filterText: "",
      selectedData: [], // 简单的选中数据数组
      isPagingOperation: false, // 标记是否是分页操作
      searchDebounceTimer: null, // 搜索防抖定时器
      selectedClassify: null, // 选中的数据源分类
      selectedCountry: null, // 选中的国家
      dynamicTableHeight: null, // 动态计算的表格高度
    };
  },
  computed: {
    tableData() {
      // 直接返回传入的数据，过滤由后端接口处理
      return this.data;
    },
    // 判断是否显示国家筛选下拉框
    showCountryFilter() {
      // 当当前页面是MonitorUse并且地址上的id参数等于1时才显示
      return (
        this.$route.path == "/MonitorUse" &&
        !this.$route.query.menuType &&
        this.$route.query.id === "1"
      );
    },
    // 获取已勾选数据源的显示文本
    getSelectedSourcesText() {
      if (!this.selectedSources || this.selectedSources.length === 0) {
        return "";
      }

      const totalCount = this.selectedSources.length;

      if (totalCount <= 3) {
        // 小于等于3个时，显示所有名称，不显示"等X个数据源"
        const names = this.selectedSources.map(
          (item) => item.label || item.name
        );
        return `当前已勾选${names.join("、")}`;
      } else {
        // 超过3个时，显示前3个名称加上剩余数量
        const names = this.selectedSources
          .slice(0, 3)
          .map((item) => item.label || item.name);
        const remainingCount = totalCount - 3;
        return `当前已勾选${names.join("、")}等${remainingCount}个数据源`;
      }
    },
    // 计算表格高度
    tableHeight() {
      return this.dynamicTableHeight || "auto";
    },
  },
  watch: {
    data: {
      handler() {
        // 数据变化时，只滚动到顶部，不清空选择
        this.$nextTick(() => {
          // 滚动到顶部
          this.scrollToTop();
        });
        // 重新计算表格高度
        this.updateTableHeight();
      },
      immediate: true,
    },
    currentPage: {
      handler() {
        // 分页变化时滚动到顶部
        this.$nextTick(() => {
          this.scrollToTop();
        });
      },
    },
    filterText: {
      handler(newVal) {
        // 清除之前的防抖定时器
        if (this.searchDebounceTimer) {
          clearTimeout(this.searchDebounceTimer);
        }

        // 设置防抖，500ms后执行搜索
        this.searchDebounceTimer = setTimeout(() => {
          this.handleFilterSearch(newVal);
        }, 500);
      },
    },
    // 监听已选择数据源变化，重新计算高度
    selectedSources: {
      handler() {
        this.updateTableHeight();
      },
      deep: true,
    },
  },
  methods: {
    // 处理行点击 - 重写版本（点击行是单选，直接替换保存勾选数据）
    handleRowClick(row, column) {
      // 如果点击的是复选框列，不处理
      if (column && column.type === "selection") {
        return;
      }

      console.log("行点击（单选）:", row.label);

      // 检查当前行是否已选中
      const isSelected = this.selectedData.some(
        (item) => item[this.rowKey] === row[this.rowKey]
      );

      if (isSelected) {
        // 如果已选中，则取消选中
        this.selectedData = this.selectedData.filter(
          (item) => item[this.rowKey] !== row[this.rowKey]
        );
        // 直接操作表格取消选中
        this.$refs.table.toggleRowSelection(row, false);
      } else {
        // 如果未选中，则清空其他选择，只选中当前行（单选）
        this.selectedData = [{ ...row }];
        // 直接操作表格：先清空，再选中当前行
        this.$refs.table.clearSelection();
        this.$refs.table.toggleRowSelection(row, true);
      }

      console.log(
        "行点击后选中数据:",
        this.selectedData.map((item) => item.label)
      );

      // 触发父组件事件（行点击是单选，直接替换）
      this.$emit("selection-change", this.selectedData, "row-click");
    },

    // 处理复选框选择变化（点击勾选框是多选，往里面push或删除）
    handleSelectionChange(selection) {
      this.selectedData = selection.map((item) => ({ ...item }));

      // 如果是分页操作导致的选择变化，不触发父组件事件
      if (!this.isPagingOperation) {
        console.log("复选框变化触发父组件事件（这会更新保存的勾选数据）");
        this.$emit("selection-change", this.selectedData, "checkbox-change");
      }
    },

    // 处理重置（重置才更新保存的勾选数据）
    handleReset() {
      // 清空搜索关键字（会触发 watch 调用后端接口）
      this.filterText = "";
      this.selectedClassify = null; // 重置数据源分类选择
      this.selectedCountry = null; // 重置国家筛选选择
      this.selectedData = [];
      if (this.$refs.table) {
        this.$refs.table.clearSelection();
      }
      console.log("重置，触发父组件事件（这会更新保存的勾选数据）");
      this.$emit("reset");
      // 滚动到顶部
      this.scrollToTop();
    },

    // 处理全选当前页（全选是把当页所有数据都push进去）
    handleSelectAll() {
      if (this.tableData && this.tableData.length > 0) {
        // 选中当前页所有数据
        this.selectedData = this.tableData.map((item) => ({ ...item }));

        // 更新表格选中状态
        if (this.$refs.table) {
          this.$refs.table.clearSelection();
          this.tableData.forEach((row) => {
            this.$refs.table.toggleRowSelection(row, true);
          });
        }

        console.log("全选当前页，触发父组件事件（这会更新保存的勾选数据）");
        // 触发父组件事件（全选需要追加数据）
        this.$emit("selection-change", this.selectedData, "select-all");
      }
    },

    // 处理取消所有选中（取消选中是当页所有数据都从保存勾选数据中删除）
    handleClearAll() {
      // 清空选中数据
      this.selectedData = [];

      // 清空表格选中状态
      if (this.$refs.table) {
        this.$refs.table.clearSelection();
      }

      console.log("取消所有选中，触发父组件事件（这会更新保存的勾选数据）");
      // 触发父组件事件（取消选中是直接替换为空）
      this.$emit("selection-change", this.selectedData, "clear-all");
    },

    // 处理数据源分类变化（不更新保存的勾选数据）
    handleClassifyChange(value) {
      this.isPagingOperation = true;
      // 清空选中数据（仅清空界面显示，不更新保存的勾选数据）
      this.selectedData = [];
      if (this.$refs.table) {
        this.$refs.table.clearSelection();
      }

      console.log("数据源分类变化，不更新保存的勾选数据");
      // 触发父组件事件，只更新左侧列表，不更新右侧列表
      this.$emit("classify-change", value);
      // 滚动到顶部
      this.scrollToTop();
      this.$nextTick(() => {
        setTimeout(() => {
          this.isPagingOperation = false;
        }, 100);
      });
    },

    // 处理国家筛选变化（不更新保存的勾选数据）
    handleCountryChange(value) {
      this.isPagingOperation = true;
      // 清空选中数据（仅清空界面显示，不更新保存的勾选数据）
      this.selectedData = [];
      if (this.$refs.table) {
        this.$refs.table.clearSelection();
      }

      console.log("国家筛选变化，不更新保存的勾选数据");
      // 触发父组件事件，只更新左侧列表，不更新右侧列表
      this.$emit("country-change", value);
      this.$nextTick(() => {
        setTimeout(() => {
          this.isPagingOperation = false;
        }, 100);
      });
    },

    // 处理分页大小变化（不更新保存的勾选数据）
    handleSizeChange(size) {
      this.isPagingOperation = true;
      this.selectedData = [];
      if (this.$refs.table) {
        this.$refs.table.clearSelection();
      }
      console.log("分页大小变化，不更新保存的勾选数据");
      this.$emit("size-change", size);
      // 滚动到顶部
      this.scrollToTop();
      // 延迟重置标记
      this.$nextTick(() => {
        setTimeout(() => {
          this.isPagingOperation = false;
        }, 100);
      });
    },

    // 处理当前页变化（不更新保存的勾选数据）
    handleCurrentChange(page) {
      this.isPagingOperation = true;
      this.selectedData = [];
      if (this.$refs.table) {
        this.$refs.table.clearSelection();
      }
      console.log("当前页变化，不更新保存的勾选数据");
      this.$emit("current-change", page);
      // 滚动到顶部
      this.scrollToTop();
      // 延迟重置标记
      this.$nextTick(() => {
        setTimeout(() => {
          this.isPagingOperation = false;
        }, 100);
      });
    },

    // 打开链接
    openUrl(url) {
      window.open(url, "_blank");
    },

    // 滚动到顶部
    scrollToTop() {
      this.$nextTick(() => {
        if (this.$refs.table && this.$refs.table.bodyWrapper) {
          this.$refs.table.bodyWrapper.scrollTop = 0;
        }
        // 如果表格的 bodyWrapper 不存在，尝试其他方式
        else if (this.$refs.table && this.$refs.table.$el) {
          const tableBody = this.$refs.table.$el.querySelector(
            ".el-table__body-wrapper"
          );
          if (tableBody) {
            tableBody.scrollTop = 0;
          }
        }
      });
    },

    // 处理过滤搜索（不更新保存的勾选数据）
    handleFilterSearch(keyword) {
      // 触发父组件的过滤搜索事件，传递 filterwords 参数
      this.isPagingOperation = true;
      this.selectedData = [];
      if (this.$refs.table) {
        this.$refs.table.clearSelection();
      }
      console.log("关键字过滤，不更新保存的勾选数据");
      this.$emit("filter-search", keyword);
      // 滚动到顶部
      this.scrollToTop();
      this.$nextTick(() => {
        setTimeout(() => {
          this.isPagingOperation = false;
        }, 100);
      });
    },

    // 恢复选中状态（供父组件调用）
    restoreSelection(itemsToSelect) {
      if (!itemsToSelect || itemsToSelect.length === 0) {
        return;
      }

      // 更新内部选中数据
      this.selectedData = itemsToSelect.map((item) => ({ ...item }));

      // 等待表格渲染完成后设置选中状态
      this.$nextTick(() => {
        if (this.$refs.table) {
          // 先清空选择
          this.$refs.table.clearSelection();

          // 逐个设置选中状态
          itemsToSelect.forEach((item) => {
            // 在当前表格数据中查找对应的行
            const tableRow = this.tableData.find(
              (row) => row[this.rowKey] === item[this.rowKey]
            );
            if (tableRow) {
              this.$refs.table.toggleRowSelection(tableRow, true);
            }
          });
        }
      });
    },

    // 静默恢复选中状态（不触发 selection-change 事件）
    restoreSelectionSilently(itemsToSelect) {
      if (!itemsToSelect || itemsToSelect.length === 0) {
        return;
      }

      // 设置标记，避免触发 selection-change 事件
      this.isPagingOperation = true;

      // 更新内部选中数据
      this.selectedData = itemsToSelect.map((item) => ({ ...item }));

      // 等待表格渲染完成后设置选中状态
      this.$nextTick(() => {
        if (this.$refs.table) {
          // 先清空选择
          this.$refs.table.clearSelection();

          // 逐个设置选中状态
          itemsToSelect.forEach((item) => {
            // 在当前表格数据中查找对应的行
            const tableRow = this.tableData.find(
              (row) => row[this.rowKey] === item[this.rowKey]
            );
            if (tableRow) {
              this.$refs.table.toggleRowSelection(tableRow, true);
            }
          });
        }

        // 延迟重置标记
        setTimeout(() => {
          this.isPagingOperation = false;
        }, 100);
      });
    },

    // 计算并更新表格高度
    updateTableHeight() {
      this.$nextTick(() => {
        const tableContainer = this.$el.querySelector(".table-container");
        if (tableContainer) {
          const containerHeight = tableContainer.clientHeight;
          this.dynamicTableHeight =
            containerHeight > 0 ? containerHeight : "auto";
        }
      });
    },
  },
  mounted() {
    // 初始化表格高度
    this.updateTableHeight();

    // 监听窗口大小变化
    window.addEventListener("resize", this.updateTableHeight);
  },
  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener("resize", this.updateTableHeight);
  },
};
</script>

<style lang="scss" scoped>
.tree-table-container {
  height: calc(100vh - 58px);
  display: flex;
  flex-direction: column;
}

.action-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.filter-container {
  flex: 0 0 auto;
  padding-bottom: 0;
}

.button-container {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

.selected-sources-info {
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 4px;
  font-size: 14px;
}

.selected-text {
  color: #1976d2;
  font-weight: 500;
}

.table-container {
  flex: 1;
  overflow: hidden;
}

.name-cell {
  display: flex;
  align-items: center;
  padding-left: 10px;
}

.name-text {
  white-space: nowrap;
}

::v-deep .country-text {
  display: flex;
  align-items: center;
  margin-left: 4px;

  .country-tag {
    span {
      white-space: nowrap;
    }
  }
}

.count-text {
  font-size: 16px;
  font-weight: 600;
  margin-left: 4px;
  white-space: nowrap;
}

.treeTable-pagination {
  flex: 0 0 auto;
  text-align: center;
  padding: 10px 0;
}

.link-icon {
  color: #409eff;
  cursor: pointer;
  margin-left: 4px;
  font-size: 18px;
}

.link-icon:hover {
  color: #66b1ff;
}

::v-deep .el-pagination__sizes {
  margin-top: -3px;
}

::v-deep .el-table__cell .cell {
  padding: 0 !important;
  margin: 0 !important;
  padding-left: 0 !important;
}

::v-deep .classify-select {
  .el-input__inner {
    font-size: 14px;
  }
}

::v-deep .country-select {
  .el-input__inner {
    font-size: 14px;
  }
}
</style>
