import request from "@/utils/request";

// 查询数据源分类列表
export function listClassify(query) {
  return request({
    url: "/article/classify/list",
    method: "get",
    params: query,
  });
}

// 查询数据源分类详细
export function getClassify(id) {
  return request({
    url: "/article/classify/" + id,
    method: "get",
  });
}

// 新增数据源分类
export function addClassify(data) {
  return request({
    url: "/article/classify",
    method: "post",
    data: data,
  });
}

// 修改数据源分类
export function updateClassify(data) {
  return request({
    url: "/article/classify/edit",
    method: "post",
    data: data,
  });
}

// 删除数据源分类
export function delClassify(id) {
  return request({
    url: "/article/classify/remove",
    method: "post",
    data: id,
  });
}

// 查询数据源分类列表
export function getListClassify(query) {
  return request({
    url: "/article/generic/classify/list",
    method: "get",
    params: query,
  });
}