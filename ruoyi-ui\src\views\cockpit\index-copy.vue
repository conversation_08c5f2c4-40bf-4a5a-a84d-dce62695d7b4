<template>
  <div class="default_Main" id="full_screen">
    <span class="timeStrle">{{ timeStr }}</span>
    <div class="title_Style">
      <div class="title">开源科技情报地平线数据分析系统</div>
    </div>
    <div class="mainCokpit">
      <div class="first-child">
        <div class="backround">
          <div class="moduleTitle_parent">
            <i class="icon-shengyin"></i>
            <p class="moduleTitle">科情趋势</p>
          </div>
          <div id="trend" class="trend_Box"></div>
        </div>
        <div class="backround">
          <div style="height: 310px;">
            <div class="moduleTitle_parent">
              <i class="icon-shengyin"></i>
              <p class="moduleTitle">成果报告</p>
            </div>
            <div class="icon_parent">
              <div class="icon_Style">
                <img src="../../assets/images/rotate.svg" class="rotate-center" />
                <img src="../../assets/images/building.svg" />
              </div>
              <div class="icon_Style">
                <img src="../../assets/images/rotate.svg" class="rotate-center" />
                <img src="../../assets/images/building.svg" />
              </div>
              <div class="icon_Style">
                <img src="../../assets/images/rotate.svg" class="rotate-center" />
                <img src="../../assets/images/building.svg" />
              </div>
            </div>
            <div class="icon_parent_text">
              <div class="icon_Style">
                <span>
                  {{Cooperation.reportCount[0]
                  .dayCount}}
                </span>
              </div>
              <div class="icon_Style">
                <span>
                  {{Cooperation.reportCount[0]
                  .weekCount}}
                </span>
              </div>
              <div class="icon_Style">
                <span>
                  {{Cooperation.reportCount[0]
                  .monthCount}}
                </span>
              </div>
            </div>
            <div class="icon_parent_text">
              <div class="icon_Style">
                <p>日报</p>
              </div>
              <div class="icon_Style">
                <p>周报</p>
              </div>
              <div class="icon_Style">
                <p>月报</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="nth-child">
        <div class="topNumber">
          <div class="total">
            <p>总数</p>
            <span class="number">
              <div
                v-for="(data, index) in toalScoll"
                :key="index"
                class="real-time-num"
                :style="{ width: !isNaN(data) ? '26px' : '15px' }"
              >
                <div v-if="isNaN(data)" style="font-size: 45px;width: 15px;">,</div>
                <div
                  class="real-time-num-item"
                  :style="{ transform: `translateY(-${data * 40}px)` }"
                >
                  <template v-if="!isNaN(data)">
                    <div v-for="(value, key) in scollList" :key="key">{{ value }}</div>
                  </template>
                </div>
              </div>
            </span>
          </div>
          <div class="WeChat">
            <p>微信</p>
            <span class="number">
              <div
                v-for="(data, index) in WxScoll"
                :key="index"
                class="real-time-num"
                :style="{ width: !isNaN(data) ? '26px' : '15px' }"
              >
                <div v-if="isNaN(data)" style="font-size: 45px;width: 15px;">,</div>
                <div
                  class="real-time-num-item"
                  :style="{ transform: `translateY(-${data * 40}px)` }"
                >
                  <template v-if="!isNaN(data)">
                    <div v-for="(value, key) in scollList" :key="key">{{ value }}</div>
                  </template>
                </div>
              </div>
            </span>
          </div>
          <div class="Web">
            <p>网站</p>
            <span class="number">
              <div
                v-for="(data, index) in WyScoll"
                :key="index"
                class="real-time-num"
                :style="{ width: !isNaN(data) ? '26px' : '15px' }"
              >
                <div v-if="isNaN(data)" style="font-size: 45px;width: 15px;">,</div>
                <div
                  class="real-time-num-item"
                  :style="{ transform: `translateY(-${data * 40}px)` }"
                >
                  <template v-if="!isNaN(data)">
                    <div v-for="(value, key) in scollList" :key="key">{{ value }}</div>
                  </template>
                </div>
              </div>
            </span>
          </div>
        </div>
        <div class="cpitBack">
          <img class="top" src="../../assets/images/u2.svg" />
          <img class="bottom" src="../../assets/images/cpitBack.png" alt />
        </div>
      </div>
      <div class="last-child">
        <div class="backround">
          <div class="moduleTitle_parent">
            <i class="icon-shengyin"></i>
            <p class="moduleTitle">行业统计</p>
          </div>
          <div id="total" class="total_Box"></div>
        </div>
        <div class="backround">
          <div class="moduleTitle_parent">
            <i class="icon-shengyin"></i>
            <p class="moduleTitle">领域统计</p>
          </div>
          <div id="area" class="total_Box"></div>
        </div>
      </div>
    </div>
    <div class="bottom_parent">
      <div class="first_child">
        <div class="moduleTitle_parent">
          <i class="icon-shengyin"></i>
          <p class="moduleTitle">行业趋势</p>
        </div>
        <div id="industry" class="industry_trend"></div>
      </div>
      <div class="last_child">
        <div class="moduleTitle_parent">
          <i class="icon-shengyin"></i>
          <p class="moduleTitle">热门文章</p>
        </div>
        <div class="table">
          <div class="header">
            <div>文章标题</div>
            <div>发布时间</div>
          </div>
          <div class="table_main">
            <div class="scoll-Table">
              <div
                class="table_col"
                v-for="(item, index) in Cooperation.hotListVoList
"
                :key="index"
              >
                <div class="first-child">
                  <span class="spot" :style="{ backgroundColor: colorList[index] }"></span>
                  <span class="textOverflow" style="width:550px">{{ item.cnTitle}}</span>
                </div>
                <div class="last-child">{{item.publishTime}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import img from '@/assets/images/title.png'
import * as echarts from 'echarts'
import api from '@/api/infoEscalation/index'
export default {
  data() {
    return {
      url: img,
      timeStr: null,
      timer: null,
      WeChatNum: '',
      WebNum: '',
      totalNum: '',
      toalScoll: [],
      WxScoll: [],
      WyScoll: [],
      scollList: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      colorList: [],
      myChart: null,
      myChart1: null,
      myChart2: null,
      myChart3: null,
      Cooperation: {
        reportCount: [{}]
      } /* 产融数据 */
    }
  },
  mounted() {},
  watch: {
    totalNum: function (newVal, oldVal) {
      // this.totalNum = this.totalNum.split('')
    }
  },
  created() {
    this.getCooperation()
    this.timer = setInterval(() => {
      this.timeHandler()
      this.totalNum = Number(this.totalNum) + 1
      this.WeChatNum = Number(this.WeChatNum) + 1
      this.WebNum = Number(this.WebNum) + 1
      this.toalScoll = String(this.totalNum.toLocaleString()).split('')
      this.WxScoll = String(this.WeChatNum.toLocaleString()).split('')
      this.WyScoll = String(this.WebNum.toLocaleString()).split('')
    }, 2000)
    this.toalScoll = this.totalNum.split('')
    this.WxScoll = this.WeChatNum.split('')
    this.WyScoll = this.WebNum.split('')
    this.colorCreated()
  },
  computed: {},
  methods: {
    /* 随机颜色生成 */
    colorCreated() {
      let color = '#',
        lettes = '0123456789ABCDEF'
      for (let i = 0; i < 10; i++) {
        for (let i = 0; i < 6; i++) {
          color += lettes[Math.floor(Math.random() * 16)]
        }
        this.colorList.push(color)
        color = '#'
      }
    },
    /* 处理时间 */
    timeHandler() {
      let time = new Date()
      let day = ['日', '一', '二', '三', '四', '五', '六']
      this.timeStr = time.getFullYear() + '年' + (time.getMonth() + 1) + '月' + time.getDate() + '日' + '  ' + '星期' + day[time.getDay()] + '  ' + time.getHours() + ':' + time.getMinutes() + ':' + time.getSeconds()
    },
    renderTrend() {
      var chartDom = document.getElementById('trend')
      this.myChart = echarts.init(chartDom)
      let xAixs = [],
        data = []
      this.Cooperation.weekMonitoring.forEach(item => {
        xAixs.push(item.toDay)
        data.push(item.count)
      })
      var option
      option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          icon: 'rect',
          show: false,
          itemWidth: 14,
          itemHeight: 5,
          itemGap: 13,
          data: [],
          right: '4%',
          textStyle: {
            fontSize: 12,
            color: '#F1F1F3'
          }
        },
        grid: {
          top: '4%',
          left: '1%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLine: {
              show: false,
              lineStyle: {
                color: '#57617B'
              }
            },
            data: xAixs
          },
          {
            axisPointer: {
              show: false
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#57617B'
              }
            },
            axisTick: {
              show: false
            },

            position: 'bottom',
            offset: 20
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '',
            axisTick: {
              show: false
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#57617B'
              }
            },

            axisLabel: {
              margin: 10,
              fontSize: 14
            },
            splitLine: {
              lineStyle: {
                color: '#57617B',
                type: 'dashed'
              }
            }
          }
        ],
        series: [
          {
            name: '科情',
            type: 'line',
            smooth: true,
            showSymbol: false,
            lineStyle: {
              width: 0,
              color: 'rgba(10,20,30,40)'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(128, 255, 165,0.9)'
                },
                {
                  offset: 1,
                  color: 'rgba(1, 191, 236,0.2)'
                }
              ]),
              shadowColor: 'rgba(0, 0, 0, 0.1)',
              shadowBlur: 10
            },
            itemStyle: {
              color: 'rgb(137,189,27)',
              borderColor: 'rgba(137,189,2,0.27)',
              borderWidth: 12
            },
            data: data
          }
        ]
      }

      option && this.myChart.setOption(option)
      this.renderTotal()
    },
    /* 行业统计 */
    renderTotal() {
      let total = document.getElementById('total')
      this.myChart1 = echarts.init(total)
      let xAixs = [],
        data = []
      try {
        this.Cooperation.industryCount.forEach(item => {
          xAixs.push(item.industryName)
          data.push(item.count)
        })
      } catch (error) {
        this.$message.info('数据获取中,请稍候')
      }
      let options = {
        tooltip: {
          show: true,
          trigger: 'item'
        },
        grid: {
          top: 10,
          left: '11%',
          right: '8%',
          bottom: '12%'
        },
        xAxis: {
          type: 'category',
          data: xAixs,
          splitLine: {
            show: false,
            lineStyle: {
              color: 'pink',
              type: 'dashed'
            }
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 4,
          splitLine: {
            show: true,
            lineStyle: {
              color: '#b8b9bbba',
              type: 'dashed',
              dashOffset: 4
            }
          }
        },
        series: [
          {
            data: data,
            type: 'bar',
            barWidth: '22%',
            label: {
              show: true,
              position: 'top',
              color: '#fff'
            },
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#71aeef'
                },
                {
                  offset: 1,
                  color: '#0080d6d7'
                }
              ]),
              borderRadius: 12
            }
          }
        ]
      }
      options && this.myChart1.setOption(options)
      this.renderArea()
    },
    /* 领域统计 */
    renderArea() {
      let total = document.getElementById('area')
      this.myChart2 = echarts.init(total)
      let xAixs = [],
        data = []
      try {
        this.Cooperation.domainCount.forEach(item => {
          xAixs.push(item.domainName)
          data.push(item.count)
        })
      } catch (error) {
        this.$message.info('数据获取中,请稍候')
      }
      let options = {
        tooltip: {
          show: true,
          trigger: 'item'
        },
        grid: {
          top: 10,
          left: '11%',
          right: '8%',
          bottom: '20%'
        },
        xAxis: {
          name: '',
          type: 'category',
          data: xAixs,
          axisLabel: {
            rotate: 45,
            color: '#fff'
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 4,
          splitLine: {
            show: true,
            lineStyle: {
              color: '#b8b9bbba',
              type: 'dashed',
              dashOffset: 4
            }
          }
        },
        series: [
          {
            data: data,
            type: 'bar',
            barWidth: '22%',
            label: {
              show: true,
              position: 'top',
              color: '#fff'
            },
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#71aeef'
                },
                {
                  offset: 1,
                  color: '#0080d6d7'
                }
              ]),
              borderRadius: 12
            }
          }
        ]
      }
      options && this.myChart2.setOption(options)
      this.renderIndustry()
    },
    /* 行业趋势 */
    renderIndustry() {
      let total = document.getElementById('industry')
      this.myChart3 = echarts.init(total)
      let xAixs = [],
        name = [],
        data = {}
      let industry = this.Cooperation.industryTrends
      let colorList = ['#12c2e9', '', '#c471ed', '#f7797d', '#b92b27', '#1565C0', '#4286f4']
      /* 处理数据 */
      Object.keys(industry).forEach(item => {
        xAixs.push(item)
        industry[item].forEach(key => {
          name.push(key.industryName)
          if (!data[key.industryName]) {
            data[key.industryName] = []
          }
          data[key.industryName].push(key.count)
        })
      })
      name = Array.from(new Set(name))
      let series = []
      /* 循环生成series */
      name.forEach((item, index) => {
        series.push({
          name: item,
          data: data[item],
          type: 'bar',
          barWidth: '8%',
          label: {
            show: false,
            position: 'top',
            color: '#fff'
          },
          itemStyle: {
            color: colorList[index],
            borderRadius: 0
          }
        })
      })
      let options = {
        tooltip: {
          show: true,
          trigger: 'axis'
        },
        grid: {
          top: 10,
          left: '5%',
          right: '5%',
          bottom: '10%'
        },
        xAxis: {
          name: '',
          type: 'category',
          data: xAixs,
          axisLine: {
            lineStyle: {
              color: 'white'
            }
          },
          axisLabel: {
            textStyle: {
              fontFamily: 'Microsoft YaHei'
            }
          }
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              color: '#b8b9bbba',
              type: 'solid',
              dashOffset: 4
            }
          }
        },
        series: series
      }
      options && this.myChart3.setOption(options)
      /* 动画轮播 */
      var count = 0
      var timeTicket = null
      var dataLength
      try {
        dataLength = options.series[0].data.length
      } catch (error) {}

      timeTicket && clearInterval(timeTicket)
      timeTicket = setInterval(() => {
        this.myChart3.dispatchAction({
          type: 'downplay',
          seriesIndex: 0
        })
        this.myChart3.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: count % dataLength
        })
        this.myChart3.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: count % dataLength
        })
        count++
      }, 2000)
    },
    /* 获取产融合作数据 */
    getCooperation() {
      api.productCooperation().then(res => {
        if (res.code === 200) {
          this.Cooperation = res.data
          /* 滚动数据 */
          this.WeChatNum = res.data.platformCount.wxTotal
          this.WebNum = res.data.platformCount.wyTotal
          this.totalNum = res.data.platformCount.totalCount
          this.renderTrend()
          window.onresize = params => {
            this.myChart.resize()
            this.myChart1.resize()
            this.myChart2.resize()
            this.myChart3.resize()
          }
        }
      })
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
  }
}
</script>

<style lang="scss" scoped>
.textOverflow {
  width: 350px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/* 斑点 */
.spot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 15px;
  margin-right: 10px;
  position: relative;
  top: 0px;
  // background-color: #5b10be;
}

.backround {
  background-color: #0808085e;
  margin-bottom: 10px;
}

/* 滚动表格 */
.table {
  width: 95%;
  margin: 0 auto;
  height: calc(400px - 57px);

  .header {
    width: 100%;
    background-color: #1162db;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    display: flex;
    color: #fff;
    z-index: 3;

    :first-child {
      width: 70%;
    }

    :last-child {
      width: 30%;
    }
  }

  .table_main {
    width: 100%;
    height: calc(400px - 117px);
    overflow: hidden;
    z-index: 0;

    .table_col {
      width: 100%;
      height: 40px;
      line-height: 40px;
      border-bottom: solid 1px #172652;
      background-color: #121624;
      display: flex;
      color: #ffff;
      padding-left: 10px;
      font-size: 14px;

      .first-child {
        width: 70%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }

      .last-child {
        width: 30%;
      }
    }
  }
}

.bottom_parent {
  width: 100%;
  height: 400px;
  display: flex;
  padding: 0 15px;
  margin-top: -40px;
  gap: 20px;

  .last_child {
    width: 45%;
    height: 100%;
    background-color: #0808085e;
  }

  .first_child {
    width: 55%;
    height: 100%;
    background-color: #0808085e;

    .industry_trend {
      width: 100%;
      height: calc(400px - 57px);
    }
  }
}

/* ----------------------------------------------
 * Generated by Animista on 2023-9-12 10:42:31
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation rotate-center  旋转
 * ----------------------------------------
 */
@-webkit-keyframes rotate-center {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes rotate-center {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.rotate-center {
  -webkit-animation: rotate-center 1.5s infinite;
  animation: rotate-center 1.5s infinite;
  animation-delay: 0ms;
  animation-timing-function: linear;
}

/**
 * ----------------------------------------
 * animation rotate-center  滚动
 * ----------------------------------------
 */
@keyframes scoll-Table {
  from {
    transform: translate(0, 0px);
  }

  to {
    transform: translate(0, -135px);
  }
}

.scoll-Table {
  animation: scoll-Table 10s infinite;
  transition: all 1s ease-out;
  //  animation-timing-function: linear;
  // animation-fill-mode: forwards;
  /* 在动画结束后保持最后一个关键帧的状态 */
}

/* 鼠标进入 */
.scoll-Table:hover {
  animation-play-state: paused;
}

/* 鼠标离开 */
.scoll-Table:not(:hover) {
  animation-play-state: running;
}

.default_Main {
  width: 100%;
  min-height: 100vh;
  background-image: url('../../assets/images/background.jpg');
  .icon_parent_text {
    width: 97%;
    height: 50px;
    display: flex;
    justify-content: space-between;
    text-align: center;
    .icon_Style {
      height: 50px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    span {
      margin-top: 10px;
      font-size: 38px;
      color: #fff;
      width: 110px;
      text-align: center;
      font-weight: 500;
      display: block;
    }

    p {
      width: 110px;
      color: #fff;
      text-align: center;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .icon_parent {
    width: 90%;
    height: auto;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    text-align: center;

    .icon_Style {
      span {
        margin-left: -10px;
        color: #fff;
        text-align: center;
        font-size: 34px;
        font-weight: 700;
      }

      :nth-child(2) {
        position: relative;
        right: 40px;
        bottom: 22px;
      }
    }
  }

  .moduleTitle_parent {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    padding-left: 10px;

    .moduleTitle {
      color: #ffff;
      font-size: 17px;
      font-weight: 550;
    }
  }

  .title_Style {
    width: 100%;
    height: 105px;
    background: url('../../assets/images/title.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;

    .title {
      color: aliceblue;
      text-align: center;
      font-size: 32px;
      width: 100%;
      line-height: 105px;
    }
  }

  .timeStrle {
    color: aliceblue;
    font-size: 18px;
    position: absolute;
    right: 2%;
    margin: 8px 10px 0 0;
  }

  .mainCokpit {
    display: flex;
    justify-content: space-around;
    height: 62vh;

    .first-child {
      // background-color: aquamarine;
      // border: solid 1px #003077;
      margin-top: -45px;
      padding: 15px;
      width: 27%;

      .trend_Box {
        width: 100%;
        height: 250px;
      }
    }

    .nth-child {
      // background-color: rgb(204, 0, 255);
      // border: solid 1px #003077;
      display: flex;
      // gap: 20px;
      max-height: 650px;
      padding: 10px 20px;
      flex-direction: column;
      justify-content: flex-start;
      width: 46%;

      .topNumber {
        margin-top: 30px;
        display: flex;
        gap: 15px;
        justify-content: center;

        .number {
          font-size: 45px;
          margin-top: -5px;
          display: block;
          width: 100%;
          overflow: hidden;
          // text-overflow: ellipsis;
          white-space: nowrap;
          // display: inline-block;
        }

        .total {
          width: 33%;
          height: 100px;
          background: url('../../assets/images/emissonBack.png') no-repeat;
          background-size: 100%100%;
          box-shadow: 0px 0px 11px 0px #62a8f1;
          color: #fff;
          padding-left: 10px;
        }

        .WeChat {
          width: 33%;
          height: 100px;
          background: url('../../assets/images/emissonBack.png') no-repeat;
          background-size: 100%100%;
          box-shadow: 0px 0px 11px 0px #62a8f1;
          color: #fff;
          padding-left: 10px;
        }

        .Web {
          width: 33%;
          height: 100px;
          background: url('../../assets/images/emissonBack.png') no-repeat;
          background-size: 100%100%;
          box-shadow: 0px 0px 11px 0px #62a8f1;
          color: #fff;
          padding-left: 10px;
        }
      }

      .cpitBack {
        width: 100%;
        height: 420px;

        .top {
          width: 100%;
          height: 100%;
          z-index: 2;
        }

        .bottom {
          z-index: 0;
          position: absolute;
          top: 25%;
          left: 22%;
          width: 35%;
          margin: -2% 0 0 10%;
          height: 480px;
          transform: rotateX(35deg);
        }
      }
    }

    .last-child {
      // background-color: rgb(0, 255, 34);
      // border: solid 1px #003077;
      margin-top: -30px;
      width: 27%;

      .total_Box {
        width: 100%;
        height: 250px;
      }
    }
  }
}

.real-time-num {
  display: inline-block;
  width: 26px;
  height: 40px;
  font-size: 45px;
  margin-left: 3px;
  line-height: 40px;
  text-align: center;
  overflow: hidden;
}

.real-time-num > div {
  width: 26px;
  height: 40px;
  transition: all 1s ease-out;
}
.textOverflow {
  width: 350px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>