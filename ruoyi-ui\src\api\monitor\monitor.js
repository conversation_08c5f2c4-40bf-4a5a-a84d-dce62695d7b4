import request from "@/utils/request";

// 微信公众号采集监控列表
export function listMonitor(query) {
  return request({
    url: "/wechat/monitor/list",
    method: "get",
    params: query,
  });
}
// 智库采集监控列表
export function webMonitorList(query) {
  return request({
    url: "/web/monitor/list",
    method: "get",
    params: query,
  });
}
// 根据数据源名称获取文章分页列表
export function articleListWechatSourceName(query) {
  return request({
    url: "/article/articleList/wechat/sourceName",
    method: "get",
    params: query,
  });
}
// 根据数据源名称获取文章分页列表
export function articleListWebSourceName(query) {
  return request({
    url: "/article/articleList/web/sourceName",
    method: "get",
    params: query,
  });
}

// 查询微信公众号采集监控详细
export function getMonitor(id) {
  return request({
    url: "/monitor/monitor/" + id,
    method: "get",
  });
}

// 设置频次
export function editFrequency(data) {
  return request({
    url: "/article/source/edit/frequency",
    method: "post",
    data: data,
  });
}

// 卡片
export function getStatistics() {
  return request({
    url: "/wechat/monitor/statistics",
    method: "get",
  });
}
// 卡片
export function getWebStatistics() {
  return request({
    url: "/web/monitor/statistics",
    method: "get",
  });
}

// 公众号停更数据分页
export function monitorStopUpdating(data) {
  return request({
    url: "/wechat/monitor/stop/updating",
    method: "get",
    params: data,
  });
}

// 公众号最长发布间隔数据分页
export function monitorStopIntervalDays(data) {
  return request({
    url: "/wechat/monitor/max/intervalDays",
    method: "get",
    params: data,
  });
}
// 近5日未发布文章的公众号分页
export function monitorUnpublished(data) {
  return request({
    url: "/wechat/monitor/unpublished",
    method: "get",
    params: data,
  });
}
// 异常公众号分页
export function monitorAbnormalWechatName(data) {
  return request({
    url: "/wechat/monitor/abnormal/wechatName",
    method: "get",
    params: data,
  });
}
// 网站停更数据分页
export function webMonitorStopUpdating(data) {
  return request({
    url: "/web/monitor/stop/updating",
    method: "get",
    params: data,
  });
}

// 网站最长发布间隔数据分页
export function webMonitorStopIntervalDays(data) {
  return request({
    url: "/web/monitor/max/intervalDays",
    method: "get",
    params: data,
  });
}
// 近5日未发布文章的网站分页
export function webMonitorUnpublished(data) {
  return request({
    url: "/web/monitor/unpublished",
    method: "get",
    params: data,
  });
}
// 异常网站分页
export function webMonitorAbnormalwebName(data) {
  return request({
    url: "/web/monitor/abnormal/webName",
    method: "get",
    params: data,
  });
}
