import { getToken } from "@/utils/auth";
// deepseek
const deepseekUrl = `https://api.deepseek.com/v1/chat/completions`;
const deepseekApiKey = `***********************************`;
const deepseekModel = `deepseek-reasoner`;

// 公司Ollama
const ollamaUrl = `http://***********:21434/api/generate`;
const ollamaModel = `deepseek-r1:32b`;

// 公司Dify
// const difyUrl = `http://************:9801`; // 本地
const getBaseUrl = () => {
  const protocol = window.location.protocol; // 获取协议 (包含 ":")
  const hostname = window.location.hostname; // 获取主机名（IP或域名）
  const port = window.location.port; // 获取端口号

  // 如果端口号存在则添加，否则不添加
  return `${protocol}//${hostname}${port ? ":" + port : ""}`;
};
const difyUrl = `${getBaseUrl()}/prod-api`; // 线上
// const difyUrl = `https://***********:18006/prod-api`; // 准生产线上

export async function deepseekAiQa(text, isStream) {
  const url = new URL(deepseekUrl);
  const params = {};
  Object.keys(params).forEach((key) => {
    url.searchParams.append(key, params[key]);
  });

  const req = new Request(url, {
    method: "post",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${deepseekApiKey}`,
    },
    body: JSON.stringify({
      model: deepseekModel,
      stream: isStream,
      messages: [
        {
          role: "user",
          content: text,
        },
      ],
    }),
  });
  return fetch(req);
}

export async function difyAiQa(text, responseMode, configKey) {
  console.log(difyUrl);
  let url = new URL(`${difyUrl}/dify/chat/stream`);
  const params = {};
  Object.keys(params).forEach((key) => {
    url.searchParams.append(key, params[key]);
  });

  const req = new Request(url, {
    method: "post",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getToken()}`,
    },
    body: JSON.stringify({
      question: text,
      configKey: configKey,
      response_mode: responseMode,
    }),
  });
  return fetch(req);
}

export async function ollamaAiQa(text, isStream) {
  const url = new URL(ollamaUrl);
  const params = {};
  Object.keys(params).forEach((key) => {
    url.searchParams.append(key, params[key]);
  });

  const req = new Request(url, {
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: ollamaModel,
      prompt: text,
      stream: isStream,
      options: {
        temperature: 1,
        num_ctx: 10240,
      },
    }),
  });
  return fetch(req);
}
