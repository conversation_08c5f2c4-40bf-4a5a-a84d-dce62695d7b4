<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="90%"
    :before-close="handleClose"
    custom-class="markmap-dialog"
    append-to-body
  >
    <div class="dialog-content-wrapper">
      <!-- 左侧Markdown文本显示区域 -->
      <div class="markdown-text-container">
        <div class="markdown-content-text" v-html="formattedMarkdown"></div>
      </div>
      
      <!-- 右侧思维导图显示区域 -->
      <div class="markmap-wrapper">
        <div v-if="loading" class="thinking-container">
          <div class="thinking-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <div class="thinking-text">正在解读中...</div>
        </div>
        <svg v-else ref="markmap" class="markmap-svg"></svg>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { Transformer } from "markmap-lib";
import { Markmap } from "markmap-view";
import { formatMarkdown } from "@/utils/markdownUtils";

export default {
  name: "MarkmapDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    content: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "Deepseek文章内容解读",
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
    };
  },
  computed: {
    formattedMarkdown() {
      return formatMarkdown(this.content);
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (!val) {
        // 当对话框关闭时，触发关闭事件
        this.$emit("close");
      }
      if (val && this.content && !this.loading) {
        this.$nextTick(() => {
          this.renderMarkmap();
        });
      }
    },
    dialogVisible(val) {
      if (!val) {
        // 当对话框关闭时，同步更新 visible 属性
        this.$emit("update:visible", false);
        this.$emit("close");
      }
    },
    content(val) {
      if (val && this.visible && !this.loading) {
        this.$nextTick(() => {
          this.renderMarkmap();
        });
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit("update:visible", false);
      this.$emit("close");
    },
    async renderMarkmap() {
      if (!this.content) {
        this.loading = false;
        return;
      }

      try {
        await this.$nextTick();
        const svg = this.$refs.markmap;
        if (!svg) {
          throw new Error("SVG element not found");
        }

        // 清空之前的内容
        svg.innerHTML = "";

        // 处理内容，移除 markdown 标记
        let processedContent = this.content
          .replace(/^```markdown\s*/i, "") // 移除开头的 ```markdown
          .replace(/\s*```\s*$/, ""); // 移除结尾的 ```

        const transformer = new Transformer();
        const { root } = transformer.transform(processedContent);

        // 创建思维导图
        const mm = Markmap.create(
          svg,
          {
            autoFit: true,
            duration: 500,
            nodeMinHeight: 20,
            spacingVertical: 10,
            spacingHorizontal: 100,
            paddingX: 20,
            color: (node) => {
              const colors = {
                0: "#0052ff", // 亮蓝色
                1: "#009600", // 亮绿色
                2: "#ff6600", // 亮橙色
                3: "#8000ff", // 亮紫色
                4: "#ff0066", // 亮粉色
              };
              return colors[node.depth] || "#0052ff";
            },
            nodeFont: (node) => {
              const fonts = {
                0: 'bold 20px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
                1: '600 18px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
                2: '500 16px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
              };
              return (
                fonts[node.depth] ||
                '400 14px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto'
              );
            },
            maxWidth: 300,
            initialExpandLevel: -1,
            zoom: true,
            pan: true,
            linkShape: "diagonal",
            linkWidth: (node) => 2.5 - node.depth * 0.5,
            linkColor: (node) => {
              const colors = {
                0: "rgba(0, 82, 255, 0.8)", // 亮蓝色
                1: "rgba(0, 150, 0, 0.8)", // 亮绿色
                2: "rgba(255, 102, 0, 0.8)", // 亮橙色
              };
              return colors[node.depth] || "rgba(128, 0, 255, 0.8)";
            },
          },
          root
        );

        // 修改初始化动画部分
        setTimeout(() => {
          mm.fit(); // 适应视图大小

          // 重新设置数据以触发重绘
          const fitRatio = 0.95; // 留出一些边距
          const { minX, maxX, minY, maxY } = mm.state;
          const width = maxX - minX;
          const height = maxY - minY;
          const containerWidth = svg.clientWidth;
          const containerHeight = svg.clientHeight;

          // 计算合适的缩放比例
          const scale = Math.min(
            (containerWidth / width) * fitRatio,
            (containerHeight / height) * fitRatio
          );

          // 更新数据以应用新的缩放
          mm.setData(root, {
            initialScale: scale,
            initialPosition: [
              (containerWidth - width * scale) / 2,
              (containerHeight - height * scale) / 2,
            ],
          });
        }, 100);

        // 监听窗口大小变化
        const resizeHandler = () => mm.fit();
        window.addEventListener("resize", resizeHandler);

        // 组件销毁时清理
        this.$once("hook:beforeDestroy", () => {
          window.removeEventListener("resize", resizeHandler);
        });
      } catch (error) {
        console.error("Markmap rendering error:", error);
        this.$message.error("思维导图渲染失败");
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0 !important;
  background: transparent;
  height: calc(90vh - 100px); // 设置固定高度
}

::v-deep .el-dialog {
  margin-top: 5vh !important;
  margin-bottom: 5vh !important;
  display: flex;
  flex-direction: column;
}

.dialog-content-wrapper {
  display: flex;
  height: 100%;
}

.markdown-text-container {
  width: 30%;
  padding: 20px 20px;
  background-color: #fff;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
  background-color: #f9f9f9;

  .text-header {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    color: #333;
  }

  .markdown-content-text {
    height: calc(100%);
    overflow-y: auto;
    padding: 10px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar, 
  .markdown-content-text::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track, 
  .markdown-content-text::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb,
  .markdown-content-text::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover,
  .markdown-content-text::-webkit-scrollbar-thumb:hover {
    background-color: #ccc;
  }
}

/* Markdown 内容样式 */
::v-deep .markdown-content-text {
  .md-h1, h1.md-h1 {
    font-size: 20px;
    font-weight: bold;
    margin: 16px 0 8px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    color: #333;
  }

  .md-h2, h2.md-h2 {
    font-size: 18px;
    font-weight: bold;
    margin: 14px 0 8px 0;
    color: #333;
  }

  .md-h3, h3.md-h3 {
    font-size: 16px;
    font-weight: bold;
    margin: 12px 0 8px 0;
    color: #333;
  }

  .md-paragraph {
    margin: 8px 0;
    line-height: 1.6;
    color: #333;
  }

  .md-paragraph-space {
    height: 8px;
  }

  .md-blockquote {
    border-left: 4px solid #ddd;
    padding: 5px 10px;
    margin: 10px 0;
    background-color: #f6f6f6;
    color: #555;
  }

  .md-code-block {
    background-color: #f6f8fa;
    border-radius: 3px;
    padding: 10px;
    margin: 10px 0;
    font-family: monospace;
    overflow-x: auto;
    font-size: 13px;
  }

  .md-code-inline {
    background-color: #f6f8fa;
    border-radius: 3px;
    padding: 2px 4px;
    font-family: monospace;
    font-size: 13px;
  }

  .md-ul, .md-ol {
    margin: 10px 0;
    padding-left: 25px;
  }

  .md-ul {
    list-style-type: disc;
  }

  .md-ol {
    list-style-type: decimal;
  }

  strong {
    font-weight: bold;
  }

  em {
    font-style: italic;
  }
}

.markmap-wrapper {
  width: 70%;
  height: 100%;
  min-height: 500px; // 设置最小高度
  position: relative;
  background: transparent;
  border-radius: 4px;
}

.markmap-svg {
  width: 100%;
  height: 100%;
  display: block;
}

.thinking-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .thinking-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;

    span {
      display: inline-block;
      width: 12px;
      height: 12px;
      margin: 0 6px;
      background-color: #10d8ff;
      border-radius: 50%;
      animation: thinking 1.4s infinite ease-in-out both;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }

  .thinking-text {
    color: #ffffff;
    font-size: 18px;
    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);
  }
}

@keyframes thinking {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.3;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

:deep(.markmap-node) {
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

:deep(.markmap-node-circle) {
  fill: transparent; // 修改节点背景为透明
  stroke-width: 2px;
}

:deep(.markmap-node-text) {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial;

  tspan {
    fill: #333 !important; // 修改文字颜色为深色
    font-size: 14px;
    font-weight: 500;
  }
}

:deep(.markmap-link) {
  fill: none;
  stroke-width: 2.5px; // 加粗连线
}

// 根节点样式
:deep(.markmap-node[data-depth="0"]) {
  .markmap-node-circle {
    stroke: #0052ff; // 亮蓝色
    stroke-width: 3px;
  }

  .markmap-node-text tspan {
    font-size: 20px !important;
    font-weight: bold !important;
    fill: #333 !important;
  }
}

// 二级节点样式
:deep(.markmap-node[data-depth="1"]) {
  .markmap-node-circle {
    stroke: #009600; // 亮绿色
    stroke-width: 2.5px;
  }

  .markmap-node-text tspan {
    font-size: 18px !important;
    font-weight: 600 !important;
    fill: #333 !important;
  }
}

// 三级节点样式
:deep(.markmap-node[data-depth="2"]) {
  .markmap-node-circle {
    stroke: #ff6600; // 亮橙色
    stroke-width: 2px;
  }

  .markmap-node-text tspan {
    font-size: 16px !important;
    font-weight: 500 !important;
    fill: #333 !important;
  }
}

// 其他层级节点样式
:deep(.markmap-node[data-depth="3"]),
:deep(.markmap-node[data-depth="4"]) {
  .markmap-node-circle {
    stroke: #8000ff; // 亮紫色
    stroke-width: 2px;
  }

  .markmap-node-text tspan {
    font-size: 14px !important;
    font-weight: 500 !important;
    fill: #333 !important;
  }
}
</style>
