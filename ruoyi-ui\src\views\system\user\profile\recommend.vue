<template>
  <div :class="type == '' ? 'app-container' : ''">
    <div v-if="type == ''">
      <h1>我的推荐</h1>
    </div>
    <div>
      <el-table v-loading="loading3" :data="recommendList" border :header-cell-style="{ textAlign: 'center' }"
        style="width: 100%" height="calc(100vh - 220px)" ref="tableRef">
        <el-table-column label="文章来源" show-overflow-tooltip width="150" prop="sourceName" />
        <el-table-column label="文章标题" show-overflow-tooltip min-width="100" prop="title">
          <template slot-scope="scope">
            <span class="title" @click="openNewView(scope.row)">{{ scope.row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column label="推荐时间" align="center" prop="createTime" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="address" label="操作" width="80" align="center">
          <template slot-scope="scope">
            <span class="title" @click="handleRecommend(scope.row)">删除</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total3 > 0" :total="total3" :page.sync="queryParams3.pageNum"
        :limit.sync="queryParams3.pageSize" :autoScroll="false" @pagination="getList3" />
    </div>
  </div>
</template>

<script>
import API from '@/api/ScienceApi/index.js'

export default {
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading3: true,
      recommendList: [],
      total3: 0,
      queryParams3: {
        pageNum: 1,
        pageSize: 50,
      },
    };
  },
  methods: {
    getList3() {
      this.loading3 = true;
      API.recommendList(this.queryParams3).then(response => {
        this.recommendList = response.rows;
        this.total3 = response.total;
        this.loading3 = false;
        this.$nextTick(() => {
          this.scrollToTop();
        });
      });
    },
    // 表格滚动到顶部
    scrollToTop() {
      if (this.$refs.tableRef) {
        const tableEl = this.$refs.tableRef.$el.querySelector(
          ".el-table__body-wrapper"
        );
        if (tableEl) {
          tableEl.scrollTop = 0;
        }
      }
    },
    handleRecommend(item) {
      this.$confirm('此操作将取消推荐该条文章, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {
        let query = new FormData()
        query.append('articleId', item.articleId)
        API.recommendCancel(query).then(res => {
          if (res.code == 200) {
            this.$message({ message: '已取消推荐', type: 'success' })
            this.getList3()
          } else {
            this.$message({ message: '取消推荐失败', type: 'info' })
          }
        })
      }).catch(() => { })
    },
    openNewView(item) {
      window.open(`/expressDetails?id=${item.articleId}&docId=${item.articleId}`, '_blank')
    },
  },
  computed: {
    // 在这里定义计算属性
  },
  created() {
    this.getList3();
  },
  mounted() {
    // 在这里定义挂载后的操作
  },
};
</script>

<style lang="scss" scoped>
.title:hover {
  color: #1d8af0;
  border-bottom: solid 1px #1d8af0;
  cursor: pointer;
}
</style>