import request from '@/utils/request'

// 查询大屏 -区域分析关联文章列表
export function listRegionArticle(query) {
  return request({
    url: '/screen/regionArticle/list',
    method: 'get',
    params: query
  })
}

// 查询大屏 -区域分析关联文章详细
export function getRegionArticle(id) {
  return request({
    url: '/screen/regionArticle/' + id,
    method: 'get'
  })
}

// 新增大屏 -区域分析关联文章
export function addRegionArticle(data) {
  return request({
    url: '/screen/regionArticle',
    method: 'post',
    data: data
  })
}

// 修改大屏 -区域分析关联文章
export function updateRegionArticle(data) {
  return request({
    url: '/screen/regionArticle',
    method: 'put',
    data: data
  })
}

// 删除大屏 -区域分析关联文章
export function delRegionArticle(id) {
  return request({
    url: '/screen/regionArticle/' + id,
    method: 'delete'
  })
}

export function updateRegionArticleById(data) {
  return request({
    url: '/screen/regionArticle/updateById',
    method: 'post',
    data: data
  })
}
