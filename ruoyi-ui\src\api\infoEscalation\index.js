import request from "@/utils/request";

const dayInfo = (params) => {
  return request({
    url: "/article/articleList/overview",
    method: "post",
    data: params,
  });
};
/* 首页数据 */
const indexPageData = (params) => {
  return request({
    url: "/article/home/<USER>",
    method: "get",
  });
};
/* 首页统计 */
const indexStatistic = (params) => {
  return request({
    url: `/article/home/<USER>/${params}`,
    method: "get",
  });
};
/* 产融合作接口 */
const productCooperation = (params) => {
  return request({
    url: `/article/largeScreen/finance`,
    method: "get",
    params,
  });
};
// 新大屏数据
const getNewLargeScreen = (params) => {
  return request({
    url: "/article/largeScreen/screen",
    method: "get",
  });
};

export default {
  dayInfo,
  indexPageData,
  indexStatistic,
  productCooperation,
  getNewLargeScreen
};
