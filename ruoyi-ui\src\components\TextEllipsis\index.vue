<template>
  <div ref="compRef" class="text-ellipsis">
    <div v-if="!isExpanded" class="ellipsis-content">
      <div ref="textRef" class="text-content" :class="{'text-limited': textOver}" :style="textOver ? ellipsisStyle : {}">
        {{ text }}
      </div>
      <div v-if="textOver" class="ellipsis-actions">
        <span class="show-more" @click="toggleExpand">{{ moreText }}<i class="el-icon-arrow-down"></i></span>
      </div>
    </div>
    <div v-else class="full-content">
      <div class="text-content">
        {{ text }}
      </div>
      <div class="ellipsis-actions">
        <span class="show-less" @click="toggleExpand">{{ lessText }}<i class="el-icon-arrow-up"></i></span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TextEllipsis',
  props: {
    text: {
      type: String,
      default: ''
    },
    maxLines: {
      type: Number,
      default: 10
    },
    moreText: {
      type: String,
      default: '展开'
    },
    lessText: {
      type: String,
      default: '收起'
    }
  },
  data() {
    return {
      isExpanded: false,
      textOver: false,
      observer: null
    };
  },
  computed: {
    ellipsisStyle() {
      return {
        '-webkit-line-clamp': this.maxLines,
        'display': '-webkit-box',
        '-webkit-box-orient': 'vertical',
        'overflow': 'hidden',
        'text-overflow': 'ellipsis',
        'word-break': 'break-word'
      };
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.checkTextOverflow();
      
      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize);
      
      // 创建ResizeObserver观察元素大小变化
      if (window.ResizeObserver) {
        this.observer = new ResizeObserver(this.debounce(this.checkTextOverflow, 50));
        this.observer.observe(this.$refs.compRef);
      }
    });
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    
    // 清理ResizeObserver
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  },
  watch: {
    text() {
      this.$nextTick(() => {
        this.checkTextOverflow();
      });
    }
  },
  methods: {
    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    },
    checkTextOverflow() {
      const textElem = this.$refs.textRef;
      if (!textElem) return;
      
      // 清除之前可能应用的样式
      textElem.style.webkitLineClamp = '';
      textElem.style.display = '';
      textElem.style.overflow = '';
      textElem.style.textOverflow = '';
      textElem.style.webkitBoxOrient = '';
      
      // 测量全高
      const lineHeight = parseInt(window.getComputedStyle(textElem).lineHeight) || 20;
      const fullHeight = textElem.scrollHeight;
      const maxHeight = lineHeight * this.maxLines;
      
      // 判断是否超出
      this.textOver = fullHeight > maxHeight;
      
      // 在下一个渲染周期应用样式
      this.$nextTick(() => {
        if (this.textOver && !this.isExpanded) {
          // 手动应用样式确保生效
          Object.entries(this.ellipsisStyle).forEach(([key, value]) => {
            textElem.style[key] = value;
          });
        }
      });
    },
    handleResize() {
      this.debounce(this.checkTextOverflow, 50)();
    },
    // 简单的防抖函数
    debounce(fn, delay) {
      let timer = null;
      return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(context, args);
        }, delay);
      };
    }
  }
};
</script>

<style scoped>
.text-ellipsis {
  position: relative;
  width: 100%;
}

.text-content {
  line-height: 30px;
  word-break: break-word;
  white-space: normal;
}

.text-limited {
  /* 保证样式在类上也生效 */
  display: -webkit-box !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.ellipsis-actions {
  text-align: right;
  /* margin-top: 5px; */
}

.show-more,
.show-less {
  display: inline-block;
  cursor: pointer;
  color: #0798f8;
  font-size: 14px;
  user-select: none;
  transition: color 0.2s;
}

.show-more:hover,
.show-less:hover {
  color: #0071ce;
}

/* 图标样式 */
.show-more i,
.show-less i {
  margin-left: 3px;
  font-size: 12px;
}
</style> 