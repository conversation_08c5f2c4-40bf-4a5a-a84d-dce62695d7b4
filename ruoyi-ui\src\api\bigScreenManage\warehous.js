import request from "@/utils/request";

// 查询报告入库列表
export function listWarehous(query) {
  return request({
    url: "/screen/warehous/list",
    method: "get",
    params: query,
  });
}

// 查询报告入库详细
export function getWarehous(id) {
  return request({
    url: "/screen/warehous/" + id,
    method: "get",
  });
}

// 新增报告入库
export function addWarehous(data) {
  return request({
    url: "/screen/warehous",
    method: "post",
    data: data,
  });
}

// 修改报告入库
export function updateWarehous(data) {
  return request({
    url: "/screen/warehous/edit",
    method: "post",
    data: data,
  });
}

// 删除报告入库
export function delWarehous(id) {
  return request({
    url: "/screen/warehous/remove",
    method: "post",
    data: id,
  });
}
