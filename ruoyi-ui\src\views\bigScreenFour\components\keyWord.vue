<template>
  <div style="width: 100%; height: 100%">
    <div id="myChart" ref="myChart" style="width: 100%; height: 100%"></div>
    <el-dialog class="iframe-dialog" :visible.sync="iframeDialog" :width="iframeWidth" append-to-body :close-on-click-modal="false">
      <div slot="title" class="iframe-header">
        <div class="left">突发事件分析</div>
        <div class="center">{{ iframeName }}</div>
      </div>
      <div class="iframe-body">
        <iframe ref="iframeRef" frameborder="0" width="100%" height="100%" :src="iframeUrl" @load="onIFrameLoad"></iframe>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from "@/utils/request";
import { demo } from "../demo";
import * as echarts from "echarts";
require("echarts-wordcloud");
const color = [
  "#FFFFFF",
  "#F4EFBD",
  "#F8C0DA",
  "#F3C59A",
  "#C697E9",
  "#A9DDC2",
  "#9BBFFF",
  "#6BB2B2",
  "#88BCE8",
];
export default {
  data() {
    return {
      chart: null,
      echartSzie: { width: "auto", height: "auto" }, // 图size
      option: null,
      iframeDialog: false,
      iframeUrl: '',
      iframeName: '',
      iframeWidth: '500px',
    };
  },
  props: {
    wordValueList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  mounted() { },
  watch: {
    wordValueList: {
      handler(value) {
        if (value) {
          this.chart = echarts.init(this.$refs["myChart"]);
          this.funResize = this.debounce(this.resize, 200);
          this.chartFun(value);
        }
      },
      deep: true,
    },
  },

  components: {},
  methods: {
    chartFun(data) {
      this.chart.resize(this.echartSzie);
      this.chart.clear();
      this.option = {
        series: [
          {
            type: "wordCloud",
            shape: "circle",
            left: "center",
            top: "center",
            right: null,
            bottom: null,
            width: "100%",
            height: "100%",
            sizeRange: [12, 26],
            rotationRange: [-90, 90],
            rotationStep: 90,
            gridSize: 8,
            drawOutOfBound: false, // 超出画布部分不显示，与sizeRange相关
            textStyle: {
              normal: {
                fontFamily: "sans-serif",
                fontWeight: "normal",
              },
              emphasis: {
                shadowBlur: 5,
                shadowColor: "#333",
              },
            },
            data: data.map((item) => {
              return {
                name: item.words,
                value: item.wordsNum,
                url: item.url,
                textStyle: {
                  color: color[Math.floor(Math.random() * 10)],
                },
              };
            }),
          },
        ],
      };
      this.option && this.chart.setOption(this.option);
      this.chart.on("click", (params) => {
        if (params.data.url) {
          this.iframeUrl = params.data.url
          this.iframeName = params.data.name
          this.iframeDialog = true
        }
      });
    },

    // 防抖
    debounce(fn, delay) {
      let timer;
      return function () {
        let context = this;
        let args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(context, args);
        }, delay);
      };
    },
    // 页面重绘
    resize() {
      this.chart.resize(this.echartSzie);
      this.chart.clear();
      this.option && this.chart.setOption(this.option);
    },
    getRandomInt(min, max) {
      min = Math.ceil(min); // 将min向上取整
      max = Math.floor(max); // 将max向下取整
      return Math.floor(Math.random() * (max - min + 1)) + min; // 返回min到max之间的随机整数
    },
    onIFrameLoad() {
      const iframe = this.$refs.iframeRef;
      if (iframe.contentWindow) {
        // 获取内部文档的高度
        const doc = iframe.contentWindow.document;
        const body = doc.body;
        const docElem = doc.documentElement;
        const height = Math.max(
          body.scrollHeight, docElem.scrollHeight,
          body.offsetHeight, docElem.offsetHeight,
          body.clientHeight, docElem.clientHeight
        );
        const width = Math.max(
          body.scrollWidth, docElem.scrollWidth,
          body.offsetWidth, docElem.offsetWidth,
          body.clientWidth, docElem.clientWidth
        );
        this.iframeWidth = `${width}px`;
        // 更新 iframe 高度
        iframe.style.height = `${height}px`;
      }
    },
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.funResize);
  },
};
</script>

<style lang="scss" scoped>
.el-table .warning-row {
  background-color: #13436d;
}

.el-table .success-row {
  background-color: #113a65;
}

.iframe-dialog {
  ::v-deep .el-dialog {
    .el-dialog__header {
      // display: none;
      background-color: #1d2334;
      border-bottom: 1px solid rgb(51, 51, 51);
      height: 50px;

      .iframe-header {
        position: relative;
        display: flex;
        color: rgb(242, 242, 242);
        padding-left: 15px;
        height: 40px;
        align-items: center;

        .left {
          font-size: 18px;
          font-weight: 700;
        }

        .center {
          position: absolute;
          left: 50%;
          transform: translate(-50%, 0);
        }
      }
    }

    .el-dialog__body {
      padding: 0;
      height: 80vh;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
}
</style>

