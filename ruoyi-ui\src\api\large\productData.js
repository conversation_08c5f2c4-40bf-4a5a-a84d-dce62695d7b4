import request from "@/utils/request";

// 查询大屏-产品数据列

export function listProductData(query) {
  return request({
    url: "/large/productData/list",
    method: "get",
    params: query,
  });
}

// 查询大屏-产品数据列

export function getProductData(id) {
  return request({
    url: "/large/productData/" + id,
    method: "get",
  });
}

// 新增大屏-产品数据列

export function addProductData(data) {
  return request({
    url: "/large/productData/add",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "multipart/form-data; boundary=something",
      Accept: "*/*",
    },
  });
}

// 修改大屏-产品数据列

export function updateProductData(data) {
  return request({
    url: "/large/productData/edit",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "multipart/form-data; boundary=something",
      Accept: "*/*",
    },
  });
}

// 删除大屏-产品数据列

export function delProductData(id) {
  return request({
    url: "/large/productData/remove",
    method: "post",
    data: id,
  });
}

export function courseStream(data) {
  return request({
    url: `/large/productData/download/file`,
    method: "post",
    data,
    responseType: "blob",
    headers: {
      "Content-Type": "multipart/form-data; boundary=something",
      Accept: "*/*",
    },
  });
}

// pdf数据
export function courseStreamPdf(data) {
  return request({
    url: `/large/productData/download/file`,
    method: "post",
    data,
    responseType: "arraybuffer",
    headers: {
      "Content-Type": "multipart/form-data; boundary=something",
      Accept: "*/*",
    },
  });
}
