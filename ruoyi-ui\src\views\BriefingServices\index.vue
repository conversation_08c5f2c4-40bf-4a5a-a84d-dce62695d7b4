<!-- 简报服务 -->
<template>
  <div
    v-loading="$store.state.app.loading"
    class="brieFing"
    element-loading-text="数据加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <div class="topSeach" v-show="!preview">
      <div class="formStyle">
        <el-row :gutter="20">
          <el-col :span="1.5">简报名称</el-col>
          <el-col :span="3">
            <el-input
              size="mini"
              v-model="BriefingName"
              placeholder="请输入简报名称"
              clearable
            />
          </el-col>
          <el-col :span="1.5">创建时间</el-col>
          <el-col :span="3">
            <el-select size="mini" placeholder v-model="timeRange">
              <el-option value="all" label="全部">全部</el-option>
              <el-option value="time" label="时间选择器">时间选择器</el-option>
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              value-format="yyyy-MM-dd HH:mm:ss"
              unlink-panels
              v-if="timeRange == 'time'"
              v-model="TImeInterval"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-col>
          <el-col :span="1.5" v-if="switchView == '定时任务'">触发频率</el-col>
          <el-col :span="3" v-if="switchView == '定时任务'">
            <el-select v-model="frequency" clearable>
              <el-option label="天" :value="1">天</el-option>
              <el-option label="周" :value="2">周</el-option>
              <el-option label="月" :value="3">月</el-option>
            </el-select>
          </el-col>
          <el-col :span="2.5">
            <el-button
              type="primary"
              size="mini"
              @click="getList"
              :loading="seachLoading"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetFields"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </div>
      <div class="btnGroup">
        <div>
          <el-button
            type="primary"
            v-if="switchView !== '定时任务'"
            size="mini"
            @click="generate"
            v-hasPermi="['article:report:add']"
            >生成简报</el-button
          >
          <el-button
            size="mini"
            v-if="switchView !== '定时任务'"
            @click="switchView = '定时任务'"
            v-hasPermi="['article:cron:add']"
            >定时任务</el-button
          >
          <el-button
            type="primary"
            v-if="switchView !== '定时任务'"
            size="mini"
            @click="addBriefing"
            v-hasPermi="['article:report:add']"
            >新建简报</el-button
          >
          <el-button
            size="mini"
            type="primary"
            v-if="switchView == '定时任务'"
            @click="TimeOut"
            v-hasPermi="['article:cron:add']"
            >新建定时任务</el-button
          >
        </div>
        <div class="iconTop">
          <el-tooltip
            class="item"
            effect="dark"
            content="图片视图"
            placement="bottom"
          >
            <p
              class="el-icon-menu"
              :style="{ color: switchView == '图片视图' ? '#1890ff' : '' }"
              @click="switchView = '图片视图'"
            ></p>
          </el-tooltip>
          <el-tooltip
            class="item"
            effect="dark"
            content="列表视图"
            placement="bottom"
          >
            <p
              class="el-icon-s-operation"
              :style="{ color: switchView == '列表视图' ? '#1890ff' : '' }"
              @click="switchView = '列表视图'"
            ></p>
          </el-tooltip>
        </div>
      </div>
    </div>
    <mainBriefingVue
      ref="mainBriefing"
      :preview="preview"
      :switchView="switchView"
      :tableData="tableData"
      :total="total"
      :pageCurrent="pageCurrent"
      :pageSize="pageSize"
      @deleteBrie="deleteBrie"
      :timeData="timeData"
      @editTime="editTime"
      @handleCurrentChange="handleCurrentChange"
      @handleSizeChange="handleSizeChange"
    />
    <briefingDialog
      v-if="shouldShowBriefingDialog"
      :visble="visble"
      @beforCloe="cloeEvent"
      :distinguish="distinguish"
      :switchView="switchView"
      @onsubmit="onsubmit"
      :editData="editData"
      :editorUsers="editorUsers"
    />
  </div>
</template>

<script>
import mainBriefingVue from "@/views/components/mainBriefing.vue";
import briefingDialog from "@/views/components/briefingDialog.vue";
import API from "@/api/ScienceApi/briefing.js";
import { allocatedUserList } from "@/api/system/role";
export default {
  components: {
    mainBriefingVue,
    briefingDialog,
  },
  data() {
    return {
      preview: false,
      BriefingName: "",
      timeRange: "",
      switchView: "图片视图",
      visble: false,
      TImeInterval: [],
      total: 0,
      pageSize: 50,
      pageCurrent: 1,
      tableData: [],
      distinguish: "生成简报",
      frequency: "" /* 出发频率 */,
      timeData: [] /* 定时任务列表 */,
      loadingOpen: false,
      editData: {},
      seachLoading: false,
      editorUsers: [], // 编辑角色用户列表
    };
  },
  computed: {
    // 获取当前登录用户信息
    currentUserInfo() {
      return JSON.parse(window.sessionStorage.getItem("userInfo") || "{}");
    },
    // 获取当前用户角色
    currentUserRoles() {
      return this.$store.getters.roles || [];
    },
    // 判断当前用户是否为主编
    isChiefEditor() {
      return this.currentUserRoles.includes("chief");
    },
    // 判断当前用户是否为编辑
    isEditor() {
      return this.currentUserRoles.includes("editor");
    },
    // 判断是否需要显示briefingDialog组件
    shouldShowBriefingDialog() {
      return this.isChiefEditor;
    },
  },
  watch: {
    switchView: function (newVal, oldVal) {
      this.pageCurrent = 1;
      this.pageSize = 50;
      this.resetFields();
      this.getList();
    },
  },
  created() {
    this.getList();
  },
  methods: {
    resetFields() {
      this.BriefingName = "";
      this.timeRange = "";
      this.frequency = "";
      this.TImeInterval = "";
      this.getList();
    },
    cloeEvent(flag) {
      this.visble = flag;
      this.editData = {};
    },
    /* 新增简报 */
    async onsubmit(model) {
      this.visble = false;
      let params;
      let res;
      /* 修改定时任务 */
      if (model.id) {
        this.editTimeOut(model);
        return;
      }
      /* 新增的情况 */
      if (this.distinguish == "生成简报") {
        params = {
          title: model.briefingName,
          startTime: model.TImeInterval[0],
          endTime: model.TImeInterval[1],
          sourceType: String(model.platformType),
          keywords: model.keyWords,
          exclusions: model.excludeWords,
          type: 4,
        };
      } else if (this.distinguish == "新建简报") {
        params = {
          title: model.briefingName /* 简报名称 */,
          type: 5,
          templateId: model.report,
          sourceType: String(model.platformType),
          userId: this.currentUserInfo.userId,
          editors: Array.isArray(model.editors) ? model.editors.join(",") : "",
        };
      }
      if (this.distinguish !== "定时任务") {
        res = await API.addReport(params);
        if (res.code == 200) {
          this.$message({
            message: "新增成功",
            type: "success",
          });
          this.getList();
        } else {
          this.$message({
            message: "新增失败,请联系管理员",
            type: "error",
          });
        }
        return;
      }
      /* 新增定时任务方法 */
      this.AddTimeOut(model);
    },
    /* 定时任务新增方法 */
    async AddTimeOut(model) {
      let str;
      switch (model.switchTime) {
        case "1":
          str = `每天${model.sendTime.time}发送`;
          break;
        case "2":
          str = `每周${model.sendTime.week} ${model.sendTime.time}发送`;
          break;
        case "3":
          str = `每月${model.sendTime.month}号 ${model.sendTime.time}发送`;
          break;
        default:
          break;
      }
      let params = {
        title: model.briefingName,
        type: model.switchTime,
        hour: model.sendTime.time.slice(0, 2),
        minute: model.sendTime.time.slice(3, 5),
        dayOfWeek: model.sendTime.week,
        dayOfMonth: model.sendTime.month,
        sourceType: String(model.platformType),
        keywords: model.keyWords,
        exclusions: model.excludeWords,
        frequency: str,
      };
      let res = await API.addTimeOut(params);
      if (res.code == 200) {
        this.$message({
          message: "定时任务新增成功",
          type: "success",
        });
        this.getList();
      } else {
        this.$message({
          message: "新增失败,请联系管理员",
          type: "error",
        });
      }
    },
    /* 修改定时任务 */
    async editTimeOut(model) {
      let str;
      switch (model.switchTime) {
        case "1":
          str = `每天${model.sendTime.time}发送`;
          break;
        case "2":
          str = `每周${model.sendTime.week} ${model.sendTime.time}发送`;
          break;
        case "3":
          str = `每月${model.sendTime.month}号 ${model.sendTime.time}发送`;
          break;
        default:
          break;
      }
      let params = {
        id: model.id,
        title: model.briefingName,
        type: model.switchTime,
        hour: model.sendTime.time.slice(0, 2),
        minute: model.sendTime.time.slice(3, 5),
        dayOfWeek: model.sendTime.week,
        dayOfMonth: model.sendTime.month,
        sourceType: String(model.platformType),
        keywords: model.keyWords,
        exclusions: model.excludeWords,
        frequency: str,
      };
      let res = await API.editTime(params);
      if (res.code == 200) {
        this.$message({ message: "定时任务修改成功", type: "success" });
        this.getList();
      } else {
        this.$message({ message: "定时任务修改失败", type: "error" });
      }
      this.distinguish = "生成简报";
    },
    /* 简报列表 */
    async getList() {
      this.loadingOpen = true;
      this.seachLoading = true;
      if (this.seachLoading == false) return;
      let params = {
        pageSize: this.pageSize,
        pageNum: this.pageCurrent,
        title: this.BriefingName,
        startTime: this.TImeInterval[0],
        endTime: this.TImeInterval[1],
        type: this.frequency,
        // reportStatus: "1",
      };

      // 只有当当前登录用户的角色是主编和编辑的情况下才需要传editors参数
      if (this.isChiefEditor || this.isEditor) {
        params.editors = this.currentUserInfo.userId;
      }
      if (this.switchView !== "定时任务") {
        await API.queryReportList(params)
          .then((response) => {
            if (response.code == 200) {
              this.tableData = response.rows.sort(
                (a, b) =>
                  new Date(b.createTime).getTime() -
                  new Date(a.createTime).getTime()
              );
              this.tableData = this.tableData.map((item) => {
                return {
                  ...item,
                  inputShow: false,
                };
              });
              this.total = response.total;
            } else {
              this.$message({
                message: "简报数据获取失败,请反馈给管理员",
                type: "error",
              });
            }
            this.seachLoading = false;
          })
          .catch((err) => {
            this.$message({
              message: "简报数据获取失败,请反馈给管理员",
              type: "error",
            });
            this.seachLoading = false;
          });
      } else {
        await API.getTimeList(params)
          .then((response) => {
            if (response.code == 200) {
              this.timeData = response.rows;
              this.total = response.total;
              this.seachLoading = false;
            } else {
              this.$message({
                message: "定时任务获取失败,请反馈给管理员",
                type: "error",
              });
              this.seachLoading = false;
            }
          })
          .catch((err) => {
            this.$message({
              message: "定时任务获取失败,请反馈给管理员",
              type: "error",
            });
            this.seachLoading = false;
          });
      }
      this.loadingOpen = false;
    },
    async addBriefing() {
      // 查询同部门下角色是编辑的用户
      await this.getEditorUsersByDept();
      this.visble = true;
      this.distinguish = "新建简报";
    },
    generate() {
      this.visble = true;
      this.distinguish = "生成简报";
    },
    TimeOut() {
      this.visble = true;
      this.distinguish = "定时任务";
    },
    /* 页码变化 */
    handleCurrentChange(current) {
      this.pageCurrent = current;
      this.getList();
      document.querySelector(".mainBriefing_imgView") &&
        document.querySelector(".mainBriefing_imgView").scrollTo(0, 0);
      document.querySelector(".mainBriefing") &&
        document.querySelector(".mainBriefing").scrollTo(0, 0);
      window.scrollTo(0, 0);

      // 处理列表视图表格滚动
      this.$nextTick(() => {
        const mainBriefingRef = this.$refs.mainBriefing;
        if (mainBriefingRef && mainBriefingRef.$refs.tableRef) {
          mainBriefingRef.$refs.tableRef.bodyWrapper.scrollTop = 0;
        }
      });
    },
    /* 数量变化 */
    handleSizeChange(size) {
      this.pageSize = size;
      this.getList();
      document.querySelector(".mainBriefing_imgView") &&
        document.querySelector(".mainBriefing_imgView").scrollTo(0, 0);
      document.querySelector(".mainBriefing") &&
        document.querySelector(".mainBriefing").scrollTo(0, 0);
      window.scrollTo(0, 0);

      // 处理列表视图表格滚动
      this.$nextTick(() => {
        const mainBriefingRef = this.$refs.mainBriefing;
        if (mainBriefingRef && mainBriefingRef.$refs.tableRef) {
          mainBriefingRef.$refs.tableRef.bodyWrapper.scrollTop = 0;
        }
      });
    },
    /* 删除简报 */
    deleteBrie(id) {
      this.$confirm("您确定要删除这条数据吗", "删除提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let res;
          if (this.switchView !== "定时任务") {
            res = await API.removeBriefing([id]);
          } else {
            res = await API.removeTime([id]);
          }
          if (res.code == 200) {
            this.$message({ message: "删除成功", type: "success" });
            this.getList();
          } else {
            this.$message({ message: "删除失败，请联系管理员", type: "error" });
          }
        })
        .catch(() => {
          /* 错误捕捉 */
        });
    },
    /* 修改定时任务 */
    editTime(item) {
      this.visble = true;
      this.editData = item;
      this.distinguish = "查看定时任务";
    },

    /* 根据主编查询同部门下角色是编辑的用户 */
    async getEditorUsersByDept() {
      try {
        if (!this.currentUserInfo.dept || !this.currentUserInfo.dept.deptId) {
          console.error("当前用户部门信息不完整");
          return;
        }

        const response = await allocatedUserList({
          roleId: 25, // 固定角色ID为25（编辑角色）
          deptId: this.currentUserInfo.dept.deptId,
        });

        if (response.code === 200) {
          this.editorUsers = response.rows || [];
        } else {
          console.error("获取编辑用户列表失败:", response.msg);
        }
      } catch (error) {
        console.error("获取编辑用户列表失败:", error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.brieFing {
  height: calc(100vh - 94px);
}

.topSeach {
  width: 100%;
  height: 120px;
  line-height: 40px;
  box-shadow: 1px 1px 16px 6px #efefef;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
}

.formStyle {
  padding: 20px 0 0 20px;
}

.btnGroup {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
}

.iconTop {
  margin-right: 20px;

  p {
    font-size: 28px;
    margin: 0 5px;
    color: rgb(90, 89, 89);
  }
}
</style>
