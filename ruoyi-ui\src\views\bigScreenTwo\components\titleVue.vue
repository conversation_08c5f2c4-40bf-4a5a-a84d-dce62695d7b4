<template>
  <div
    :class="['title', { title2: type == 2 }]"
    :style="{ width: width, height: '45px' }"
  >
    {{ title }}
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    width: {
      default: "877px",
    },
    title: {
      required: true,
      default: "",
    },
    type: {
      default: 1,
    },
  },
  computed: {},
  watch: {},
  data() {
    return {};
  },
  created() {},

  methods: {},
};
</script>

<style lang="scss" scoped>
.title {
  background: url("../../../assets/bigScreenTwo/title.png") -5px 0px no-repeat;
  background-size: 100% 100% !important;
  background-size: cover;
  font-family: "pingFangMedium";
  font-size: 20px;
  color: #ffffff;
  line-height: 45px;
  letter-spacing: 2px;
  text-align: left;
  font-style: normal;
  padding-left: 65px;
}
.title2 {
  background: url("../../../assets/bigScreenTwo/title-long.png") -5px 0px no-repeat;
}
</style>
