<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标题" prop="cnTitle">
        <el-input v-model="queryParams.cnTitle" placeholder="请输入用户ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="留言内容" prop="content">
        <el-input v-model="queryParams.content" placeholder="请输入单位ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="留言人" prop="createBy">
        <el-input v-model="queryParams.createBy" placeholder="请输入单位ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="workList" @selection-change="handleSelectionChange" height="calc(100vh - 200px)" ref="tableRef">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="用户名称" show-overflow-tooltip width="200" prop="createBy" />
      <el-table-column label="留言内容" show-overflow-tooltip min-width="300" prop="content" />
      <el-table-column label="文章标题" show-overflow-tooltip min-width="300" prop="cnTitle" />
      <el-table-column label="留言时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['article:work:edit']">修改</el-button> -->
          <el-button size="mini" type="text" @click="openNewView(scope.row)">查看</el-button>
          <el-button v-hasPermi="['article:feedback:remove']" size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改文章工作对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="文章唯一标识" prop="sn">
          <el-input v-model="form.sn" placeholder="请输入文章唯一标识" />
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="中文标题" prop="cnTitle">
          <el-input v-model="form.cnTitle" placeholder="请输入中文标题" />
        </el-form-item>
        <el-form-item label="来源名称" prop="sourceName">
          <el-input v-model="form.sourceName" placeholder="请输入来源名称" />
        </el-form-item>
        <el-form-item label="来源唯一标识" prop="sourceSn">
          <el-input v-model="form.sourceSn" placeholder="请输入来源唯一标识" />
        </el-form-item>
        <el-form-item label="文章原文链接URL" prop="originalUrl">
          <el-input v-model="form.originalUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="文章短链接URL" prop="shortUrl">
          <el-input v-model="form.shortUrl" placeholder="请输入文章短链接URL" />
        </el-form-item>
        <el-form-item label="作者" prop="author">
          <el-input v-model="form.author" placeholder="请输入作者" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="摘要" prop="summary">
          <el-input v-model="form.summary" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="中文摘要" prop="cnSummary">
          <el-input v-model="form.cnSummary" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="封面图片" prop="cover">
          <el-input v-model="form.cover" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="地区编码" prop="publishCode">
          <el-input v-model="form.publishCode" placeholder="请输入地区编码" />
        </el-form-item>
        <el-form-item label="发布地区" prop="publishArea">
          <el-input v-model="form.publishArea" placeholder="请输入发布地区" />
        </el-form-item>
        <el-form-item label="发布时间" prop="publishTime">
          <el-date-picker clearable v-model="form.publishTime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择发布时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="点赞数" prop="numberLikes">
          <el-input v-model="form.numberLikes" placeholder="请输入点赞数" />
        </el-form-item>
        <el-form-item label="阅读数" prop="numberReads">
          <el-input v-model="form.numberReads" placeholder="请输入阅读数" />
        </el-form-item>
        <el-form-item label="收藏数" prop="numberCollects">
          <el-input v-model="form.numberCollects" placeholder="请输入收藏数" />
        </el-form-item>
        <el-form-item label="分享数" prop="numberShares">
          <el-input v-model="form.numberShares" placeholder="请输入分享数" />
        </el-form-item>
        <el-form-item label="评论数" prop="numberComments">
          <el-input v-model="form.numberComments" placeholder="请输入评论数" />
        </el-form-item>
        <el-form-item label="情感属性：1.负面；2.中性；3.正面；4.待处理" prop="emotion">
          <el-input v-model="form.emotion" placeholder="请输入情感属性：1.负面；2.中性；3.正面；4.待处理" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="单位ID" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入单位ID" />
        </el-form-item>
        <el-form-item label="附件" prop="fileUrl">
          <el-input v-model="form.fileUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { feedbackList, feedbackRemove, } from "@/api/article/leaveMessage";

export default {
  name: "Work",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 文章工作表格数据
      workList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {

      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询文章工作列表 */
    getList() {
      this.loading = true;
      feedbackList(this.queryParams).then(response => {
        this.workList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.$nextTick(() => {
          this.scrollToTop();
        });
      });
    },
    // 表格滚动到顶部
    scrollToTop() {
      if (this.$refs.tableRef) {
        const tableEl = this.$refs.tableRef.$el.querySelector(
          ".el-table__body-wrapper"
        );
        if (tableEl) {
          tableEl.scrollTop = 0;
        }
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加留言";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getWork(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改留言";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateWork(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWork(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids;
      this.$modal.confirm('是否确认删除留言编号为"' + ids + '"的数据项？').then(function () {
        return feedbackRemove(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('article/work/export', {
        ...this.queryParams
      }, `work_${new Date().getTime()}.xlsx`)
    },
    openNewView(item) {
      window.open(`/expressDetails?id=${item.articleId}&docId=${item.docId}`, '_blank')
    },
  }
};
</script>
