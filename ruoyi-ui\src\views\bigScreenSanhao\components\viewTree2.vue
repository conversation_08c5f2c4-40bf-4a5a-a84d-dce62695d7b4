<template>
  <div :id="chartId" style="width: 100%; height: 100%"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "ViewTree",
  props: {
    treeData: {
      type: Array,
      required: true,
    },
    title: {
      type: String,
      default: "树状图",
    },
    visible: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      myChart: null,
      option: {},
    };
  },
  computed: {
    chartId() {
      return `viewChart_${this.title.replace(/\s+/g, "_")}`;
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
  },
  watch: {
    treeData: {
      handler() {
        this.initChart();
      },
      deep: true,
    },
    visible(newVal) {
      if (newVal && this.myChart) {
        this.$nextTick(() => {
          this.myChart.resize();
        });
      }
    },
  },
  methods: {
    initChart() {
      this.$nextTick(() => {
        let chartDom = document.getElementById(this.chartId);
        if (!chartDom) return;

        if (this.myChart) {
          this.myChart.dispose();
        }

        this.myChart = echarts.init(chartDom);

        this.option = {
          tooltip: {
            trigger: "item",
            formatter: "{b}",
            triggerOn: "mousemove",
            backgroundColor: "#52aced40", // 设置背景颜色
            textStyle: {
              color: "#333",
              color: "#fff",
              fontSize: 14,
            },
            borderColor: "#52aced",
          },
          series: [
            {
              type: "tree",
              data: this.recursionFun(this.treeData),
              initialTreeDepth: 3, //默认树展开的层数
              left: "7%",
              right: "23%",
              top: "1%",
              bottom: "2%",
              symbolSize: [90, 30], //设置框的大小
              symbol: "rect", // 节点标记形状
              edgeShape: "polyline",
              orient: "LR", //树整体的方向horizontal横向 vertical竖向
              expandAndCollapse: true,
              roam: true,
              itemStyle: {
                borderColor: "#333",
                borderWidth: 0.1,
                overflow: "truncate",
              },
              //lable 设置含有子节点的样式
              label: {
                show: true,
                position: "inside",
                textStyle: {
                  fontSize: 13,
                  color: "#fff",
                },
                verticalAlign: "middle",
                align: "center",
                overflow: "truncate",
                height: 120,
                width: 140,
              },
              leaves: {
                // 设置末节点的样式
                label: {
                  position: "inside",
                  color: "#fff",
                  verticalAlign: "middle",
                  align: "center",
                  overflow: "truncate",
                  height: 120,
                  width: 140,
                },
              },
              lineStyle: {
                color: "#7b7b7b", //连接线的颜色
                width: 1,
                type: "dashed",
              },
              animationDurationUpdate: 0,
            },
          ],
        };

        this.myChart.setOption(this.option);

        // 添加点击事件
        this.myChart.on("click", (params) => {
          console.log("点击了节点:", params);
          if (params && params.data && params.data.sn) {
            console.log(params.data.sn);
            this.$emit("openNewView", params.data);
          }
        });

        // 响应式调整
        window.addEventListener("resize", () => {
          this.myChart.resize();
        });
      });
    },
    recursionFun(data) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].type == "level0") {
          data[i].symbol = "";
          data[i].symbolSize = [0, 0];
          data[i].lineStyle = {
            color: "#7b7b7b",
            width: 0,
            type: "dashed",
          };
          data[i].name = "";
        } else if (data[i].type == "empty") {
          data[i].symbol = "";
          data[i].symbolSize = [0, 0];
        } else if (data[i].type == "level1") {
          data[i].symbol =
            "image://data:image/png;base64,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";
          data[i].symbolSize = [160, 160];
          data[i].label = {
            show: true,
            position: "inside",
            formatter: function (params) {
              const text = params.name || "";
              const maxLength = 4; // 每行最大字符数

              if (text.length <= maxLength) {
                return text;
              }

              const line1 = text.substring(0, maxLength);
              const line2 = text.substring(maxLength);
              return line1 + "\n" + line2;
            },
            textStyle: {
              lineHeight: 16,
            },
          };
        } else if (data[i].type == "level2-1") {
          data[i].symbol =
            "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAAAgCAYAAACvgw7DAAAAAXNSR0IArs4c6QAACgpJREFUaEPtWj2SXEUT7H4TgVZYOMiFCxBYSCaYiFNwBG4AN8CCoyA8AnNXOHAB4ARgIWTMa6j8qarehROAgvh2dua9t9PZVVmZ2d98+6u7T+eYX44x3hr//xtjrl/WOL6YT77+4efX3/34znr1as6xxjrXmuOcY6011hp8fY54jZ9jrRn/neeK6/H+WHOMNWZdU9fjGfGoeG5cF5fHc/AobQVe8DO8wJP51pxrzInP4kkjXuLXeP+Y8TnuPo45xjHmcfDeefi6seaBR6xx8Fm4/xgLL+Nb8Xe8/+bNeOPD93+fT756uf789mWsdK51xsf44gHQGCceN+L1usbdgVx87bhWwMV6gRTei6+dQAK4uD4wEHBEOO7B44QAvhMWxLfWFBgCJxcZF8aTuEB8Wyw8gJrHER8soGkA5xG/B1AToK1275zjjPsIYt5z8/HTIWDu+M0XgfDOo3pmFFCAcPKrx+LxdF/nSiIo6wRwriRWh6ulqsf7r0rCavKfKiYKhDgLCO06a6vv8uxVwq0NIHBfVBJQP+I1qi5AN3AByjmOFbihosYcN88/mATmxd0YwA4VgR1n+cd7bZHnFfsJYFAdrBaDFtc+qJqsIlaI7mOfnLHxhCT+DEvSi8YLVAaLKBbEReJ3tE5vKbQGr499w6UCBxWBqmLbqM0SILUVwb6sfwBGIGCX284nd5BrCoD4M7xHnBOgxupVXW6lxjEdaNaC4UiG6WzD/i9eQY9pkbHA7XMturiIgCTnREUccdER3xCctOYBquGzCN7Nc7dSVAzKnaQbPxsJZ5uplQwMq2mwVVxJbh3yiIB0a5nEq9VIumixwkWVgB9/Twj9BBewSuKHdzkJGW0SFQM+6a1W4F3ETSRnA6Fr1wJoAcwzccyLO5Crdm+xhUS8OZGqZcA5+BrmHXFPJ15UDqZY1DYrTdOJ7Rr8FdjGdeA+VVlgwS/oSuGd4A1e50ohz6jd4m1yiNqmCDoJmyTLKsJU4x/W81xpDZhbjdesmsHFgwjalBGRgmOunEAnFg9S1gQiWbNicnKBWwIcMCum3j7GgQsQQmcCJ/6P26WItBaPXTdoqhbdpzYDz4Jr2ghnS23vESxc01opgBHRasRyqgKBaBeO1yLcsc6rJhY+JwCQKiTUdV6Li8A58a+AhR5gFekzaxnzibRIJ0zpE+0sF5y8EhhdPMUIMPjDhI1F39Mx/XeCEs/bKiZHrIh3nBAw2VJYxbq6skS6sdC4DiCwsqSDDDTaUpwi0hbA5BaAlfzLioEmmXPGJy5ztEz8w/j17qYwo9g7NNrHBUMstiq1DSeTuCd4yqNb5HxcXDHr5vlTjuvXL27ZDqlBtGAslHyACtCtqBaoqBPjdyNeCsIBEAkSW4wqd451RXWJc9r0QqlxYquFUr1qshRIboucSuQZVciufA+DQE4BeUvQgcekYeZBxYBWSvK9hbrlzgah2gKIc4JHUBk5xs0t1jBefLacOQbbZj5KXdR5iTMDxCJQKOQa+YIOtNt9cWgzLdbSPghaE8tSPwFlKyV3aTJtGxHPK4755o6jGq0jJdt/shU0uZpIc/tI9ndPJZC7x9o5p/ROkbbE3a5DCJBlvycJPBLAKmBq2iTRQs1GO1FBW+E2Is6JVOp3ayXKf5JstAh2UB5IY1dt8WBMe7pokiWJazpt6hntZ5+EVvToBwnLY1qdknjhZQCCJf5eKaommUBXm9Su2grX1Di3DooKwkNhCQzyI7cSOMZAjKvaAT0g87gpYnsit17xBYmbyheiD6BsajjAoIvPdko1TUHHqQQwCIBLPXUHq6ar3pourCyDtIG1oAwlDjm1QvCRV0oNX9bNJyLfPwkMHLKEW5lAaZLcaRFud9HhUVPAnZxc3WulhunkG60LqdRGOArGFA/TB5+UFdMAMzFbe6R/UmuxOhQtqPLAUxCEAmNeYA1K4MlEfvLMJvJWI5VxA6OG2FnrF9mErAY6bJkk5ThpKnPCdd1TrznJOKVQiTKt2BvrDOiPU+KscYzymQucTqlXm0VMHLYkK6qJu3jvQgsSU0m+CcsgiEnM95QvJ88RPsna44xFNDV8T+SRbLM17LZTCCKCiCpgFdEk2w4gv2F4JavglMELJuE4cJqhM/D75oeqYnIMyzhSC9Fr5XiXfQjhh7+MakEmI46J5z0ApiIGEmYAJEXbFlVAOOFjEAWRJ35R0heEilEvy8BWqrxGWY2DBmQ85YsgxnYfU75IvolRAzKG0CsiVSpBei5HDknKUTUoqkBD/qqR8Dzmo3LXt90payGU9KlOK264tzD7ohZtbh7p6razai4n32IHCkWRTEYN92MFJnUOn1ANoAsRNQrKCra0T1wX9HLPL5nYGVx50s2jlK/I12M6kzz0kaJMxJs0lGqZLdpUUudJA47CdfRDJ+NRG0s77i3pYwTLL08SziylOWGZSvgihRVI5jxxyDu0BJXrZiRRugf3lJ6JLMHC8IFXau0DclyIIpIkndc4rix+qZZzYO6RvPFOEC3azkmgkxZmwhWIO6VTatdD8I1Qe3QJSc8sJiPQ3lomXKV65JSoIhIyg/EHJpLKFwE4AnGHTN0U9miBLjrKP6gtnTamL/xVtlslex7N8k+OSBlPa4edVrliSM3cySivsgW4xWGT8hYZxu2EoOmdezrIlsPxRE2mpnzvcvpQ7cIEZmx57/jkX45JBEZpE49trMkRQz9K0XtMdBXHkL29mz46cbJvnsgwCuMbXIMxzTCLpwScXhU7JDk3USerIU6DCT2O+ShPCSLBi6MS0EiMWCpTmjuNbJpLnhAUbyhEj3lCHsnxW/dnBiz9QtMYuQa69GxHJTLkaCuQKqMDBuKoMfieLcdVfrMrYQffymh0xNITPwXfmk4e7RjnW+YrmY7dLWFXB20cyf33NJuwxYNWgupZOYuDbt1nUeczJZGxugWCgNVCtaukVfmMzook2FLPbCYyTwAe5MDNmTcv5cM2eKWMPGMq9eOTTchpusjPFAH3MyKKOwfZXHxPAffED8uOSRWUoZ/gNAgXHhUQFMcyIBWdIPZqicurSnwyKRXsKHRL9yrGKG3DZ1+kg/YceTtw4xTKHScZ+/SQoXafRjltqj1yeuVk2oDCuI6ttLvWYVLyLQM6LIIvK7gS5+zju6LIOpWsaCHPpNpRbapoHJ9sE4wTLU8J2kmkc5hqGR7H8vRgX/QWg7ajEe6xnDPlvrwQuUcWQKGUBGGdDiQBW8e4peq0AIaSJ4z4CfB4sKZxmyYUlqm0SqV6PtPW2VJGHBmeD1fMb6+//+mt8cerPHjvB/udb4qHaufb4X4685o2PrfGqALAAU4sg/kPXIT1S+kY1o48DnEkIM07bTxCgCTYmOu21J9xpj6HVaijWh7P6sAtfjx+vB599N6vk/83kOPzMda7XUX8Z1+v8fv1mJ/9Be80xdabrGphAAAAAElFTkSuQmCC";
          data[i].symbolSize = [80, 40];
          data[i].label = {
            width: 60,
            height: 40,
          };
        } else if (data[i].type == "level2-2") {
          data[i].symbol =
            "image://data:image/png;base64,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";
          data[i].symbolSize = [80, 40];
          data[i].label = {
            width: 60,
            height: 40,
          };
        } else if (data[i].type == "level2-3") {
          data[i].symbol =
            "image://data:image/png;base64,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";
          data[i].symbolSize = [80, 40];
          data[i].label = {
            width: 60,
            height: 40,
          };
        } else if (data[i].type == "level3") {
          data[i].symbol =
            "image://data:image/png;base64,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";
          data[i].symbolSize = [140, 35];
          data[i].label = {
            width: 120,
            height: 35,
          };
        } else if (data[i].type == "level4") {
          data[i].symbol =
            "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHEAAAAvCAYAAADU+iVXAAAE2klEQVR4nO2cS44jRRCGI/6ITLv6sYAlB0CaE3ABdmy4C4fgPtwAiTViMXvYsUIjzXS3XZXxYJFly00PK3ADRX5SynKqXLL9KUqVpfyDv/khU5ko6Tl7IfrpN6LvfqbBvxwlIrrHi/msyPgMMb9he0+ZLSmdMp0o/+h78LfDTMzCxELMhRmVgR0z9sQsl0e+XYi0gujXD0Tf/9InM/wxw58y/JARc2bMmdEywyjTM4fEa8O8SmQoMwozdgzsGDIx5IYht5fH6+WbDP8Qbg/p9hDph3Q7ZHiXGGGUYTkq8eowMRNDGatEyI5FJ0TMLGkgCobcn44/S8zwx3B7CG/v09tDuD1F2CHdjhG+ZIZRhCdF/DM/7f8DE0CAMEMBqSy6R/hCoo0pg4gIRDhVpPYPUWT4U7o9dIHtQ1h7dG+H9HYMtyXDWmZ4Zg6JV4aZwQxhaEnRylEWkmhE6SCiJEYyKzP2RCxrJaZl+CHSD+H2FNYe3ZbHsPnJbTmE2xLeWmY4xajEqwOAGQIpBaJV1I3OxcNCkMKBXUKORHK7VmJ6Rszpdoiwg3s7hM1P3uZHs/mQtizuy5LhnkPi1WEADBGRWkNrOwtkBgGFXWqyTBkxnyUSpWfGnOFzuh3T29FtOZjNh2jHo9s8hy0trFnmkHhtmAFo0VR3yQhbJxmiCakJ3a+rhoWISL/9kegNtXfrMqJF+NIvn7akLYvbPNv8dAxbWnizCB8SrwwggBeFmxN1qQEt4baw+ILVFWU2ovXGJnslWkZYf7UW3pr7svQKXJq14xK2eA6JVycggHooEYWoOETYS80o7ZknSic6LTFyfRpzWkbkOsI9rFl4s7DFwxbLsCHxyjAUREQBsbBm0Hp2QhHd08kZn9eJ2aHMpIjMDIqIjIjMiAiPDI8Mi3E5vT4gogzp/3t2DxTdS1JErr5Oj0BfPjUd/OcYEjfAkLgBhsQNMCRugCFxAwyJG2BI3ABD4gYYEjfAkLgBhsQNMCRugCFxAwyJG2BI3ABD4gYYEjfAkLgBhsQNMCRugCFxA6xbFrlDzEwA9z3/4B7sACAICBiKYf36MBQMASBg7h56yIbBBPDqi4iZ6CSRWfqArrm4PiACLQovCu37TTNk7Du9MgwBtAqkKLQoQ85OCOieTs7onE9co8U9maoMLZBSRGpNdYebK607ksfm4asDCLrAWqC1iNQKKYWh5ZknupC4hvsLMwogNUUrRGtobbKmoEJURirqdTiloqC1iO52rLVidQJIPbn68vPyydd3p0pk1B7ulx2L7jnKcgo22npShwi0jnziK3CZT2StVXU3idaJpexZdM+QHTN2dFmJvTMD1nC/92jxRbAxoIW91JEUfiVeJIXrBN3diJQJ0IlFJwZ2+UwiY8+QCREziTZaI1OnYGPP7JeR2X8lLjP7EK0sZS9SJmi5hegNWCaGTESsRBd3pwy5YUljyujLCBYCSkIqy+ie8Zp8tHsGdILoDUu5Y9E7htzkus4/t0BhyC2I+jKCGATp2XDoHqOPzavyp31sWCYWvUOXeG5I9KwZEUPuQdTbawR2yTKNjlKvz1/qKEXUK5IZ+4Qc17Zgy+jt9tq87O321Ru9/3Ri9SS+PPKdrxLfLi9OIkRy28fl9FW/+eBjZB9fOFFJosuw/UnH739RVVDTUJBcAAAAAElFTkSuQmCC";
          data[i].symbolSize = [360, 35];
          // data[i].symbolOffset = [-90, 0];
          data[i].label = {
            width: 360,
            height: 35,
          };
        } else {
          data[i].symbol =
            "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHEAAAAvCAYAAADU+iVXAAAE2klEQVR4nO2cS44jRRCGI/6ITLv6sYAlB0CaE3ABdmy4C4fgPtwAiTViMXvYsUIjzXS3XZXxYJFly00PK3ADRX5SynKqXLL9KUqVpfyDv/khU5ko6Tl7IfrpN6LvfqbBvxwlIrrHi/msyPgMMb9he0+ZLSmdMp0o/+h78LfDTMzCxELMhRmVgR0z9sQsl0e+XYi0gujXD0Tf/9InM/wxw58y/JARc2bMmdEywyjTM4fEa8O8SmQoMwozdgzsGDIx5IYht5fH6+WbDP8Qbg/p9hDph3Q7ZHiXGGGUYTkq8eowMRNDGatEyI5FJ0TMLGkgCobcn44/S8zwx3B7CG/v09tDuD1F2CHdjhG+ZIZRhCdF/DM/7f8DE0CAMEMBqSy6R/hCoo0pg4gIRDhVpPYPUWT4U7o9dIHtQ1h7dG+H9HYMtyXDWmZ4Zg6JV4aZwQxhaEnRylEWkmhE6SCiJEYyKzP2RCxrJaZl+CHSD+H2FNYe3ZbHsPnJbTmE2xLeWmY4xajEqwOAGQIpBaJV1I3OxcNCkMKBXUKORHK7VmJ6Rszpdoiwg3s7hM1P3uZHs/mQtizuy5LhnkPi1WEADBGRWkNrOwtkBgGFXWqyTBkxnyUSpWfGnOFzuh3T29FtOZjNh2jHo9s8hy0trFnmkHhtmAFo0VR3yQhbJxmiCakJ3a+rhoWISL/9kegNtXfrMqJF+NIvn7akLYvbPNv8dAxbWnizCB8SrwwggBeFmxN1qQEt4baw+ILVFWU2ovXGJnslWkZYf7UW3pr7svQKXJq14xK2eA6JVycggHooEYWoOETYS80o7ZknSic6LTFyfRpzWkbkOsI9rFl4s7DFwxbLsCHxyjAUREQBsbBm0Hp2QhHd08kZn9eJ2aHMpIjMDIqIjIjMiAiPDI8Mi3E5vT4gogzp/3t2DxTdS1JErr5Oj0BfPjUd/OcYEjfAkLgBhsQNMCRugCFxAwyJG2BI3ABD4gYYEjfAkLgBhsQNMCRugCFxAwyJG2BI3ABD4gYYEjfAkLgBhsQNMCRugCFxA6xbFrlDzEwA9z3/4B7sACAICBiKYf36MBQMASBg7h56yIbBBPDqi4iZ6CSRWfqArrm4PiACLQovCu37TTNk7Du9MgwBtAqkKLQoQ85OCOieTs7onE9co8U9maoMLZBSRGpNdYebK607ksfm4asDCLrAWqC1iNQKKYWh5ZknupC4hvsLMwogNUUrRGtobbKmoEJURirqdTiloqC1iO52rLVidQJIPbn68vPyydd3p0pk1B7ulx2L7jnKcgo22npShwi0jnziK3CZT2StVXU3idaJpexZdM+QHTN2dFmJvTMD1nC/92jxRbAxoIW91JEUfiVeJIXrBN3diJQJ0IlFJwZ2+UwiY8+QCREziTZaI1OnYGPP7JeR2X8lLjP7EK0sZS9SJmi5hegNWCaGTESsRBd3pwy5YUljyujLCBYCSkIqy+ie8Zp8tHsGdILoDUu5Y9E7htzkus4/t0BhyC2I+jKCGATp2XDoHqOPzavyp31sWCYWvUOXeG5I9KwZEUPuQdTbawR2yTKNjlKvz1/qKEXUK5IZ+4Qc17Zgy+jt9tq87O321Ru9/3Ri9SS+PPKdrxLfLi9OIkRy28fl9FW/+eBjZB9fOFFJosuw/UnH739RVVDTUJBcAAAAAElFTkSuQmCC";
          data[i].symbolSize = [160, 40];
          data[i].label = {
            width: 160,
            height: 40,
          };
        }
        if (data[i].children && data[i].children.length > 0) {
          this.recursionFun(data[i].children);
        }
      }
      return data;
    },
  },
};
</script>

<style lang="scss" scoped></style>
