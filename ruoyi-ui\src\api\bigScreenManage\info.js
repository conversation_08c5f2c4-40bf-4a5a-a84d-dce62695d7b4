import request from "@/utils/request";

// 查询大屏 科技安全监控 实体信息列表
export function listInfo(query) {
  return request({
    url: "/screen/info/list",
    method: "get",
    params: query,
  });
}

// 查询大屏 科技安全监控 实体信息详细
export function getInfo(id) {
  return request({
    url: "/screen/info/" + id,
    method: "get",
  });
}

// 新增大屏 科技安全监控 实体信息
export function addInfo(data) {
  return request({
    url: "/screen/info",
    method: "post",
    data: data,
  });
}

// 修改大屏 科技安全监控 实体信息
export function updateInfo(data) {
  return request({
    url: "/screen/info/edit",
    method: "post",
    data: data,
  });
}

// 删除大屏 科技安全监控 实体信息
export function delInfo(id) {
  return request({
    url: "/screen/info/remove",
    method: "post",
    data: id,
  });
}
