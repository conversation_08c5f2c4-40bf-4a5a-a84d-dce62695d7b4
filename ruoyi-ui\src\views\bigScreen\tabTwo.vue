<template>
  <el-row class="tabTwo">
    <!-- 左侧 -->
    <el-col :span="8">
      <el-row class="left">
        <!-- 上半部分 -->
        <el-row class="left-top" type="flex" justify="space-between">
          <el-col
            :class="[{ active: activeKey === 1 }]"
            @click.native="
              () => {
                activeKey = 1;
                mapFun();
              }
            "
          >
            <el-row>
              <img src="@/assets/bigScreen/zhineng.png" alt="" />
            </el-row>
            <el-row style="margin-top: 10px">人工智能</el-row>
          </el-col>
          <el-col
            :class="[{ active: activeKey === 2 }]"
            @click.native="
              () => {
                activeKey = 2;
                graphFun();
              }
            "
          >
            <el-row>
              <img src="@/assets/bigScreen/wangluo.png" alt="" />
            </el-row>
            <el-row style="margin-top: 10px">网络安全</el-row>
          </el-col>
        </el-row>
        <!-- 热词 -->
        <el-row class="left-center">
          <el-row class="tempTitle" style="margin-bottom: 10px">
            <img
              src="@/assets/bigScreen/section-title-info.png"
              alt=""
              style="width: 100%; margin-top: 26px"
            />
            <span class="title-linear">热词</span>
          </el-row>
          <div
            id="myChart"
            ref="myChart"
            style="
              width: 100%;
              height: 246px;
              background: linear-gradient(to bottom, #1c0bda, #060d5b);
              padding: 12px 25px;
            "
          >
            <keyWordVue :wordValueList="wordValueList" />
          </div>
        </el-row>
        <el-row class="left-bottom">
          <el-row style="margin-bottom: 10px">
            <img
              src="@/assets/bigScreen/section-title-info.png"
              alt=""
              style="width: 100%; margin-top: 26px"
            />
            <span class="title-linear">实体</span>
            <el-row type="flex" :class="['info-tags']">
              <el-row
                :class="[{ active: tagActiveKey === 1 }]"
                @click.native="tagActiveKeyFun(1)"
                >品牌</el-row
              >
              <el-row
                :class="[{ active: tagActiveKey === 2 }]"
                @click.native="tagActiveKeyFun(2)"
                >国家</el-row
              >
            </el-row>
          </el-row>

          <div class="left-bottom-list">
            <div
              v-for="(item, index) in bottomList[
                tagActiveKey === 1 ? 'entityList' : 'country'
              ]"
              :key="index"
              ref="imgSingleRef"
            >
              <div @click="showOpenFun(item.id)" class="img-info" ref="imgRef">
                <div class="img">
                  <img :src="baseUrl + item.imgUrl" alt="" />
                </div>
                <div class="image-intro">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </el-row>
      </el-row>
    </el-col>
    <!-- 中间 -->
    <el-col :span="16">
      <div style="height: 100%; width: 100%">
        <mapVue v-if="activeKey === 1" :mapList="mapList" />
        <graphEchart v-if="activeKey === 2" :graphList="graphList" />
      </div>
    </el-col>
    <!-- 右边 -->
    <!-- <el-col></el-col> -->

    <goodsInfo :open.sync="open" :id="id" />
  </el-row>
</template>
<script>
import { demo1 } from "./demo";
import mapVue from "./components/map.vue";
import graphEchart from "./components/graphEchart.vue";
import goodsInfo from "./components/goodsInfo.vue";
import keyWordVue from "./components/keyWord.vue";
import {
  listWord,
  groupInfo,
  listMap,
  atlasNode,
} from "@/api/bigScreen/index.js";
function unique(arr) {
  return Array.from(new Set(arr));
}

function findElementIndex(arr, element) {
  return arr.indexOf(element);
}
export default {
  data() {
    return {
      activeKey: 1,
      tagActiveKey: 1,
      wordValueList: [],
      //左侧商品信息
      bottomList: {
        entityList: [],
        country: [],
      },
      uuid: "",
      graphList: {},
      open: false,
      id: null,
      baseUrl: process.env.VUE_APP_BASE_API,
      mapList: [],
    };
  },
  components: { mapVue, graphEchart, goodsInfo, keyWordVue },
  created() {
    this.init();
  },
  mounted() {
    window.addEventListener("resize", () => {
      this.resizeFun();
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", () => {
      this.resizeFun();
    });
  },
  methods: {
    resizeFun() {
      this.$refs.imgSingleRef.forEach((item) => {
        item.style.transform = `scale(${1 / (window.innerWidth / 1920)},${
          1 / (window.innerHeight / 1080)
        })`;
      });
      this.$refs.imgRef.forEach((item) => {
        item.style.transform = `scale(${window.innerWidth / 1920})`;
      });
    },
    init() {
      //左二
      listWord().then((res) => {
        res.rows.forEach((item) => {
          item.keyWord = item.word;
          item.wordFrequency = item.heat;
        });
        this.wordValueList = res.rows;
      });
      //左三
      groupInfo().then((res) => {
        this.bottomList = {
          entityList: res.data[1],
          country: res.data[2],
        };
        this.$nextTick(() => {
          this.resizeFun();
        });
      });
      //地图
      listMap().then((res) => {
        this.mapList = res.rows;
      });
    },
    //实体
    tagActiveKeyFun(index) {
      this.tagActiveKey = index;
    },
    mapFun() {
      listMap().then((res) => {
        this.mapList = res.rows;
      });
    },
    graphFun() {
      atlasNode().then((res) => {
        let colorList = [];
        res.data.nodeList.map((item) => {
          colorList.push(item.nodeColor);
        });
        colorList = unique(colorList);

        this.graphList = {
          color: colorList,
          nodes: res.data.nodeList.map((item) => {
            return {
              id: String(item.id),
              name: item.nodeName,
              symbolSize: Number(item.nodeValue),
              x: Number(item.abscissa),
              y: Number(item.ordinate),
              value: Number(item.nodeValue),
              category: findElementIndex(colorList, item.nodeColor),
              label: {
                show: true,
                fontSize: "14px",
                color: "#fff",
              },
            };
          }),
          links: res.data.relationList.map((item) => {
            return {
              source: String(item.originNodeId),
              target: String(item.targetNodeId),
              lineStyle: {
                width: 1.5,
                opacity: 1,
              },
            };
          }),
          categories: colorList.map((item) => {
            return {
              name: item,
            };
          }),
        };
      });
    },
    showOpenFun(id) {
      this.id = id;
      this.open = true;
    },
  },
};
</script>
<style lang="scss" scoped>
.tabTwo {
  height: 100%;
  font-family: "微软雅黑";
  > div {
    height: 100%;
  }
  .left {
    margin: 30px 0px 0 50px;
    height: 100%;
    display: flex;
    flex-direction: column;

    //上半部分
    .left-top {
      padding-top: 20px;
      text-align: center;
      font-size: 18px;
      font-family: "微软雅黑";
      font-weight: bold;
      color: #fff;
      > div {
        width: 282px;
        height: 244px;
        margin: 0 2px;
        border: 1px solid transparent;
        cursor: pointer;

        background: url("../../assets/bigScreen/enterbg-default.png") no-repeat
          0px 0px !important;
        background-size: 100% 100% !important;
      }
      .active {
        background: url("../../assets/bigScreen/enterbg-hover.png") no-repeat
          0px 0px !important;
        background-size: 100% 100% !important;
      }
      img {
        margin-top: 20px;
        height: 160px;
        width: 160px;
      }
    }

    .left-center {
      margin-top: 5px;
    }
    .left-bottom {
      margin-top: 5px;
      flex: 1;
      .info-tags {
        position: absolute;
        right: 0;
        bottom: 0;
        > div {
          color: #fff;
          font-weight: bold;
          padding: 10px 0;
          margin: 6px 22px 6px 0;
          font-size: 14px;

          cursor: pointer;
        }
        > div:first-child {
          margin-right: 15px;
        }
        .active {
          color: #1bdcff;
        }
      }
    }

    .left-bottom-list {
      display: flex;
      flex-wrap: wrap;
      background: linear-gradient(to bottom, #1c0bda90, #1b5bff90);
      margin-top: 15px;
      padding: 45px 20px 35px 20px;
      // justify-content: space-between;
      > div {
        width: calc(100% / 6);
        text-align: center;
        .img-info {
          margin: auto;
          position: relative;
          display: inline-block;
          cursor: pointer;
          .img {
            width: 80px;
            height: 80px;
            line-height: 80px;
            img {
              width: 80px;
              display: inline-block;
              vertical-align: middle;
            }
          }
        }

        .image-intro {
          width: 80px;
          margin: auto;
          color: #fff;
          text-align: center;
          padding-top: 6px;
          font-size: 14px;
        }
      }

      // > div > div:hover .image-intro {
      //   visibility: visible;
      //   opacity: 1;
      // }
    }
  }

  .title-linear {
    position: absolute;
    top: 30px;
    left: 100px;
    font-size: 24px;
    font-weight: bold;
    color: linear-gradient(to right, #fff, #fee4c4, #fec989);
    background: linear-gradient(to right, #fff, #fee4c4, #fec989);
    /* 使用渐变背景时，通常需要设置这些值以确保文字可读 */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}
</style>
<style>
/* .el-dialog__header {
  display: none;
} */
</style>
