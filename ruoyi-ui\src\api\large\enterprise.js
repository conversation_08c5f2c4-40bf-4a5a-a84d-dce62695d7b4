import request from '@/utils/request'

// 查询信通院大屏 企业列列表
export function listEnterprise(query) {
  return request({
    url: '/large/enterprise/list',
    method: 'get',
    params: query
  })
}

// 查询信通院大屏 企业列详细
export function getEnterprise(id) {
  return request({
    url: '/large/enterprise/' + id,
    method: 'get'
  })
}

// 新增信通院大屏 企业列

export function addEnterprise(data) {
  return request({
    url: '/large/enterprise',
    method: 'post',
    data: data
  })
}

// 修改信通院大屏 企业列

export function updateEnterprise(data) {
  return request({
    url: '/large/enterprise/edit',
    method: 'post',
    data: data
  })
}

// 删除信通院大屏 企业列

export function delEnterprise(data) {
  return request({
    url: '/large/enterprise/remove',
    method: 'post',
    data: data
  })
}
