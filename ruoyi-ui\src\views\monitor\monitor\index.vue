<template>
  <div class="app-container">
    <el-card style="margin-bottom: 20px">
      <div class="el-card---BOX">
        <el-card shadow="always">
          <div class="title">公众号数量</div>
          <div class="number">
            {{ padWithZeros(statisticsObj.weChatTotal || 0)
            }}<span class="unit">个</span>
          </div>
        </el-card>
        <el-card shadow="always">
          <div class="title" @click="openQueryList({ title: '停更数据量' })">停更数据量</div>
          <div class="number" @click="openQueryList({ title: '停更数据量' })">
            {{ padWithZeros(statisticsObj.disableTotal || 0)
            }}<span class="unit">个</span>
          </div>
        </el-card>
        <el-card shadow="always">
          <div class="title">平均发布周期</div>
          <div class="number">
            {{ padWithZeros(statisticsObj.frequencyAvg || 0)
            }}<span class="unit">天</span>
          </div>
        </el-card>
        <el-card shadow="always">
          <div class="title" @click="openQueryList({ title: '最长发布间隔' })">最长发布间隔</div>
          <div class="number" @click="openQueryList({ title: '最长发布间隔' })">
            {{ padWithZeros(statisticsObj.intervalDays || 0)
            }}<span class="unit">天</span>
          </div>
        </el-card>
        <el-card shadow="always">
          <div class="title" @click="openQueryList({ title: '近5日未发布文章的公众号' })">近5日未发布文章的公众号</div>
          <div class="number" @click="openQueryList({ title: '近5日未发布文章的公众号' })">
            {{ padWithZeros(statisticsObj.unpublishedTotal || 0)
            }}<span class="unit">个</span>
          </div>
        </el-card>
        <el-card shadow="always">
          <div class="title" @click="openQueryList({ title: '公众号异常数量' })">公众号异常数量</div>
          <div class="number" @click="openQueryList({ title: '公众号异常数量' })">
            {{ padWithZeros(statisticsObj.exceptionTotal || 0)
            }}<span class="unit">个</span>
          </div>
        </el-card>
      </div>
    </el-card>
    <el-card>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
        <el-form-item label="公众号名称" prop="wechatName">
          <el-input style="width: 160px;" v-model="queryParams.wechatName" placeholder="请输入公众号名称" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="日期间隔" prop="intervalDays">
          <el-input style="width: 120px;" v-model="queryParams.intervalDays" placeholder="请输入间隔" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="官方发布频次" prop="publishFrequency">
          <el-input style="width: 120px;" v-model="queryParams.publishFrequency" placeholder="请输入频次" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="发布时间" prop="lastArticlePublishTime">
          <el-date-picker clearable v-model="queryParams.lastArticlePublishTime" type="daterange"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            style="width: 240px">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="采集时间" prop="lastArticleCollectionTime">
          <el-date-picker clearable v-model="queryParams.lastArticleCollectionTime" type="daterange"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            style="width: 240px">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否异常" prop="isException">
          <el-select style="width: 140px;" v-model="queryParams.isException" placeholder="请选择是否异常" clearable>
            <el-option :label="'异常'" :value="'1'" />
            <el-option :label="'正常'" :value="'0'" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table ref="tableRef" v-loading="loading" :data="monitorList" @selection-change="handleSelectionChange"
        @sort-change="handleSortChange">
        <el-table-column width="80" label="序号" align="center" prop="id" />
        <el-table-column label="唯一标识号" align="center" prop="wechatBiz" show-overflow-tooltip width="200" />
        <el-table-column label="公众号名称" prop="wechatName" show-overflow-tooltip min-width="100">
          <template slot-scope="scope">
            <span style="color: #40a9ff; cursor: pointer" @click="openWachatList(scope.row)">
              {{ scope.row.wechatName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="最新发布的一篇文章" prop="lastArticleTitle" show-overflow-tooltip min-width="200" >
        <template slot-scope="scope">
          <span style="color: #40A9FF;;cursor: pointer;" @click="openNewView(scope.row)">{{ scope.row.lastArticleTitle }}</span>
        </template>
      </el-table-column>
        <el-table-column label="文章发布时间" align="center" prop="lastArticlePublishTime" width="160" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.lastArticlePublishTime, "{y}-{m}-{d} {h}:{i}:{s}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="文章采集时间" align="center" prop="lastArticleCollectionTime" width="160" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.lastArticleCollectionTime, "{y}-{m}-{d} {h}:{i}:{s}") }}</span>
          </template>
        </el-table-column>
        <el-table-column sortable label="官方发布频次" align="center" prop="publishFrequency" show-overflow-tooltip
          width="140" />
        <el-table-column sortable label="日期间隔" align="center" prop="intervalDays" show-overflow-tooltip width="100" />
        <el-table-column label="是否异常" align="center" prop="isException" show-overflow-tooltip width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.isException == "1" ? "是" : "否" }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="统计时间" align="center" prop="statisticalTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.statisticalTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="80">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['monitor:monitor:edit']">设置</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </el-card>

    <!-- 添加或修改微信公众号采集监控对话框 -->
    <el-dialog style="margin-top: 30vh !important;" :title="title" :visible.sync="open" width="400px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="发布频次" prop="frequency">
          <el-input style="width: 100px;margin-right: 30px;" v-model="form.frequency" placeholder="请输入官方发布频次" />天
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="title2" :visible.sync="open2" width="1350px" append-to-body :close-on-click-modal="false">
      <monitorList v-if="open2" @openWachatList="openWachatList" :title="title2"></monitorList>
    </el-dialog>

    <el-dialog :title="title1" :visible.sync="open1" width="1150px" append-to-body :close-on-click-modal="false">
      <sourceList v-if="open1" :id="listId"></sourceList>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMonitor,
  delMonitor,
  editFrequency,
  getStatistics,
} from "@/api/monitor/monitor";
import sourceList from "./sourceList.vue";
import monitorList from "./monitorList.vue";

export default {
  name: "Monitor",
  components: { monitorList, sourceList },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      statisticsObj: {},
      // 微信公众号采集监控表格数据
      monitorList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        wechatBiz: null,
        wechatName: null,
        intervalDays: null,
        publishFrequency: null,
        lastArticlePublishTime: null,
        lastArticleCollectionTime: null,
        lastArticleTitle: null,
        statisticalTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        wechatBiz: [
          {
            required: true,
            message: "公众号唯一标识不能为空",
            trigger: "blur",
          },
        ],
        wechatName: [
          {
            required: true,
            message: "微信公众号名称不能为空",
            trigger: "blur",
          },
        ],
        intervalDays: [
          { required: true, message: "日期间隔不能为空", trigger: "blur" },
        ],
        publishFrequency: [
          { required: true, message: "官方发布频次不能为空", trigger: "blur" },
        ],
      },
      title1: "",
      open1: false,
      listId: null,
      title2: "",
      open2: false,
    };
  },
  created() {
    this.getStatisticsFun();
    this.getList();
  },
  methods: {
    getStatisticsFun() {
      getStatistics().then((res) => {
        this.statisticsObj = res.data;
      });
    },
    /** 查询微信公众号采集监控列表 */
    getList() {
      this.loading = true;
      let queryParams = JSON.parse(JSON.stringify(this.queryParams));
      if (
        queryParams.lastArticlePublishTime &&
        queryParams.lastArticlePublishTime.length
      ) {
        [queryParams.publishTimeStart, queryParams.publishTimeEnd] =
          queryParams.lastArticlePublishTime;
        delete queryParams.lastArticlePublishTime;
      }
      if (
        queryParams.lastArticleCollectionTime &&
        queryParams.lastArticleCollectionTime.length
      ) {
        [queryParams.collectionTimeStart, queryParams.collectionTimeEnd] =
          queryParams.lastArticleCollectionTime;
        delete queryParams.lastArticleCollectionTime;
      }
      listMonitor(queryParams).then((response) => {
        this.monitorList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    openWachatList(row) {
      this.title1 = row.wechatName;
      this.listId = row.wechatBiz;
      this.open1 = true;
    },
    openQueryList(row) {
      this.title2 = row.title;
      this.open2 = true;
    },
    cancel1() {
      this.open1 = false;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        wechatBiz: null,
        wechatName: null,
        intervalDays: null,
        publishFrequency: null,
        lastArticlePublishTime: null,
        lastArticleCollectionTime: null,
        lastArticleTitle: null,
        statisticalTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 50
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {}
      this.resetForm("queryForm");
      this.$refs["tableRef"].clearSort()
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加微信公众号采集监控";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.form = { sn: row.wechatBiz, frequency: row.publishFrequency };
      this.open = true;
      this.title = "调整频次";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let queryParams = new FormData()
          queryParams.append('sn', this.form.sn)
          queryParams.append('frequency', this.form.frequency)
          editFrequency(queryParams).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "monitor/monitor/export",
        {
          ...this.queryParams,
        },
        `monitor_${new Date().getTime()}.xlsx`
      );
    },
    padWithZeros(num) {
      return `${num}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    handleSortChange({ column, prop, order }) {
      let obj = {
        'ascending': 'asc',
        'descending': 'desc'
      }
      if (prop == 'publishFrequency') {
        this.queryParams.publishFrequencySort = order ? obj[order] : ''
        this.queryParams.intervalDaysSort = ''
      } else if (prop == 'intervalDays') {
        this.queryParams.intervalDaysSort = order ? obj[order] : ''
        this.queryParams.publishFrequencySort = ''
      }
      this.getList();
    },
    openNewView(item) {
      window.open(`/expressDetails?id=${item.lastArticleId}&docId=${item.lastArticleId}`, '_blank')
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep .el-dialog__body {
  padding: 10px 0px;
}

::v-deep .el-card__body {
  padding: 10px 10px;
}

.el-card---BOX {
  height: 120px;
  display: flex;
  justify-content: space-around;

  .el-card {
    width: 16%;

    ::v-deep .el-card__body {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .title {
        font-size: 14px;
        margin-bottom: 20px;
        cursor: pointer;
      }

      .number {
        position: relative;
        font-size: 32px;
        cursor: pointer;

        span {
          font-size: 14px;
          position: absolute;
          bottom: 0;
        }
      }
    }
  }
}

// ::v-deep .el-dialog:not(.is-fullscreen) {
//   margin-top: 30vh !important;
// }
</style>
