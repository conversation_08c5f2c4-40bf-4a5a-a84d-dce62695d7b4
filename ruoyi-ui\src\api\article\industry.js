import request from "@/utils/request";

// 查询行业列表
export function listIndustry(query) {
  return request({
    url: "/article/industry/list",
    method: "get",
    params: query,
  });
}

// 查询行业详细
export function getIndustry(id) {
  return request({
    url: "/article/industry/" + id,
    method: "get",
  });
}

// 新增行业
export function addIndustry(data) {
  return request({
    url: "/article/industry",
    method: "post",
    data: data,
  });
}

// 修改行业
export function updateIndustry(data) {
  return request({
    url: "/article/industry/edit",
    method: "post",
    data: data,
  });
}

// 删除行业
export function delIndustry(id) {
  return request({
    url: "/article/industry/remove",
    method: "post",
    data: id,
  });
}
