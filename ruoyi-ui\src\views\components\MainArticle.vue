<template>
  <div class="main" v-loading="loading">
    <div class="MainArticle" v-if="flag == 'Monitor'">
      <div class="TopBtnGroup">
        <div class="leftBtnGroup">
          <p>
            <el-checkbox
              v-model="checked"
              :isIndeterminate="isIndeterminate"
              @change="handleCheckAllChange"
              >全选</el-checkbox
            >
          </p>
          <!-- <p class="toolTitle"><i title="下载" class="icon-xiazai1" style="color:green" @click="downLoad"></i></p>  2023-9-4 沈老师  暂时注释 -->
          <p class="toolTitle">
            <i class="icon-shuaxin-copy" title="刷新" @click="Refresh"></i>
          </p>
          <p class="toolTitle">
            <i
              class="icon--_tianjiadaoku"
              title="添加到报告"
              @click="dialogVisible = true"
              v-hasPermi="['result:report:add']"
            ></i>
          </p>
        </div>
        <div>
          <el-checkbox
            v-model="showSummary"
            @change="(e) => (showSummary = e)"
            style="margin-right: 10px"
            >是否显示摘要</el-checkbox
          >
          <span style="font-size: 14px">排序方式:</span>&nbsp;
          <el-select v-model="SeachData.sortMode" size="mini">
            <el-option label="按发布时间倒序排序" :value="false"></el-option>
            <el-option label="按发布时间正序排序" :value="true"></el-option>
          </el-select>
        </div>
      </div>
      <el-checkbox-group
        v-model="checkedCities"
        @change="handleCheckedCitiesChange"
      >
        <div
          class="scollBox"
          @mousewheel="scrollChange"
          ref="scroll"
          style="height: calc(100vh - 405px)"
        >
          <div class="Articl" v-for="(item, key) in ArticleList" :key="key">
            <div class="Articl_left">
              <div class="ArticlTop">
                <p style="margin: 20px 0 5px 0; line-height: 20px">
                  <el-checkbox :label="item.id" :value="item.id">
                    {{ null }} </el-checkbox
                  >&nbsp;&nbsp;
                  <a href="#"
                    ><span style="padding: 0 10px 0 0"
                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span
                    ><span
                      class="title_Article"
                      @click="openNewView(item)"
                      v-html="changeColor(item.cnTitle || item.title)"
                    >
                    </span
                    >&nbsp;&nbsp;&nbsp;&nbsp;</a
                  >
                </p>
              </div>
              <div class="info_flex">
                <div
                  class="ArticlBottom"
                  v-if="item.sourceType !== '1' && item.sourceType !== '3'"
                  style="width: 100%"
                >
                  <div>
                    原文标题:
                    <span class="infomation">
                      {{ item.title }}
                    </span>
                  </div>
                </div>
                <div class="ArticlBottom">
                  <div>
                    类型:
                    <span class="infomation">
                      {{ typeHandle(item.sourceType) }}
                    </span>
                  </div>
                  <div>
                    媒体来源:
                    <span class="infomation">{{ item.sourceName }}</span>
                  </div>
                  <div v-if="item.author">
                    作者:
                    <span class="infomation" v-html="item.author"></span>
                  </div>
                  <div>
                    发布时间:
                    <span class="infomation">{{
                      formatPublishTime(
                        item.publishTime,
                        item.webstePublishTime
                      )
                    }}</span>
                  </div>
                  <!-- <span style="color: rgb(32, 126, 250);">相似文章</span> -->
                  <!-- <p>标签A</p> -->
                </div>
                <div class="ArticlBottom">
                  <div v-if="item.industryName">
                    所属行业:
                    <span class="infomation">{{ industryHandle(item) }}</span>
                  </div>
                  <div v-if="item.domain">
                    所属领域:
                    <span class="infomation">{{ domainHandle(item) }}</span>
                  </div>
                  <div>
                    原文链接:
                    <span
                      class="linkStyle"
                      @click="openNewView(item, 'link')"
                      style="color: #1889f3"
                    >
                      {{
                        typeHandle(item.sourceType) == "微信公众号"
                          ? item.shortUrl
                          : item.originalUrl
                      }}
                    </span>
                  </div>
                  <div>
                    大模型筛选:
                    <span class="infomation">
                      {{ getTechnologyLabel(item.isTechnology) }}
                    </span>
                  </div>
                </div>
              </div>
              <div
                class="ArticlMain"
                style="
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  word-break: break-all;
                "
                v-if="
                  showSummary &&
                  hasActualContent(item.cnSummary || item.summary)
                "
              >
                <span style="color: #9b9b9b">摘要：</span>
                <span
                  v-html="changeColor(getSafeSummary(item))"
                  @click="openNewView(item)"
                ></span>
              </div>
              <!-- <div class="imgBox" v-if="item.imgage">
                <img src="../../assets/images/priview.png" alt="加载失败了" />
                <img src="../../assets/images/priview.png" alt="加载失败了" />
                <img src="../../assets/images/priview.png" alt="加载失败了" />
                <img src="../../assets/images/priview.png" alt="加载失败了" />
                <img src="../../assets/images/priview.png" alt="加载失败了" />
                <img src="../../assets/images/priview.png" alt="加载失败了" />
              </div> -->
            </div>
            <div class="leftBtnGroup2">
              <p>
                <i
                  :class="{
                    'icon-hot2-copy':
                      item.isWhether == null || item.isWhether == 0,
                    'icon-hot2': item.isWhether == 1,
                  }"
                  title="热点"
                  @click="hotIncrease(item)"
                  v-hasPermi="['article:label:add']"
                ></i>
              </p>
              <p>
                <i
                  :class="{
                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,
                    'icon-jiaxingshoucangtianchong': item.favorites,
                  }"
                  title="收藏"
                  @click.passive="collect(item)"
                  v-hasPermi="['article:collection:add']"
                ></i>
              </p>
              <p>
                <i
                  class="icon--_tianjiadaoku"
                  title="添加到报告"
                  @click="separateAdd(item)"
                  v-hasPermi="['result:report:add']"
                ></i>
              </p>
              <p>
                <i
                  class="icon-biaoqian"
                  title="标签"
                  @click="tagHandler(item)"
                  v-hasPermi="['article:label:add']"
                ></i>
              </p>
              <!-- <p><i class="icon-fuzhi" title="复制" @click="copyText(item)"></i></p>   2023-9-4 沈老师 暂时注释 -->
            </div>
          </div>
          <el-empty
            :image-size="200"
            description="当前筛选条件下暂无数据"
            v-if="ArticleList == null || ArticleList.length == 0"
          ></el-empty>
        </div>
      </el-checkbox-group>
    </div>
    <div
      class="MainArticle"
      v-if="flag == 'MonitorUse' || flag == 'specialSubjectUse'"
    >
      <div class="TopBtnGroup">
        <div class="leftBtnGroup">
          <p>
            <el-checkbox
              v-model="checked"
              :isIndeterminate="isIndeterminate"
              @change="handleCheckAllChange"
              >全选</el-checkbox
            >
          </p>
          <p class="toolTitle">
            <i
              title="批量下载文章"
              class="icon-xiazai1"
              style="color: green"
              @click="downLoadExcel"
            ></i>
          </p>
          <p class="toolTitle">
            <i
              title="批量删除文章"
              class="icon-shanchu"
              @click="batchDelete"
            ></i>
          </p>
          <!-- <p class="toolTitle">
            <i title="批量生成快照" class="icon-pingmukuaizhao" style="color:green" @click="resultEvent('BatchGeneration')"></i>
          </p> -->
          <p class="toolTitle">
            <i class="icon-shuaxin-copy" title="刷新" @click="Refresh"></i>
          </p>
          <p class="toolTitle">
            <i
              class="icon--_tianjiadaoku"
              title="添加到报告"
              @click="dialogVisible = true"
              v-hasPermi="['result:report:add']"
            ></i>
          </p>
          <p class="toolTitle">
            <i
              class="el-icon-document-add"
              style="font-size: 24px"
              title="发布到每日最新热点"
              @click="publishHot"
            ></i>
          </p>
          <p class="toolTitle" v-if="$route.query.domain">
            <i
              class="el-icon-chat-dot-round"
              style="font-size: 24px"
              title="Deepseek深度解读"
              @click="articleAiChat"
            ></i>
            <span class="deepseek-text" @click="articleAiChat"
              >Deepseek深度解读</span
            >
          </p>
        </div>
        <div>
          <el-checkbox
            v-model="showSummary"
            @change="(e) => (showSummary = e)"
            style="margin-right: 10px"
            >是否显示摘要</el-checkbox
          >
          <span style="font-size: 14px">排序方式:</span>&nbsp;
          <el-select v-model="SeachData.sortMode" size="mini">
            <el-option label="按发布时间倒序排序" :value="'0'"></el-option>
            <el-option label="按发布时间正序排序" :value="'1'"></el-option>
            <el-option label="按采集时间倒序排序" :value="'2'"></el-option>
            <el-option label="按采集时间正序排序" :value="'3'"></el-option>
            <el-option label="按系统推荐排序" :value="'4'"></el-option>
          </el-select>
        </div>
      </div>
      <el-checkbox-group
        v-model="checkedCities"
        @change="handleCheckedCitiesChange"
      >
        <div
          class="scollBox"
          @mousewheel="scrollChange"
          ref="scroll"
          :style="
            $route.query.domain
              ? 'height: calc(100vh - 170px)'
              : 'height: calc(100vh - 389px)'
          "
        >
          <div class="Articl" v-for="(item, key) in ArticleList" :key="key">
            <div class="Articl_left">
              <div class="ArticlTop">
                <p style="margin: 20px 0 5px 0; line-height: 20px">
                  <el-checkbox :label="item.id" :value="item.id">
                    {{ null }} </el-checkbox
                  >&nbsp;&nbsp;
                  <a href="#"
                    ><span style="padding: 0 10px 0 0">{{
                      (currentPage - 1) * pageSize + key + 1
                    }}</span
                    ><span
                      class="title_Article"
                      @click="openNewView(item)"
                      v-html="changeColor(item.title)"
                    ></span
                  ></a>
                  &nbsp;&nbsp;&nbsp;&nbsp;
                  <!-- v-if="item.isTranslated == 0" -->
                  <i
                    class="icon-taizhangtranslate"
                    @click="translateTitle(item)"
                    v-if="item.sourceType !== '1' && item.sourceType !== '3'"
                  ></i>
                  <span
                    class="linkStyle"
                    @click="openNewView(item, 'link')"
                    style="color: #1889f3; cursor: pointer"
                  >
                    原文链接
                  </span>
                </p>
                <!-- v-if="item.fileUrl" -->
              </div>
              <div
                class="ArticlMain"
                id
                v-if="
                  item.sourceType !== '1' &&
                  item.sourceType !== '3' &&
                  item.cnTitle
                "
              >
                <a href="#">
                  <span style="color: #9b9b9b">中文标题：</span>
                  <span
                    v-html="changeColor(item.cnTitle)"
                    @click="openNewView(item)"
                  ></span>
                </a>
              </div>
              <div class="info_flex">
                <div class="ArticlBottom">
                  <div>
                    媒体来源:
                    <span class="infomation">{{ item.sourceName }}</span>
                  </div>
                  <div>
                    发布时间:
                    <span class="infomation">{{
                      formatPublishTime(
                        item.publishTime,
                        item.webstePublishTime
                      )
                    }}</span>
                  </div>
                  <div>
                    采集时间:
                    <span class="infomation">{{
                      parseTime(item.createTime, "{y}-{m}-{d} {h}:{i}:{s}")
                    }}</span>
                  </div>
                  <div>
                    大模型筛选:
                    <span class="infomation">
                      {{
                        $route.query.domain
                          ? "是"
                          : getTechnologyLabel(item.isTechnology)
                      }}
                    </span>
                  </div>
                </div>
              </div>
              <div
                class="ArticlMain"
                style="
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  word-break: break-all;
                "
                v-if="
                  showSummary &&
                  hasActualContent(item.cnSummary || item.summary)
                "
              >
                <span style="color: #9b9b9b">摘要：</span>
                <span
                  v-html="changeColor(getSafeSummary(item))"
                  @click="openNewView(item)"
                ></span>
              </div>
            </div>
            <div class="btnBox">
              <!-- <p>
                  <el-button type="primary" plain size="mini" @click="resultEvent(item)">
                    {{ item.snapshotUrl
                      ? '下载快照' : '生成快照' }}
                  </el-button>
                </p> -->
              <p>
                <el-button
                  v-if="item.fileUrl"
                  :type="item.fileUrl ? 'primary' : 'info'"
                  :disabled="!item.fileUrl"
                  plain
                  size="mini"
                  @click="documentDownload(item)"
                  >{{ "附件下载" }}</el-button
                >
              </p>
              <p>
                <el-button
                  style="width: 80px"
                  size="mini"
                  type="primary"
                  plain
                  @click="handleUpdate(item)"
                  v-hasPermi="['article:articleList:edit']"
                  >{{ ` 修改 ` }}</el-button
                >
              </p>
              <p>
                <el-button
                  style="width: 80px"
                  size="mini"
                  type="danger"
                  plain
                  @click="handleDelete(item)"
                  v-hasPermi="['article:list:remove']"
                  >{{ `删除` }}</el-button
                >
              </p>
            </div>
          </div>
          <el-empty
            :image-size="200"
            description="当前筛选条件下暂无数据"
            v-if="ArticleList == null || ArticleList.length == 0"
          ></el-empty>
        </div>
      </el-checkbox-group>
    </div>
    <div class="MainArticle" v-if="flag == 'Special'">
      <div class="TopBtnGroup">
        <div class="leftBtnGroup">
          <p>
            <el-checkbox
              v-model="checked"
              :isIndeterminate="isIndeterminate"
              @change="handleCheckAllChange"
              >全选</el-checkbox
            >
          </p>
          <!-- <p class="toolTitle"><i title="下载" class="icon-xiazai1" style="color:green" @click="downLoad"></i></p>  2023-9-4 沈老师  暂时注释 -->
          <p class="toolTitle">
            <i class="icon-shuaxin-copy" title="刷新" @click="Refresh"></i>
          </p>
          <p class="toolTitle">
            <i
              class="icon--_tianjiadaoku"
              title="添加到报告"
              @click="dialogVisible = true"
              v-hasPermi="['result:report:add']"
            ></i>
          </p>
        </div>
        <div>
          <el-checkbox
            v-model="showSummary"
            @change="(e) => (showSummary = e)"
            style="margin-right: 10px"
            >是否显示摘要</el-checkbox
          >
          <span style="font-size: 14px">排序方式:</span>&nbsp;
          <el-select v-model="SeachData.sortMode" size="mini">
            <el-option label="按发布时间倒序排序" :value="false"></el-option>
            <el-option label="按发布时间正序排序" :value="true"></el-option>
          </el-select>
        </div>
      </div>
      <el-checkbox-group
        v-model="checkedCities"
        @change="handleCheckedCitiesChange"
      >
        <div
          class="scollBox"
          @mousewheel="scrollChange"
          ref="scroll"
          style="height: calc(100vh - 405px)"
        >
          <div class="Articl" v-for="(item, key) in ArticleList" :key="key">
            <div class="Articl_left">
              <div class="ArticlTop">
                <p style="margin: 20px 0 5px 0; line-height: 20px">
                  <el-checkbox :label="item.id" :value="item.id">
                    {{ null }} </el-checkbox
                  >&nbsp;&nbsp;
                  <a href="#"
                    ><span style="padding: 0 10px 0 0"
                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span
                    ><span
                      class="title_Article"
                      @click="openNewView(item)"
                      v-html="changeColor(item.cnTitle || item.title)"
                    ></span
                    >&nbsp;&nbsp;&nbsp;&nbsp;</a
                  >
                </p>
              </div>
              <div class="info_flex">
                <div
                  class="ArticlBottom"
                  v-if="item.sourceType !== '1' && item.sourceType !== '3'"
                  style="width: 100%"
                >
                  <div>
                    原文标题:
                    <span class="infomation">
                      {{ item.title }}
                    </span>
                  </div>
                </div>
                <div class="ArticlBottom">
                  <div>
                    类型:
                    <span class="infomation">
                      {{ typeHandle(item.sourceType) }}
                    </span>
                  </div>
                  <div>
                    媒体来源:
                    <span class="infomation">{{ item.sourceName }}</span>
                  </div>
                  <div v-if="item.author">
                    作者:
                    <span class="infomation" v-html="item.author"></span>
                  </div>
                  <div>
                    发布时间:
                    <span class="infomation">{{
                      formatPublishTime(
                        item.publishTime,
                        item.webstePublishTime
                      )
                    }}</span>
                  </div>
                  <!-- <span style="color: rgb(32, 126, 250);">相似文章</span> -->
                  <!-- <p>标签A</p> -->
                </div>
                <div class="ArticlBottom">
                  <div v-if="item.industryName">
                    所属行业:
                    <span class="infomation">{{ industryHandle(item) }}</span>
                  </div>
                  <div v-if="item.domain">
                    所属领域:
                    <span class="infomation">{{ domainHandle(item) }}</span>
                  </div>
                  <div>
                    原文链接:
                    <span
                      class="linkStyle"
                      @click="openNewView(item, 'link')"
                      style="color: #1889f3"
                    >
                      {{
                        typeHandle(item.sourceType) == "微信公众号"
                          ? item.shortUrl
                          : item.originalUrl
                      }}
                    </span>
                  </div>
                  <div>
                    大模型筛选:
                    <span class="infomation">
                      {{ getTechnologyLabel(item.isTechnology) }}
                    </span>
                  </div>
                </div>
              </div>
              <div
                class="ArticlMain"
                style="
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  word-break: break-all;
                "
                v-if="
                  showSummary &&
                  hasActualContent(item.cnSummary || item.summary)
                "
              >
                <span style="color: #9b9b9b">摘要：</span>
                <span
                  v-html="changeColor(getSafeSummary(item))"
                  @click="openNewView(item)"
                ></span>
              </div>
              <!-- <div class="imgBox" v-if="item.imgage">
                <img src="../../assets/images/priview.png" alt="加载失败了" />
                <img src="../../assets/images/priview.png" alt="加载失败了" />
                <img src="../../assets/images/priview.png" alt="加载失败了" />
                <img src="../../assets/images/priview.png" alt="加载失败了" />
                <img src="../../assets/images/priview.png" alt="加载失败了" />
                <img src="../../assets/images/priview.png" alt="加载失败了" />
              </div> -->
            </div>
            <div class="leftBtnGroup2">
              <p>
                <i
                  :class="{
                    'icon-hot2-copy':
                      item.isWhether == null || item.isWhether == 0,
                    'icon-hot2': item.isWhether == 1,
                  }"
                  title="热点"
                  @click="hotIncrease(item)"
                  v-hasPermi="['article:label:add']"
                ></i>
              </p>
              <p>
                <i
                  :class="{
                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,
                    'icon-jiaxingshoucangtianchong': item.favorites,
                  }"
                  title="收藏"
                  @click.passive="collect(item)"
                  v-hasPermi="['article:collection:add']"
                ></i>
              </p>
              <p>
                <i
                  class="icon--_tianjiadaoku"
                  title="添加到报告"
                  @click="separateAdd(item)"
                  v-hasPermi="['result:report:add']"
                ></i>
              </p>
              <p>
                <i
                  class="icon-biaoqian"
                  title="标签"
                  @click="tagHandler(item)"
                  v-hasPermi="['article:label:add']"
                ></i>
              </p>
              <!-- <p><i class="icon-fuzhi" title="复制" @click="copyText(item)"></i></p>   2023-9-4 沈老师 暂时注释 -->
            </div>
          </div>
          <el-empty
            :image-size="200"
            description="当前筛选条件下暂无数据"
            v-if="ArticleList == null || ArticleList.length == 0"
          ></el-empty>
        </div>
      </el-checkbox-group>
    </div>
    <div class="MainArticle" v-if="flag == 'infoInter'">
      <div class="TopBtnGroup">
        <div class="leftBtnGroup">
          <p>
            <el-checkbox
              v-model="checked"
              :isIndeterminate="isIndeterminate"
              @change="handleCheckAllChange"
              >全选</el-checkbox
            >
          </p>
          <!-- <p class="toolTitle"><i title="下载" class="icon-xiazai1" style="color:green" @click="downLoad"></i></p>  2023-9-4 沈老师  暂时注释 -->
          <p class="toolTitle">
            <i class="icon-shuaxin-copy" title="刷新" @click="Refresh"></i>
          </p>
          <p class="toolTitle">
            <i
              class="icon--_tianjiadaoku"
              title="添加到报告"
              @click="dialogVisible = true"
              v-hasPermi="['result:report:add']"
            ></i>
          </p>
        </div>
        <div>
          <el-checkbox
            v-model="showSummary"
            @change="(e) => (showSummary = e)"
            style="margin-right: 10px"
            >是否显示摘要</el-checkbox
          >
          <span style="font-size: 14px">排序方式:</span>&nbsp;
          <el-select v-model="SeachData.sortMode" size="mini">
            <el-option label="按发布时间倒序排序" :value="false"></el-option>
            <el-option label="按发布时间正序排序" :value="true"></el-option>
          </el-select>
        </div>
      </div>
      <el-checkbox-group
        v-model="checkedCities"
        @change="handleCheckedCitiesChange"
      >
        <div class="scollBox" @mousewheel="scrollChange" ref="scroll">
          <div class="Articl" v-for="(item, key) in ArticleList" :key="key">
            <div class="Articl_left">
              <div class="ArticlTop">
                <p style="margin: 20px 0 5px 0; line-height: 20px">
                  <el-checkbox :label="item.id" :value="item.id">
                    {{ null }} </el-checkbox
                  >&nbsp;&nbsp;
                  <a href="#"
                    ><span style="padding: 0 10px 0 0"
                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span
                    ><span
                      class="title_Article"
                      @click="openNewView(item)"
                      v-html="changeColor(item.cnTitle || item.title)"
                    ></span
                    >&nbsp;&nbsp;&nbsp;&nbsp;</a
                  >
                </p>
              </div>
              <div class="info_flex">
                <div class="ArticlBottom">
                  <div>
                    类型:
                    <span class="infomation">
                      {{ typeHandle(item.sourceType) }}
                    </span>
                  </div>
                  <div>
                    媒体来源:
                    <span class="infomation">{{ item.sourceName }}</span>
                  </div>
                  <div v-if="item.author">
                    作者:
                    <span class="infomation" v-html="item.author"></span>
                  </div>
                  <div>
                    发布时间:
                    <span class="infomation">{{
                      formatPublishTime(
                        item.publishTime,
                        item.webstePublishTime
                      )
                    }}</span>
                  </div>
                  <!-- <span style="color: rgb(32, 126, 250);">相似文章</span> -->
                  <!-- <p>标签A</p> -->
                </div>
                <div class="ArticlBottom">
                  <div v-if="item.industryName">
                    所属行业:
                    <span class="infomation">{{ industryHandle(item) }}</span>
                  </div>
                  <div v-if="item.domain">
                    所属领域:
                    <span class="infomation">{{ domainHandle(item) }}</span>
                  </div>
                  <div>
                    原文链接:
                    <span
                      class="linkStyle"
                      @click="openNewView(item, 'link')"
                      style="color: #1889f3"
                    >
                      {{
                        typeHandle(item.sourceType) == "微信公众号"
                          ? item.shortUrl
                          : item.originalUrl
                      }}
                    </span>
                  </div>
                </div>
              </div>
              <div
                class="ArticlMain"
                style="
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  word-break: break-all;
                "
                v-if="
                  showSummary &&
                  hasActualContent(item.cnSummary || item.summary)
                "
              >
                <span style="color: #9b9b9b">摘要：</span>
                <span
                  v-html="changeColor(getSafeSummary(item))"
                  @click="openNewView(item)"
                ></span>
              </div>
              <!-- <div class="imgBox" v-if="item.imgage">
              <img src="../../assets/images/priview.png" alt="加载失败了" />
              <img src="../../assets/images/priview.png" alt="加载失败了" />
              <img src="../../assets/images/priview.png" alt="加载失败了" />
              <img src="../../assets/images/priview.png" alt="加载失败了" />
              <img src="../../assets/images/priview.png" alt="加载失败了" />
              <img src="../../assets/images/priview.png" alt="加载失败了" />
            </div> -->
            </div>
            <div class="leftBtnGroup2">
              <p>
                <i
                  :class="{
                    'icon-hot2-copy':
                      item.isWhether == null || item.isWhether == 0,
                    'icon-hot2': item.isWhether == 1,
                  }"
                  title="热点"
                  @click="hotIncrease(item)"
                  v-hasPermi="['article:label:add']"
                ></i>
              </p>
              <p>
                <i
                  :class="{
                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,
                    'icon-jiaxingshoucangtianchong': item.favorites,
                  }"
                  title="收藏"
                  @click.passive="collect(item)"
                  v-hasPermi="['article:collection:add']"
                ></i>
              </p>
              <p>
                <i
                  class="icon--_tianjiadaoku"
                  title="添加到报告"
                  @click="separateAdd(item)"
                  v-hasPermi="['result:report:add']"
                ></i>
              </p>
              <p>
                <i
                  class="icon-biaoqian"
                  title="标签"
                  @click="tagHandler(item)"
                  v-hasPermi="['article:label:add']"
                ></i>
              </p>
              <!-- <p><i class="icon-fuzhi" title="复制" @click="copyText(item)"></i></p>   2023-9-4 沈老师 暂时注释 -->
            </div>
          </div>
          <el-empty
            :image-size="200"
            description="当前筛选条件下暂无数据"
            v-if="ArticleList == null || ArticleList.length == 0"
          ></el-empty>
        </div>
      </el-checkbox-group>
    </div>
    <div class="MainArticle" v-if="flag == 'Wechat'">
      <div class="TopBtnGroup">
        <div class="leftBtnGroup">
          <p>
            <el-checkbox
              v-model="checked"
              :isIndeterminate="isIndeterminate"
              @change="handleCheckAllChange"
              >全选</el-checkbox
            >
          </p>
          <!-- <p class="toolTitle"><i title="下载" class="icon-xiazai1" style="color:green" @click="downLoad"></i></p>  2023-9-4 沈老师  暂时注释 -->
          <p class="toolTitle">
            <i class="icon-shuaxin-copy" title="刷新" @click="Refresh"></i>
          </p>
          <!-- <p class="toolTitle">
            <i
              class="icon--_tianjiadaoku"
              title="添加到报告"
              @click="dialogVisible = true"
              v-hasPermi="['result:report:add']"
            ></i>
          </p> -->
        </div>
        <div>
          <el-checkbox
            v-model="showSummary"
            @change="(e) => (showSummary = e)"
            style="margin-right: 10px"
            >是否显示摘要</el-checkbox
          >
          <span style="font-size: 14px">排序方式:</span>&nbsp;
          <el-select v-model="SeachData.sortMode" size="mini">
            <el-option label="按发布时间倒序排序" :value="false"></el-option>
            <el-option label="按发布时间正序排序" :value="true"></el-option>
          </el-select>
        </div>
      </div>
      <el-checkbox-group
        v-model="checkedCities"
        @change="handleCheckedCitiesChange"
      >
        <div
          class="scollBox"
          @mousewheel="scrollChange"
          ref="scroll"
          style="height: calc(50vh - 200px)"
        >
          <div class="Articl" v-for="(item, key) in ArticleList" :key="key">
            <div class="Articl_kqcb">
              <div>
                <div class="ArticlTop" style="width: 55%">
                  <p style="line-height: 20px">
                    <el-checkbox :label="item.id" :value="item.id">
                      {{ null }} </el-checkbox
                    >&nbsp;&nbsp;
                    <a href="#"
                      ><span style="padding: 0 10px 0 0"
                        >{{
                          (currentPage - 1) * pageSize + key + 1
                        }}&nbsp;</span
                      ><span
                        class="title_Article"
                        @click="openNewView(item)"
                        v-html="changeColor(item.cnTitle || item.title)"
                      ></span
                      >&nbsp;&nbsp;&nbsp;&nbsp;</a
                    >
                    <span
                      class="linkStyle"
                      @click="openNewView(item, 'link')"
                      style="color: #1889f3; cursor: pointer"
                    >
                      原文链接
                    </span>
                  </p>
                </div>
                <div class="info_flex" style="width: auto">
                  <div class="ArticlBottom">
                    <div>
                      类型:
                      <span class="infomation">
                        {{ typeHandle(item.sourceType) }}
                      </span>
                    </div>
                    <div>
                      媒体来源:
                      <span class="infomation">{{ item.sourceName }}</span>
                    </div>
                    <div v-if="item.author">
                      作者:
                      <span class="infomation" v-html="item.author"></span>
                    </div>
                    <div>
                      发布时间:
                      <span class="infomation">{{
                        formatPublishTime(
                          item.publishTime,
                          item.webstePublishTime
                        )
                      }}</span>
                    </div>
                    <!-- <span style="color: rgb(32, 126, 250);">相似文章</span> -->
                    <!-- <p>标签A</p> -->
                  </div>
                  <div class="ArticlBottom">
                    <div v-if="item.industryName">
                      所属行业:
                      <span class="infomation">{{ industryHandle(item) }}</span>
                    </div>
                    <div v-if="item.domain">
                      所属领域:
                      <span class="infomation">{{ domainHandle(item) }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="ArticlMain"
                style="
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  word-break: break-all;
                "
                v-if="
                  showSummary &&
                  hasActualContent(item.cnSummary || item.summary)
                "
              >
                <span style="color: #9b9b9b">摘要：</span>
                <span
                  v-html="changeColor(getSafeSummary(item))"
                  @click="openNewView(item)"
                ></span>
              </div>
            </div>
            <!-- <div class="leftBtnGroup2">
              <p>
                <i
                  :class="{
                    'icon-hot2-copy':
                      item.isWhether == null || item.isWhether == 0,
                    'icon-hot2': item.isWhether == 1,
                  }"
                  title="热点"
                  @click="hotIncrease(item)"
                  v-hasPermi="['article:label:add']"
                ></i>
              </p>
              <p>
                <i
                  :class="{
                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,
                    'icon-jiaxingshoucangtianchong': item.favorites,
                  }"
                  title="收藏"
                  @click.passive="collect(item)"
                  v-hasPermi="['article:collection:add']"
                ></i>
              </p>
              <p>
                <i
                  class="icon--_tianjiadaoku"
                  title="添加到报告"
                  @click="separateAdd(item)"
                  v-hasPermi="['result:report:add']"
                ></i>
              </p>
              <p>
                <i
                  class="icon-biaoqian"
                  title="标签"
                  @click="tagHandler(item)"
                  v-hasPermi="['article:label:add']"
                ></i>
              </p> -->
            <!-- <p><i class="icon-fuzhi" title="复制" @click="copyText(item)"></i></p>   2023-9-4 沈老师 暂时注释 -->
            <!-- </div> -->
          </div>
          <el-empty
            :image-size="200"
            description="当前筛选条件下暂无数据"
            v-if="ArticleList == null || ArticleList.length == 0"
          ></el-empty>
        </div>
      </el-checkbox-group>
    </div>
    <div
      class="MainArticle"
      v-if="flag == 'artificialIntelligence' || flag == 'networkSecurity'"
    >
      <div class="TopBtnGroup">
        <div class="leftBtnGroup">
          <p>
            <el-checkbox
              v-model="checked"
              :isIndeterminate="isIndeterminate"
              @change="handleCheckAllChange"
              >全选</el-checkbox
            >
          </p>
          <p class="toolTitle">
            <i
              title="批量下载文章"
              class="icon-xiazai1"
              style="color: green"
              @click="downLoadExcel"
            ></i>
          </p>
          <p class="toolTitle">
            <i
              title="批量删除文章"
              class="icon-shanchu"
              @click="batchDelete"
            ></i>
          </p>
          <p class="toolTitle">
            <i class="icon-shuaxin-copy" title="刷新" @click="Refresh"></i>
          </p>
          <p class="toolTitle">
            <i
              class="icon--_tianjiadaoku"
              title="添加到报告"
              @click="dialogVisible = true"
              v-hasPermi="['result:report:add']"
            ></i>
          </p>
        </div>
        <div>
          <el-checkbox
            v-model="showSummary"
            @change="(e) => (showSummary = e)"
            style="margin-right: 10px"
            >是否显示摘要</el-checkbox
          >
          <span style="font-size: 14px">排序方式:</span>&nbsp;
          <el-select v-model="SeachData.sortMode" size="mini">
            <el-option label="按发布时间倒序排序" :value="'0'"></el-option>
            <el-option label="按发布时间正序排序" :value="'1'"></el-option>
            <el-option label="按采集时间倒序排序" :value="'2'"></el-option>
            <el-option label="按采集时间正序排序" :value="'3'"></el-option>
            <el-option label="按系统推荐排序" :value="'4'"></el-option>
          </el-select>
        </div>
      </div>
      <el-checkbox-group
        v-model="checkedCities"
        @change="handleCheckedCitiesChange"
      >
        <div class="scollBox" @mousewheel="scrollChange" ref="scroll">
          <div class="Articl" v-for="(item, key) in ArticleList" :key="key">
            <div
              class="Articl_left"
              :style="item.fileUrl ? 'width: 85%' : 'width: 100%'"
            >
              <div class="ArticlTop">
                <p style="margin: 20px 0 5px 0; line-height: 20px">
                  <el-checkbox :label="item.id" :value="item.id">
                    {{ null }} </el-checkbox
                  >&nbsp;&nbsp;
                  <a href="#"
                    ><span style="padding: 0 10px 0 0"
                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span
                    ><span
                      class="title_Article"
                      @click="openNewView(item)"
                      v-html="changeColor(item.title)"
                    ></span
                  ></a>
                  &nbsp;&nbsp;&nbsp;&nbsp;
                  <!-- v-if="item.isTranslated == 0" -->
                  <i
                    class="icon-taizhangtranslate"
                    @click="translateTitle(item)"
                    v-if="item.sourceType !== '1' && item.sourceType !== '3'"
                  ></i>
                  <span
                    class="linkStyle"
                    @click="openNewView(item, 'link')"
                    style="color: #1889f3; cursor: pointer"
                  >
                    原文链接
                  </span>
                </p>
              </div>
              <div
                class="ArticlMain"
                id
                v-if="
                  item.sourceType !== '1' &&
                  item.sourceType !== '3' &&
                  item.cnTitle
                "
              >
                <a href="#">
                  <span style="color: #9b9b9b">中文标题:</span>
                  <span
                    v-html="changeColor(item.cnTitle)"
                    @click="openNewView(item)"
                  ></span>
                </a>
              </div>
              <div class="info_flex">
                <div class="ArticlBottom">
                  <div>
                    媒体来源:
                    <span class="infomation">{{ item.sourceName }}</span>
                  </div>
                  <div>
                    发布时间:
                    <span class="infomation">{{
                      formatPublishTime(
                        item.publishTime,
                        item.webstePublishTime
                      )
                    }}</span>
                  </div>
                  <div>
                    采集时间:
                    <span class="infomation">{{
                      parseTime(item.createTime, "{y}-{m}-{d} {h}:{i}:{s}")
                    }}</span>
                  </div>
                  <div>
                    大模型筛选:
                    <span class="infomation">
                      {{ getTechnologyLabel(item.isTechnology) }}
                    </span>
                  </div>
                </div>
              </div>
              <div
                class="ArticlMain"
                style="
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  word-break: break-all;
                "
                v-if="
                  showSummary &&
                  hasActualContent(item.cnSummary || item.summary)
                "
              >
                <span style="color: #9b9b9b">摘要：</span>
                <span
                  v-html="changeColor(getSafeSummary(item))"
                  @click="openNewView(item)"
                ></span>
              </div>
            </div>
            <div class="btnBox" v-if="item.fileUrl">
              <p>
                <el-button
                  v-if="item.fileUrl"
                  :type="item.fileUrl ? 'primary' : 'info'"
                  :disabled="!item.fileUrl"
                  plain
                  size="mini"
                  @click="documentDownload(item)"
                  >{{ "附件下载" }}</el-button
                >
              </p>
              <!-- <p style="margin-top: -5px;">
                  <el-button style="width: 80px;" size="mini" type="primary" plain @click="handleUpdate(item)"
                    v-hasPermi="['article:articleList:edit']">{{ ` 修改 ` }}</el-button>
                </p>
                <p style="margin-top: -5px;">
                  <el-button style="width: 80px;" size="mini" type="danger" plain @click="handleDelete(item)"
                    v-hasPermi="['article:list:remove']">{{ `删除` }}</el-button>
                </p> -->
            </div>
          </div>
          <el-empty
            :image-size="200"
            description="当前筛选条件下暂无数据"
            v-if="ArticleList == null || ArticleList.length == 0"
          ></el-empty>
        </div>
      </el-checkbox-group>
    </div>
    <div class="MainArticle" v-if="flag == 'zhikubaogao'">
      <div class="TopBtnGroup">
        <div class="leftBtnGroup">
          <p>
            <el-checkbox
              v-model="checked"
              :isIndeterminate="isIndeterminate"
              @change="handleCheckAllChange"
              >全选</el-checkbox
            >
          </p>
          <p class="toolTitle">
            <i
              class="el-icon-upload2"
              style="font-size: 22px; color: #1296db"
              title="批量导入报告"
              @click="openBatchImportDialog"
            ></i>
          </p>
          <p class="toolTitle">
            <i
              title="批量下载报告"
              class="icon-xiazai1"
              style="color: green"
              @click="downLoadExcel"
            ></i>
          </p>
          <p class="toolTitle">
            <i
              title="批量删除报告"
              class="icon-shanchu"
              @click="batchDelete"
            ></i>
          </p>
          <p class="toolTitle">
            <i class="icon-shuaxin-copy" title="刷新" @click="Refresh"></i>
          </p>
          <!-- <p class="toolTitle">
            <i
              class="el-icon-chat-dot-round"
              style="font-size: 24px"
              title="Deepseek报告解读"
              @click="reportAiChat"
            ></i>
            <span class="deepseek-text" @click="reportAiChat"
              >Deepseek报告解读</span
            >
          </p> -->
        </div>
        <div>
          <el-checkbox
            v-model="showSummary"
            @change="(e) => (showSummary = e)"
            style="margin-right: 10px"
            >是否显示摘要</el-checkbox
          >
          <span style="font-size: 14px">排序方式:</span>&nbsp;
          <el-select v-model="SeachData.sortMode" size="mini">
            <el-option label="按发布时间倒序排序" :value="'0'"></el-option>
            <el-option label="按发布时间正序排序" :value="'1'"></el-option>
            <el-option label="按采集时间倒序排序" :value="'2'"></el-option>
            <el-option label="按采集时间正序排序" :value="'3'"></el-option>
            <el-option label="按系统推荐排序" :value="'4'"></el-option>
          </el-select>
        </div>
      </div>
      <el-checkbox-group
        v-model="checkedCities"
        @change="handleCheckedCitiesChange"
      >
        <div
          class="scollBox"
          style="height: calc(100vh - 310px)"
          @mousewheel="scrollChange"
          ref="scroll"
        >
          <div class="Articl" v-for="(item, key) in ArticleList" :key="key">
            <div class="Articl_left">
              <div class="ArticlTop">
                <p style="margin: 20px 0 5px 0; line-height: 20px">
                  <el-checkbox :label="item.id" :value="item.id">
                    {{ null }} </el-checkbox
                  >&nbsp;&nbsp;
                  <a href="#"
                    ><span style="padding: 0 10px 0 0">{{
                      (currentPage - 1) * pageSize + key + 1
                    }}</span
                    ><span
                      class="title_Article"
                      @click="openNewView(item)"
                      v-html="changeColor(item.title)"
                    ></span
                  ></a>
                  &nbsp;&nbsp;&nbsp;&nbsp;
                  <!-- v-if="item.isTranslated == 0" -->
                  <!-- <i
                    class="icon-taizhangtranslate"
                    @click="translateTitle(item)"
                    v-if="item.sourceType !== '1' && item.sourceType !== '3'"
                  ></i>
                  <span
                    class="linkStyle"
                    @click="openNewView(item, 'link')"
                    style="color: #1889f3; cursor: pointer"
                  >
                    原文链接
                  </span> -->
                </p>
                <!-- v-if="item.fileUrl" -->
              </div>
              <div
                class="ArticlMain"
                id
                v-if="
                  item.sourceType !== '1' &&
                  item.sourceType !== '3' &&
                  item.cnTitle
                "
              >
                <a href="#">
                  <span style="color: #9b9b9b">中文标题：</span>
                  <span
                    v-html="changeColor(item.cnTitle)"
                    @click="openNewView(item)"
                  ></span>
                </a>
              </div>
              <div class="info_flex">
                <div class="ArticlBottom">
                  <div>
                    媒体来源:
                    <span class="infomation">{{ item.sourceName }}</span>
                  </div>
                  <div>
                    发布时间:
                    <span class="infomation">{{
                      formatPublishTime(
                        item.publishTime,
                        item.webstePublishTime
                      )
                    }}</span>
                  </div>
                  <div>
                    采集时间:
                    <span class="infomation">{{
                      parseTime(item.createTime, "{y}-{m}-{d} {h}:{i}:{s}")
                    }}</span>
                  </div>
                  <!-- <div>
                    大模型筛选:
                    <span class="infomation">
                      {{ getTechnologyLabel(item.isTechnology) }}
                    </span>
                  </div> -->
                </div>
              </div>
              <div
                class="ArticlMain"
                style="
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  word-break: break-all;
                "
                v-if="
                  showSummary &&
                  hasActualContent(item.cnSummary || item.summary)
                "
              >
                <span style="color: #9b9b9b">摘要：</span>
                <span
                  v-html="changeColor(getSafeSummary(item))"
                  @click="openNewView(item)"
                ></span>
              </div>
            </div>
            <div class="btnBox">
              <!-- <p>
                  <el-button type="primary" plain size="mini" @click="resultEvent(item)">
                    {{ item.snapshotUrl
                      ? '下载快照' : '生成快照' }}
                  </el-button>
                </p> -->
              <p>
                <el-button
                  v-if="item.fileUrl"
                  :type="item.fileUrl ? 'primary' : 'info'"
                  :disabled="!item.fileUrl"
                  plain
                  size="mini"
                  @click="documentDownload(item)"
                  >{{ "附件下载" }}</el-button
                >
              </p>
              <p>
                <el-button
                  style="width: 80px"
                  size="mini"
                  type="danger"
                  plain
                  @click="handleDelete(item)"
                  >删除</el-button
                >
              </p>
            </div>
          </div>
          <el-empty
            :image-size="200"
            description="当前筛选条件下暂无数据"
            v-if="ArticleList == null || ArticleList.length == 0"
          ></el-empty>
        </div>
      </el-checkbox-group>
    </div>
    <div class="pagination" ref="pagination">
      <el-pagination
        :small="false"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="pageSize"
        layout="->, total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
    <el-dialog
      title="添加到报告"
      :visible.sync="dialogVisible"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-row style="line-height: 50px">
        <el-col :span="18">
          <el-select
            v-model="reportId"
            placeholder="请选择报告"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="(item, key) in reportOptions"
              :key="key"
              :label="item.title"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="reportSubmit">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title
      :visible.sync="tagDialog"
      width="20%"
      :before-close="closeTag"
      :close-on-click-modal="false"
    >
      <el-form
        label-position="left"
        label-width="40px"
        :model="formLabelAlign"
        ref="ruleForm"
      >
        <el-form-item label="行业" prop="industry">
          <el-select
            v-model="formLabelAlign.industry"
            clearable
            style="width: 100%"
            multiple
            placeholder="请选择行业"
            :filterable="true"
            default-first-option
          >
            <el-option
              v-for="item in options1"
              :key="item.id"
              :label="item.industryName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="领域" prop="domain">
          <el-select
            v-model="formLabelAlign.domain"
            filterable
            style="width: 100%"
            multiple
            clearable
            placeholder="请选择领域"
            default-first-option
          >
            <el-option
              v-for="item in options"
              :key="item.id"
              :label="item.fieldName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标签" prop="tag">
          <el-select
            v-model="formLabelAlign.tag"
            style="width: 100%"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请添加文章标签"
          >
            <!-- <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>-->
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="closeTag" size="mini">取 消</el-button>
        <el-button @click="SubmitTag" type="primary" size="mini"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer
      @open="openDrawer"
      :title="drawerInfo.cnTitle || drawerInfo.title"
      :visible.sync="drawer"
      custom-class="drawer_box"
      direction="rtl"
      style="width: 100%"
    >
      <div
        v-if="flag == 'MonitorUse' || flag == 'specialSubjectUse'"
        style="width: 100%; text-align: right; padding-right: 10px"
      >
        <el-button
          @click="documentDownload('drawer')"
          :type="drawerInfo.fileUrl ? 'primary' : 'info'"
          size="mini"
          plain
          :disabled="!drawerInfo.fileUrl"
          >{{ "附件下载" }}</el-button
        >
        <!-- <el-button @click="resultEvent('drawer')" type="primary" size="mini" plain>
          {{ drawerInfo.snapshotUrl
            ? '下载快照' : '生成快照' }}
        </el-button> -->
        <!-- v-if="translationBtnShow" -->
        <el-button
          type="primary"
          size="mini"
          plain
          @click="translateEvent(drawerInfo)"
          >翻译内容</el-button
        >
      </div>
      <template slot="title">
        <span class="drawer_Title">{{
          drawerInfo.cnTitle || drawerInfo.title
        }}</span>
      </template>
      <div class="drawer_Style">
        <p class="title">
          {{ drawerInfo.cnTitle || drawerInfo.title }}
          <!-- v-if="!drawerInfo.cnTitle" -->
          <i
            class="icon-taizhangtranslate"
            @click="translateTitle(drawerInfo)"
          ></i>
        </p>
        <p style="text-align: center">
          <span class="source">{{ drawerInfo.sourceName }}</span>
          <span class="time">{{ drawerInfo.publishTime }}</span>
        </p>
        <div
          style="user-select: text !important; line-height: 30px"
          v-html="
            drawerInfo.cnContent &&
            drawerInfo.cnContent.replace(/<img\b[^>]*>/gi, '')
          "
        ></div>
        <el-empty
          description="当前文章暂无数据"
          v-if="!drawerInfo.cnContent"
        ></el-empty>
      </div>
    </el-drawer>
    <el-dialog
      :title="'修改文章'"
      :visible.sync="open"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="dialog_Box">
        <el-form
          :model="form"
          class="form_Style"
          label-position="top"
          ref="form"
          :rules="rules"
          size="mini"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="文章标题" prop="title">
                <el-input v-model="form.title"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="中文标题" prop="cnTitle">
                <el-input v-model="form.cnTitle"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="平台类型" prop="sourceType">
                <el-select
                  v-model="form.sourceType"
                  style="width: 100%"
                  clearable
                >
                  <el-option
                    v-for="(item, index) in sourceTypeList"
                    :key="index"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="媒体来源" prop="sourceName">
                <el-select
                  v-model="form.sourceName"
                  style="width: 100%"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="(item, index) in sourceTypeLists"
                    :key="index"
                    :label="item.name"
                    :value="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="发布时间" prop="publishTime">
                <el-date-picker
                  v-model="form.publishTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetime"
                  style="width: 100%"
                  clearable
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="文章作者" prop="author">
                <el-input
                  v-model="form.author"
                  style="width: 100%"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="原文链接" prop="originalUrl">
            <el-input v-model="form.originalUrl"></el-input>
          </el-form-item>
          <el-form-item label="摘要" prop="summary">
            <el-input
              v-model="form.summary"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
            ></el-input>
          </el-form-item>
          <el-form-item label="中文摘要" prop="cnSummary">
            <el-input
              v-model="form.cnSummary"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
            ></el-input>
          </el-form-item>
          <el-form-item label="中文内容" prop="cnContent">
            <editor v-model="form.cnContent" :minHeight="150"></editor>
          </el-form-item>
          <el-form-item label="文章内容" prop="content">
            <editor v-model="form.content" :minHeight="150"></editor>
          </el-form-item>
          <!-- <el-form-item label="封面图片" prop="cover">
            <el-upload action="#" ref="upload" :limit="3" :on-exceed="exceed" list-type="picture-card"
              :auto-upload="false" :headers="vertifyUpload.headers" :file-list="fileList" :on-change="handleChange"
              :http-request="requestLoad">
              <i slot="default" class="el-icon-plus"></i>
              <div slot="file" slot-scope="{ file }">
                <img class="el-upload-list__item-thumbnail" :src="file.url" alt="文件缩略图加载失败" />
                <span class="el-upload-list__item-actions">
                  <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="上传附件" prop="fileUrl">
            <el-upload class="upload-demo" :action="fileUrlurl" :before-upload="beforeUploadUrl" multiple :limit="1"
              :http-request="uploadUrlRequest" :on-success="uploadUrlSuccess" :file-list="fileUrlList"
              :on-exceed="uploadUrlExceed" :on-remove="uploadUrlRemove">
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
            </el-upload>
          </el-form-item> -->
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量导入弹框 -->
    <el-dialog
      title="批量导入报告"
      :visible.sync="batchImportVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="batch-import-container">
        <!-- 文件选择区域 -->
        <div class="file-select-area">
          <el-upload
            ref="batchUpload"
            action="#"
            :auto-upload="false"
            :show-file-list="false"
            multiple
            :on-change="handleFileSelect"
            accept=".pdf,.doc,.docx,.txt"
          >
            <el-button type="primary" icon="el-icon-upload">选择文件</el-button>
            <div slot="tip" class="el-upload__tip">
              支持选择多个文件，格式：PDF、DOC、DOCX、TXT
            </div>
          </el-upload>
        </div>

        <!-- 文件列表表格 -->
        <div class="file-table-area" v-if="batchImportFiles.length > 0">
          <el-table
            :data="batchImportFiles"
            border
            style="width: 100%; margin-top: 20px"
            max-height="300"
          >
            <el-table-column prop="fileName" label="文件名称" width="300">
              <template slot-scope="scope">
                {{ scope.row.fileName }}
              </template>
            </el-table-column>
            <el-table-column prop="sourceName" label="数据源名称">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.sourceName"
                  placeholder="请输入数据源名称"
                  size="small"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="removeFile(scope.$index)"
                  style="color: #f56c6c"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelBatchImport">取 消</el-button>
        <el-button
          type="primary"
          @click="confirmBatchImport"
          :disabled="batchImportFiles.length === 0"
        >
          确 认
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="Deepseek深度解读"
      :visible.sync="aiDialogVisible"
      width="1000px"
      :before-close="closeAiDialog"
      custom-class="ai-dialog"
      :close-on-click-modal="false"
    >
      <div class="ai-chat-container">
        <div class="chat-messages" ref="chatMessages">
          <div
            class="message"
            v-for="(message, index) in chatMessages"
            :key="index"
            :class="[
              'message',
              message.role === 'user' ? 'user-message' : 'ai-message',
            ]"
          >
            <div class="avatar">
              <img
                style="width: 30px; height: 30px"
                v-if="message.role === 'user'"
                :src="userAvatar || require('@/assets/images/home/<USER>')"
                alt="用户头像"
              />
              <img v-else src="@/assets/images/logo2.png" alt="AI头像" />
            </div>
            <div class="message-wrapper">
              <div
                v-if="
                  message.role === 'assistant' && isThinking && !message.content
                "
                class="thinking-animation"
              >
                <span></span>
                <span></span>
                <span></span>
              </div>
              <div
                v-else
                class="message-content"
                v-html="
                  message.role === 'assistant'
                    ? message.content
                    : message.content
                "
              ></div>
            </div>
          </div>
        </div>
      </div>
      <template slot="footer">
        <el-button @click="closeAiDialog">取 消</el-button>
      </template>
    </el-dialog>

    <!-- Deepseek报告解读弹窗 -->
    <deepseek-report-dialog
      :visible.sync="showDeepseekDialog"
      :article-data="currentArticle"
    />
  </div>
</template>

<script>
import API from "@/api/ScienceApi/index.js";
import request from "@/utils/request";
import { getToken } from "@/utils/auth";
import { MessageBox } from "element-ui";
import axios from "axios";
import { getListClassify } from "@/api/article/classify";
import { saveAs } from "file-saver";
import { blobValidate, tansParams } from "@/utils/ruoyi";
import { articleListEdit, uploadCover } from "@/api/articleCrawler/list";
import DeepseekReportDialog from "./DeepseekReportDialog.vue";
import { deepseekAiQa, difyAiQa, ollamaAiQa } from "@/api/infoEscalation/ai";
import { marked } from "marked";
import { getConfigKey } from "@/api/system/config";
import { getListByIds } from "@/api/article/articleHistory";

export default {
  props: {
    downLoadShow: {
      /* 下载按钮 */ required: false,
      type: Boolean,
      default: true,
    },
    editShow: {
      /* 编辑按钮 */ required: false,
      type: Boolean,
      default: true,
    },
    copyShow: {
      /* 复制按钮 */ reuqired: false,
      type: Boolean,
      default: true,
    },
    height: {
      type: Number,
      default: 655,
    },
    currentPage: {
      reuqired: true,
      default: 1,
    },
    pageSize: {
      reuqired: true,
      default: 50,
    },
    total: {
      reuqired: true,
      default: 0,
    },
    ArticleList: {
      required: true,
      default: [],
    },
    flag: {
      required: true,
    },
    SeachData: {
      required: true,
    },
    keywords: {
      type: String,
      default: "",
    },
    // 报告类型字段
    sourceType: {
      default: "",
    },
  },
  components: {
    DeepseekReportDialog,
  },
  data() {
    return {
      loading: false,
      regExpImg: /^\n$/,
      reportId: "",
      reportOptions: [],
      dialogVisible: false,
      checkedCities: [] /* 多选 */,
      checked: false /* 全选 */,
      html: "",
      text: "",
      that: this,
      tagShow: false,
      isIndeterminate: true,
      count: 0,
      separate: {},
      /* 标签功能 */
      tagDialog: false,
      formLabelAlign: {
        tag: "",
        industry: "",
        domain: "",
      },
      options: [],
      options1: [],
      tagItem: {} /* 标签对象 */,
      areaList: [] /* 领域 */,
      industry: [] /* 行业 */,
      num: 0,
      timer: null,
      drawer: false,
      drawerInfo: {},
      AreaId: null,
      translationBtnShow: null,
      open: false,
      sourceTypeList: [], // 数据源分类
      sourceLists: [], // 数据源列表
      sourceTypeLists: [],
      form: {}, // 表单参数
      rules: {
        // 表单校验
        title: [{ required: true, message: "文章标题为必填项" }],
        content: [{ required: true, message: "文章详情为必填项" }],
        publishTime: [{ required: true, message: "发布时间为必填项" }],
        cnTitle: [{ required: true, message: "中文名称为必填项" }],
        sourceType: [{ required: true, message: "平台类型为必填项" }],
        originalUrl: [{ required: true, message: "原文为必填项" }],
        summary: [{ required: true, message: "请填写摘要" }],
        // cnSummary: [{ required: true, message: '请填写中文摘要' }],
        sn: [{ required: true, message: "请填写文章地址唯一识别号" }],
      },
      vertifyUpload: {
        isUploading: false,
        // 设置上传的请求头部
        headers: {
          Authorization: "Bearer " + getToken(),
          ContentType: "application/json;charset=utf-8",
        },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/article/articleList/cover",
      },
      fileList: [],
      fileUrlList: [],
      fileUrlurl:
        process.env.VUE_APP_BASE_API + "/article/articleList/upload/file",
      showSummary: true,
      // 批量导入相关数据
      batchImportVisible: false,
      batchImportFiles: [],
      // Deepseek报告解读弹窗
      showDeepseekDialog: false,
      currentArticle: {},
      // ai相关
      aiDialogVisible: false,
      chatMessages: [],
      isThinking: false,
      userAvatar: "", // 用户头像
      streamingMessage: "", // 添加用于存储正在流式输出的消息
      markdownOptions: {
        gfm: true,
        breaks: true,
        headerIds: true,
        mangle: false,
        headerPrefix: "",
        pedantic: false,
        sanitize: false,
        smartLists: true,
        smartypants: true,
        xhtml: true,
      },
      isRequesting: false, // 标记是否正在请求中
      isAborted: false, // 标记是否已中断
      currentReader: null, // 当前的 reader
      aiPlatform: "",
      articleAiPrompt: "",
    };
  },
  computed: {},
  watch: {
    dialogVisible: function (newVal, oldVal) {
      if (newVal) {
        API.getNewBuilt({ sourceType: this.sourceType }).then((data) => {
          if (data.code == 200) {
            this.reportOptions = data.data;
          } else {
            this.$message({ message: "报告列表获取失败了", type: "error" });
          }
        });
      }
    },
    // 'formLabelAlign.industry': {
    //   handler(newVal) {
    //     if (newVal == '') {
    //       this.options1 = this.industry
    //     }
    //   },
    //   deep: true
    // },
    // 'formLabelAlign.domain': {
    //   handler(newVal) {
    //     if (newVal == '') {
    //       this.options = this.areaList
    //     }
    //   },
    //   deep: true
    // },
    "SeachData.sortMode": {
      handler(newVal, oldVal) {
        this.Refresh();
      },
      deep: true,
    },
    "form.sourceType": {
      handler(newVal, oldVal) {
        this.sourceTypeLists = this.sourceLists.filter((item) => {
          return item.type == newVal;
        });
      },
      deep: true,
    },
  },
  mounted() {},
  created() {
    if (
      this.flag !== "MonitorUse" &&
      this.flag !== "specialSubjectUse" &&
      this.flag !== "Wechat"
    ) {
      this.openDialog();
    }
    if (this.flag !== "Wechat") {
      getListClassify().then((res) => {
        this.sourceTypeList = res.data;
      });
      API.getSourceList().then((data) => {
        if (data.code == 200) {
          this.sourceLists = data.data;
        }
      });
    }
    if (this.$route.query.domain) {
      getConfigKey("sys.ai.platform").then((res) => {
        if (res.code == 200) {
          this.aiPlatform = res.msg;
        }
      });
      getConfigKey("wechat.ai.articlePrompt").then((res) => {
        if (res.code == 200) {
          this.articleAiPrompt = res.msg;
        }
      });
      // 获取用户头像
      this.userAvatar = this.$store.getters.avatar;
    }

    this.showSummary = true;
  },
  updated() {},
  filters: {},
  methods: {
    // 处理科技相关字段的显示映射
    getTechnologyLabel(value) {
      const mapping = {
        0: "排除",
        1: "选中",
        2: "待定",
        3: "排队中",
      };
      return mapping[value];
    },
    // 安全处理摘要内容，避免null调用replace报错
    getSafeSummary(item) {
      const cnSummary = item.cnSummary || "";
      const summary = item.summary || "";

      const processedCnSummary = cnSummary
        ? cnSummary
            .replace(/p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6|ul|li|a/g, "span")
            .replace(/<img[^>]*>/g, "") // 移除img标签
            .replace(/\\n|\\\\n|\\\\\\n|\n/g, " ") // 移除各种换行符
            .replace(/\s+/g, " ") // 合并多个空格为一个
            .trim()
        : "";

      const processedSummary = summary
        ? summary
            .replace(/p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6|ul|li|a/g, "span")
            .replace(/<img[^>]*>/g, "") // 移除img标签
            .replace(/\\n|\\\\n|\\\\\\n|\n/g, " ") // 移除各种换行符
            .replace(/\s+/g, " ") // 合并多个空格为一个
            .trim()
        : "";

      return processedCnSummary || processedSummary;
    },
    // 处理发布时间的显示
    formatPublishTime(publishTime, webstePublishTime) {
      // 格式化publishTime为年月日
      const formattedPublishTime = this.parseTime(publishTime, "{y}-{m}-{d}");

      // 如果webstePublishTime不存在，直接返回publishTime
      if (!webstePublishTime) {
        return "[北京]" + formattedPublishTime;
      }

      let formattedWebsteTime = "";
      // 处理不同格式的webstePublishTime
      if (webstePublishTime) {
        // 处理2025-04-12 10:09:21.971191格式（包含连字符的标准格式）
        if (webstePublishTime.includes("-")) {
          const dateMatch = webstePublishTime.match(/(\d{4})-(\d{2})-(\d{2})/);
          if (dateMatch) {
            const year = dateMatch[1];
            const month = dateMatch[2];
            const day = dateMatch[3];
            formattedWebsteTime = `${year}-${month}-${day}`;
          } else {
            formattedWebsteTime = webstePublishTime;
          }
        }
        // 处理2025年04月14日 11:29:22格式（中文年月日格式，带"日"字）
        else if (
          webstePublishTime.includes("年") &&
          webstePublishTime.includes("月") &&
          webstePublishTime.includes("日")
        ) {
          const dateMatch = webstePublishTime.match(
            /(\d{4})年(\d{1,2})月(\d{1,2})日/
          );
          if (dateMatch) {
            const year = dateMatch[1];
            const month = dateMatch[2].padStart(2, "0");
            const day = dateMatch[3].padStart(2, "0");
            formattedWebsteTime = `${year}-${month}-${day}`;
          } else {
            formattedWebsteTime = webstePublishTime;
          }
        }
        // 处理2025年4月15格式（中文年月格式，不带"日"字）
        else if (
          webstePublishTime.includes("年") &&
          webstePublishTime.includes("月")
        ) {
          const dateMatch = webstePublishTime.match(
            /(\d{4})年(\d{1,2})月(\d{1,2})/
          );
          if (dateMatch) {
            const year = dateMatch[1];
            const month = dateMatch[2].padStart(2, "0");
            const day = dateMatch[3].padStart(2, "0");
            formattedWebsteTime = `${year}-${month}-${day}`;
          } else {
            formattedWebsteTime = webstePublishTime;
          }
        }
        // 处理2025/04/14 11:29:22格式（斜杠分隔的格式）
        else if (webstePublishTime.includes("/")) {
          const dateMatch = webstePublishTime.match(
            /(\d{4})\/(\d{1,2})\/(\d{1,2})/
          );
          if (dateMatch) {
            const year = dateMatch[1];
            const month = dateMatch[2].padStart(2, "0");
            const day = dateMatch[3].padStart(2, "0");
            formattedWebsteTime = `${year}-${month}-${day}`;
          } else {
            formattedWebsteTime = webstePublishTime;
          }
        } else {
          // 其他格式直接使用原值
          formattedWebsteTime = webstePublishTime;
        }
      }

      // 比较年月日是否相同
      if (formattedPublishTime === formattedWebsteTime) {
        return "[北京]" + formattedPublishTime;
      } else {
        return `[北京]${formattedPublishTime} / [当地]${webstePublishTime}`;
      }
    },

    // 检查文本是否有实际内容（去除HTML标签后）
    hasActualContent(text) {
      if (!text) {
        return false;
      }
      // 去除HTML标签
      const contentWithoutTags = text.replace(/<[^>]*>/g, "");
      // 检查是否有中文、英文、数字等实际内容
      return /[\u4e00-\u9fa5a-zA-Z0-9]/.test(contentWithoutTags);
    },
    // 关键字替换
    changeColor(str) {
      if (!str || !this.keywords) {
        return str;
      }

      let result = str;
      let keywords;

      // 解析关键词
      if (this.keywords.includes(",")) {
        keywords = this.keywords.split(",");
      } else if (this.keywords.includes("|")) {
        keywords = this.keywords.split("|");
      } else {
        keywords = [this.keywords];
      }

      // 过滤空关键词
      keywords = keywords.filter(
        (keyword) => keyword && keyword.trim().length > 0
      );

      keywords.forEach((keyitem) => {
        const trimmedKeyword = keyitem.trim();
        if (trimmedKeyword.length > 0) {
          // 转义特殊正则字符
          const escapedKeyword = trimmedKeyword.replace(
            /[.*+?^${}()|[\]\\]/g,
            "\\$&"
          );

          // 高亮替换字符串
          const replaceString = `<span class="highlight" style="color: red;">${trimmedKeyword}</span>`;

          // 使用更安全的方法：先分离HTML标签和文本内容
          const htmlTagRegex = /<[^>]*>/g;
          const parts = [];
          let lastIndex = 0;
          let match;

          // 分离HTML标签和文本
          while ((match = htmlTagRegex.exec(result)) !== null) {
            // 添加标签前的文本
            if (match.index > lastIndex) {
              parts.push({
                type: "text",
                content: result.substring(lastIndex, match.index),
              });
            }
            // 添加HTML标签
            parts.push({
              type: "html",
              content: match[0],
            });
            lastIndex = match.index + match[0].length;
          }

          // 添加最后剩余的文本
          if (lastIndex < result.length) {
            parts.push({
              type: "text",
              content: result.substring(lastIndex),
            });
          }

          // 如果没有HTML标签，直接处理整个字符串
          if (parts.length === 0) {
            parts.push({
              type: "text",
              content: result,
            });
          }

          // 只在文本部分进行关键词替换
          parts.forEach((part) => {
            if (part.type === "text") {
              // 使用全局替换，但要考虑词边界
              const regex = new RegExp(escapedKeyword, "gi");
              part.content = part.content.replace(regex, replaceString);
            }
          });

          // 重新组合结果
          result = parts.map((part) => part.content).join("");
        }
      });

      return result;
    },
    /* 下载Excel */
    async downLoadExcel() {
      if (this.checkedCities.length == 0) {
        this.$message({ message: "请选择要导出的数据", type: "warning" });
        return;
      }

      if (this.flag == "specialSubjectUse") {
        API.downLoadExcel(this.checkedCities).then((response) => {
          let a = document.createElement("a");
          a.href = window.URL.createObjectURL(response);
          a.download = `source_${new Date().getTime()}.xlsx`;
          a.click();
        });
      } else {
        await API.downLoadExportExcel(this.checkedCities).then((response) => {
          let a = document.createElement("a");
          a.href = window.URL.createObjectURL(response);
          a.download = `source_${new Date().getTime()}.xlsx`;
          a.click();

          // saveAs(blob, `source_${new Date().getTime()}.xlsx`)
        });
      }
    },
    batchDelete() {
      if (this.checkedCities.length == 0) {
        this.$message({ message: "请选择要删除的数据", type: "warning" });
        return;
      }
      this.$confirm("是否确认删除已勾选的数据项?")
        .then(() => {
          API.batchRemove(this.checkedCities.join(",")).then((response) => {
            this.$message({ message: "删除成功", type: "success" });
            this.$emit("Refresh");
            this.checkedCities = [];
          });
        })
        .catch(() => {});
    },
    /* 发布到每日最新热点 */
    publishHot() {
      if (this.checkedCities.length == 0) {
        this.$message({
          message: "请选择要发布到每日最新热点的数据",
          type: "warning",
        });
        return;
      }
      this.$confirm("是否确认发布已勾选的数据项到每日最新热点?")
        .then(() => {
          API.publishEverydayHot(this.checkedCities.join(",")).then(() => {
            this.$message({ type: "success", message: "发布成功!" });
            this.$emit("Refresh");
            this.checkedCities = [];
          });
        })
        .catch(() => {});
    },
    /* 返回顶部动画 */
    mainScorll() {
      var scrollStep = -this.$refs.scroll.scrollTop / (800 / 15); // 计算每一步滚动的距离
      var scrollInterval = setInterval(() => {
        if (this.$refs.scroll.scrollTop !== 0) {
          this.$refs.scroll.scrollBy(0, scrollStep); // 按照给定步长滚动窗口
        } else {
          clearInterval(scrollInterval); // 到达顶部时清除定时器
        }
      }, 15);
    },
    scrollChange() {
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.stopScroll();
      }, 500);
      // this.$refs.pagination.style.opacity = 0
      // this.$refs.pagination.style.transition = '0'
    } /* 滚动事件 */,
    stopScroll() {
      // this.$refs.pagination.style.transition = '1s'
      // this.$refs.pagination.style.opacity = 1
    },
    /* 下载 */
    downLoad() {
      this.dialogVisible = true;
    },
    /* 每页条数变化 */
    handleSizeChange(num) {
      this.$emit("handleSizeChange", num);
      this.mainScorll();
      this.checked = false;
    },
    /* 页码变化 */
    handleCurrentChange(current) {
      this.$emit("handleCurrentChange", current);
      this.mainScorll();
      this.checked = false;
    },
    /* 收藏 */
    async collect(item) {
      /* 点击列表收藏 */
      if (item.id) {
        this.checkedCities = [item.id];
      }
      /* 未选择提示 */
      if (this.checkedCities.length == 0) {
        this.$message({ message: "请选择要收藏的文章", type: "info" });
        return;
      }
      /* 收藏 */
      if (!item.favorites) {
        let res = await API.collectApi([item.id]);
        if (res.code) {
          this.$message({
            message: "收藏成功,请前往个人中心查看",
            type: "success",
          });
          this.$emit("Refresh");
          this.checkedCities = [];
          return;
        }
        this.$message({ message: "收藏失败", type: "info" });
      } else {
        let res = await API.cocelCollect([item.id]);
        if (res.code) {
          this.$message({ message: "已取消收藏", type: "success" });
          this.$emit("Refresh");
          this.checkedCities = [];
          return;
        }
        this.$message({ message: "取消收藏失败", type: "info" });
      }
    },
    /* 一键复制 */
    copyText(item) {
      navigator.clipboard
        .writeText(item.cnTitle)
        .then(() => {
          this.$message({ message: "已成功复制到剪贴板", type: "success" });
        })
        .catch(function () {
          alert("复制失败");
        });
    },
    typeHandle(data) {
      if (data == 1) {
        return "微信公众号";
      } else if (data == 2) {
        return "网站";
      } else if (data == 3) {
        return "手动录入";
      }
    },
    /* 选择事件 */
    handleCheckedCitiesChange(value) {
      this.checkedCities = value;
    },
    /* 全选 */
    handleCheckAllChange(val) {
      this.checkedCities = val ? this.ArticleList.map((item) => item.id) : [];
      this.isIndeterminate = false;
    },
    /* 刷新 */
    Refresh() {
      this.$emit("Refresh");
    },
    /*确定添加到报告 */
    async reportSubmit() {
      this.dialogVisible = false;
      let keyWordList = [];
      if (!this.reportId)
        return this.$message({
          message: "请选择要添加到的报告",
          type: "warning",
        });
      /* 单独添加 */
      if (this.separate.id) {
        // let keyword = Object.keys(this.separate.keywordCount)
        keyWordList.push({
          reportId: this.reportId,
          listId: this.separate.id,
          listSn: this.separate.sn,
        });
      } else {
        /* 批量添加 */
        if (this.checkedCities == "")
          return this.$message({
            message: "请选择要添加的数据",
            type: "warning",
          });
        this.checkedCities.forEach((item) => {
          let article = this.ArticleList.filter((value) => value.id == item);
          keyWordList.push({
            reportId: this.reportId,
            listId: item,
            listSn: article[0].sn,
          });
        });
      }
      let res = await API.AddReport(keyWordList);
      if (res.code == 200) {
        this.$message({ message: "已添加到报告", type: "success" });
        this.$emit("Refresh");
      } else {
        this.$message({
          message: "添加到报告失败,请联系管理员",
          type: "error",
        });
      }
      this.separate = {};
      this.reportId = "";
      this.checkedCities = [];
      this.checked = false;
    },
    /* 单独添加报告 */
    separateAdd(item) {
      this.dialogVisible = true;
      this.separate = item;
    },
    /* 跳转新页面 */
    openNewView(item, isLink) {
      if (isLink) {
        if (item.originalUrl) {
          window.open(item.originalUrl);
          return;
        }
        this.$message({ message: "该文章没有原文链接" });
        return;
      }
      window.open(
        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,
        "_blank"
      );
      // this.drawerInfo = item
      // this.drawer = true
    },
    /* 文章打标签 */
    tagHandler(item) {
      this.tagDialog = true;
      this.tagItem = item;
      if (item.industry) {
        this.formLabelAlign.industry = item.industry
          .split(",")
          .map((data) => Number(data));
      }
      if (item.domain) {
        this.formLabelAlign.domain = item.domain
          .split(",")
          .map((data) => Number(data));
      }
      this.formLabelAlign.tag = item.tags ? item.tags.split(",") : "";
    },
    /* 获取领域和分类 */
    async openDialog() {
      await API.areaList().then((data) => {
        if (data.code == 200) {
          this.areaList = data.data;
          this.options = data.data;
          API.industry().then((value) => {
            this.industry = value.data;
            this.options1 = value.data;
          });
        }
      });
    },
    /* 筛选领域 */
    remoteEvent(query) {
      this.options = this.areaList.filter((item) => item.fieldName == query);
    },
    /* 筛选行业 */
    remoteIndustry(query) {
      this.options1 = this.industry.filter(
        (item) => item.industryName == query
      );
    },
    async SubmitTag() {
      let params = {
        domain: String(this.formLabelAlign.domain),
        industry: String(this.formLabelAlign.industry),
        tags: String(this.formLabelAlign.tag),
        articleId: String(this.tagItem.id),
        docId: this.tagItem.docId ? String(this.tagItem.docId) : "",
      };
      let res = await API.tagAdd(params);
      if (res.code == 200) {
        this.$message({ message: "保存成功", type: "success" });
        setTimeout(() => {
          this.Refresh();
        }, 1000);
      } else {
        this.$message({ message: "保存失败", type: "error" });
      }
      this.closeTag();
    },
    closeTag() {
      this.$refs["ruleForm"].resetFields();
      this.formLabelAlign = {
        tag: "",
        industry: "",
        domain: "",
      };
      this.tagDialog = false;
    },
    async hotIncrease(item) {
      let isWhether = JSON.parse(item.isWhether);
      let res = await API.tagAdd({
        articleId: item.id,
        isWhether: +!Boolean(isWhether),
      });
      if (res.code == 200) {
        this.$message({ message: "操作成功", type: "success" });
        this.Refresh();
      } else {
        this.$message({ message: "操作失败", type: "error" });
      }
    },
    async openDrawer() {
      let docId = this.drawerInfo.docId;
      await API.AreaInfo(this.drawerInfo.id).then((res) => {
        if (res.code == 200) {
          this.drawerInfo = res.data;
          this.drawerInfo.docId = docId;
          /* 将字符串中的\n替换为<br> */
          this.translationBtnShow = !this.drawerInfo.cnContent;
          if (this.drawerInfo.cnContent || this.drawerInfo.content) {
            this.drawerInfo.cnContent = (
              this.drawerInfo.cnContent || this.drawerInfo.content
            ).replace(/\\n/g, (a, b, c) => {
              return "<br>";
            });
            this.drawerInfo.cnContent = (
              this.drawerInfo.cnContent || this.drawerInfo.content
            ).replace(/\${[^}]+}/g, "<br>");
            this.drawerInfo.cnContent = (
              this.drawerInfo.cnContent || this.drawerInfo.content
            ).replace("|xa0", "");
          }
        }
      });
    },
    /* 所属行业处理 */
    industryHandle(item) {
      let ids = [],
        str = "";
      if (item.industry) {
        ids = item.industry.split(",");
      }
      ids.forEach((data) => {
        this.industry.map((ele) => {
          if (ele.id == data) {
            if (str == undefined) {
              str = "";
            }
            str += ele.industryName + " ";
          }
        });
      });
      return str;
    },
    domainHandle(item) {
      let ids = [],
        str = "";
      if (item.domain) {
        ids = item.domain.split(",");
      }
      ids.forEach((data) => {
        this.areaList.map((ele) => {
          if (ele.id == data) {
            if (str == undefined) {
              str = "";
            }
            str += ele.fieldName + " ";
          }
        });
      });
      return str;
    },
    /* 快照生成 */
    resultEvent(item) {
      if (item == "BatchGeneration" && this.checkedCities.length == 0) {
        this.$message.warning("请先选择文章");
        return;
      }
      let ids = null;
      let zhuangtai = "生成";
      let url = "";
      if (item == "drawer") {
        ids = [this.drawerInfo.id];
        if (this.drawerInfo.snapshotUrl) zhuangtai = "查看";
        url = this.drawerInfo.snapshotUrl;
      } else if (item == "BatchGeneration") {
        ids = this.checkedCities;
      } else {
        ids = [item.id];
        if (item.snapshotUrl) zhuangtai = "查看";
        url = item.snapshotUrl;
      }
      if (zhuangtai == "生成") {
        if (this.flag == "MonitorUse") {
          API.downLoadExportKe(ids)
            .then((response) => {
              if (response.code == 200) {
                this.$msgbox({
                  title: "提示",
                  message: "快照正在生成中，请稍后查看",
                  showCancelButton: true,
                  confirmButtonText: "关闭",
                  cancelButtonText: "取消",
                  showCancelButton: false,
                  beforeClose: (action, instance, done) => {
                    done();
                  },
                });
              } else {
                this.$message({
                  message: "申请失败，请联系管理员，确认采集器是否正常",
                  type: "error",
                });
              }
            })
            .catch((err) => {});
        } else {
          API.downLoadExportZhuan(ids)
            .then((response) => {
              if (response.code == 200) {
                this.$msgbox({
                  title: "提示",
                  message: "快照正在生成中，请稍后查看",
                  showCancelButton: true,
                  confirmButtonText: "关闭",
                  cancelButtonText: "取消",
                  showCancelButton: false,
                  beforeClose: (action, instance, done) => {
                    done();
                  },
                });
              } else {
                this.$message({
                  message: "申请失败，请联系管理员，确认采集器是否正常",
                  type: "error",
                });
              }
            })
            .catch((err) => {});
        }
      } else {
        url = url.replace(new RegExp("/home/<USER>/dpx/server-api/", "g"), "/");
        url = url.replace(new RegExp("/home/<USER>/dpx/", "g"), "/");
        window.open(window.location.origin + url, "_blank");
      }
    },
    /* 附件下载 */
    async documentDownload(item) {
      this.loading = true;
      if (item.fileUrl) {
        const urls = item.fileUrl.split(",");
        for (const [index, url] of urls.entries()) {
          if (url.indexOf("https://") === -1) {
            setTimeout(async () => {
              await this.downLoadFun(url, index, item.cnTitle || item.title);
            }, index * 500);
          } else {
            this.$message.error("附件还没同步到当前系统，暂时无法下载");
          }
        }
      }
      this.loading = false;
    },

    async downLoadFun(url, index, title) {
      let formData = new FormData();
      formData.append("fileUrl", url);

      try {
        const response = await API.downloadFile(formData);
        const isBlob = blobValidate(response);

        if (isBlob) {
          const blob = new Blob([response]);
          let list = url.split("/");
          let fileName = list[list.length - 1];
          saveAs(blob, fileName);
        } else {
          const resText = await response.text();
          const rspObj = JSON.parse(resText);
          const errMsg =
            errorCode[rspObj.code] || rspObj.msg || errorCode["default"];
          this.$message.error(errMsg);
        }
      } catch (err) {
        // this.$message.error(`Error downloading file: ${err}`);
      } finally {
        // 确保 loading 在每次下载后都设置为 false
        this.loading = false;
      }

      // 之前的附件下载
      // if (item.annexUrl) {
      //   /* 有文件地址 直接下载 */
      //   let formData = new FormData()
      //   formData.append('id', item.id)
      //   this.loading = true
      //   API.documentDownloadKe(formData).then(res => {
      //     let a = document.createElement('a')
      //     a.href = URL.createObjectURL(res.data)
      //     a.download = res.headers['content-disposition'].split('filename=')[1]
      //     a.click()
      //     this.loading = false
      //   })
      // } else {
      //   /* 没有文件格式 申请下载 */
      //   API.documentDownload(id).then(response => {
      //     if (response.code == 200) {
      //       this.$msgbox({
      //         title: '提示',
      //         message: '附件正在同步中，请稍后下载',
      //         showCancelButton: true,
      //         confirmButtonText: '关闭',
      //         cancelButtonText: '取消',
      //         showCancelButton: false,
      //         beforeClose: (action, instance, done) => {
      //           done()
      //         }
      //       })
      //     } else {
      //       this.$message({ message: '附件下载失败', type: 'error' })
      //     }
      //   }).catch(err => { })
      // }
    },
    // 翻译标题
    translateTitle(row) {
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      API.translationTitle({
        originalText: row.title,
        docId: row.docId,
        id: row.id,
        translationField: "title",
        translationType: 1,
      })
        .then((res) => {
          this.drawerInfo.cnTitle = res.data;
          this.ArticleList[
            this.ArticleList.findIndex((value) => value.id == row.id)
          ].cnTitle = res.data;
          this.ArticleList[
            this.ArticleList.findIndex((value) => value.id == row.id)
          ].isTranslated = 1;
          loading.close();
        })
        .catch((err) => {
          loading.close();
        });
    },
    // 翻译文章
    translateEvent(row) {
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      API.translationTitle({
        originalText: row.content,
        docId: row.docId,
        id: row.id,
        translationField: "content",
        translationType: 1,
      })
        .then((res) => {
          this.drawerInfo.cnContent = res.data;
          this.ArticleList[
            this.ArticleList.findIndex((value) => value.id == row.id)
          ].cnContent = res.data;
          this.ArticleList[
            this.ArticleList.findIndex((value) => value.id == row.id)
          ].isTranslated = 1;
          this.translationBtnShow = false;
          loading.close();
        })
        .catch((err) => {
          loading.close();
        });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      API.AreaInfo(row.id).then((response) => {
        this.form = response.data;
        this.form.sourceType = Number(this.form.sourceType);
        this.form.docId = row.docId;
        // this.fileUrlList = this.form.fileUrl ? this.form.fileUrl.split(",").map(item => {
        //   return {
        //     name: item,
        //     url: item
        //   }
        // }) : []
        this.open = true;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除该条文章？"')
        .then(() => {
          return API.monitoringEsRemove({ id: row.id, docId: row.docId });
        })
        .then(() => {
          this.Refresh();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let queryForm = JSON.parse(JSON.stringify(this.form));
          // let cover = String(this.fileList.map(item => item.path))
          // queryForm.cover = cover
          articleListEdit(queryForm).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.Refresh();
          });
        }
      });
    },
    /* 自定义上传 */
    async requestLoad(file) {
      let data = new FormData();
      data.append("cover", file.file);
      await uploadCover(data).then((response) => {
        if (response.code == 200) {
          this.fileList.map((item) => {
            if (item.uid == file.file.uid) {
              item.path = response.imgUrl;
            }
          });
          this.$message({ message: "上传成功", type: "success" });
        } else {
          this.$message({ message: "上传失败,请稍候重试", type: "error" });
        }
      });
    },
    /* 文件超出限制 */
    exceed() {
      this.$message({
        message: "文件上传超出限制,最多可以上传三个文件",
        type: "info",
      });
    },
    /* 移除文件 */
    handleRemove(file) {
      this.fileList = this.fileList.filter((item) => item !== file);
    },
    // 文件更改
    handleChange(file, fileList) {
      this.fileList = fileList;
      this.$refs.upload.submit();
    },
    // 上传附件校验
    beforeUploadUrl(file) {
      // 判断文件是否为excel
      let fileName = file.name
          .substring(file.name.lastIndexOf(".") + 1)
          .toLowerCase(),
        condition =
          fileName == "pdf" ||
          fileName == "doc" ||
          fileName == "xls" ||
          fileName == "ppt" ||
          fileName == "xlsx" ||
          fileName == "pptx" ||
          fileName == "docx";
      let fileSize = file.size / 1024 / 1024 < 10;
      if (!condition) {
        this.$notify({
          title: "警告",
          message: "上传文件必须是pdf,doc,xls,ppt,xlsx,pptx,docx格式",
          type: "warning",
        });
      }
      /* 文件大小限制 */
      if (!fileSize) {
        this.$notify({
          title: "警告",
          message: "上传文件的大小不能超过 10MB!",
          type: "warning",
        });
      }
      return condition && fileSize;
    },
    // 文件上传成功
    uploadUrlSuccess(res, file) {
      this.$message({ message: "上传成功", type: "success" });
    },
    // 文件上传超出限制
    uploadUrlExceed() {
      this.$message({
        message: "文件上传超出限制,最多可以上传1个文件",
        type: "info",
      });
    },
    // 文件上传方法
    uploadUrlRequest(file) {
      if (this.form.originalUrl != null && this.form.originalUrl != "") {
        if (
          this.form.originalUrl.match(
            /(http|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/
          )
        ) {
          let data = new FormData();
          data.append("file", file.file);
          data.append("originalUrl", this.form.originalUrl);

          API.uploadFile(data).then((response) => {
            if (response.code == 200) {
              this.$message({ message: "上传成功", type: "success" });
              this.form.fileUrl = response.data;
            } else {
              this.$message({ message: "上传失败,请稍候重试", type: "error" });
              this.form.fileUrl = "";
            }
          });
        } else {
          this.$message({ message: "请填写正确的原文链接", type: "warning" });
          this.fileUrlList = [];
        }
      } else {
        this.$message({ message: "请填写原文链接", type: "warning" });
        this.fileUrlList = [];
      }
    },
    // 删除附件
    uploadUrlRemove() {
      API.removeFile({ filePath: this.form.fileUrl }).then((response) => {
        if (response.code == 200) {
          this.$message({ message: "删除成功", type: "success" });
          this.fileUrlList = [];
          this.form.fileUrl = "";
        } else {
          this.$message({ message: "删除失败,请稍候重试", type: "error" });
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        articleSn: null,
        title: null,
        cnTitle: null,
        sourceType: null,
        sourceName: null,
        sourceSn: null,
        originalUrl: null,
        shortUrl: null,
        author: null,
        description: null,
        summary: null,
        cnSummary: null,
        cover: null,
        publishType: null,
        publishCode: null,
        publishArea: null,
        publishTime: null,
        numberLikes: null,
        numberReads: null,
        numberCollects: null,
        numberShares: null,
        numberComments: null,
        emotion: null,
        status: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null,
        content: null,
        cnContent: null,
        fileUrl: null,
        industry: null,
        domain: null,
        tmpUrl: null,
        isFinish: null,
        groupId: null,
        appId: null,
      };
      this.resetForm("form");
    },
    // 批量导入相关方法
    // 打开批量导入弹框
    openBatchImportDialog() {
      this.batchImportVisible = true;
      this.batchImportFiles = [];
    },
    // 文件选择处理
    handleFileSelect(file, fileList) {
      // 将新选择的文件添加到列表中
      const newFiles = fileList.map((item) => ({
        fileName: item.name,
        file: item.raw,
        sourceName: "",
      }));
      this.batchImportFiles = newFiles;
    },
    // 删除文件
    removeFile(index) {
      this.batchImportFiles.splice(index, 1);
    },
    // 取消批量导入
    cancelBatchImport() {
      this.batchImportVisible = false;
      this.batchImportFiles = [];
      // 清空文件选择器
      this.$refs.batchUpload.clearFiles();
    },
    // 确认批量导入
    async confirmBatchImport() {
      // 验证数据源名称是否都已填写
      const emptySourceNames = this.batchImportFiles.filter(
        (item) => !item.sourceName.trim()
      );
      if (emptySourceNames.length > 0) {
        this.$message({
          message: "请为所有文件填写数据源名称",
          type: "warning",
        });
        return;
      }

      try {
        this.loading = true;

        // 创建FormData对象
        const formData = new FormData();

        // 添加文件到FormData
        this.batchImportFiles.forEach((item) => {
          formData.append("files", item.file);
        });

        // 获取数据源名称数组
        const sourceNames = this.batchImportFiles
          .map((item) => item.sourceName)
          .join(",");

        formData.append("sourceNames", sourceNames);

        // 调用批量导入API，传递FormData和sourceNames参数
        const response = await API.batchImportReports(formData);

        if (response.code === 200) {
          this.$message({
            message: "批量导入成功",
            type: "success",
          });
          this.batchImportVisible = false;
          this.batchImportFiles = [];
          this.$refs.batchUpload.clearFiles();
          // 刷新列表
          this.Refresh();
        } else {
          this.$message({
            message: response.msg || "批量导入失败",
            type: "error",
          });
        }
      } catch (error) {
        console.error("批量导入错误:", error);
        this.$message({
          message: "批量导入失败，请稍后重试",
          type: "error",
        });
      } finally {
        this.loading = false;
      }
    },
    reportAiChat() {
      this.showDeepseekDialog = true;
      if (this.checkedCities.length === 0) {
        this.$message({ message: "请选择要解读的文章", type: "warning" });
        return;
      }

      if (this.checkedCities.length > 1) {
        this.$message({ message: "请只选择一篇文章进行解读", type: "warning" });
        return;
      }

      // 获取选中的文章
      const selectedArticleId = this.checkedCities[0];
      const selectedArticle = this.ArticleList.find(
        (item) => item.id === selectedArticleId
      );

      if (selectedArticle) {
        this.currentArticle = selectedArticle;
        this.showDeepseekDialog = true;
      } else {
        this.$message({ message: "未找到选中的文章", type: "error" });
      }
    },
    // ai相关
    // dify
    async difyAiChat() {
      if (this.checkedCities.length === 0) {
        return this.$message({
          message: "请先选择要解读的文章",
          type: "warning",
        });
      }

      // 如果有正在进行的请求，中断它
      if (this.isRequesting) {
        this.isAborted = true;
        if (this.currentReader) {
          try {
            await this.currentReader.cancel();
          } catch (e) {
            console.log("中断之前的请求失败", e);
          }
        }
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      this.isRequesting = true;
      this.isAborted = false;
      this.aiDialogVisible = true;
      this.chatMessages = [];
      this.isThinking = true;

      try {
        // 获取选中的文章
        const selectedArticles = this.ArticleList.filter((article) =>
          this.checkedCities.includes(article.id)
        );
        const titles = selectedArticles
          .map((article) => `《${article.cnTitle || article.title}》`)
          .join("\n");

        // 获取文章内容
        const articlesResponse = await getListByIds(
          this.checkedCities.join(",")
        );
        if (!articlesResponse.data?.length) {
          throw new Error("获取文章内容失败");
        }

        // 格式化文章内容
        const articlesContent = articlesResponse.data
          .map((article, index) => {
            const title =
              selectedArticles[index]?.cnTitle ||
              selectedArticles[index]?.title ||
              "";
            const content = article.content || "";
            return `【第 ${index + 1} 篇文章】《${title}》\n\n${content}`;
          })
          .join("\n\n-------------------------------------------\n\n");

        // 添加用户消息
        this.chatMessages.push({
          role: "user",
          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\n${titles}`,
        });

        // 创建AI消息
        const aiMessage = {
          role: "assistant",
          content: "",
        };
        this.chatMessages.push(aiMessage);

        // 构建提示词
        const prompt =
          this.articleAiPrompt
            .replace("articleLength", this.checkedCities.length)
            .replace(/\&gt;/g, ">") +
          `**以下是待处理的文章：**\n\n${articlesContent}`;

        // 调用AI接口
        const response = await difyAiQa(
          articlesContent,
          "streaming",
          "dify.article.apikey"
        );
        if (!response.ok) {
          throw new Error("AI接口调用失败");
        }

        // 处理流式响应
        const reader = response.body.getReader();
        this.currentReader = reader;
        const decoder = new TextDecoder();
        let buffer = "";
        let pendingBuffer = ""; // 用于存储待处理的不完整数据
        let isInThinkTag = false; // 新增：标记是否在think标签内

        // 将Unicode转义字符(\uXXXX)转换为实际字符
        const decodeUnicode = (str) => {
          return str.replace(/\\u[\dA-Fa-f]{4}/g, (match) => {
            return String.fromCharCode(parseInt(match.replace(/\\u/g, ""), 16));
          });
        };

        // 更新内容的函数
        const updateContent = (newContent) => {
          try {
            const renderedContent = marked(newContent, this.markdownOptions);
            aiMessage.content = renderedContent;

            // 确保消息容器滚动到底部
            this.$nextTick(() => {
              const chatMessages = this.$refs.chatMessages;
              if (chatMessages) {
                chatMessages.scrollTop = chatMessages.scrollHeight;
              }
            });
          } catch (error) {
            console.error("渲染内容时出错:", error);
          }
        };

        // 处理流式响应
        while (true) {
          // 检查是否已中断
          if (this.isAborted) {
            throw new Error("AbortError");
          }

          const { done, value } = await reader.read();

          if (done) {
            // 处理最后可能剩余的数据
            if (pendingBuffer) {
              try {
                const lastData = JSON.parse(pendingBuffer);
                if (lastData.answer) {
                  // 解码Unicode转义字符
                  const decodedAnswer = decodeUnicode(lastData.answer);
                  buffer += decodedAnswer;
                  updateContent(buffer);
                }
              } catch (e) {
                console.warn("处理最后的数据时出错:", e);
              }
            }
            break;
          }

          const chunk = decoder.decode(value);
          pendingBuffer += chunk;

          // 处理完整的数据行
          while (pendingBuffer.includes("\n")) {
            const newlineIndex = pendingBuffer.indexOf("\n");
            const line = pendingBuffer.slice(0, newlineIndex).trim();
            pendingBuffer = pendingBuffer.slice(newlineIndex + 1);

            if (!line || line === "data:" || !line.startsWith("data:")) {
              continue;
            }

            try {
              const data = line.slice(5).trim();
              if (data === "[DONE]") {
                continue;
              }

              const jsonData = JSON.parse(data);
              if (!jsonData.answer) {
                continue;
              }

              // 跳过特殊字符
              if (jsonData.answer === "```" || jsonData.answer === "markdown") {
                continue;
              }

              // 解码Unicode转义字符
              let answer = decodeUnicode(jsonData.answer);

              // 检查是否包含<think>开始标签
              if (answer.includes("<think>")) {
                isInThinkTag = true;
                continue; // 跳过包含<think>的部分
              }

              // 检查是否包含</think>结束标签
              if (answer.includes("</think>")) {
                isInThinkTag = false;
                continue; // 跳过包含</think>的部分
              }

              // 只有不在think标签内的内容才会被添加到buffer中
              if (!isInThinkTag && answer) {
                buffer += answer;
                updateContent(buffer);
              }
            } catch (parseError) {
              console.warn("解析数据行时出错:", {
                line,
                error: parseError.message,
                pendingBuffer,
              });
              continue;
            }
          }
        }
      } catch (error) {
        console.error("AI解读出错:", error);
        this.$message.error(error.message || "AI解读失败，请稍后重试");
        if (this.chatMessages[1]) {
          this.chatMessages[1].content = "抱歉，服务器繁忙，请稍后再试";
        }
      } finally {
        this.currentReader = null;
        if (this.aiDialogVisible) {
          this.isThinking = false;
          this.isRequesting = false;
        }
      }
    },
    // Ollama
    async ollamaAiChat() {
      if (this.checkedCities.length === 0) {
        return this.$message({
          message: "请先选择要解读的文章",
          type: "warning",
        });
      }

      // 如果有正在进行的请求，中断它
      if (this.isRequesting) {
        this.isAborted = true;
        if (this.currentReader) {
          try {
            await this.currentReader.cancel();
          } catch (e) {
            console.log("中断之前的请求失败", e);
          }
        }
        // 等待之前的请求状态清理完成
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      this.isRequesting = true;
      this.isAborted = false;
      this.aiDialogVisible = true;
      this.chatMessages = [];
      this.isThinking = true;

      try {
        // 获取选中的文章
        const selectedArticles = this.ArticleList.filter((article) =>
          this.checkedCities.includes(article.id)
        );
        const titles = selectedArticles
          .map((article) => `《${article.cnTitle || article.title}》`)
          .join("\n");

        // 获取文章内容
        const articlesResponse = await getListByIds(
          this.checkedCities.join(",")
        );
        if (!articlesResponse.data?.length) {
          throw new Error("获取文章内容失败");
        }

        // 格式化文章内容
        const articlesContent = articlesResponse.data
          .map((article, index) => {
            const title =
              selectedArticles[index]?.cnTitle ||
              selectedArticles[index]?.title ||
              "";
            const content = article.content || "";
            return `【第 ${index + 1} 篇文章】《${title}》\n\n${content}`;
          })
          .join("\n\n-------------------------------------------\n\n");

        // 添加用户消息
        this.chatMessages.push({
          role: "user",
          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\n${titles}`,
        });

        // 创建AI消息
        const aiMessage = {
          role: "assistant",
          content: "",
        };
        this.chatMessages.push(aiMessage);

        // 构建提示词
        const prompt =
          this.articleAiPrompt
            .replace("articleLength", this.checkedCities.length)
            .replace(/\&gt;/g, ">") +
          `**以下是待处理的文章：**\n\n${articlesContent}`;

        // 调用AI接口
        const response = await ollamaAiQa(prompt, true);
        if (!response.ok) {
          throw new Error("AI接口调用失败");
        }

        // 处理流式响应
        const reader = response.body.getReader();
        this.currentReader = reader; // 保存当前的 reader
        const decoder = new TextDecoder();
        let buffer = "";
        let lastUpdateTime = Date.now();
        let isThinkContent = false;
        let tempBuffer = "";

        // 更新内容的函数
        const updateContent = (newContent) => {
          const currentTime = Date.now();
          // 控制更新频率，避免过于频繁的DOM更新
          if (currentTime - lastUpdateTime >= 50) {
            aiMessage.content = newContent;
            lastUpdateTime = currentTime;
            // 确保消息容器滚动到底部
            this.$nextTick(() => {
              const chatMessages = this.$refs.chatMessages;
              if (chatMessages) {
                chatMessages.scrollTop = chatMessages.scrollHeight;
              }
            });
          }
        };

        // 处理流式响应
        const processStream = async () => {
          try {
            while (true) {
              // 检查是否已中断
              if (this.isAborted) {
                throw new Error("AbortError");
              }

              const { done, value } = await reader.read();
              if (done) {
                if (buffer.length > 0) {
                  updateContent(buffer);
                }
                break;
              }

              const chunk = decoder.decode(value);
              const lines = chunk.split("\n").filter((line) => line.trim());

              for (const line of lines) {
                try {
                  const jsonData = JSON.parse(line);
                  if (!jsonData.response) continue;

                  const response = jsonData.response;

                  // 跳过特殊字符
                  if (response === "```" || response === "markdown") {
                    continue;
                  }

                  tempBuffer += response;

                  // 检查是否包含完整的think标签对
                  while (true) {
                    const thinkStartIndex = tempBuffer.indexOf("<think>");
                    const thinkEndIndex = tempBuffer.indexOf("</think>");

                    if (thinkStartIndex === -1 && thinkEndIndex === -1) {
                      // 没有think标签，直接添加到buffer
                      if (!isThinkContent) {
                        buffer += tempBuffer;
                        // 使用marked渲染markdown内容
                        updateContent(marked(buffer, this.markdownOptions));
                      }
                      tempBuffer = "";
                      break;
                    } else if (thinkStartIndex !== -1 && thinkEndIndex === -1) {
                      // 只有开始标签，等待结束标签
                      isThinkContent = true;
                      if (thinkStartIndex > 0) {
                        buffer += tempBuffer.substring(0, thinkStartIndex);
                        // 使用marked渲染markdown内容
                        updateContent(marked(buffer, this.markdownOptions));
                      }
                      tempBuffer = tempBuffer.substring(thinkStartIndex);
                      break;
                    } else if (thinkStartIndex === -1 && thinkEndIndex !== -1) {
                      // 只有结束标签，移除之前的内容
                      isThinkContent = false;
                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);
                      continue;
                    } else {
                      // 有完整的think标签对
                      if (thinkStartIndex > 0) {
                        buffer += tempBuffer.substring(0, thinkStartIndex);
                        // 使用marked渲染markdown内容
                        updateContent(marked(buffer, this.markdownOptions));
                      }
                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);
                      isThinkContent = false;
                      continue;
                    }
                  }
                } catch (parseError) {
                  console.warn("无效的JSON行，已跳过", {
                    line,
                    error: parseError.message,
                  });
                }
              }
            }
          } catch (streamError) {
            if (streamError.message === "AbortError") {
              throw new Error("AbortError");
            }
            console.error("处理流式响应时出错:", streamError);
            throw streamError;
          }
        };

        await processStream();
      } catch (error) {
        // 判断是否是中断导致的错误
        if (error.message === "AbortError") {
          console.log("请求已被中断");
          return;
        }
        console.error("AI解读出错:", error);
        this.$message.error(error.message || "AI解读失败，请稍后重试");
        if (this.chatMessages[1]) {
          this.chatMessages[1].content = "抱歉，服务器繁忙，请稍后再试";
        }
      } finally {
        this.currentReader = null; // 清理当前的 reader
        // 只有在没有被中断的情况下才重置状态
        if (this.aiDialogVisible) {
          this.isThinking = false;
          this.isRequesting = false;
        }
      }
    },
    // deepseek
    async deepseekAiChat() {
      if (this.checkedCities.length === 0) {
        return this.$message({
          message: "请先选择要解读的文章",
          type: "warning",
        });
      }

      // 如果有正在进行的请求，中断它
      if (this.isRequesting) {
        this.isAborted = true;
        if (this.currentReader) {
          try {
            await this.currentReader.cancel();
          } catch (e) {
            console.log("中断之前的请求失败", e);
          }
        }
        // 等待之前的请求状态清理完成
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      this.isRequesting = true;
      this.isAborted = false;
      this.aiDialogVisible = true;
      this.chatMessages = [];
      this.isThinking = true;

      const selectedArticles = this.ArticleList.filter((article) =>
        this.checkedCities.includes(article.id)
      );
      const titles = selectedArticles
        .map((article) => `《${article.cnTitle || article.title}》`)
        .join("\n");

      try {
        const articlesResponse = await getListByIds(
          this.checkedCities.join(",")
        );
        if (!articlesResponse.data || !articlesResponse.data.length) {
          throw new Error("Failed to get article contents");
        }

        const articlesContent = articlesResponse.data
          .map((article, index) => {
            const title =
              selectedArticles[index]?.cnTitle ||
              selectedArticles[index]?.title ||
              "";
            const content = article.content || "";
            return `【第 ${index + 1} 篇文章】《${title}》\n\n${content}`;
          })
          .join("\n\n-------------------------------------------\n\n");

        // 添加用户消息
        this.chatMessages.push({
          role: "user",
          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\n${titles}`,
        });

        // 创建AI消息并添加到对话中
        const aiMessage = {
          role: "assistant",
          content: "",
        };
        this.chatMessages.push(aiMessage);
        this.isThinking = true;

        const prompt =
          this.articleAiPrompt
            .replace("articleLength", this.checkedCities.length)
            .replace(/\&gt;/g, ">") +
          `\n\n**以下是待处理的文章：**\n\n${articlesContent}`;

        const response = await deepseekAiQa(prompt, true);

        if (response.ok) {
          const reader = response.body.getReader();
          this.currentReader = reader; // 保存当前的 reader
          const decoder = new TextDecoder();
          let buffer = "";
          let lastUpdateTime = Date.now();

          const updateContent = (newContent) => {
            const currentTime = Date.now();
            if (currentTime - lastUpdateTime >= 50) {
              aiMessage.content = newContent;
              lastUpdateTime = currentTime;
              this.$nextTick(() => {
                const chatMessages = this.$refs.chatMessages;
                if (chatMessages) {
                  chatMessages.scrollTop = chatMessages.scrollHeight;
                }
              });
            }
          };

          while (true) {
            // 检查是否已中断
            if (this.isAborted) {
              throw new Error("AbortError");
            }

            const { done, value } = await reader.read();
            if (done) {
              if (buffer.length > 0) {
                updateContent(buffer);
              }
              break;
            }

            const chunk = decoder.decode(value);
            try {
              const lines = chunk.split("\n");

              for (const line of lines) {
                if (!line.trim() || !line.startsWith("data: ")) continue;

                const data = line.slice(5);
                if (data === "[DONE]") break;

                try {
                  const jsonData = JSON.parse(data);
                  if (jsonData.choices?.[0]?.delta?.content) {
                    let content = jsonData.choices[0].delta.content;

                    // 跳过特殊字符
                    if (content === "```" || content === "markdown") {
                      continue;
                    }

                    buffer += content;
                    updateContent(buffer);
                  }
                } catch (parseError) {
                  console.error("Error parsing JSON:", parseError);
                }
              }
            } catch (e) {
              console.error("Error processing chunk:", e);
            }
          }
        } else {
          throw new Error("Request failed");
        }
      } catch (error) {
        // 判断是否是中断导致的错误
        if (error.message === "AbortError") {
          console.log("请求已被中断");
          return;
        }
        console.error("AI Chat Error:", error);
        this.$message.error("AI解读失败，请稍后重试");
        if (this.chatMessages[1]) {
          this.chatMessages[1].content = "抱歉，服务器繁忙，请稍后再试";
        }
      } finally {
        this.currentReader = null; // 清理当前的 reader
        // 只有在没有被中断的情况下才重置状态
        if (this.aiDialogVisible) {
          this.isThinking = false;
          this.isRequesting = false;
        }
      }
    },
    // 关闭AI对话
    closeAiDialog() {
      this.isAborted = true; // 设置中断标志
      if (this.currentReader) {
        this.currentReader.cancel(); // 中断当前的读取
      }
      this.aiDialogVisible = false;
      this.chatMessages = [];
      this.isThinking = false;
      this.isRequesting = false;
      this.currentReader = null;
    },
    articleAiChat() {
      if (this.aiPlatform === "dify") {
        this.difyAiChat();
      } else if (this.aiPlatform === "ollama") {
        this.ollamaAiChat();
      } else if (this.aiPlatform === "deepseek") {
        this.deepseekAiChat();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/* drawer样式修改 */
.main ::v-deep .el-drawer.drawer_box {
  width: 700px !important;
}

.MainArticle {
  width: 100%;
  box-shadow: 0 0px 10px 0px rgb(206, 205, 205);
  // margin-top: 10px;
  user-select: text;

  .TopBtnGroup {
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    align-items: center;
    border-bottom: solid 1px #e2e2e2;
  }

  .leftBtnGroup {
    display: flex;
    justify-content: space-around;
    gap: 15px;

    .toolTitle {
      color: #606266;
      font-size: 14px;
      cursor: pointer;
    }

    & > p {
      display: flex;
      align-items: center;
    }
  }

  .leftBtnGroup2 {
    display: flex;
    justify-content: space-around;
    align-items: center;
    gap: 15px;
    flex: 1;
    padding-left: 20px;

    .toolTitle {
      color: #606266;
      font-size: 14px;
      line-height: 36px;
      cursor: pointer;
    }
  }

  .scollBox {
    overflow-y: auto;
    height: calc(100vh - 389px);
    transition: transform 5s ease;

    .Articl {
      display: flex;
      width: 100%;
      border-bottom: solid 1px #d4d4d4;

      .Articl_left {
        width: 85%;
        padding-bottom: 16px;
      }

      .Articl_kqcb {
        width: 100%;
        padding-bottom: 16px;
        & > div:first-child {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }

      .ArticlTop {
        display: flex;
        justify-content: space-between;
        padding: 0 0 0 20px;
        font-size: 15px;
      }

      .ArticlMain {
        padding: 0 0 0 30px;
        color: #3f3f3f;
        font-size: 14px;
        line-height: 24px;
      }

      .ArticlMain > span:hover {
        color: #1889f3;
        border-bottom: solid 1px #0798f8;
        cursor: pointer;
      }

      .info_flex {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
      }

      .ArticlBottom {
        padding: 0 0 0 30px;
        display: flex;
        flex-wrap: wrap;
        gap: 0 25px;
        line-height: 24px;
        color: #9b9b9b;
        font-size: 14px;

        div {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .linkStyle:hover {
          border-bottom: solid 1px #1889f3;
        }

        .infomation {
          color: #464749;
          font-size: 14px;
          margin-left: 8px;
        }

        p {
          border: solid 1px #f0a147;
          width: 45px;
          height: 25px;
          line-height: 25px;
          font-size: 14px;
          color: #f0a147;
          text-align: center;
        }
      }

      .imgBox {
        padding: 0 20px 0 30px;
        display: flex;
        gap: 15px;
      }
    }
  }
}

.pagination {
  z-index: 2;
  background-color: rgba(255, 255, 255, 1);
  width: 100%;
  box-shadow: 0 0px 10px 0px rgb(206, 205, 205);
  padding: 10px 0;
}

.title_Article {
  color: rgb(8, 8, 8);
  font-size: 15px;
  line-height: 16px;
}

.title_Article:hover {
  color: #1889f3;
  border-bottom: solid 1px #0798f8;
}

.drawer_Style {
  z-index: 2;
  margin: 0 15px 0 15px;
  width: 661px;
  height: 80vh;

  .title {
    font-size: 16px;
    font-weight: 500px;
    text-align: center;
  }

  .source {
    color: #0798f8;
    text-align: center;
    font-size: 14px;
  }

  .time {
    font-size: 14px;
    text-align: center;
    margin-left: 10px;
    color: #9b9b9b;
  }
}

.drawer_Title {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.btnBox {
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;

  & > p {
    margin-bottom: 10px;
  }
}

// 批量导入弹框样式
.batch-import-container {
  .file-select-area {
    text-align: center;
    padding: 20px 0;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background-color: #fafafa;

    &:hover {
      border-color: #409eff;
    }
  }

  .file-table-area {
    margin-top: 20px;

    .el-table {
      border-radius: 4px;
    }
  }
}

::v-deep .el-icon-chat-dot-round:before {
  color: #1296db;
}

::v-deep .el-icon-document-add:before {
  color: #1296db;
}

.deepseek-text {
  color: #1296db; // 使用与图标相同的颜色
  margin-left: 4px;
  font-size: 14px;
  line-height: 24px;
}

// ai相关
.ai-chat-container {
  height: 550px;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 24px;

    .message {
      margin-bottom: 28px;
      display: flex;
      align-items: flex-start;

      .avatar {
        width: 42px;
        height: 42px;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;
        border: 2px solid #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          background-color: #fff;
        }
      }

      .message-wrapper {
        margin: 0 16px;
        max-width: calc(100% - 100px);
      }

      .message-content {
        padding: 12px 16px;
        border-radius: 12px;
        font-size: 16px;
        line-height: 1;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        position: relative;

        &::before {
          content: "";
          position: absolute;
          top: 14px;
          width: 0;
          height: 0;
          border: 6px solid transparent;
        }
      }
    }

    .user-message {
      flex-direction: row-reverse;

      .message-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
      }

      .message-content {
        background-color: #e6f3ff;
        color: #2d2d2d;
        line-height: 1.8em;
        font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,
          Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,
          Arial, sans-serif;

        &::before {
          right: -12px;
          border-left-color: #e6f3ff;
        }
      }
    }

    .ai-message {
      .message-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }

      .message-content {
        background-color: #fff;
        color: #2d2d2d;

        &::before {
          left: -12px;
          border-right-color: #fff;
        }
      }
    }
  }

  .thinking-animation {
    display: inline-flex;
    align-items: center;
    padding: 12px 16px;
    min-height: 45px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: 14px;
      left: -12px;
      width: 0;
      height: 0;
      border: 6px solid transparent;
      border-right-color: #fff;
    }

    span {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin: 0 3px;
      background-color: #409eff;
      border-radius: 50%;
      opacity: 0.7;
      animation: thinking 1.4s infinite ease-in-out both;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }

  .message-content {
    min-height: 45px;
    white-space: pre-wrap;
    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,
      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,
      Arial, sans-serif;

    ::v-deep {
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin: 0.05em 0 0.02em 0;
        font-weight: 600;
        line-height: 1.8em;
        color: #2d3748;
      }

      h1 {
        font-size: 1.6em;
        margin-top: 0;
        padding-bottom: 0.05em;
        margin-bottom: 0.02em;
      }

      h2 {
        font-size: 1.4em;
        padding-bottom: 0.05em;
        margin-bottom: 0.02em;
      }

      h3 {
        font-size: 1.2em;
      }

      p {
        margin: 0;
        line-height: 1.8em;
        color: #2d3748;
      }

      strong {
        font-weight: 600;
        color: #1a1a1a;
      }

      em {
        font-style: italic;
        color: #2c5282;
      }

      ul,
      ol {
        margin: 0;
        padding-left: 1em;
        display: flex !important;
        flex-direction: column !important;
        // row-gap: 20px !important;

        li {
          margin: 0;
          line-height: 1.8em;
          color: #2d3748;

          // 如果li中包含p标签，则设置行高为1
          &:has(p) {
            line-height: 1;
          }

          // 如果li中没有p标签，保持默认行高1.8em（已在上面设置）

          p {
            margin: 0;
            line-height: 1.8em;
          }
        }
      }

      blockquote {
        margin: 0.05em 0;
        padding: 0.05em 0.4em;
        color: #2c5282;
        background: #ebf8ff;
        border-left: 4px solid #4299e1;

        p {
          margin: 0.02em 0;
          line-height: 1.8em;
        }

        > :first-child {
          margin-top: 0;
        }

        > :last-child {
          margin-bottom: 0;
        }
      }

      code {
        padding: 0.05em 0.1em;
        margin: 0;
        font-size: 0.9em;
        background: #edf2f7;
        border-radius: 3px;
        color: #2d3748;
      }

      hr {
        height: 1px;
        margin: 0.1em 0;
        border: none;
        background-color: #e2e8f0;
      }
    }
  }
}

.chat-messages {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(192, 196, 204, 0.5);
    border-radius: 3px;

    &:hover {
      background-color: rgba(192, 196, 204, 0.8);
    }
  }
}

@keyframes thinking {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

// 修改弹窗样式
::v-deep .ai-dialog {
  .el-dialog__body {
    padding: 0;
    background-color: #f5f7fa;
  }

  .el-dialog__footer {
    padding: 15px 20px;
    background: #fff;
    border-top: 1px solid #e4e7ed;

    .el-button {
      padding: 9px 20px;
      font-size: 14px;
    }
  }
}
</style>
<style>
.title_Article {
  color: rgb(8, 8, 8);
  font-size: 15px;
}

.title_Article:hover {
  color: #1889f3;
  border-bottom: solid 1px #0798f8;
}
</style>
