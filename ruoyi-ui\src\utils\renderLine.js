import { antiShake } from "@/utils/utils";
import * as echarts from "echarts";
import china from "../assets/china.json";
echarts.registerMap("china", china);
let defaultOption = {
  xAxis: {
    type: "category",
    data: ["00:00", "01:00", "02:00", "03:00", "04:00", "05:00", "06:00"],
  },
  legend: {
    top: "93%",
  },
  dataZoom: [
    {
      show: true,
      height: 10,
      xAxisIndex: [0],
      bottom: 35,
      start: 0,
      end: 80,
      handleSize: "80%",
      handleStyle: {
        color: "#0b6ba7",
      },
      textStyle: {
        color: "#00000",
      },
      borderColor: "#90979c",
    },
    {
      type: "inside",
      show: true,
      height: 15,
      start: 1,
      end: 35,
    },
  ],
  grid: {
    left: "5%",
    right: "5%",
  },
  yAxis: {
    type: "value",
  },
  title: {
    text: "据下图可知，2023年01月01日 00:00:00 ~ 2023年12月31日 23:59:59，微信公众号信息xoxx条，占比x%；网页信息xoxx条，占比x%。",
    show: false,
    textStyle: {
      color: "rgb(106 107 108)",
      fontSize: "14px",
    },
  },
  tooltip: {
    show: true,
    trigger: "axis",
    axisPointer: {
      type: "line",
      axis: "auto",
    },
  },
  series: [
    {
      name: "WeChat",
      data: [150, 230, 224, 218, 135, 147, 260],
      type: "line",
    },
    {
      name: "web",
      data: [234, 432, 123, 256, 115, 187, 200],
      type: "line",
    },
  ],
};
let columnarOption = {
  xAxis: {
    type: "category",
    data: [],
  },
  yAxis: {
    type: "value",
  },
  grid: {
    top: 10,
    left: "5%",
    right: "5%",
  },
  tooltip: {},
  dataZoom: [
    {
      show: true,
      height: 8,
      xAxisIndex: [0],
      bottom: "8%",
      start: 10,
      end: 90,
      handleIcon:
        "path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z",
      handleSize: "110%",
      handleStyle: {
        color: "#d3dee5",
      },
      textStyle: {
        color: "rgba(0, 0, 0, 0.8)",
      },
      borderColor: "#90979c",
    },
    {
      type: "inside",
      show: true,
      height: 15,
      start: 1,
      end: 35,
    },
  ],
  series: [
    {
      data: [],
      type: "bar",
      barWidth: "20%",
    },
  ],
};
let total = [];
let AnnularOption = {
  // color: ["rgb(68, 230, 216)", "rgb(44, 109, 250)"],
  tooltip: {
    trigger: "item",
  },
  legend: {
    show: true,
    top: "88%",
    left: "center",
  },
  title: {
    show: true,
    text: "总占比",
    subtext: "   200%",
    top: "47%",
    left: "45.5%",
    align: "center",
    subtextStyle: {
      align: "center",
      width: 300,
      height: 40,
      color: "#4682b4",
    },
  },
  series: [
    {
      name: "",
      type: "pie",
      radius: ["40%", "70%"],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: "center",
      },
      itemStyle: {
        normal: {
          label: {
            show: true,
            position: "outside",
            color: "black",
            formatter: function (params) {
              var percent = 0;
              var totalNum = 0;
              // for (var i = 0; i < trafficWay.length; i++)
              // {
              //   total += trafficWay[i].value;
              // }
              total.push(params.value);
              total = Array.from(new Set(total));
              total.forEach((key) => {
                totalNum += key;
              });
              // total = 1048 + 735;
              percent = ((params.value / totalNum) * 100).toFixed(0);
              if (params.name !== "") {
                return params.name + "\n" + "\n" + percent + "%";
              } else {
                return "";
              }
            },
          },

          labelLine: {
            length: 30,
            length2: 20,
            show: true,
            color: "#00ffff",
          },
        },
      },

      emphasis: {
        label: {
          show: true,
          fontSize: 40,
          fontWeight: "bold",
        },
      },
      labelLine: {
        show: true,
      },
      data: [],
    },
  ],
};

let NationwideOption = {
  tooltip: {
    show: true,
    trigger: "item",
    borderColor: "#ffff",
    backgroundColor: "rgba(255,255,255,0.6)",
    color: "#3f4041",
    decoration: "none",
    enterable: true,
    showContent: true /* 是否显示tooltip */,
    confine: true,
    transitionDuration: 1,
    formatter: function (params) {
      if (params.value.length > 1) {
        return (
          "&nbsp;&nbsp;" +
          params.name +
          "&nbsp;&nbsp;&nbsp;" +
          params.value[2] +
          "人&nbsp;&nbsp;"
        );
      } else {
        return (
          "&nbsp;&nbsp;" +
          params.name +
          "&nbsp;&nbsp;&nbsp;" +
          params.value +
          "人&nbsp;&nbsp;"
        );
      }
    },
  },
  // 关联数据
  // geo: {
  //   // 指定类型
  //   type: "map",
  //   // 读取的是那个地图
  //   map: "china",
  //   // 一些其他属性
  //   zoom: 1.2, // 地图按比例显示 ，例如 填 2 就是放大 2倍 显示
  //   roam: true, // 允许能对地图进行缩放、拖动 的操作
  //   label: {
  //     show: true,
  //     color: "#fff",
  //     position: "top",
  //     // offset: [-5, 20],
  //     fontWeight: 300,
  //     // normal: {
  //     //     show: true,
  //     //     itemStyle: {
  //     //         color: '#fff'
  //     //     }
  //     // },
  //     label: {
  //       show: false,
  //     },
  //   },
  //   itemStyle: {
  //     areaColor: {
  //       type: "linear-gradient",
  //       x: 0.5,
  //       y: 0.5,
  //       r: 0.8,
  //       colorStops: [
  //         {
  //           offset: 0,
  //           color: "rgba(45,68,121,0.15)", // 0% 处的颜色
  //         },
  //         {
  //           offset: 1,
  //           color: "rgba(45,68,121,0.18)", // 100% 处的颜色
  //         },
  //       ],
  //       global: true, // 缺省为 false
  //     },
  //     // areaColor: 'transparent',
  //     borderColor: "#83BAFF",
  //     borderWidth: 1,
  //     shadowColor: "rgba(56,164,255,.26)",
  //     opacity: 0.5,
  //     shadowOffsetX: 5,
  //     shadowOffsetY: 5,
  //     shadowBlur: 5,
  //     show: true, // 是否显示对应地名
  //     textStyle: {
  //       //字体颜色
  //       color: "#797979",
  //     },

  //     color: "transparent", //悬浮背景
  //     textStyle: {
  //       color: "#fff",
  //     },
  //   },
  //   emphasis: {
  //     disabled: false,
  //     // focus: 'self',
  //     label: {
  //       color: "black",
  //       fontWeight: "bold",
  //       fontSize: 18,
  //     },
  //     itemStyle: {
  //       areaColor: "#f8bf3d", //悬浮区背景
  //       color: "pink",
  //       fontSize: 18,
  //       borderColor: "#f8bf3d",
  //       shadowBlur: "none",
  //       //  borderWidth:2,
  //     },
  //   },
  //   itemStyle: {
  //     borderColor: "#409eff",
  //     borderWidth: 2,
  //     areaColor: {
  //       type: "radial",
  //       x: 0.5,
  //       y: 0.5,
  //       r: 0.8,
  //       colorStops: [
  //         {
  //           offset: 0,
  //           color: "rgba(147, 235, 248, 0)", // 0% 处的颜色
  //         },
  //         {
  //           offset: 1,
  //           color: "rgba(147, 235, 248, .2)", // 100% 处的颜色
  //         },
  //       ],
  //       globalCoord: false, // 缺省为 false
  //     },
  //     shadowColor: "#409eff",
  //     // shadowColor: 'rgba(255, 255, 255, 1)',
  //     shadowOffsetX: -2,
  //     shadowOffsetY: 2,
  //     shadowBlur: 10,

  //     emphasis: {
  //       areaColor: "#409eff",
  //       borderWidth: 3,
  //     },
  //   },
  //   nameProperty: "name",
  // },
  geo: {
    map: "china",
    aspectScale: 0.75,
    layoutCenter: ["45%", "59.5%"], //地图位置
    layoutSize: "118%",
    roam: true,
    itemStyle: {
      normal: {
        borderColor: "rgba(147, 235, 248, 1)",
        borderWidth: 0.5,
        color: {
          type: "linear-gradient",
          x: 0,
          y: 1500,
          x2: 2500,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: "#009DA1", // 0% 处的颜色
            },
            {
              offset: 1,
              color: "#005B9E", // 50% 处的颜色
            },
          ],
          global: true, // 缺省为 false
        },
        opacity: 0.5,
      },
      emphasis: {
        areaColor: "#2a333d",
      },
    },
    regions: [
      {
        name: "南海诸岛",
        itemStyle: {
          areaColor: "rgba(0, 10, 52, 1)",
          borderColor: "rgba(0, 10, 52, 1)",
        },
        emphasis: {
          areaColor: "rgba(0, 10, 52, 1)",
          borderColor: "rgba(0, 10, 52, 1)",
        },
      },
    ],
    z: 2,
  },
  series: [
    {
      type: "map",
      map: "china",
      tooltip: {
        show: false,
      },
      label: {
        show: true,
        color: "#FFFFFF",
        fontSize: 16,
      },
      aspectScale: 0.75,
      layoutCenter: ["45%", "58%"], //地图位置
      layoutSize: "118%",
      roam: true,
      itemStyle: {
        normal: {
          borderColor: "rgba(147, 235, 248, 0.8)",
          borderWidth: 0.8,
          areaColor: {
            type: "linear-gradient",
            x: 0,
            y: 1200,
            x2: 1000,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: "#009DA1", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#005B9E", // 50% 处的颜色
              },
            ],
            global: true, // 缺省为 false
          },
        },
        emphasis: {
          areaColor: "rgba(147, 235, 78, 0)",
        },
      },
      zlevel: 1,
    },
  ],
};
/* resize监听dom变化 */
function reSizeObseve(Ele, resize, father) {
  /* 监听dom变化 */
  const ob = new ResizeObserver(antiShake(100, resize, father));
  ob.observe(Ele);
}
/* 折线图 */
export class renderLineCharts {
  constructor({ dom, data, titleShow, option = defaultOption, color, xAxis }) {
    const Ele = document.getElementById(dom);
    this.chart = echarts.init(Ele);
    this.option = option;
    this.xAxis = xAxis;
    if (titleShow) {
      this.option.title.show = titleShow;
    }
    /* 调用公共方法 */
    reSizeObseve(Ele, this.chart.resize, this);
    if (data && Object.prototype.toString.call(data) == "[object Object]") {
      let series = [];
      Object.keys(data).forEach((item) => {
        series.push({
          name: item,
          data: data[item],
          type: "line",
        });
      });
      this.option.series = series;
    } else if (
      data &&
      Object.prototype.toString.call(data) == "[object Array]"
    ) {
      this.option.series = data;
    }
  }
  render() {
    if (this.xAxis) {
      this.option.xAxis = this.xAxis;
    }
    this.option && this.chart.setOption(this.option);
    // this.option = defaultOption;
  }
  clear() {
    this.chart.clear();
  }
  reRender() {
    this.chart.resize();
  }
}
/* 柱状图 */
export class renderCol {
  constructor({ dom, data, option = columnarOption, xAxis }) {
    const Ele = document.getElementById(dom);
    this.chart = echarts.init(Ele);
    this.option = option;
    if (data) {
      this.option.series[0].data = data;
    }
    if (xAxis) {
      this.option.xAxis = xAxis;
    }
    /* 调用公共方法 */
    reSizeObseve(Ele, this.chart.resize, this);
  }
  render() {
    this.option && this.chart.setOption(this.option);
  }
  clear() {
    this.chart.clear();
  }
  resize() {
    this.chart.resize();
  }
}

/* 环形图 */
export class renderAnnular {
  constructor({ dom, data, title, legend }) {
    let ELe = document.getElementById(dom);
    this.chart = echarts.init(ELe);
    this.option = AnnularOption;
    if (!title) {
      this.option.title.show = title;
    }
    /* legend */
    this.option.legend.show = legend;
    reSizeObseve(ELe, this.chart.resize, this);
    if (data && Object.prototype.toString.call(data) == "[object Array]") {
      this.option.series[0].data = data;
      this.render();
    } else {
      this.render();
    }
  }
  render() {
    this.chart.setOption(this.option);
    this.option = AnnularOption;
  }
  clear() {
    this.chart.clear();
  }
}
/* 全国地图 */
export class renderNationwide {
  constructor(dom, data, option = NationwideOption) {
    let Ele = document.getElementById(dom);
    this.myChart = echarts.init(Ele);
    this.option = option;
    reSizeObseve(Ele, this.myChart.resize, this);
    this.render();
  }
  render() {
    this.myChart.setOption(this.option);
    //echarts 设置地图外边框以及多个geo实现缩放拖曳同步
    this.myChart.on("georoam", function (params) {
      var option = this.getOption(); //获得option对象
      if (params.zoom != null && params.zoom != undefined) {
        //捕捉到缩放时
        option.geo[0].zoom = option.series[0].zoom; //下层geo的缩放等级跟着上层的geo一起改变
        option.geo[0].center = option.series[0].center; //下层的geo的中心位置随着上层geo一起改变
      } else {
        //捕捉到拖曳时
        option.geo[0].center = option.series[0].center; //下层的geo的中心位置随着上层geo一起改变
      }
      this.setOption(option); //设置option
    });
  }
  clear() {}
}
