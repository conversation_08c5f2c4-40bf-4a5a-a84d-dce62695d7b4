import request from "@/utils/request";

export function largeSourceList(query) {
  return request({
    url: "large/source/list",
    method: "post",
    data: query,
  });
}
export function largeSourceDataList(data, query) {
  return request({
    url: "large/sourceData/list",
    method: "post",
    data: data,
    params: query,
  });
}
export function largeRegionDataList(data, query) {
  return request({
    url: "large/regionData/list",
    method: "post",
    data: data,
    params: query,
  });
}
export function largeHotList(data, query) {
  return request({
    url: "large/hot/list",
    method: "post",
    params: query,
    data: data,
  });
}
export function largeHotList2(data) {
  return request({
    url: "large/hot/newHotList",
    method: "post",
    data: data,
  });
}
export function largeStatisticsAllData(query) {
  return request({
    url: "large/statistics/allData",
    method: "post",
    data: query,
  });
}
export function largeTrendQueryByTime(query) {
  return request({
    url: "large/trend/queryByTime",
    method: "get",
    params: query,
  });
}
export function largeGatherQueryGatherData(query) {
  return request({
    url: "large/gather/queryGatherData",
    method: "get",
    params: query,
  });
}

export function infoScreen(item) {
  return request({
    url: `/screen/info/${item.id}`,
    method: "get",
    params: item,
  });
}
export function largeHotQueryById(id) {
  return request({
    url: `large/hot/queryById/${id}`,
    method: "get",
  });
}
export function largeRegionDataQueryById(id) {
  return request({
    url: `large/regionData/queryById/${id}`,
    method: "get",
  });
}
export function largeSourceDataQueryById(id) {
  return request({
    url: `large/sourceData/queryById/${id}`,
    method: "get",
  });
}
export function largeSourceGetKeywords(name) {
  return request({
    url: `/large/source/getKeywords/${name}`,
    method: "get",
  });
}
export function largeEnterpriseDataList(data) {
  return request({
    url: `/large/enterpriseData/list`,
    method: "get",
    params: data,
  });
}
export function largeEnterpriseKeywordsNames(enterpriseName) {
  let data = new FormData();
  data.append("enterpriseName", enterpriseName);
  return request({
    url: `/large/enterpriseKeywords/names`,
    method: "post",
    data: data,
  });
}
export function largeEnterpriseData(id) {
  return request({
    url: `/large/enterpriseData/${id}`,
    method: "get",
  });
}
export function aiStatisticsAllData() {
  return request({
    url: `/large/aiStatistics/all/data`,
    method: "get",
  });
}
export function largeNseKeywordsNames(enterpriseName) {
  let data = new FormData();
  data.append("enterpriseName", enterpriseName);
  return request({
    url: `/large/nseKeywords/names`,
    method: "post",
    data: data,
  });
}
export function nsStatisticsAllData() {
  return request({
    url: `/large/nsStatistics/all/data`,
    method: "get",
  });
}

export function largeAitaDataList(data) {
  return request({
    url: `/large/aitaData/list`,
    method: "get",
    params: data,
  });
}
export function largeAitaKeywordsNames(keywordId) {
  let data = new FormData();
  data.append("keywordId", keywordId);
  return request({
    url: `/large/aitaKeywords/names`,
    method: "post",
    data: data,
  });
}
export function largeAitaData(id) {
  return request({
    url: `/large/aitaData/${id}`,
    method: "get",
  });
}
export function largeNsioDataList(data) {
  return request({
    url: `/large/naioData/list`,
    method: "get",
    params: data,
  });
}
export function largeNsioKeywordsNames(name) {
  let data = new FormData();
  data.append("name", name);
  return request({
    url: `/large/nsioKeywords/names`,
    method: "post",
    data: data,
  });
}
export function largeNsioData(id) {
  return request({
    url: `/large/naioData/${id}`,
    method: "get",
  });
}
export function achievementsResearch(data) {
  return request({
    url: `/large/achievements/research`,
    method: "get",
  });
}
export function largeAicKeywordsNames(character) {
  let data = new FormData();
  data.append("enterpriseName", character);
  return request({
    url: `/large/aicKeywords/names`,
    method: "post",
    data: data,
  });
}
export function largeNscKeywordsNames(character) {
  let data = new FormData();
  data.append("nsCharacterName", character);
  return request({
    url: `/large/nscKeywords/names`,
    method: "post",
    data: data,
  });
}
export function largeCharactersDataList(data) {
  return request({
    url: `/large/charactersData/list`,
    method: "get",
    params: data,
  });
}
export function largeCharactersData(id) {
  return request({
    url: `/large/charactersData/${id}`,
    method: "get",
  });
}

// 查询大屏字体
export function getScreenFont() {
  return request({
    url: "/system/user/getScreenFont",
    method: "get",
  });
}

// 保存大屏字体
export function saveScreenFont(screenFont) {
  return request({
    url: "/system/user/saveScreenFont/" + screenFont,
    method: "get",
  });
}

export function largeFTTList() {
  return request({
    url: "large/hot/FTTList",
    method: "get",
  });
}

export function getLargeFTT(sn) {
  return request({
    url: "large/hot/FTT/" + sn,
    method: "get",
  });
}