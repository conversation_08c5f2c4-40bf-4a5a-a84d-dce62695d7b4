<template>
  <div class="" style="height: calc(100vh - 61px);" v-show="showIframe">
    <div class="timelineBoxGB"></div>
    <!-- ifarm嵌套页面 -->
    <!-- <iframe :src="'https://b2yv5t.axshare.com/%E6%97%B6%E9%97%B4%E8%BD%B4.html'" style="width: 1920px;height: 1080px;transform: scale(0.9);top: 0;left: 0;transform-origin: top left;" width="100%" height="100%"
        frameborder="0" scrolling="yes"></iframe> -->
    <!-- <iframe v-show="showIframe" :src="'https://b2yv5t.axshare.com/#id=hkcomc&p=%E6%97%B6%E9%97%B4%E8%BD%B4&sc=1&c=1'"
      width="100%" height="100%" frameborder="0" scrolling="yes"></iframe> -->
    <div class="timelineTop">
      <div class="timelineTitle">激 光 雷 达</div>
      <div class="timelineIcon"></div>
    </div>
    <div class="timelineBox">
      <el-timeline>
        <el-timeline-item v-for="(item, index) in versionAnnouncementList" :key="index" color="#000">
          <div v-if="leftOrRight(index)" class="evenDiv">
            <div class="timelinItemBox0" @click="openArticle(item)">
              <div class="timelinItemBox-top f-r a-c j-e" style="color:#000">
                <div>{{ item.releaseTime }}</div>
              </div>
              <div class="timelinItemBox-bottom">
                <span>{{ item.content }}</span>
              </div>
            </div>
            <div class="timelinItemBox1">
              <div class="timelinItemBox-top f-r a-c" style="    color: #ffffff00;">
                <div>{{ item.releaseTime }}</div>
              </div>
              <div class="timelinItemBox-bottom" style="    color: #ffffff00;">
                {{ item.content }}
              </div>
            </div>
          </div>
          <div v-else class="unevenDiv">
            <div class="timelinItemBox0">
              <div class="timelinItemBox-top f-r a-c" style="    color: #ffffff00;">
                <div>{{ item.releaseTime }}</div>
              </div>
              <div class="timelinItemBox-bottom" style="    color: #ffffff00;">
                {{ item.content }}
              </div>
            </div>
            <div class="timelinItemBox1" @click="openArticle(item)">
              <div class="timelinItemBox-top f-r a-c" style="color:#000">
                <div>{{ item.releaseTime }}</div>
              </div>
              <div class="timelinItemBox-bottom">
                {{ item.content }}
              </div>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
    <el-drawer @open="openDrawer" :title="drawerInfo.cnTitle || drawerInfo.title" :visible.sync="drawer" :size="700"
      custom-class="drawer_box" direction="rtl" style="width: 100%;">
      <template slot="title">
        <span class="drawer_Title">{{ drawerInfo.cnTitle || drawerInfo.title }}</span>
      </template>
      <div class="drawer_Style">
        <p class="title" v-html="drawerInfo.cnTitle || drawerInfo.title"></p>
        <p style="text-align:center">
          <span class="source">{{ drawerInfo.sourceName }}</span>
          <span class="time">{{ drawerInfo.publishTime }}</span>
        </p>
        <div v-html="drawerInfo.cnContent" style="user-select: text !important;line-height:30px"></div>
        <el-empty description="当前文章暂无数据" v-if="!drawerInfo.cnContent"></el-empty>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import api from '@/api/ScienceApi/index.js'
export default {
  data() {
    return {
      loading: false,
      showIframe: false,
      versionAnnouncementList: [
        {
          id: 170038,
          content: "美国官员表示，美国国防部建立的一份名单将十几家中国企业增列其中，这份名单列出被控与中国军方合作的实体。根据美国国防部网站，五角大厦今天依照2021财政年度国防授权法案（National Defense Authorization Act, NDAA）1260H节法定要求，公布在美国直接或间接运营的「中国军事企业」（Chinese military companies）名单更新。新增列入名单的企业包括中国记忆体厂长江存储、人工智慧（AI）公司旷视科技、雷达制造商禾赛科技及科技公司东方网力。",
          releaseTime: "2024年1月31日",
        },
        {
          id: 170060,
          content: "新华社拉斯维加斯消息称，在自动驾驶竞争领域，中国激光雷达企业正在发挥越来越重要的作用。",
          releaseTime: "2024年1月15日",
        },
        {
          id: 170062,
          content: "小米支持的中国激光雷达公司禾赛即将在美国提交 IPO 申请。",
          releaseTime: "2024年1月10日",
        },
        {
          id: 170063,
          content: "美国战略与国际研究中心发布《国家和经济安全贸易工具包：激光雷达案例研究》报告指出，禾赛是中国销量最大的激光雷达公司，美国应考虑扩大制裁范围，将激光雷达技术纳入其中。",
          releaseTime: "2024年1月5日",
        },
        {
          id: 170064,
          content: "美国新闻网站BNN Breaking发表报道《LIDAR传感器技术：美国 - 中国贸易关系的新战场》称，美国激光雷达公司Ouster与其他美国公司一起发出警告，称中国激光雷达技术可能被滥用于间谍活动，为中国政府提供敏感数据，存在重大国家安全风险。",
          releaseTime: "2023年12月28日",
        },
        {
          id: 170068,
          content: "Armis 对外和政府事务副总裁汤姆·瓜伦特发表《为什么美国政府需要关注中国激光雷达产业》文章指出，激光雷达是一项快速发展的技术，具有多种重要的商业和军事应用。现在，它成为美国和中国之间因中国技术构成的潜在安全威胁而产生的最新紧张关系的核心。",
          releaseTime: "2023年12月19日",
        },
        {
          id: 170069,
          content: "20名美国国会议员联名写信给商务部长吉娜·雷蒙多（Gina Raimondo）、国防部长劳埃德·奥斯汀（Lloyd Austin）和财政部长珍妮特·耶伦（Janet Yellen），表示出于国家安全考虑，两党议员小组要求拜登政府调查所有中国光探测和测距（LiDAR）技术公司。",
          releaseTime: "2023年11月28日",
        },
        {
          id: 170070,
          content: "华尔街日报发文《科技贸易战蔓延，中国激光传感器制造商成为目标》称，禾赛科技正在反驳美国对激光雷达系统应用于下一代车辆和武器的担忧。",
          releaseTime: "2023年10月31日",
        },
        {
          id: 170071,
          content: "The Wire China发文称，中国在对自动驾驶至关重要的激光雷达系统方面已处于领先地位。",
          releaseTime: "2023年10月29日",
        },
        {
          id: 170072,
          content: "香港《南华早报》报道，中国宣称激光武器技术取得“巨大突破”，国防科技大学研究人员开发了冷却技术，提供了不间断连续发射激光束的潜力，使激光武器在战斗场景中更加可行。",
          releaseTime: "2023年8月15日",
        },
        {
          id: 170074,
          content: "麻省理工科技评论发文称，自动驾驶主导地位的争夺使中国在自动驾驶方面占据优势。",
          releaseTime: "",
        },
        {
          id: 170074,
          content: "美国国会研究服务处发表《美国与中国在新兴技术领域的竞争：激光雷达》报告指出，中国的激光雷达公司受益于中国的产业政策和相关补贴、市场保护、优惠（采购）以及其他普遍认为不公平的做法，中国的政策鼓励采取激进的策略来获取外国知识产权，这可能会扭曲贸易工具的普遍使用，并涉及可疑的做法或非法活动。",
          releaseTime: "2023年8月14日",
        },
        {
          id: 170074,
          content: "彭博社发文称，对中国自动驾驶汽车技术的的担忧正在加剧，激光雷达联盟称，激光雷达技术可以收集有关美国基础设施的大量敏感数据，并可能被中国公司用于军用车辆，使其实现自动驾驶。",
          releaseTime: "2023年7月26日",
        },
      ],
      drawer: false, // 文章详情弹窗
      drawerInfo: {}, // 文章详情
    }
  },
  created() {
    this.$prompt('请输入传播分析关键词', '传播分析', { confirmButtonText: '确定', cancelButtonText: '取消' }).then(({ value }) => {
      this.showIframe = true
    }).catch(() => {
      this.showIframe = true
    });
  },
  computed: {

  },
  methods: {
    leftOrRight(index) {
      if (index < 10) {
        return index % 2 == 1
      }
      return index % 2 == 0
    },
    // 文章详情
    openArticle(item, row) {
      window.open(`/expressDetails?id=${item.id}&docId=${item.docId}`, '_blank')
    },
    // 获取文章详情
    async openDrawer() {
      await api.AreaInfo(this.drawerInfo.id).then(res => {
        if (res.code == 200) {
          this.drawerInfo = res.data
          /* 将字符串中的\n替换为<br> */
          if (this.drawerInfo.cnContent || this.drawerInfo.content) {
            this.drawerInfo.cnContent = (this.drawerInfo.cnContent || this.drawerInfo.content).replace(/\\n/g, (a, b, c) => {
              return '<br>'
            })
            this.drawerInfo.cnContent = (this.drawerInfo.cnContent || this.drawerInfo.content).replace(/\${[^}]+}/g, '<br>')
            this.drawerInfo.cnContent = (this.drawerInfo.cnContent || this.drawerInfo.content).replace('|xa0', '')
            this.drawerInfo.cnContent = (this.drawerInfo.cnContent || this.drawerInfo.content).replace('/<img\b[^>]*>/gi', '')
          }
        }
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.h-100 {
  height: 100%;
}

.p-r {
  position: relative;
}

.f-r {
  display: flex;
  flex-direction: row;
}

.j-e {
  justify-content: flex-end;
}

.a-c {
  align-content: center;
  align-items: center;
}

.m-l20 {
  margin-left: 20px;
}

.timelineBoxGB {
  height: calc(100vh - 50px);
  width: 100%;
  overflow: hidden;
  background-image: url("../../assets/images/timeline-bg.png");
  background-size: cover;
  position: fixed;
  top: 50;
}

.timelineTop {
  display: flex;
  position: absolute;
  width: 21%;
  left: calc(40% - 7px);
  flex-direction: column;
  align-items: center;

  .timelineTitle {
    font-size: 42px;
    font-style: normal;
    font-weight: 700;
    height: 60px;

    &::before {
      content: "";
      width: 40%;
      height: 3px;
      background-color: #000000;
      position: absolute;
      left: 30%;
      top: 60px;
      z-index: 1;
    }
  }

  .timelineIcon {
    margin-top: 20px;
    width: 120px;
    height: 120px;
    background-image: url("../../assets/images/timeline-top.png");
    background-size: cover;
    z-index: 2;
  }

  &::after {
    content: "";
    width: 4px;
    height: 170px;
    background-color: #000000;
    position: absolute;
    left: calc(50% + 1px);
    top: 60px;
    z-index: 1;
  }
}

.timelineBox {
  position: absolute;
  left: 50%;
  top: 217px;
  width: 40%;

  .el-timeline-item {
    padding-bottom: 5px;
  }
}

.timelinItemBox0 {
  width: calc(100% - 28px - 2px);
  // height: calc(100% - 2px);
  position: absolute;
  right: calc(100% + 18px);
  // border: 1px solid #dedede;
  border-radius: 5px;
  cursor: pointer;
}

.timelinItemBox1 {
  // height: calc(100% - 2px);
  // border: 1px solid #dedede;
  border-radius: 5px;
  cursor: pointer;
}

.timelinItemBox-top {
  height: 35px;
  padding: 0 10px;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  color: #ffffff;
  font-size: 28px;
  font-style: normal;
  font-weight: 700;
  line-height: 50px;
}

.timelinItemBox-bottom {
  padding: 0 10px 10px;
  color: #000;
  line-height: 20px;
  white-space: pre-wrap;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px;
}

.evenDiv {
  .timelinItemBox1 {
    // border: 1px solid #ffffff;
    -moz-user-select: none;
    -khtml-user-select: none;
    user-select: none;
    cursor: auto;

    .timelinItemBox-bottom {
      color: #ffffff;
    }
  }
}

.unevenDiv {
  .timelinItemBox0 {
    // border: 1px solid #ffffff;
    -moz-user-select: none;
    -khtml-user-select: none;
    user-select: none;
    cursor: auto;

    .timelinItemBox-bottom {
      color: #ffffff;
    }
  }
}

::v-deep .el-timeline-item__tail {
  border-color: #000;
  border-width: 3px;
}

::v-deep .el-timeline-item__node--normal {
  left: -3px;
  width: 16px;
  height: 16px;
}

::v-deep .drawer_Title {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

::v-deep .drawer_Style {
  z-index: 2;
  margin: 0 15px 0 15px;
  width: 661px;
  height: 80vh;

  .title {
    font-size: 16px;
    font-weight: 500px;
    text-align: center;
  }

  .source {
    color: #0798f8;
    text-align: center;
    font-size: 14px;
  }

  .time {
    font-size: 14px;
    text-align: center;
    margin-left: 10px;
    color: #9b9b9b;
  }

  img {
    width: 100%;
  }
}

ul {
  padding: 0;
}

::v-deep .el-message-box {
  width: 300px !important;
}
</style>