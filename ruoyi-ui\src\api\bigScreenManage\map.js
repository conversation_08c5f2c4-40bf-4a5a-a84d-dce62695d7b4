import request from "@/utils/request";

// 查询大屏 科技安全监控 地图列表
export function listMap(query) {
  return request({
    url: "/screen/map/list",
    method: "get",
    params: query,
  });
}

// 查询大屏 科技安全监控 地图详细
export function getMap(id) {
  return request({
    url: "/screen/map/" + id,
    method: "get",
  });
}

// 新增大屏 科技安全监控 地图
export function addMap(data) {
  return request({
    url: "/screen/map",
    method: "post",
    data: data,
  });
}

// 修改大屏 科技安全监控 地图
export function updateMap(data) {
  return request({
    url: "/screen/map/edit",
    method: "post",
    data: data,
  });
}

// 删除大屏 科技安全监控 地图
export function delMap(id) {
  return request({
    url: "/screen/map/remove",
    method: "post",
    data: id,
  });
}
