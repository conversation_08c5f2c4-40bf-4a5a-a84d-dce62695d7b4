import request from '@/utils/request'

// 查询热点列列表
export function listList(query) {
  return request({
    url: '/hot/list/list',
    method: 'get',
    params: query
  })
}

// 查询热点列详细
export function getList(id) {
  return request({
    url: '/hot/list/' + id,
    method: 'get'
  })
}

// 新增热点列
export function addList(data) {
  return request({
    url: '/hot/list',
    method: 'post',
    data: data
  })
}

// 修改热点列
export function updateList(data) {
  return request({
    url: '/hot/list',
    method: 'put',
    data: data
  })
}

// 删除热点列
export function delList(id) {
  return request({
    url: '/hot/list/' + id,
    method: 'delete'
  })
}
