<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style=" display: block; position: relative; shape-rendering: auto;" width="164" height="164" preserveAspectRatio="xMidYMid" viewBox="0 0 164 164"><g transform="scale(0.64)"><filter id="blur-0.9406692398803724">
  <feGaussianBlur stdDeviation="3"></feGaussianBlur>
</filter><g data-idx="0" data-dup="" dx="0" dy="-128" style="transform-origin: 235.231px 45.5846px; animation: 4s linear 0s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="235.23100122120147" cy="173.58464630673697" r="9.103983835116342" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="1" data-dup="" dx="0" dy="-128" style="transform-origin: 16.583px 120.749px; animation: 2s linear -0.04s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="16.58302954028859" cy="248.7485114873564" r="12.606217655339247" fill="#ddd" filter="url(#blur-0.9406692398803724)" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="2" data-dup="" dx="0" dy="-128" style="transform-origin: 19.1207px 19.0216px; animation: 1.33333s linear -0.08s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="19.120716392932493" cy="147.02162945488408" r="10.400954587715914" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="3" data-dup="" dx="0" dy="-128" style="transform-origin: 204.022px 88.3001px; animation: 4s linear -0.12s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="204.0218057160565" cy="216.30006160194304" r="1.2267742962592167" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="4" data-dup="" dx="0" dy="-128" style="transform-origin: 175.931px 105.229px; animation: 2s linear -0.16s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="175.93053153491113" cy="233.22937378242054" r="8.605622544598656" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="5" data-dup="" dx="0" dy="-128" style="transform-origin: 200.348px -111.7px; animation: 1.33333s linear -0.2s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="200.34820329130724" cy="16.300136628673553" r="3.8409474364089746" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="6" data-dup="" dx="0" dy="-128" style="transform-origin: 144.474px 2.55743px; animation: 4s linear -0.24s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="144.47409806235152" cy="130.55742994153982" r="4.779455361898764" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="7" data-dup="" dx="0" dy="-128" style="transform-origin: 248.32px 118.35px; animation: 2s linear -0.28s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="248.3202444949348" cy="246.3503239552727" r="2.9546741926225444" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="8" data-dup="" dx="0" dy="-128" style="transform-origin: 171.126px -50.8829px; animation: 1.33333s linear -0.32s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="171.12633851124932" cy="77.11713753274788" r="15.15501665597421" fill="#eee" filter="url(#blur-0.9406692398803724)" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="9" data-dup="" dx="0" dy="-128" style="transform-origin: 233.268px -48.4357px; animation: 4s linear -0.36s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="233.26820565047439" cy="79.56435682275568" r="5.1002611502093895" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="10" data-dup="" dx="0" dy="-128" style="transform-origin: 163.442px 82.5161px; animation: 2s linear -0.4s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="163.44173189123651" cy="210.51608021711306" r="7.989452934551883" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="11" data-dup="" dx="0" dy="-128" style="transform-origin: 193.116px -64.602px; animation: 1.33333s linear -0.44s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="193.1164057584258" cy="63.398010245759615" r="6.2170455335664" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="12" data-dup="" dx="0" dy="-128" style="transform-origin: 40.4324px -102.977px; animation: 4s linear -0.48s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="40.43242359606011" cy="25.022536882740397" r="7.511122011605775" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="13" data-dup="" dx="0" dy="-128" style="transform-origin: 59.6286px 79.2343px; animation: 2s linear -0.52s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="59.628577162791515" cy="207.23428091823124" r="4.464511812478952" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="14" data-dup="" dx="0" dy="-128" style="transform-origin: 216.78px -82.3197px; animation: 1.33333s linear -0.56s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="216.77993221633648" cy="45.680271881524796" r="3.7323164011923553" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="15" data-dup="" dx="0" dy="-128" style="transform-origin: 18.5589px -84.4691px; animation: 4s linear -0.6s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="18.558890853769487" cy="43.5309335547685" r="5.414434560533745" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="16" data-dup="" dx="0" dy="-128" style="transform-origin: 17.2817px 98.6136px; animation: 2s linear -0.64s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="17.281643356726548" cy="226.61362650100384" r="8.694675935210231" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="17" data-dup="" dx="0" dy="-128" style="transform-origin: 181.463px -56.8984px; animation: 1.33333s linear -0.68s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="181.46297341379415" cy="71.10164141944101" r="13.439318355868387" fill="#ddd" filter="url(#blur-0.9406692398803724)" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="18" data-dup="" dx="0" dy="-128" style="transform-origin: 62.1415px -35.2786px; animation: 4s linear -0.72s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="62.14148293038541" cy="92.72138061659847" r="5.023065627598162" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="19" data-dup="b" dx="0" dy="-128" style="transform-origin: 70.0526px -126.216px; animation: 2s linear -0.76s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="70.05255057513438" cy="1.783724171955417" r="11.000108758977241" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="20" data-dup="" dx="0" dy="-128" style="transform-origin: 67.8273px -101.745px; animation: 1.33333s linear -0.8s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="67.82727828580447" cy="26.25492481356664" r="7.185044719626369" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="21" data-dup="" dx="0" dy="-128" style="transform-origin: 253.035px 42.4131px; animation: 4s linear -0.84s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="253.03486611783893" cy="170.41313855616974" r="4.969128620136502" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="22" data-dup="" dx="0" dy="-128" style="transform-origin: 116.779px 53.3207px; animation: 2s linear -0.88s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="116.77929259344407" cy="181.3206752746197" r="4.000996263678891" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="23" data-dup="" dx="0" dy="-128" style="transform-origin: 89.0118px -13.8757px; animation: 1.33333s linear -0.92s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="89.01184510425209" cy="114.12434911217437" r="15.305975781451952" fill="#ccc" filter="url(#blur-0.9406692398803724)" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="24" data-dup="r" dx="0" dy="-128" style="transform-origin: 0.318158px 6.09378px; animation: 4s linear -0.96s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,-128)"><circle cx="0.3181594229751688" cy="134.09377730954105" r="7.976726761094282" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="24" data-dup="r" dx="256" dy="-128" style="transform-origin: 256.318px 6.09378px; animation: 4s linear -0.96s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(256,-128)"><circle cx="0.3181594229751688" cy="134.09377730954105" r="7.976726761094282" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="0" data-dup="" dx="0" dy="128" style="transform-origin: 235.231px 301.585px; animation: 4s linear 0s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="235.23100122120147" cy="173.58464630673697" r="9.103983835116342" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="1" data-dup="" dx="0" dy="128" style="transform-origin: 16.583px 376.749px; animation: 2s linear -0.04s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="16.58302954028859" cy="248.7485114873564" r="12.606217655339247" fill="#ddd" filter="url(#blur-0.9406692398803724)" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="2" data-dup="" dx="0" dy="128" style="transform-origin: 19.1207px 275.022px; animation: 1.33333s linear -0.08s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="19.120716392932493" cy="147.02162945488408" r="10.400954587715914" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="3" data-dup="" dx="0" dy="128" style="transform-origin: 204.022px 344.3px; animation: 4s linear -0.12s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="204.0218057160565" cy="216.30006160194304" r="1.2267742962592167" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="4" data-dup="" dx="0" dy="128" style="transform-origin: 175.931px 361.229px; animation: 2s linear -0.16s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="175.93053153491113" cy="233.22937378242054" r="8.605622544598656" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="5" data-dup="" dx="0" dy="128" style="transform-origin: 200.348px 144.3px; animation: 1.33333s linear -0.2s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="200.34820329130724" cy="16.300136628673553" r="3.8409474364089746" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="6" data-dup="" dx="0" dy="128" style="transform-origin: 144.474px 258.557px; animation: 4s linear -0.24s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="144.47409806235152" cy="130.55742994153982" r="4.779455361898764" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="7" data-dup="" dx="0" dy="128" style="transform-origin: 248.32px 374.35px; animation: 2s linear -0.28s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="248.3202444949348" cy="246.3503239552727" r="2.9546741926225444" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="8" data-dup="" dx="0" dy="128" style="transform-origin: 171.126px 205.117px; animation: 1.33333s linear -0.32s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="171.12633851124932" cy="77.11713753274788" r="15.15501665597421" fill="#eee" filter="url(#blur-0.9406692398803724)" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="9" data-dup="" dx="0" dy="128" style="transform-origin: 233.268px 207.564px; animation: 4s linear -0.36s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="233.26820565047439" cy="79.56435682275568" r="5.1002611502093895" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="10" data-dup="" dx="0" dy="128" style="transform-origin: 163.442px 338.516px; animation: 2s linear -0.4s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="163.44173189123651" cy="210.51608021711306" r="7.989452934551883" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="11" data-dup="" dx="0" dy="128" style="transform-origin: 193.116px 191.398px; animation: 1.33333s linear -0.44s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="193.1164057584258" cy="63.398010245759615" r="6.2170455335664" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="12" data-dup="" dx="0" dy="128" style="transform-origin: 40.4324px 153.023px; animation: 4s linear -0.48s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="40.43242359606011" cy="25.022536882740397" r="7.511122011605775" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="13" data-dup="" dx="0" dy="128" style="transform-origin: 59.6286px 335.234px; animation: 2s linear -0.52s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="59.628577162791515" cy="207.23428091823124" r="4.464511812478952" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="14" data-dup="" dx="0" dy="128" style="transform-origin: 216.78px 173.68px; animation: 1.33333s linear -0.56s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="216.77993221633648" cy="45.680271881524796" r="3.7323164011923553" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="15" data-dup="" dx="0" dy="128" style="transform-origin: 18.5589px 171.531px; animation: 4s linear -0.6s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="18.558890853769487" cy="43.5309335547685" r="5.414434560533745" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="16" data-dup="" dx="0" dy="128" style="transform-origin: 17.2817px 354.614px; animation: 2s linear -0.64s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="17.281643356726548" cy="226.61362650100384" r="8.694675935210231" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="17" data-dup="" dx="0" dy="128" style="transform-origin: 181.463px 199.102px; animation: 1.33333s linear -0.68s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="181.46297341379415" cy="71.10164141944101" r="13.439318355868387" fill="#ddd" filter="url(#blur-0.9406692398803724)" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="18" data-dup="" dx="0" dy="128" style="transform-origin: 62.1415px 220.721px; animation: 4s linear -0.72s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="62.14148293038541" cy="92.72138061659847" r="5.023065627598162" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="19" data-dup="b" dx="0" dy="128" style="transform-origin: 70.0526px 129.784px; animation: 2s linear -0.76s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="70.05255057513438" cy="1.783724171955417" r="11.000108758977241" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="20" data-dup="" dx="0" dy="128" style="transform-origin: 67.8273px 154.255px; animation: 1.33333s linear -0.8s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="67.82727828580447" cy="26.25492481356664" r="7.185044719626369" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="21" data-dup="" dx="0" dy="128" style="transform-origin: 253.035px 298.413px; animation: 4s linear -0.84s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="253.03486611783893" cy="170.41313855616974" r="4.969128620136502" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="22" data-dup="" dx="0" dy="128" style="transform-origin: 116.779px 309.321px; animation: 2s linear -0.88s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="116.77929259344407" cy="181.3206752746197" r="4.000996263678891" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="23" data-dup="" dx="0" dy="128" style="transform-origin: 89.0118px 242.124px; animation: 1.33333s linear -0.92s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="89.01184510425209" cy="114.12434911217437" r="15.305975781451952" fill="#ccc" filter="url(#blur-0.9406692398803724)" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="24" data-dup="r" dx="0" dy="128" style="transform-origin: 0.318158px 262.094px; animation: 4s linear -0.96s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,128)"><circle cx="0.3181594229751688" cy="134.09377730954105" r="7.976726761094282" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="24" data-dup="r" dx="256" dy="128" style="transform-origin: 256.318px 262.094px; animation: 4s linear -0.96s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(256,128)"><circle cx="0.3181594229751688" cy="134.09377730954105" r="7.976726761094282" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="0" data-dup="" dx="0" dy="384" style="transform-origin: 235.231px 557.585px; animation: 4s linear 0s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="235.23100122120147" cy="173.58464630673697" r="9.103983835116342" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="1" data-dup="" dx="0" dy="384" style="transform-origin: 16.583px 632.749px; animation: 2s linear -0.04s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="16.58302954028859" cy="248.7485114873564" r="12.606217655339247" fill="#ddd" filter="url(#blur-0.9406692398803724)" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="2" data-dup="" dx="0" dy="384" style="transform-origin: 19.1207px 531.022px; animation: 1.33333s linear -0.08s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="19.120716392932493" cy="147.02162945488408" r="10.400954587715914" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="3" data-dup="" dx="0" dy="384" style="transform-origin: 204.022px 600.3px; animation: 4s linear -0.12s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="204.0218057160565" cy="216.30006160194304" r="1.2267742962592167" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="4" data-dup="" dx="0" dy="384" style="transform-origin: 175.931px 617.229px; animation: 2s linear -0.16s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="175.93053153491113" cy="233.22937378242054" r="8.605622544598656" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="5" data-dup="" dx="0" dy="384" style="transform-origin: 200.348px 400.3px; animation: 1.33333s linear -0.2s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="200.34820329130724" cy="16.300136628673553" r="3.8409474364089746" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="6" data-dup="" dx="0" dy="384" style="transform-origin: 144.474px 514.557px; animation: 4s linear -0.24s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="144.47409806235152" cy="130.55742994153982" r="4.779455361898764" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="7" data-dup="" dx="0" dy="384" style="transform-origin: 248.32px 630.35px; animation: 2s linear -0.28s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="248.3202444949348" cy="246.3503239552727" r="2.9546741926225444" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="8" data-dup="" dx="0" dy="384" style="transform-origin: 171.126px 461.117px; animation: 1.33333s linear -0.32s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="171.12633851124932" cy="77.11713753274788" r="15.15501665597421" fill="#eee" filter="url(#blur-0.9406692398803724)" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="9" data-dup="" dx="0" dy="384" style="transform-origin: 233.268px 463.564px; animation: 4s linear -0.36s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="233.26820565047439" cy="79.56435682275568" r="5.1002611502093895" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="10" data-dup="" dx="0" dy="384" style="transform-origin: 163.442px 594.516px; animation: 2s linear -0.4s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="163.44173189123651" cy="210.51608021711306" r="7.989452934551883" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="11" data-dup="" dx="0" dy="384" style="transform-origin: 193.116px 447.398px; animation: 1.33333s linear -0.44s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="193.1164057584258" cy="63.398010245759615" r="6.2170455335664" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="12" data-dup="" dx="0" dy="384" style="transform-origin: 40.4324px 409.023px; animation: 4s linear -0.48s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="40.43242359606011" cy="25.022536882740397" r="7.511122011605775" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="13" data-dup="" dx="0" dy="384" style="transform-origin: 59.6286px 591.234px; animation: 2s linear -0.52s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="59.628577162791515" cy="207.23428091823124" r="4.464511812478952" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="14" data-dup="" dx="0" dy="384" style="transform-origin: 216.78px 429.68px; animation: 1.33333s linear -0.56s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="216.77993221633648" cy="45.680271881524796" r="3.7323164011923553" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="15" data-dup="" dx="0" dy="384" style="transform-origin: 18.5589px 427.531px; animation: 4s linear -0.6s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="18.558890853769487" cy="43.5309335547685" r="5.414434560533745" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="16" data-dup="" dx="0" dy="384" style="transform-origin: 17.2817px 610.614px; animation: 2s linear -0.64s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="17.281643356726548" cy="226.61362650100384" r="8.694675935210231" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="17" data-dup="" dx="0" dy="384" style="transform-origin: 181.463px 455.102px; animation: 1.33333s linear -0.68s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="181.46297341379415" cy="71.10164141944101" r="13.439318355868387" fill="#ddd" filter="url(#blur-0.9406692398803724)" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="18" data-dup="" dx="0" dy="384" style="transform-origin: 62.1415px 476.721px; animation: 4s linear -0.72s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="62.14148293038541" cy="92.72138061659847" r="5.023065627598162" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="19" data-dup="b" dx="0" dy="384" style="transform-origin: 70.0526px 385.784px; animation: 2s linear -0.76s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="70.05255057513438" cy="1.783724171955417" r="11.000108758977241" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="19" data-dup="b" dx="0" dy="640" style="transform-origin: 70.0526px 641.784px; animation: 2s linear -0.76s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,640)"><circle cx="70.05255057513438" cy="1.783724171955417" r="11.000108758977241" fill="#eee" opacity="0.7674424412968572" data-fill="c2" style="fill: rgba(117, 199, 255, 0.25);"></circle></g></g><g data-idx="20" data-dup="" dx="0" dy="384" style="transform-origin: 67.8273px 410.255px; animation: 1.33333s linear -0.8s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="67.82727828580447" cy="26.25492481356664" r="7.185044719626369" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="21" data-dup="" dx="0" dy="384" style="transform-origin: 253.035px 554.413px; animation: 4s linear -0.84s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="253.03486611783893" cy="170.41313855616974" r="4.969128620136502" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="22" data-dup="" dx="0" dy="384" style="transform-origin: 116.779px 565.321px; animation: 2s linear -0.88s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="116.77929259344407" cy="181.3206752746197" r="4.000996263678891" fill="#ccc" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="23" data-dup="" dx="0" dy="384" style="transform-origin: 89.0118px 498.124px; animation: 1.33333s linear -0.92s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="89.01184510425209" cy="114.12434911217437" r="15.305975781451952" fill="#ccc" filter="url(#blur-0.9406692398803724)" opacity="0.7674424412968572" data-fill="c3" style="fill: rgba(107, 165, 255, 0.47);"></circle></g></g><g data-idx="24" data-dup="r" dx="0" dy="384" style="transform-origin: 0.318158px 518.094px; animation: 4s linear -0.96s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(0,384)"><circle cx="0.3181594229751688" cy="134.09377730954105" r="7.976726761094282" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g><g data-idx="24" data-dup="r" dx="256" dy="384" style="transform-origin: 256.318px 518.094px; animation: 4s linear -0.96s infinite normal forwards running move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4;"><g transform="translate(256,384)"><circle cx="0.3181594229751688" cy="134.09377730954105" r="7.976726761094282" fill="#ddd" opacity="0.7674424412968572" data-fill="c1" style="fill: rgba(89, 188, 255, 0.78);"></circle></g></g></g><style id="move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4" data-anikit="">@keyframes move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4
{
  0% {
    transform: matrix(1,0,0,1,0,0);
  }
  100% {
    transform: matrix(1,0,0,1,0,-256);
  }
}</style><style id="move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4" data-anikit="">@keyframes move-btt-d5545e4a-2429-450a-bcf6-2f10339100a4
{
  0% {
    transform: matrix(1,0,0,1,0,0);
  }
  100% {
    transform: matrix(1,0,0,1,0,-256);
  }
}</style></svg>