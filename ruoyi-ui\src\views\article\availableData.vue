<template>
  <div class="dashboard-editor-container">
    <el-input v-model="id" placeholder="请输入数据源id" clearable @keyup.enter.native="handleQuery" />
    <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
    <div v-html="res"></div>
  </div>
</template>
  
<script>
import request from "@/utils/request";

export default {
  components: {
  },
  data() {
    return {
      id: '',
      res: {}
    }
  },
  methods: {
    handleQuery() {
      request({
        url: `/article/articleList/getDocument/${this.id}`,
        method: "get",
      }).then(res => {
        this.res = res
      })
    }
  }
}
</script>
  
<style lang="scss" scoped></style>
  