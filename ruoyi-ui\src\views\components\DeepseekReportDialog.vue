<template>
  <el-dialog
    title="Deepseek报告解读"
    :visible.sync="visible"
    width="90%"
    :before-close="handleClose"
    class="deepseek-dialog"
    top="5vh"
    :close-on-click-modal="false"
  >
    <div class="deepseek-content">
      <split-pane
        split="vertical"
        :default-percent="30"
        :min-percent="20"
        :max-percent="50"
        @resize="handleResize"
        class="deepseek-split-pane"
      >
        <!-- 左侧关键词列表 -->
        <template slot="paneL">
          <div class="keywords-container">
            <div class="keywords-header">
              <h3>关键词分析</h3>
            </div>
            <div class="keywords-list" v-loading="keywordsLoading">
              <!-- 关键词列表 -->
              <div
                v-for="(item, index) in keywordsList"
                :key="index"
                class="keyword-item"
              >
                <div class="keyword-content">
                  <div class="keyword-main">
                    <span class="keyword-name">{{ item.keyword }}</span>
                    <div class="keyword-tags">
                      <el-tag
                        v-for="tag in item.techTags"
                        :key="tag"
                        size="mini"
                        type="info"
                        class="tech-tag"
                      >
                        {{ tag }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="keyword-status" v-if="item.hasAnalysis">
                    <el-tag type="success" size="mini"> 已有 </el-tag>
                  </div>
                </div>
              </div>

              <!-- 空状态提示 -->
              <div
                v-if="!keywordsLoading && keywordsList.length === 0"
                class="empty-keywords"
              >
                <i class="el-icon-document"></i>
                <p>暂无关键词数据</p>
              </div>
            </div>
          </div>
        </template>

        <!-- 右侧思维导图 -->
        <template slot="paneR">
          <div class="mindmap-container">
            <div class="mindmap-header">
              <h3>{{ articleData.title || "报告思维导图" }}</h3>
            </div>
            <div class="mindmap-wrapper">
              <div v-if="mindmapLoading" class="thinking-container">
                <div class="thinking-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <div class="thinking-text">正在加载思维导图...</div>
              </div>
              <div v-else-if="!mindmapContent" class="empty-state">
                <i class="el-icon-document"></i>
                <p>暂无思维导图数据</p>
              </div>
              <svg v-else ref="mindmapSvg" class="mindmap-svg"></svg>
            </div>
          </div>
        </template>
      </split-pane>
    </div>
  </el-dialog>
</template>

<script>
import { Transformer } from "markmap-lib";
import { Markmap } from "markmap-view";
import splitPane from "vue-splitpane";
import { difyAiQa } from "@/api/infoEscalation/ai";

export default {
  name: "DeepseekReportDialog",
  components: {
    splitPane,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    articleData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      keywordsLoading: false,
      mindmapLoading: false,
      keywordsList: [],
      mindmapContent: "",
      markmapInstance: null,
      useMockData: true, // 控制是否使用假数据
    };
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadData();
      } else {
        this.resetData();
      }
    },
  },
  methods: {
    handleClose() {
      this.$emit("update:visible", false);
    },
    resetData() {
      this.mindmapContent = "";
      this.markmapInstance = null;
      this.keywordsList = [];
      this.keywordsLoading = false;
      this.mindmapLoading = false;
    },
    async loadData() {
      if (this.useMockData) {
        // 使用假数据
        await this.loadMockData();
      } else {
        // 调用真实接口获取数据
        await this.loadDeepseekData();
      }
    },
    async loadDeepseekData() {
      this.keywordsLoading = true;
      this.mindmapLoading = true;

      try {
        const response = await difyAiQa("", "blocking", "dify.report.apikey");

        if (response.ok) {
          const data = await response.json();
          console.log("Deepseek API Response:", data);

          if (data.answer) {
            let content2 = JSON.parse(data.answer).answer;

            // 处理思考标记
            const thinkStartIndex = content2.indexOf("<think>");
            const thinkEndIndex = content2.indexOf("</think>");

            // 提取有效内容
            if (thinkStartIndex !== -1 && thinkEndIndex !== -1) {
              content2 = content2.substring(thinkEndIndex + 8).trim();
            } else {
              content2 = content2.trim();
            }

            // 清理markdown标记
            content2 = content2
              .replace(/```json\s*|```markdown\s*|```\s*/g, "")
              .replace(/```[a-zA-Z]*\s*/g, "")
              .replace(/\n\s*\n\s*\n/g, "\n\n")
              .trim();

            // 解析返回的JSON数据
            const parsedData = JSON.parse(content2);

            // 处理关键词数据
            if (parsedData.words && Array.isArray(parsedData.words)) {
              this.keywordsList = parsedData.words.map((item, index) => ({
                id: index + 1,
                keyword: item.word,
                techTags: item.in
                  ? item.in.split(",").map((tag) => tag.trim())
                  : [],
                hasAnalysis: true,
              }));
            }

            // 处理思维导图数据
            if (parsedData.article) {
              this.mindmapContent = parsedData.article;
              this.$nextTick(() => {
                this.renderMindmap();
              });
            }
          } else {
            throw new Error("Invalid response format");
          }
        } else {
          throw new Error("Request failed");
        }
      } catch (error) {
        console.error("Deepseek API Error:", error);
        this.$message.error("数据加载失败，请稍后重试");
      } finally {
        this.keywordsLoading = false;
        this.mindmapLoading = false;
      }
    },
    async loadMockData() {
      this.keywordsLoading = true;
      this.mindmapLoading = true;

      try {
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 1500));

        // 丰富的假数据
        this.keywordsList = [
          {
            id: 1,
            keyword: "人工智能",
            techTags: ["机器学习", "深度学习", "神经网络"],
            hasAnalysis: true
          },
          {
            id: 2,
            keyword: "大数据分析",
            techTags: ["数据挖掘", "实时处理"],
            hasAnalysis: true
          },
          {
            id: 3,
            keyword: "云计算",
            techTags: ["分布式计算", "虚拟化", "容器技术"],
            hasAnalysis: false
          },
          {
            id: 4,
            keyword: "区块链技术",
            techTags: ["分布式账本", "智能合约"],
            hasAnalysis: true
          },
          {
            id: 5,
            keyword: "物联网",
            techTags: ["传感器", "边缘计算", "5G"],
            hasAnalysis: false
          },
          {
            id: 6,
            keyword: "网络安全",
            techTags: ["加密技术", "防火墙"],
            hasAnalysis: true
          },
          {
            id: 7,
            keyword: "量子计算",
            techTags: ["量子算法", "量子纠缠"],
            hasAnalysis: false
          },
          {
            id: 8,
            keyword: "边缘计算",
            techTags: ["实时处理", "低延迟"],
            hasAnalysis: true
          },
          {
            id: 9,
            keyword: "自动驾驶",
            techTags: ["计算机视觉", "传感器融合", "路径规划"],
            hasAnalysis: false
          },
          {
            id: 10,
            keyword: "数字孪生",
            techTags: ["仿真建模", "实时同步"],
            hasAnalysis: true
          }
        ];

        // 思维导图假数据
        this.mindmapContent = this.getMockMindmapContent(this.articleData.title || '技术发展报告');

        this.$nextTick(() => {
          this.renderMindmap();
        });

      } catch (error) {
        console.error("Mock data loading error:", error);
        this.$message.error("数据加载失败");
      } finally {
        this.keywordsLoading = false;
        this.mindmapLoading = false;
      }
    },
    getMockMindmapContent(title) {
      return `# ${title}

## 技术发展趋势
### 人工智能
- 机器学习算法优化
- 深度学习模型创新
- 自然语言处理突破
- 计算机视觉应用扩展

### 云计算与边缘计算
- 混合云架构普及
- 边缘计算节点部署
- 服务网格技术成熟
- 无服务器计算发展

### 数据技术
- 实时数据处理
- 数据湖架构演进
- 隐私计算技术
- 数据治理标准化

## 应用场景分析
### 智能制造
- 工业4.0推进
- 数字孪生应用
- 预测性维护
- 质量智能检测

### 金融科技
- 区块链技术应用
- 智能风控系统
- 数字货币发展
- 开放银行生态

### 医疗健康
- 精准医疗技术
- 远程医疗服务
- 医疗AI诊断
- 健康数据管理

## 技术挑战
### 安全与隐私
- 数据安全保护
- 隐私计算需求
- 网络安全威胁
- 合规性要求

### 技术融合
- 跨领域技术整合
- 标准化协议制定
- 互操作性提升
- 生态系统建设

## 发展建议
### 技术投入
- 加大研发投资
- 人才培养计划
- 产学研合作
- 国际技术交流

### 产业布局
- 核心技术突破
- 产业链完善
- 应用场景拓展
- 商业模式创新`;
    },
    async renderMindmap() {
      if (!this.mindmapContent) return;

      try {
        await this.$nextTick();
        const svg = this.$refs.mindmapSvg;
        if (!svg) return;

        svg.innerHTML = "";

        const transformer = new Transformer();
        const { root } = transformer.transform(this.mindmapContent);

        const options = {
          autoFit: true,
          duration: 300,
          nodeMinHeight: 20,
          spacingVertical: 10,
          spacingHorizontal: 80,
          paddingX: 20,
          initialExpandLevel: -1,
          zoom: true,
          pan: true,
          color: (node) => {
            const colors = {
              0: "#0052ff",
              1: "#009600",
              2: "#ff6600",
              3: "#8000ff",
              4: "#ff0066",
            };
            return colors[node.depth] || "#0052ff";
          },
          nodeFont: (node) => {
            const fonts = {
              0: 'bold 18px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
              1: '600 16px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
              2: '500 14px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
            };
            return (
              fonts[node.depth] ||
              '400 12px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto'
            );
          },
          maxWidth: 250,
          linkShape: "diagonal",
          linkWidth: (node) => 2.5 - node.depth * 0.3,
        };

        this.markmapInstance = Markmap.create(svg, options, root);
      } catch (error) {
        console.error("思维导图渲染失败:", error);
        this.$message.error("思维导图渲染失败");
      }
    },
    handleResize() {
      if (this.markmapInstance && !this.mindmapLoading) {
        this.$nextTick(() => {
          try {
            this.markmapInstance.fit();
          } catch (error) {
            console.error("调整大小失败:", error);
          }
        });
      }
    },
  },
  beforeDestroy() {
    this.markmapInstance = null;
  },
};
</script>

<style lang="scss" scoped>
.deepseek-dialog {
  ::v-deep .el-dialog {
    border-radius: 8px;
    overflow: hidden;
  }

  ::v-deep .el-dialog__body {
    padding: 0;
    height: 80vh;
  }
}

.deepseek-content {
  height: 80vh;
  display: flex;
  flex-direction: column;
}

.deepseek-split-pane {
  flex: 1;
  height: 100%;
}

/* 左侧关键词列表样式 */
.keywords-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  border-right: 1px solid #e9ecef;
}

.keywords-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}

.keywords-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  position: relative;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 3px;

    &:hover {
      background-color: #ccc;
    }
  }
}

.keyword-item {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 6px;
  padding: 8px 12px;
  transition: all 0.3s ease;

  // &:hover {
  //   border-color: #409eff;
  //   box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  //   transform: translateY(-1px);
  // }
}

.keyword-content {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .keyword-main {
    display: flex;
    align-items: center;
    flex: 1;

    .keyword-name {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-right: 8px;
      white-space: nowrap;
    }

    .keyword-tags {
      display: flex;
      align-items: center;
      gap: 4px;

      .tech-tag {
        background: #f0f2f5;
        border: none;
        color: #666;
        font-size: 12px;
        height: 20px;
        line-height: 22px;
        padding: 0 6px;
      }
    }
  }

  .keyword-status {
    flex-shrink: 0;
    margin-left: 8px;
  }
}

/* 右侧思维导图样式 */
.mindmap-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.mindmap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .mindmap-actions {
    display: flex;
    gap: 8px;
  }
}

.mindmap-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.mindmap-svg {
  width: 100%;
  height: 100%;
  display: block;
}

.thinking-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 9999;

  .thinking-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;

    span {
      display: inline-block;
      width: 12px;
      height: 12px;
      margin: 0 6px;
      background-color: #409eff;
      border-radius: 50%;
      animation: thinking 1.4s infinite ease-in-out both;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }

  .thinking-text {
    color: #333;
    font-size: 16px;
    font-weight: 500;
  }
}

.empty-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;

  i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #ddd;
  }

  p {
    font-size: 16px;
    margin: 0;
  }
}

.empty-keywords {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;

  i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #ddd;
  }

  p {
    font-size: 16px;
    margin: 0;
    color: #999;
  }
}

@keyframes thinking {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.3;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 自定义 vue-splitpane 样式 */
::v-deep .splitter-pane-resizer.vertical {
  width: 8px !important;
  background: #f0f2f5;
  border-left: 1px solid #e9ecef;
  border-right: 1px solid #e9ecef;
  cursor: col-resize;

  &:hover {
    background: #e9ecef;
  }

  &:after {
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 30px;
    background-color: #ccc;
    border-radius: 1px;
  }
}

/* 思维导图节点样式 */
::v-deep .markmap-node {
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

::v-deep .markmap-node-circle {
  fill: transparent;
  stroke-width: 2px;
}

::v-deep .markmap-node-text {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial;

  tspan {
    fill: #333 !important;
    font-weight: 500;
  }
}

::v-deep .markmap-link {
  fill: none;
  stroke-width: 2px;
}
</style>
