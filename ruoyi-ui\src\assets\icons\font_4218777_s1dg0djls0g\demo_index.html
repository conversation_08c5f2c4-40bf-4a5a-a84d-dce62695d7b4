<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
        <span class="sub-title">彩色字体</span>
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4218777" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">收藏</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe651;</span>
                <div class="name">加星收藏</div>
                <div class="code-name">&amp;#xe651;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">标签</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69e;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe69e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">复制</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe727;</span>
                <div class="name">hot</div>
                <div class="code-name">&amp;#xe727;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63e;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe63e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#x10118;</span>
                <div class="name">报告</div>
                <div class="code-name">&amp;#x10118;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#x10119;</span>
                <div class="name">刷新-copy</div>
                <div class="code-name">&amp;#x10119;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: 
       url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2'),
       url('iconfont.woff?t=1692785335224') format('woff'),
       url('iconfont.ttf?t=1692785335224') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-shoucang"></span>
            <div class="name">
              收藏
            </div>
            <div class="code-name">.icon-shoucang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiaxingshoucang"></span>
            <div class="name">
              加星收藏
            </div>
            <div class="code-name">.icon-jiaxingshoucang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-biaoqian"></span>
            <div class="name">
              标签
            </div>
            <div class="code-name">.icon-biaoqian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuaxin"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.icon-shuaxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fuzhi"></span>
            <div class="name">
              复制
            </div>
            <div class="code-name">.icon-fuzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hot"></span>
            <div class="name">
              hot
            </div>
            <div class="code-name">.icon-hot
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiazai"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.icon-xiazai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-baogao"></span>
            <div class="name">
              报告
            </div>
            <div class="code-name">.icon-baogao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuaxin-copy"></span>
            <div class="name">
              刷新-copy
            </div>
            <div class="code-name">.icon-shuaxin-copy
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucang"></use>
                </svg>
                <div class="name">收藏</div>
                <div class="code-name">#icon-shoucang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiaxingshoucang"></use>
                </svg>
                <div class="name">加星收藏</div>
                <div class="code-name">#icon-jiaxingshoucang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-biaoqian"></use>
                </svg>
                <div class="name">标签</div>
                <div class="code-name">#icon-biaoqian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuaxin"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#icon-shuaxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuzhi"></use>
                </svg>
                <div class="name">复制</div>
                <div class="code-name">#icon-fuzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hot"></use>
                </svg>
                <div class="name">hot</div>
                <div class="code-name">#icon-hot</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiazai"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#icon-xiazai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baogao"></use>
                </svg>
                <div class="name">报告</div>
                <div class="code-name">#icon-baogao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuaxin-copy"></use>
                </svg>
                <div class="name">刷新-copy</div>
                <div class="code-name">#icon-shuaxin-copy</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
