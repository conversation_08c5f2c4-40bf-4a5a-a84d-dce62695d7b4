<!-- 日况总览 -->
<template>
  <div v-loading="DayInfo" class="brieFing" element-loading-text="数据加载中" element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)">
    <TopSeach :urlType="'dailyOverview'" :ActiveData="ActiveData" :buttonDisabled="buttonDisabled" :SwitchShow="false" :SeachData="SeachData"
      @SeachEvent="getData">
    </TopSeach>
    <statisticsVue :height="800" :statisticsList="statisticsList" :articleList="articleList" :lineData="lineData" />
  </div>
</template>

<script>
import TopSeach from '@/views/components/topSeach.vue'
import statisticsVue from '../components/statistics.vue'
import API from '@/api/infoEscalation/index.js'
export default {
  components: {
    TopSeach,
    statisticsVue
  },
  data () {
    return {
      ActiveData: {
        title: '信息上报专题',
      },
      SeachData: {
        timeRange: '',
        customDay: [],
      },
      statisticsList: [],
      lineData: {},
      articleList: [],
      DayInfo: false,
      buttonDisabled: false
    }
  },
  watch: {
    'SeachData.timeRange': {
      handler (newVal, oldVal) {
        this.SeachData.customDay = ''
      },
      deep: true
    }
  },
  created () {
    this.getData()
  },
  methods: {
    async getData () {
      this.DayInfo = true
      this.buttonDisabled = true
      if (this.buttonDisabled == false) return
      let params = {
        dateType: Number(this.SeachData.timeRange),
        endTime: this.SeachData.customDay[1],
        startTime: this.SeachData.customDay[0],
      }
      await API.dayInfo(params)
        .then((data) => {
          this.statisticsList = new Map(Object.entries(data.data.sourceNames))
          this.articleList = data.data.hotList
          this.lineData = data.data
        }).catch(err => {
          console.log(err);
        }).finally()
      this.DayInfo = false
      setTimeout(() => {
        this.buttonDisabled = false
      }, 1000);
    }
  },
}
</script>

<style lang="scss" scoped></style>