<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="80px"
      class="queryForm"
    >
      <el-row type="flex" justify="space-between">
        <el-col :span="6">
          <el-form-item label="区域名称" prop="regionName">
            <el-select
              v-model="queryParams.regionName"
              placeholder="请选择区域名称"
              @change="handleQuery"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in regionNames"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="平台类型" prop="sourceType">
            <el-select
              v-model="queryParams.sourceType"
              placeholder="请选择平台类型"
              @change="handleQuery"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.source_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="标题" prop="title">
            <el-input
              v-model="queryParams.title"
              placeholder="请输入标题"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否审核" prop="isScreenPass">
            <el-select
              v-model="queryParams.isScreenPass"
              placeholder="审核状态"
              @change="handleQuery"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.is_screen_pass"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="来源名称" prop="sourceName">
            <el-input
              v-model="queryParams.sourceName"
              placeholder="请输入来源名称"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" justify="space-between">
        <el-col :span="6">
          <el-form-item label="长度" prop="contentLength">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
              "
            >
              <el-input-number
                size="small"
                v-model="queryParams.minLength"
                :min="0"
                :controls="false"
                style="width: 40%"
              ></el-input-number>
              <span style="flex: 1; text-align: center">—</span>
              <el-input-number
                size="small"
                v-model="queryParams.maxLength"
                :min="0"
                :controls="false"
                style="width: 40%"
              ></el-input-number>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否翻译" prop="isTranslated">
            <el-select
              v-model="queryParams.isTranslated"
              placeholder="正文翻译状态"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.is_translated"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              style="margin-left: 40px"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multiple"
          @click="handlePass"
          >审核通过</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multiple"
          @click="handleNoPass"
          >审核不通过</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar> -->
    </el-row>

    <el-table
      v-loading="loading"
      :data="articleList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 250px)"
      ref="tableRef"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column
        prop="isScreenPass"
        label="审核"
        align="center"
        width="90"
      >
        <template slot-scope="scope">
          <el-radio-group
            v-model="scope.row.isScreenPass"
            class="radio-group"
            @input="(e) => handleRadioChange(e, scope.row.articleId)"
          >
            <el-radio :label="'1'" style="color: #67c23a">通过</el-radio>
            <el-radio :label="'2'" style="color: #f56c6c">不通过</el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
      <el-table-column
        label="文章id"
        align="center"
        prop="articleId"
        width="80"
      />
      <el-table-column prop="title" label="标题" align="center">
        <template slot-scope="scope">
          <el-link
            :href="`/expressDetails?id=${scope.row.articleId}&docId=${scope.row.articleId}`"
            :underline="false"
            target="_blank"
          >
            <span
              class="el-icon-document"
              style="word-break: normal; word-wrap: break-word"
            >
              {{ scope.row.title }}
            </span>
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="中文标题" align="center" prop="cnTitle">
        <template slot-scope="scope">
          <span
            class="el-icon-document"
            style="word-break: normal; word-wrap: break-word"
          >
            {{ scope.row.cnTitle }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="sourceType"
        label="平台类型"
        align="center"
        width="80"
      >
        <template slot-scope="scope">
          <dict-tag
            v-if="scope.row.sourceType"
            :options="dict.type.source_type"
            :value="scope.row.sourceType"
          />
          <span v-else>未定义</span>
        </template>
      </el-table-column>
      <el-table-column
        label="区域名称"
        align="center"
        prop="regionName"
        width="80"
      />
      <!-- <el-table-column
        prop="sourceId"
        label="所属领域"
        align="center"
        width="80"
      >
        <template slot-scope="scope">
          <dict-tag
            v-if="scope.row.sourceId"
            :options="dict.type.source_area"
            :value="scope.row.sourceId"
          />
          <span v-else>未定义</span>
        </template>
      </el-table-column> -->
      <el-table-column
        label="来源名称"
        align="center"
        prop="sourceName"
        width="100"
      />
      <el-table-column
        prop="isTranslated"
        label="翻译"
        align="center"
        width="65"
      >
        <template slot-scope="scope">
          <dict-tag
            v-if="scope.row.isTranslated"
            :options="dict.type.is_translated"
            :value="scope.row.isTranslated"
          />
          <span v-else>未定义</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="contentLength"
        label="长度"
        align="center"
        width="60"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.contentLength }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="发布时间"
        align="center"
        prop="publishTime"
        width="95"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publishTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="95"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="60"
      >
        <template slot-scope="scope">
          <div
            style="
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
            "
          >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              style="margin-left: 0"
              v-if="false"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改大屏 -区域分析关联文章对话框 -->
    <!--    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>-->
    <!--      <el-form ref="form" :model="form" :rules="rules" label-width="80px">-->
    <!--        <el-form-item label="关联文章id" prop="articleId">-->
    <!--          <el-input v-model="form.articleId" placeholder="请输入关联文章id" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="文章唯一标识" prop="sn">-->
    <!--          <el-input v-model="form.sn" placeholder="请输入文章唯一标识" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="标题" prop="title">-->
    <!--          <el-input v-model="form.title" type="textarea" placeholder="请输入内容" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="中文标题" prop="cnTitle">-->
    <!--          <el-input v-model="form.cnTitle" type="textarea" placeholder="请输入内容" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="来源名称" prop="sourceName">-->
    <!--          <el-input v-model="form.sourceName" placeholder="请输入来源名称" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="文章原文链接URL" prop="originalUrl">-->
    <!--          <el-input v-model="form.originalUrl" type="textarea" placeholder="请输入内容" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="区域名称" prop="regionName">-->
    <!--          <el-input v-model="form.regionName" placeholder="请输入区域名称" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="是否审核(0.未审核；1.已经审核)" prop="isScreenPass">-->
    <!--          <el-input v-model="form.isScreenPass" placeholder="请输入是否审核(0.未审核；1.已经审核)" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="审核时间" prop="passTime">-->
    <!--          <el-date-picker clearable-->
    <!--            v-model="form.passTime"-->
    <!--            type="date"-->
    <!--            value-format="yyyy-MM-dd"-->
    <!--            placeholder="请选择审核时间">-->
    <!--          </el-date-picker>-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="用户ID" prop="userId">-->
    <!--          <el-input v-model="form.userId" placeholder="请输入用户ID" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="发布时间" prop="publishTime">-->
    <!--          <el-date-picker clearable-->
    <!--            v-model="form.publishTime"-->
    <!--            type="date"-->
    <!--            value-format="yyyy-MM-dd"-->
    <!--            placeholder="请选择发布时间">-->
    <!--          </el-date-picker>-->
    <!--        </el-form-item>-->
    <!--      </el-form>-->
    <!--      <div slot="footer" class="dialog-footer">-->
    <!--        <el-button type="primary" @click="submitForm">确 定</el-button>-->
    <!--        <el-button @click="cancel">取 消</el-button>-->
    <!--      </div>-->
    <!--    </el-dialog>-->
    <el-dialog
      :title="'修改文章'"
      :visible.sync="open"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="dialog_Box">
        <el-form
          :model="form"
          class="form_Style"
          label-position="top"
          ref="form"
          :rules="rules"
          size="mini"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="文章标题" prop="title">
                <el-input v-model="form.title"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="中文标题" prop="cnTitle">
                <el-input v-model="form.cnTitle"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="平台类型" prop="sourceType">
                <el-select
                  v-model="form.sourceType"
                  style="width: 100%"
                  clearable
                >
                  <el-option
                    v-for="dict in dict.type.source_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="媒体来源" prop="sourceName">
                <!-- <el-select
                  v-model="form.sourceName"
                  style="width: 100%"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="(item, index) in sourceTypeLists"
                    :key="index"
                    :label="item.name"
                    :value="item.name"
                  ></el-option>
                </el-select> -->
                <el-input
                  v-model="form.sourceName"
                  style="width: 100%"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="发布时间" prop="publishTime">
                <el-date-picker
                  v-model="form.publishTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetime"
                  style="width: 100%"
                  clearable
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="文章作者" prop="author">
                <el-input
                  v-model="form.author"
                  style="width: 100%"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="原文链接" prop="originalUrl">
            <el-input v-model="form.originalUrl"></el-input>
          </el-form-item>
          <el-form-item label="摘要" prop="summary">
            <el-input
              v-model="form.summary"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
            ></el-input>
          </el-form-item>
          <el-form-item label="中文摘要" prop="cnSummary">
            <el-input
              v-model="form.cnSummary"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
            ></el-input>
          </el-form-item>
          <el-form-item label="中文内容" prop="cnContent">
            <editor v-model="form.cnContent" :minHeight="150"></editor>
          </el-form-item>
          <el-form-item label="文章内容" prop="content">
            <editor v-model="form.content" :minHeight="150"></editor>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/ScienceApi/index.js";
import {
  listRegionArticle,
  getRegionArticle,
  delRegionArticle,
  addRegionArticle,
  updateRegionArticle,
  updateRegionArticleById,
} from "@/api/article/screenRegion";
import {
  screenRegionNoPass,
  screenRegionPass,
} from "@/api/article/articleHistory";
import { articleListEdit } from "@/api/articleCrawler/list";
import { updateSourceArticleById } from "@/api/article/screenSource";

export default {
  name: "screenRegion",
  dicts: ["is_screen_pass", "source_type", "source_area", "is_translated"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      articleIds: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 大屏 -区域分析关联文章表格数据
      articleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        regionName: null,
        sourceType: null,
        title: null,
        isScreenPass: "0",
        sourceName: null,
        isTranslated: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      regionNames: [
        {
          value: "欧洲",
          label: "欧洲",
        },
        {
          value: "印度",
          label: "印度",
        },
        {
          value: "日本",
          label: "日本",
        },
        {
          value: "韩国",
          label: "韩国",
        },
        {
          value: "英国",
          label: "英国",
        },
        {
          value: "美国",
          label: "美国",
        },
        {
          value: "中国",
          label: "中国",
        },
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询大屏 -区域分析关联文章列表 */
    getList() {
      this.loading = true;
      listRegionArticle(this.queryParams).then((response) => {
        this.articleList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.$nextTick(() => {
          this.scrollToTop();
        });
      });
    },
    // 表格滚动到顶部
    scrollToTop() {
      if (this.$refs.tableRef) {
        const tableEl = this.$refs.tableRef.$el.querySelector(
          ".el-table__body-wrapper"
        );
        if (tableEl) {
          tableEl.scrollTop = 0;
        }
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        articleId: null,
        sn: null,
        title: null,
        cnTitle: null,
        sourceType: null,
        sourceName: null,
        originalUrl: null,
        regionName: null,
        status: null,
        isScreenPass: null,
        passTime: null,
        userId: null,
        publishTime: null,
        createTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.minLength = undefined;
      this.queryParams.maxLength = undefined;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.articleIds = selection.map((item) => item.articleId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加大屏 -区域分析关联文章";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      // const id = row.id || this.ids
      // getRegionArticle(id).then(response => {
      //   this.form = response.data;
      //   this.open = true;
      //   this.title = "修改大屏 -区域分析关联文章";
      // });
      API.AreaInfo(row.articleId).then((response) => {
        this.form = response.data;
        // this.form.sourceType = Number(this.form.sourceType)
        this.form.docId = row.articleId.toString();
        this.open = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            // updateRegionArticle(this.form).then(response => {
            //   this.$modal.msgSuccess("修改成功");
            //   this.open = false;
            //   this.getList();
            // });
            let queryForm = JSON.parse(JSON.stringify(this.form));
            articleListEdit(queryForm).then((response) => {
              updateRegionArticleById(this.form.id).then((response) => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            });
          } else {
            addRegionArticle(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除大屏 -区域分析关联文章编号为"' + ids + '"的数据项？'
        )
        .then(function () {
          return delRegionArticle(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    processSourceType(state) {
      if (state == 1) return "国内";
      if (state == 2) return "国外";
      if (state == 3) return "境内科技";
      if (state == 4) return "境外科技";
      return "未定义";
    },
    processPassState(state) {
      if (state == 0) return "未审核";
      if (state == 1) return "审核通过";
      if (state == 2) return "审核不通过";
      return "未定义";
    },
    handlePass(row) {
      const articleIds = row.articleId || this.articleIds;
      this.$modal
        .confirm("是否确认审核通过编号为" + articleIds + "的数据？")
        .then(function () {
          return screenRegionPass(articleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("审核通过");
        })
        .catch(() => {});
    },
    handleNoPass(row) {
      const articleIds = row.articleId || this.articleIds;
      this.$modal
        .confirm("是否确认审核不通过编号为" + articleIds + "的数据？")
        .then(function () {
          return screenRegionNoPass(articleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("审核不通过");
        })
        .catch(() => {});
    },
    handleRadioChange(e, id) {
      if (e == "1") {
        console.log("通过");
        return screenRegionPass(id);
      } else if (e == "2") {
        console.log("不通过");
        return screenRegionNoPass(id);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .queryForm {
  .el-form-item {
    width: 100%;
    margin-right: 0;
  }
  .el-form-item__content {
    width: calc(100% - 80px);
  }
}

::v-deep .radio-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .el-radio {
    margin-right: 0;
    margin-bottom: 2px;
  }
}
</style>
