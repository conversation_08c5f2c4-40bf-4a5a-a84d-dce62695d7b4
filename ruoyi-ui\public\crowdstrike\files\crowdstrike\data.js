﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,m)),n,[],o,_(h,p),q,[r,s,t,u],v,_(w,x,y,z,g,A,B,_(),C,[],D,_(E,F,G,H,I,_(J,K,L,M),N,null,O,P,P,Q,R,S,null,T,U,V,W,X,Y,Z,U,ba,bb,_(J,K,L,bc),bd,U,be,bf,_(bg,bh,bi,bj,bk,bj,bl,bj,bm,bn,L,_(bo,bp,bq,bp,br,bp,bs,bt)),i,_(j,k,l,m)),bu,_(),bv,_(),bw,_(bx,[_(by,bz,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(i,_(j,k,l,m),E,bH,I,_(J,K,L,bI),bd,bJ),bu,_(),bK,_(),bL,bh),_(by,bM,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),i,_(j,bR,l,bS),E,bH,bT,_(bU,bV,bW,bX),bd,bJ,I,_(J,K,L,bY),bZ,ca),bu,_(),bK,_(),bL,bh),_(by,cb,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),i,_(j,bR,l,bS),E,bH,bT,_(bU,bV,bW,cc),bd,bJ,I,_(J,K,L,bY),bZ,ca),bu,_(),bK,_(),bL,bh),_(by,cd,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(i,_(j,ce,l,cf),E,bH,bT,_(bU,cg,bW,cg),I,_(J,K,L,ch),ci,cj,ck,cl),bu,_(),bK,_(),bL,bh),_(by,cm,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(i,_(j,cn,l,co),E,bH,bT,_(bU,cp,bW,cq),I,_(J,K,L,cr),ci,cj,ck,cl),bu,_(),bK,_(),bL,bh),_(by,cs,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(i,_(j,ct,l,cu),E,bH,bT,_(bU,cv,bW,cw),I,_(J,K,L,ch),ck,cl),bu,_(),bK,_(),bL,bh),_(by,cx,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(i,_(j,cy,l,cz),E,bH,bT,_(bU,cA,bW,cB),I,_(J,K,L,cr),ck,cl),bu,_(),bK,_(),bL,bh),_(by,cC,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),i,_(j,cD,l,cE),E,cF,bT,_(bU,bV,bW,cG)),bu,_(),bK,_(),bL,bh),_(by,cH,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),i,_(j,cD,l,cE),E,cF,bT,_(bU,bV,bW,cI)),bu,_(),bK,_(),bL,bh),_(by,cJ,bA,h,bB,cK,y,cL,bE,cL,bF,bG,D,_(i,_(j,cM,l,cN),bT,_(bU,bV,bW,cO)),bu,_(),bK,_(),bx,[_(by,cP,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),i,_(j,cS,l,cT),E,cU,I,_(J,K,L,cV),bZ,ca,bb,_(J,K,L,cW)),bu,_(),bK,_(),cX,_(cY,cZ)),_(by,da,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),bT,_(bU,bn,bW,cT),i,_(j,cS,l,cT),E,cU,I,_(J,K,L,cV),bZ,ca,bb,_(J,K,L,cW)),bu,_(),bK,_(),cX,_(cY,cZ)),_(by,db,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),bT,_(bU,bn,bW,dc),i,_(j,cS,l,dd),E,cU,I,_(J,K,L,cV),bZ,ca,bb,_(J,K,L,cW)),bu,_(),bK,_(),cX,_(cY,de)),_(by,df,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bT,_(bU,cS,bW,bn),i,_(j,dg,l,cT),E,cU,I,_(J,K,L,cV),bZ,ca,ci,dh,di,dj,bb,_(J,K,L,cW)),bu,_(),bK,_(),cX,_(cY,dk)),_(by,dl,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bT,_(bU,cS,bW,cT),i,_(j,dg,l,cT),E,cU,I,_(J,K,L,cV),bZ,ca,bb,_(J,K,L,cW),ci,dh,di,dj),bu,_(),bK,_(),cX,_(cY,dk)),_(by,dm,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bT,_(bU,cS,bW,dc),i,_(j,dg,l,dd),E,cU,I,_(J,K,L,cV),bZ,ca,bb,_(J,K,L,cW),di,dj),bu,_(),bK,_(),cX,_(cY,dn))]),_(by,dp,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),i,_(j,cD,l,cE),E,cF,bT,_(bU,bV,bW,dq)),bu,_(),bK,_(),bL,bh),_(by,dr,bA,h,bB,cK,y,cL,bE,cL,bF,bG,D,_(i,_(j,ds,l,dt),bT,_(bU,bV,bW,du)),bu,_(),bK,_(),bx,[_(by,dv,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),i,_(j,dw,l,dx),bZ,ca,I,_(J,K,L,cV),bb,_(J,K,L,dy)),bu,_(),bK,_(),cX,_(cY,dz)),_(by,dA,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),bT,_(bU,bn,bW,dx),i,_(j,dw,l,dx),bZ,ca,I,_(J,K,L,cV),bb,_(J,K,L,dy)),bu,_(),bK,_(),cX,_(cY,dz)),_(by,dB,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),bT,_(bU,bn,bW,dC),i,_(j,dw,l,dd),bZ,ca,I,_(J,K,L,cV),bb,_(J,K,L,dy)),bu,_(),bK,_(),cX,_(cY,dD)),_(by,dE,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),bT,_(bU,bn,bW,dF),i,_(j,dw,l,dx),bZ,ca,I,_(J,K,L,cV),bb,_(J,K,L,dy)),bu,_(),bK,_(),cX,_(cY,dz)),_(by,dG,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),bT,_(bU,bn,bW,dH),i,_(j,dw,l,dd),bZ,ca,I,_(J,K,L,cV),bb,_(J,K,L,dy)),bu,_(),bK,_(),cX,_(cY,dD)),_(by,dI,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),bT,_(bU,bn,bW,dJ),i,_(j,dw,l,dd),bZ,ca,I,_(J,K,L,cV),bb,_(J,K,L,dy)),bu,_(),bK,_(),cX,_(cY,dK)),_(by,dL,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bT,_(bU,dw,bW,bn),i,_(j,dM,l,dx),bZ,ca,I,_(J,K,L,cV),ci,cj,di,dj,dN,dO,bb,_(J,K,L,dy)),bu,_(),bK,_(),cX,_(cY,dP)),_(by,dQ,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bT,_(bU,dw,bW,dx),i,_(j,dM,l,dx),bZ,ca,I,_(J,K,L,cV),ci,cj,di,dj,dN,dO,bb,_(J,K,L,dy)),bu,_(),bK,_(),cX,_(cY,dP)),_(by,dR,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bT,_(bU,dw,bW,dC),i,_(j,dM,l,dd),bZ,ca,I,_(J,K,L,cV),ci,cj,di,dj,dN,dO,bb,_(J,K,L,dy)),bu,_(),bK,_(),cX,_(cY,dS)),_(by,dT,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bT,_(bU,dw,bW,dF),i,_(j,dM,l,dx),bZ,ca,I,_(J,K,L,cV),ci,cj,di,dj,dN,dO,bb,_(J,K,L,dy)),bu,_(),bK,_(),cX,_(cY,dP)),_(by,dU,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bT,_(bU,dw,bW,dH),i,_(j,dM,l,dd),bZ,ca,I,_(J,K,L,cV),ci,cj,di,dj,dN,dO,bb,_(J,K,L,dy)),bu,_(),bK,_(),cX,_(cY,dS)),_(by,dV,bA,h,bB,cQ,y,cR,bE,cR,bF,bG,D,_(bT,_(bU,dw,bW,dJ),i,_(j,dM,l,dd),bZ,ca,I,_(J,K,L,cV),ci,cj,di,dj,dN,dO,bb,_(J,K,L,dy)),bu,_(),bK,_(),cX,_(cY,dW))]),_(by,dX,bA,h,bB,dY,y,bD,bE,bD,bF,bG,D,_(i,_(j,dZ,l,ea),E,bH,bT,_(bU,eb,bW,ec),I,_(J,ed,ee,_(bU,bn,bW,ef),eg,_(bU,eh,bW,ef),ei,[_(L,cV,ej,bn),_(L,ek,ej,bQ)])),bu,_(),bK,_(),cX,_(cY,el),bL,bh),_(by,em,bA,h,bB,dY,y,bD,bE,bD,bF,bG,D,_(i,_(j,dZ,l,ea),E,bH,bT,_(bU,en,bW,ec),I,_(J,ed,ee,_(bU,bn,bW,ef),eg,_(bU,eh,bW,ef),ei,[_(L,cV,ej,bn),_(L,ek,ej,bQ)])),bu,_(),bK,_(),cX,_(cY,el),bL,bh),_(by,eo,bA,h,bB,dY,y,bD,bE,bD,bF,bG,D,_(i,_(j,dZ,l,ea),E,bH,bT,_(bU,ep,bW,ec),I,_(J,ed,ee,_(bU,bn,bW,ef),eg,_(bU,eh,bW,ef),ei,[_(L,eq,ej,bn),_(L,ek,ej,bQ)])),bu,_(),bK,_(),cX,_(cY,er),bL,bh),_(by,es,bA,h,bB,dY,y,bD,bE,bD,bF,bG,D,_(i,_(j,dZ,l,ea),E,bH,bT,_(bU,bV,bW,ec),I,_(J,ed,ee,_(bU,bn,bW,ef),eg,_(bU,eh,bW,ef),ei,[_(L,cV,ej,bn),_(L,ek,ej,bQ)])),bu,_(),bK,_(),cX,_(cY,el),bL,bh),_(by,et,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(X,eu,ev,ew,bN,_(J,K,L,bO,bP,bQ),i,_(j,cf,l,ex),E,ey,bT,_(bU,cf,bW,ez),bZ,eA),bu,_(),bK,_(),bL,bh),_(by,eB,bA,h,bB,eC,y,eD,bE,eD,bF,bG,D,_(),bu,_(),bK,_(),eE,[_(by,eF,bA,h,bB,eG,y,bD,bE,bD,bF,bG,D,_(i,_(j,eH,l,eH),E,eI,bT,_(bU,eJ,bW,cg),bb,_(J,K,L,ek),I,_(J,K,L,cV)),bu,_(),bK,_(),cX,_(cY,eK),bL,bh),_(by,eL,bA,h,bB,eM,y,bD,bE,eN,bF,bG,D,_(i,_(j,bQ,l,eO),E,eP,bT,_(bU,eQ,bW,cw),bb,_(J,ed,ee,_(bU,ef,bW,bQ),eg,_(bU,ef,bW,eR),ei,[_(L,eS,ej,bn),_(L,ek,ej,bQ)])),bu,_(),bK,_(),cX,_(cY,eT),bL,bh)],eU,bh),_(by,eV,bA,h,bB,eC,y,eD,bE,eD,bF,bG,D,_(),bu,_(),bK,_(),eE,[_(by,eW,bA,h,bB,eG,y,bD,bE,bD,bF,bG,D,_(i,_(j,eH,l,eH),E,eI,bT,_(bU,eX,bW,eY),bb,_(J,K,L,ek),I,_(J,K,L,cV)),bu,_(),bK,_(),cX,_(cY,eK),bL,bh),_(by,eZ,bA,h,bB,eM,y,bD,bE,eN,bF,bG,D,_(i,_(j,bQ,l,cz),E,eP,bT,_(bU,fa,bW,fb),bb,_(J,ed,ee,_(bU,ef,bW,bQ),eg,_(bU,ef,bW,eR),ei,[_(L,eS,ej,bn),_(L,ek,ej,bQ)])),bu,_(),bK,_(),cX,_(cY,fc),bL,bh)],eU,bh),_(by,fd,bA,h,bB,eC,y,eD,bE,eD,bF,bG,D,_(bT,_(bU,fe,bW,ff)),bu,_(),bK,_(),eE,[_(by,fg,bA,h,bB,eG,y,bD,bE,bD,bF,bG,D,_(i,_(j,eH,l,eH),E,eI,bT,_(bU,fh,bW,ff),bb,_(J,K,L,ek),I,_(J,K,L,cV),fi,fj),bu,_(),bK,_(),cX,_(cY,eK),bL,bh),_(by,fk,bA,h,bB,eM,y,bD,bE,eN,bF,bG,D,_(i,_(j,bQ,l,eO),E,eP,bT,_(bU,fl,bW,fm),bb,_(J,ed,ee,_(bU,ef,bW,bQ),eg,_(bU,ef,bW,eR),ei,[_(L,eS,ej,bn),_(L,ek,ej,bQ)]),fi,fj),bu,_(),bK,_(),cX,_(cY,eT),bL,bh)],eU,bh),_(by,fn,bA,h,bB,eC,y,eD,bE,eD,bF,bG,D,_(bT,_(bU,fo,bW,fp)),bu,_(),bK,_(),eE,[_(by,fq,bA,h,bB,eG,y,bD,bE,bD,bF,bG,D,_(i,_(j,eH,l,eH),E,eI,bT,_(bU,fr,bW,ff),bb,_(J,K,L,ek),I,_(J,K,L,cV),fi,fj),bu,_(),bK,_(),cX,_(cY,eK),bL,bh),_(by,fs,bA,h,bB,eM,y,bD,bE,eN,bF,bG,D,_(i,_(j,bQ,l,eO),E,eP,bT,_(bU,ft,bW,fm),bb,_(J,ed,ee,_(bU,ef,bW,bQ),eg,_(bU,ef,bW,eR),ei,[_(L,eS,ej,bn),_(L,ek,ej,bQ)]),fi,fj),bu,_(),bK,_(),cX,_(cY,eT),bL,bh)],eU,bh),_(by,fu,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(X,eu,ev,ew,bN,_(J,K,L,bO,bP,bQ),i,_(j,fv,l,ex),E,ey,bT,_(bU,fr,bW,fw),bZ,eA),bu,_(),bK,_(),bL,bh),_(by,fx,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(X,eu,ev,ew,bN,_(J,K,L,bO,bP,bQ),i,_(j,fy,l,ex),E,ey,bT,_(bU,cG,bW,fw),bZ,eA),bu,_(),bK,_(),bL,bh),_(by,fz,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(X,eu,ev,ew,bN,_(J,K,L,bO,bP,bQ),i,_(j,fA,l,ex),E,ey,bT,_(bU,fB,bW,ez),bZ,eA),bu,_(),bK,_(),bL,bh),_(by,fC,bA,fD,bB,eC,y,eD,bE,eD,bF,bG,D,_(bT,_(bU,fE,bW,fF)),bu,_(),bK,_(),eE,[_(by,fG,bA,fH,bB,fI,y,fJ,bE,fJ,bF,bG,D,_(i,_(j,bQ,l,bQ),bT,_(bU,fK,bW,fL),fM,fN,bb,_(J,K,L,fO)),bu,_(),bK,_(),fP,_(fQ,bh,fR,bh,fS,bG,fT,[fU,fV,fW,fX,fY,fZ,ga,gb,gc,gd,ge,gf,gg,gh,gi,gj,gk,gl,gm,gn,go,gp,gq,gr,gs,gt,gu,gv,gw,gx,gy,gz,gA,gB,gC],gD,_(gE,bG,di,bn,gF,bn,dN,bn,gG,bn,gH,gI,gJ,bG,gK,bn,gL,bn,gM,bh,gN,gI,gO,fU,gP,_(bo,gQ,bq,gQ,br,gQ,bs,bn),gR,_(bo,gQ,bq,gQ,br,gQ,bs,bn)),h,_(j,bn,l,bn,gE,bG,di,bn,gF,bn,dN,bn,gG,bn,gJ,bG,gK,bn,gL,bn,gN,gI,gO,fU,gP,_(bo,gQ,bq,gQ,br,gQ,bs,bn),gR,_(bo,gQ,bq,gQ,br,gQ,bs,bn))),bx,[],gS,[_(gT,_(y,gU,gU,gV),gW,_(y,gU,gU,gX)),_(gT,_(y,gU,gU,gY)),_(gT,_(y,gU,gU,gZ)),_(gT,_(y,gU,gU,ha)),_(gT,_(y,gU,gU,hb)),_(gT,_(y,gU,gU,hc)),_(gT,_(y,gU,gU,hd)),_(gT,_(y,gU,gU,he)),_(gT,_(y,gU,gU,hf),gW,_(y,gU,gU,hg)),_(gT,_(y,gU,gU,hh)),_(gT,_(y,gU,gU,hi)),_(gT,_(y,gU,gU,hj)),_(gT,_(y,gU,gU,hk)),_(gT,_(y,gU,gU,hl)),_(gT,_(y,gU,gU,hm)),_(gT,_(y,gU,gU,hn)),_(gT,_(y,gU,gU,ho)),_(gT,_(y,gU,gU,hp)),_(gT,_(y,gU,gU,hq)),_(gT,_(y,gU,gU,hr)),_(gT,_(y,gU,gU,hs)),_(gT,_(y,gU,gU,ht),gW,_(y,gU,gU,hg)),_(gT,_(y,gU,gU,hu)),_(gT,_(y,gU,gU,hv)),_(gT,_(y,gU,gU,hw),gW,_(y,gU,gU,hg)),_(gT,_(y,gU,gU,hx)),_(gT,_(y,gU,gU,hy),gW,_(y,gU,gU,hz)),_(gT,_(y,gU,gU,hA)),_(gT,_(y,gU,gU,hB)),_(gT,_(y,gU,gU,hC)),_(gT,_(y,gU,gU,hD)),_(gT,_(y,gU,gU,hE)),_(gT,_(y,gU,gU,hF)),_(gT,_(y,gU,gU,hG)),_(gT,_(y,gU,gU,hH))],hI,[gT,gW],hJ,_(hK,[])),_(by,hL,bA,hM,bB,hN,y,hO,bE,hO,bF,bG,D,_(E,hP,bb,_(J,K,L,fO),i,_(j,bR,l,hQ),fM,fN,N,null,bT,_(bU,bV,bW,fL)),bu,_(),bK,_(),bv,_(hR,_(hS,hT,hU,hV,hW,[_(hU,hX,hY,h,hZ,bh,ia,ib,ic,[_(id,ie,hU,ig,ih,ii,ij,_(ik,_(h,il)),im,_(io,ip,iq,_(ir,is,gW,ik,it,[]),iu,bh),iv,iw)])])),cX,_(cY,ix))],eU,bh),_(by,iy,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),i,_(j,iz,l,cE),E,cF,bT,_(bU,iA,bW,iB)),bu,_(),bK,_(),bL,bh),_(by,iC,bA,h,bB,bC,y,bD,bE,bD,bF,bG,D,_(bN,_(J,K,L,bO,bP,bQ),i,_(j,iz,l,cE),E,cF,bT,_(bU,iA,bW,iD)),bu,_(),bK,_(),bL,bh),_(by,iE,bA,iF,bB,eC,y,eD,bE,eD,bF,bG,D,_(bT,_(bU,iG,bW,iH)),bu,_(),bK,_(),eE,[_(by,iI,bA,fH,bB,fI,y,fJ,bE,fJ,bF,bG,D,_(i,_(j,bQ,l,bQ),bT,_(bU,iJ,bW,iK),fM,fN,bb,_(J,K,L,fO)),bu,_(),bK,_(),fP,_(fQ,bh,fR,bh,fS,bG,fT,[fU,fV,fW,fX,fY,fZ,ga,gb,gc,gd,ge,gf,gg,gh,gi,gj,gk,gl,gm,gn,go,gp,gq,gr,gs,gt,gu,gv,gw,gx],gD,_(gE,bG,di,bn,gF,bn,dN,bn,gG,bn,gH,gI,gJ,bG,gK,bn,gL,bn,gM,bh,gN,gI,gO,fU,gP,_(bo,gQ,bq,gQ,br,gQ,bs,bn),gR,_(bo,gQ,bq,gQ,br,gQ,bs,bn)),h,_(j,bn,l,bn,gE,bG,di,bn,gF,bn,dN,bn,gG,bn,gJ,bG,gK,bn,gL,bn,gN,gI,gO,fU,gP,_(bo,gQ,bq,gQ,br,gQ,bs,bn),gR,_(bo,gQ,bq,gQ,br,gQ,bs,bn))),bx,[],gS,[_(gT,_(y,gU,gU,gV),gW,_(y,gU,gU,iL)),_(gT,_(y,gU,gU,gY)),_(gT,_(y,gU,gU,iM)),_(gT,_(y,gU,gU,iN)),_(gT,_(y,gU,gU,iO),gW,_(y,gU,gU,iP)),_(gT,_(y,gU,gU,iQ),gW,_(y,gU,gU,iR)),_(gT,_(y,gU,gU,iS),gW,_(y,gU,gU,iT)),_(gT,_(y,gU,gU,iU),gW,_(y,gU,gU,iV)),_(gT,_(y,gU,gU,hb)),_(gT,_(y,gU,gU,hc)),_(gT,_(y,gU,gU,hd)),_(gT,_(y,gU,gU,hn)),_(gT,_(y,gU,gU,ho)),_(gT,_(y,gU,gU,iW)),_(gT,_(y,gU,gU,hq)),_(gT,_(y,gU,gU,hr)),_(gT,_(y,gU,gU,hs)),_(gT,_(y,gU,gU,ht),gW,_(y,gU,gU,hg)),_(gT,_(y,gU,gU,hu)),_(gT,_(y,gU,gU,hw),gW,_(y,gU,gU,hg)),_(gT,_(y,gU,gU,hx)),_(gT,_(y,gU,gU,hy)),_(gT,_(y,gU,gU,hA)),_(gT,_(y,gU,gU,hB)),_(gT,_(y,gU,gU,hC)),_(gT,_(y,gU,gU,hD)),_(gT,_(y,gU,gU,hE)),_(gT,_(y,gU,gU,hF)),_(gT,_(y,gU,gU,hG)),_(gT,_(y,gU,gU,hH))],hI,[gT,gW],hJ,_(iX,[])),_(by,iY,bA,iZ,bB,hN,y,hO,bE,hO,bF,bG,D,_(E,hP,bb,_(J,K,L,fO),i,_(j,bR,l,hQ),fM,fN,N,null,bT,_(bU,ja,bW,iK)),bu,_(),bK,_(),bv,_(hR,_(hS,hT,hU,hV,hW,[_(hU,hX,hY,h,hZ,bh,ia,ib,ic,[_(id,ie,hU,ig,ih,ii,ij,_(ik,_(h,il)),im,_(io,ip,iq,_(ir,is,gW,ik,it,[]),iu,bh),iv,iw)])])),cX,_(cY,jb))],eU,bh)])),jc,_(),jd,_(je,_(jf,jg),jh,_(jf,ji),jj,_(jf,jk),jl,_(jf,jm),jn,_(jf,jo),jp,_(jf,jq),jr,_(jf,js),jt,_(jf,ju),jv,_(jf,jw),jx,_(jf,jy),jz,_(jf,jA),jB,_(jf,jC),jD,_(jf,jE),jF,_(jf,jG),jH,_(jf,jI),jJ,_(jf,jK),jL,_(jf,jM),jN,_(jf,jO),jP,_(jf,jQ),jR,_(jf,jS),jT,_(jf,jU),jV,_(jf,jW),jX,_(jf,jY),jZ,_(jf,ka),kb,_(jf,kc),kd,_(jf,ke),kf,_(jf,kg),kh,_(jf,ki),kj,_(jf,kk),kl,_(jf,km),kn,_(jf,ko),kp,_(jf,kq),kr,_(jf,ks),kt,_(jf,ku),kv,_(jf,kw),kx,_(jf,ky),kz,_(jf,kA),kB,_(jf,kC),kD,_(jf,kE),kF,_(jf,kG),kH,_(jf,kI),kJ,_(jf,kK),kL,_(jf,kM),kN,_(jf,kO),kP,_(jf,kQ),kR,_(jf,kS),kT,_(jf,kU),kV,_(jf,kW),kX,_(jf,kY),kZ,_(jf,la),lb,_(jf,lc),ld,_(jf,hK),le,_(jf,lf),lg,_(jf,lh),li,_(jf,lj),lk,_(jf,ll),lm,_(jf,iX),ln,_(jf,lo)));}; 
var b="url",c="crowdstrike.html",d="generationDate",e=new Date(1724165344878.91),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=806,l="height",m=1166,n="adaptiveViews",o="sketchKeys",p="s0",q="variables",r="OnLoadVariable",s="currentTime",t="Checkbox_2",u="Checkbox_3",v="page",w="packageId",x="********************************",y="type",z="Axure:Page",A="crowdstrike",B="notes",C="annotations",D="style",E="baseStyle",F="627587b6038d43cca051c114ac41ad32",G="pageAlignment",H="center",I="fill",J="fillType",K="solid",L="color",M=0xFF081F46,N="image",O="imageAlignment",P="near",Q="imageRepeat",R="auto",S="favicon",T="sketchFactor",U="0",V="colorStyle",W="appliedColor",X="fontName",Y="Applied Font",Z="borderWidth",ba="borderVisibility",bb="borderFill",bc=0xFF797979,bd="cornerRadius",be="cornerVisibility",bf="outerShadow",bg="on",bh=false,bi="offsetX",bj=5,bk="offsetY",bl="blurRadius",bm="spread",bn=0,bo="r",bp=0,bq="g",br="b",bs="a",bt=0.349019607843137,bu="adaptiveStyles",bv="interactionMap",bw="diagram",bx="objects",by="id",bz="7d4e7491af264c8090b4004cb9114331",bA="label",bB="friendlyType",bC="矩形",bD="vectorShape",bE="styleType",bF="visible",bG=true,bH="47641f9a00ac465095d6b672bbdffef6",bI=0xFF1D2334,bJ="5",bK="imageOverrides",bL="generateCompound",bM="8a389cd76d794a49a229dec2f8e8d257",bN="foreGroundFill",bO=0xFFFFFFFF,bP="opacity",bQ=1,bR=750,bS=37,bT="location",bU="x",bV=28,bW="y",bX=27,bY=0xFF2A3040,bZ="fontSize",ca="14px",cb="354111ba09a54345aa3120f0c3be7ad1",cc=344,cd="154b105704de45f38607dfded96df315",ce=272,cf=72,cg=80,ch=0xF1F2F2,ci="horizontalAlignment",cj="justify",ck="verticalAlignment",cl="top",cm="58014d6b4f844ee081cabe5125f67431",cn=277,co=76,cp=254,cq=233,cr=0xFBFBFB,cs="b848c60f888f4782a53b165257a57d41",ct=281,cu=64,cv=440,cw=87,cx="1f223806591747688e2c972afb1cd3bb",cy=182,cz=63,cA=596,cB=239,cC="d438f3673f424f828e5fe3031819d71c",cD=56,cE=16,cF="2285372321d148ec80932747449c36c9",cG=396,cH="4b404e7d6e2e4fdf9eeddb3da3d54cb4",cI=707,cJ="2a74628fa4fe443bbe0906e514c47000",cK="表格",cL="table",cM=756,cN=96,cO=733,cP="6cab488580df456084e71229ecc8357b",cQ="表格单元",cR="tableCell",cS=121,cT=30,cU="33ea2511485c479dbf973af3302f2352",cV=0xFFFFFF,cW=0xFFDBD5D5,cX="images",cY="normal~",cZ="images/crowdstrike/u10.png",da="5d5f987b1e5847babde5f8abf93c905a",db="67b951e900bf4078b5457a86c5005682",dc=60,dd=36,de="images/crowdstrike/u14.png",df="908ceafd9c8242d59a9b9cb786e9dbb5",dg=628,dh="left",di="paddingLeft",dj="15",dk="images/crowdstrike/u11.png",dl="2beabcebcd72420a8eda87b36f4b2686",dm="0ac70ffd86f34ed293804b8e916fd6ef",dn="images/crowdstrike/u15.png",dp="0ecc7e554675411ca95dc50bdae1f96e",dq=849,dr="96060987749445429bf1711d5f45d9fe",ds=752,dt=264,du=875,dv="bfab45e8a37e4ea9a5a35cdda44a296a",dw=116,dx=52,dy=0xFFF2F2F2,dz="images/crowdstrike/u18.png",dA="907dd4ff60df43aebe9363ab515655de",dB="5c2804749c2747d180edbe874cd386b1",dC=104,dD="images/crowdstrike/u22.png",dE="dcd2e60bbec244eb847943dc5b1333db",dF=140,dG="aabc09fea0034501a092eef7293122d8",dH=192,dI="75162ddcdb654eeaac69fa2d6320b8bb",dJ=228,dK="images/crowdstrike/u28.png",dL="d87b4de5edf842d8a9f6b9cf53d8c310",dM=636,dN="paddingRight",dO="10",dP="images/crowdstrike/u19.png",dQ="bcedb3e2e36d44449cdb3bcdd1805e39",dR="610157d2d4bb44e99ced060cb964afe7",dS="images/crowdstrike/u23.png",dT="4c88bc59b2ce4971afa61cff0d269f03",dU="5786bb55eee54249b99a1f70f01b0fec",dV="a9388ed69e7c4a37b7a958d74740023f",dW="images/crowdstrike/u29.png",dX="71c479edfbc7482ba2da9ac690f3992e",dY="形状",dZ=193,ea=57,eb=528,ec=162,ed="linearGradient",ee="startPoint",ef=0.5,eg="endPoint",eh=0.72,ei="stops",ej="offset",ek=0xFF0868FF,el="images/crowdstrike/u30.svg",em="9adfd4da3aee4e018ddc5dd459df20fb",en=362,eo="135044927d4c4bdd8aa58ced4b3c1340",ep=194,eq=0x1CFFFFFF,er="images/crowdstrike/u32.svg",es="69f5c1381f2746b1896f5480f0d63e1a",et="9c74720af9224dab895e4f5682d8a19a",eu="'宋体 Bold', '宋体 常规', '宋体', sans-serif",ev="fontWeight",ew="700",ex=23,ey="8c7a4c5ad69a4369a5f7788171ac0b32",ez=179,eA="20px",eB="25a6604a394e4718b3d8da3c73043fac",eC="组合",eD="layer",eE="objs",eF="eae13464ab8f4dad80a6c95c070b6ccd",eG="椭圆",eH=7,eI="eff044fe6497434a8c5f89f769ddde3b",eJ=68,eK="images/crowdstrike/u36.svg",eL="a1a0325baa1647bcb3ac69557875382b",eM="垂直线",eN="verticalLine",eO=75,eP="619b2148ccc1497285562264d51992f9",eQ=71,eR=0.28,eS=0xFFD0E2FB,eT="images/crowdstrike/u37.svg",eU="propagate",eV="a4208c67970d429e8bb2690ec495d721",eW="1f82cc2b9b65401e96a42c9272582ad5",eX=422,eY=92,eZ="2d0a095be7d145c28df6fc83394a60f1",fa=425,fb=99,fc="images/crowdstrike/u40.svg",fd="b94bb24c00a14700a4c95fba1ef1a717",fe=203,ff=295,fg="3f7a30a1af064428a20b64f03881fba5",fh=242,fi="rotation",fj="180",fk="b0889be28f064a0a8c6d3719fa687d2c",fl=245,fm=220,fn="7ec47de9bdd24801a8105a46b2c28aa0",fo=217,fp=302,fq="ab7b20d144fa4b68a0160633326d62d2",fr=581,fs="130735f012984b618e97d5bc848c70c8",ft=584,fu="5d8a3ab1468c4ea3ade9c67d0e7b40b9",fv=82,fw=178,fx="ce75527cfabe4861bdcec62b7fc08f2c",fy=144,fz="2a0571ec493749cd8219e127921c4889",fA=155,fB=221,fC="0cac1f0c665744b0901f83dd60d42c49",fD="柱状图",fE=94,fF=518,fG="bec35f83ffbf4d00beacea9ce2a2fda8",fH="acp-config",fI="中继器",fJ="repeater",fK=778,fL=579,fM="linePattern",fN="dashed",fO=0xFFCCCCCC,fP="repeaterPropMap",fQ="isolateRadio",fR="isolateSelection",fS="fitToContent",fT="itemIds",fU=1,fV=2,fW=3,fX=4,fY=5,fZ=6,ga=7,gb=8,gc=9,gd=10,ge=11,gf=12,gg=13,gh=14,gi=15,gj=16,gk=17,gl=18,gm=19,gn=20,go=21,gp=22,gq=23,gr=24,gs=25,gt=26,gu=27,gv=28,gw=29,gx=30,gy=31,gz=32,gA=33,gB=34,gC=35,gD="default",gE="loadLocalDefault",gF="paddingTop",gG="paddingBottom",gH="wrap",gI=-1,gJ="vertical",gK="horizontalSpacing",gL="verticalSpacing",gM="hasAltColor",gN="itemsPerPage",gO="currPage",gP="backColor",gQ=255,gR="altColor",gS="data",gT="item",gU="text",gV="编辑器配置",gW="value",gX="{\"time\":1722931075,\"config\":{\"fieldAlias\":\"\",\"textFill\":\"rgb(248, 248, 248)\",\"isGroup\":true,\"showLegend\":true,\"showTooltip\":true,\"showLabel\":false,\"point.size\":4,\"isEnt\":false,\"statistic.title.style.color\":\"rgb(248, 248, 248)\",\"statistic.content.style.color\":\"rgb(248, 248, 248)\",\"yAxis.grid.line.style.lineDash\":[6,6],\"yAxis.grid.line.style.stroke\":\"rgb(104, 103, 103)\",\"appendPadding\":10},\"data\":{\"data\":[{\"月份\":\"经济损失估计\",\"金额（亿美元）\":54},{\"月份\":\"保险行业损失\",\"金额（亿美元）\":30}],\"keys\":[\"月份\",\"金额（亿美元）\"]},\"u\":\"YXg2OTMyNA==\"}",gY="主题",gZ="图表颜色",ha="系列别名",hb="单位",hc="字体颜色",hd="字体大小",he="是否堆叠",hf="是否分组",hg="true",hh="是否区间",hi="是否百分比图",hj="柱子形状",hk="组间间距",hl="组内间距",hm="柱子宽度占比",hn="y轴最小值",ho="y轴最大值",hp="y轴刻度间隔",hq="y轴刻度",hr="y轴标题",hs="x轴数据类型",ht="显示图例",hu="图例位置",hv="图例翻页",hw="显示提示信息",hx="提示信息模板",hy="显示数据标签",hz="false",hA="标签模板",hB="显示缩略轴",hC="缩略轴终点",hD="动态数据类型",hE="初始数据范围末尾",hF="入场动画",hG="入场动画时长",hH="图表事件",hI="dataProps",hJ="evaluatedStates",hK="u51",hL="f4ebb4e91e5d4beeb7a406a89e1f3f00",hM="acp-g2pColumn-chart",hN="SVG",hO="imageBox",hP="********************************",hQ=113,hR="onLoad",hS="eventType",hT="Load",hU="description",hV="加载时",hW="cases",hX="initializeAxhubCharts",hY="conditionString",hZ="isNewIfGroup",ia="caseColorHex",ib="AB68FF",ic="actions",id="action",ie="linkWindow",ig="在 当前窗口 打开&nbsp; javascript:{if(!window.loadAcpG2p){window.loadAcpG2p = true;let url;$axure.internal($ax =&gt; {url = $ax.globalVariableProvider.getVariableValue('AxhubG2pJsLink')});$axure.utils.loadJS(url || 'https://static.axhub.im/charts/g2plotv2.js')};}",ih="displayName",ii="打开链接",ij="actionInfoDescriptions",ik=" javascript:{if(!window.loadAcpG2p){window.loadAcpG2p = true;let url;$axure.internal($ax => {url = $ax.globalVariableProvider.getVariableValue('AxhubG2pJsLink')});$axure.utils.loadJS(url || 'https://static.axhub.im/charts/g2plotv2.js')};}",il="在 当前窗口 打开  javascript:{if(!window.loadAcpG2p){window.loadAcpG2p = true;let url;$axure.internal($ax => {url = $ax.globalVariableProvider.getVariableValue('AxhubG2pJsLink')});$axure.utils.loadJS(url || 'https://static.axhub.im/charts/g2plotv2.js')};}",im="target",io="targetType",ip="webUrl",iq="urlLiteral",ir="exprType",is="stringLiteral",it="stos",iu="includeVariables",iv="linkType",iw="current",ix="images/crowdstrike/acp-g2pcolumn-chart_u52.svg",iy="6e19d65e888c4cc9bdfdab69f4820b24",iz=112,iA=347,iB=413,iC="90307206898d4c57b6a194d3ab1f35ad",iD=557,iE="8fe00e511cad4d638e2df48126710e29",iF="股票图",iG=232,iH=747,iI="55f5929f6dcb4ffd8b69f2cc08b52374",iJ=779,iK=432,iL="{\"time\":**********,\"config\":{\"meta.open.alias\":\"开盘价\",\"meta.close.alias\":\"收盘价\",\"meta.high.alias\":\"最高价\",\"meta.low.alias\":\"最低价\",\"textFill\":\"rgb(248, 248, 248)\",\"showLegend\":false,\"showTooltip\":true,\"isEnt\":false,\"statistic.title.style.color\":\"rgb(244, 244, 244)\",\"statistic.content.style.color\":\"rgb(244, 244, 244)\",\"yAxis.grid.line.style.lineDash\":[null,null],\"yAxis.grid.line.style.stroke\":\"rgb(122, 118, 118)\",\"xAxis.label.autoHide\":false,\"xAxis.label.autoEllipsis\":false,\"xAxis.label.rotate\":6,\"xAxis.label.style.fontSize\":\"\",\"yAxis.label.autoHide\":true,\"appendPadding\":10},\"data\":{\"data\":[{\"_item\":\"7/18/24\",\"open\":351,\"close\":343,\"high\":352,\"low\":336},{\"_item\":\"7/19/24\",\"open\":294,\"close\":304,\"high\":316,\"low\":290},{\"_item\":\"7/22/24\",\"open\":285,\"close\":263,\"high\":285,\"low\":261},{\"_item\":\"7/23/24\",\"open\":269,\"close\":268,\"high\":277,\"low\":258},{\"_item\":\"7/24/24\",\"open\":270,\"close\":258,\"high\":275,\"low\":257},{\"_item\":\"7/25/24\",\"open\":258,\"close\":254,\"high\":262,\"low\":250},{\"_item\":\"7/26/24\",\"open\":259,\"close\":256,\"high\":260,\"low\":251},{\"_item\":\"7/29/24\",\"open\":259,\"close\":258,\"high\":265,\"low\":257},{\"_item\":\"7/30/24\",\"open\":247,\"close\":233,\"high\":248,\"low\":226},{\"_item\":\"7/31/24\",\"open\":234,\"close\":231,\"high\":236,\"low\":228},{\"_item\":\"8/1/24\",\"open\":232,\"close\":224,\"high\":232,\"low\":221}],\"keys\":[\"_item\",\"open\",\"close\",\"high\",\"low\"]},\"u\":\"YXg2OTMyNA==\"}",iM="上升颜色",iN="下降颜色",iO="open别名",iP="开盘价",iQ="close别名",iR="收盘价",iS="high别名",iT="最高价",iU="low别名",iV="最低价",iW="y轴刻间隔",iX="u56",iY="ce34a81600e94a42adc6cc0ad208381a",iZ="acp-g2pStock-chart",ja=29,jb="images/crowdstrike/acp-g2pstock-chart_u57.svg",jc="masters",jd="objectPaths",je="7d4e7491af264c8090b4004cb9114331",jf="scriptId",jg="u0",jh="8a389cd76d794a49a229dec2f8e8d257",ji="u1",jj="354111ba09a54345aa3120f0c3be7ad1",jk="u2",jl="154b105704de45f38607dfded96df315",jm="u3",jn="58014d6b4f844ee081cabe5125f67431",jo="u4",jp="b848c60f888f4782a53b165257a57d41",jq="u5",jr="1f223806591747688e2c972afb1cd3bb",js="u6",jt="d438f3673f424f828e5fe3031819d71c",ju="u7",jv="4b404e7d6e2e4fdf9eeddb3da3d54cb4",jw="u8",jx="2a74628fa4fe443bbe0906e514c47000",jy="u9",jz="6cab488580df456084e71229ecc8357b",jA="u10",jB="908ceafd9c8242d59a9b9cb786e9dbb5",jC="u11",jD="5d5f987b1e5847babde5f8abf93c905a",jE="u12",jF="2beabcebcd72420a8eda87b36f4b2686",jG="u13",jH="67b951e900bf4078b5457a86c5005682",jI="u14",jJ="0ac70ffd86f34ed293804b8e916fd6ef",jK="u15",jL="0ecc7e554675411ca95dc50bdae1f96e",jM="u16",jN="96060987749445429bf1711d5f45d9fe",jO="u17",jP="bfab45e8a37e4ea9a5a35cdda44a296a",jQ="u18",jR="d87b4de5edf842d8a9f6b9cf53d8c310",jS="u19",jT="907dd4ff60df43aebe9363ab515655de",jU="u20",jV="bcedb3e2e36d44449cdb3bcdd1805e39",jW="u21",jX="5c2804749c2747d180edbe874cd386b1",jY="u22",jZ="610157d2d4bb44e99ced060cb964afe7",ka="u23",kb="dcd2e60bbec244eb847943dc5b1333db",kc="u24",kd="4c88bc59b2ce4971afa61cff0d269f03",ke="u25",kf="aabc09fea0034501a092eef7293122d8",kg="u26",kh="5786bb55eee54249b99a1f70f01b0fec",ki="u27",kj="75162ddcdb654eeaac69fa2d6320b8bb",kk="u28",kl="a9388ed69e7c4a37b7a958d74740023f",km="u29",kn="71c479edfbc7482ba2da9ac690f3992e",ko="u30",kp="9adfd4da3aee4e018ddc5dd459df20fb",kq="u31",kr="135044927d4c4bdd8aa58ced4b3c1340",ks="u32",kt="69f5c1381f2746b1896f5480f0d63e1a",ku="u33",kv="9c74720af9224dab895e4f5682d8a19a",kw="u34",kx="25a6604a394e4718b3d8da3c73043fac",ky="u35",kz="eae13464ab8f4dad80a6c95c070b6ccd",kA="u36",kB="a1a0325baa1647bcb3ac69557875382b",kC="u37",kD="a4208c67970d429e8bb2690ec495d721",kE="u38",kF="1f82cc2b9b65401e96a42c9272582ad5",kG="u39",kH="2d0a095be7d145c28df6fc83394a60f1",kI="u40",kJ="b94bb24c00a14700a4c95fba1ef1a717",kK="u41",kL="3f7a30a1af064428a20b64f03881fba5",kM="u42",kN="b0889be28f064a0a8c6d3719fa687d2c",kO="u43",kP="7ec47de9bdd24801a8105a46b2c28aa0",kQ="u44",kR="ab7b20d144fa4b68a0160633326d62d2",kS="u45",kT="130735f012984b618e97d5bc848c70c8",kU="u46",kV="5d8a3ab1468c4ea3ade9c67d0e7b40b9",kW="u47",kX="ce75527cfabe4861bdcec62b7fc08f2c",kY="u48",kZ="2a0571ec493749cd8219e127921c4889",la="u49",lb="0cac1f0c665744b0901f83dd60d42c49",lc="u50",ld="bec35f83ffbf4d00beacea9ce2a2fda8",le="f4ebb4e91e5d4beeb7a406a89e1f3f00",lf="u52",lg="6e19d65e888c4cc9bdfdab69f4820b24",lh="u53",li="90307206898d4c57b6a194d3ab1f35ad",lj="u54",lk="8fe00e511cad4d638e2df48126710e29",ll="u55",lm="55f5929f6dcb4ffd8b69f2cc08b52374",ln="ce34a81600e94a42adc6cc0ad208381a",lo="u57";
return _creator();
})());