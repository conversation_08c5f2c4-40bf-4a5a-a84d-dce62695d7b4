import request from "@/utils/request";

// 查询大屏-实体信息列表
export function listEntityInfo(query) {
  return request({
    url: "/large/entityInfo/list",
    method: "get",
    params: query,
  });
}

// 查询大屏-实体信息详细
export function getEntityInfo(id) {
  return request({
    url: "/large/entityInfo/" + id,
    method: "get",
  });
}

// 新增大屏-实体信息
export function addEntityInfo(data) {
  return request({
    url: "/large/entityInfo/add",
    method: "post",
    data: data,
  });
}

// 修改大屏-实体信息
export function updateEntityInfo(data) {
  return request({
    url: "/large/entityInfo/edit",
    method: "post",
    data: data,
  });
}

// 删除大屏-实体信息
export function delEntityInfo(id) {
  return request({
    url: "/large/entityInfo/remove",
    method: "post",
    data: id,
  });
}
