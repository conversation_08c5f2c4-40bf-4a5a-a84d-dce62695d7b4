<template>
  <div id="mainBar" style="width: 100%; height: 100%"></div>
</template>
 
<script>
import request from "@/utils/request";
import * as echarts from "echarts";
import { largeSourceList } from "@/api/bigScreen/index1";

export default {
  data() {
    return {
      myChart: null,
      option: {},
      data: []
    };
  },
  mounted() {
    this.initChart();
  },
  components: {},
  methods: {
    async initChart() {
      let data1, data2, zzx1 = [], zzx2 = []
      await largeSourceList({}).then(res => {
        this.data = res.rows
        data1 = res.rows.filter(item => item.sourceType === 1)
        data2 = res.rows.filter(item => item.sourceType === 2)
      })
      let data = data1.map(item => item.name)
      data.map(row => {
        zzx1.push(data1.filter(item => item.name === row)[0].tally)
        zzx2.push(data2.filter(item => item.name === row)[0].tally)
      })
      const zx = zzx1.map((item, index) => {
        return item + zzx2[index];
      });
      var barWidth = 40;
      let chartDom = document.getElementById("mainBar");
      this.myChart = echarts.init(chartDom);
      this.myChart.resize();
      this.option = {
        tooltip: {
          showContent: true,
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            let tooltipText = `${params[0].axisValue}<br/>`;
            params.forEach(function (item) {
              if (!!item.seriesName && item.seriesType != "pictorialBar") {
                tooltipText += `<span style=" margin-top:-4px;display: inline-block;width:10px;height:10px;background-color:${item.color.colorStops[0].color};margin-right:10px;  vertical-align: middle; border-radius: 50%;"> </span>${item.seriesName}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${item.value}<br/>`;
              }
            });
            return tooltipText;
          },
        },
        legend: {
          show: true, //是否显示
          textStyle: { color: "#fff" },
          padding: [15, 10],
          x: "right",
        },
        grid: {
          left: "2%",
          right: "4%",
          bottom: "4%",
          top: "14%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: data,
          axisLine: {
            show: true,
            lineStyle: {
              color: "#ffffff80",
            },
          },
          axisLabel: {
            fontSize: "14px",
            color: "#fff",
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            lineStyle: {
              color: "#ffffff70",
              type: "dotted",
            },
          },
          axisLabel: {
            interval: 0, //显示不全
            fontSize: "14px",
            color: "#fff",
          },
        },
        series: [
          {
            type: "pictorialBar",
            symbol: "diamond",
            symbolSize: [barWidth, 8],
            symbolOffset: [0, -4.5],
            symbolPosition: "end",
            z: 12,
            color: "#3185FF",
            data: zzx1,
          },
          {
            name: "国内信息源",
            type: "bar",
            barWidth: barWidth,
            stack: "1",
            itemStyle: {
              normal: {
                opacity: 0.7,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: "#2863D2", }, { offset: 1, color: "#2863D260", },]),
                barBorderRadius: 0,
              },
            },
            label: {
              show: false,
              position: ["-18", "-18"],
              color: "#00f8ff",
              fontSize: 12,
            },
            data: zzx1,
          },
          {
            name: "",
            type: "bar",
            barWidth: 0.5,
            barGap: "0%",
            itemStyle: {
              normal: {
                opacity: 0.7,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: "#2863D2", }, { offset: 1, color: "#161D6E", },]),
              },
            },
            data: zzx1,
          },
          {
            name: "国外信息源",
            type: "bar",
            barWidth: barWidth,
            stack: "1",
            itemStyle: {
              normal: {
                opacity: 0.7,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: "#43E3E3", }, { offset: 1, color: "#42E2E360", },]),
                barBorderRadius: 0,
              },
            },
            label: {
              show: false,
              position: ["-18", "-18"],
              color: "#00f8ff",
              fontSize: 12,
            },
            data: zzx2,
          },
          {
            type: "pictorialBar",
            symbol: "diamond",
            stack: "1",
            symbolSize: [barWidth, 8],
            symbolOffset: [0, -4.5],
            symbolPosition: "end",
            z: 12,
            color: "#43E3E3",
            data: zx,
          },
        ],
      };
      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
      this.myChart.setOption(this.option);
      this.myChart.on("click", (params) => {
        let sourceName = {
          1: '国内信息源',
          2: '国外信息源'
        }
        let row = this.data.filter(item => {
          return item.name == params.name && sourceName[item.sourceType] == params.seriesName;
        })
        this.$emit('openList', { query: { sourceId: row[0].id, sourceName: params.name }, title: params.name })
      })
    },
  },
  beforeDestroy() {
    window.removeEventListener("resize", () => {
      this.myChart.resize();
    });
  },
};
</script>

<style lang='scss'>
.el-table .warning-row {
  background-color: #13436d;
}

.el-table .success-row {
  background-color: #113a65;
}
</style>