<template>
  <div>
    <VueUeditorWrap @ready="ready" ref="editor" v-model="contentText" :config="config"></VueUeditorWrap>
  </div>
</template>

<script>

  import VueUeditorWrap from "vue-ueditor-wrap";
  import cookie from "js-cookie";

  export default {
    name: "index",
    components: {
      VueUeditorWrap,
    },
    props: ["content"],
    data() {
      return {
        contentText:this.content,
        config: {
          // 编辑器自动被内容撑高
          autoHeightEnabled: true,
          // 初始容器高度
          initialFrameHeight: 450,
          // 初始容器宽度
          initialFrameWidth: '100%',
          // 上传文件接口
          serverUrl: this.prefix() + "/ueditor",
          imageUrlPrefix: this.prefix(),//图片访问前缀

          videoUrlPrefix: this.prefix(),//视频访问前缀

          fileUrlPrefix: this.prefix(),//文件访问前缀

          scrawlUrlPrefix: this.prefix(),//涂鸦

          snapscreenUrlPrefix: this.prefix(),//截图

          // UEditor 资源文件的存放路径，通常Nuxt项目设置为/UEditor/即可
          // UEDITOR_HOME_URL: `/UEditor/`,
          UEDITOR_HOME_URL: `/UEditor/`,
          // 配合最新编译的资源文件，你可以实现添加自定义Request Headers,详情https://github.com/HaoChuan9421/ueditor/commits/dev-1.4.3.3
          headers: {
            Authorization: `Bearer ${cookie.get('Admin-Token')}`,
          },
          readonly: false,
          focus: true
        }
      }
    },
    methods: {
      getText() {
        return this.contentText;
      },
      prefix() {
        return process.env.VUE_APP_BASE_API;
      },
      ready(editorInstance) {
        console.log(`实例${editorInstance.key}已经初始化:`, editorInstance);
        // 不是编辑状态，禁用编辑，隐藏工具栏
        // console.log('377this.$refs.ueditor_v', this.$refs.ueditor_v)
        // this.$refs.ueditor_v.editor.body.contentEditable = false
        // document.getElementById('edui1_toolbarbox').style.display = 'none';
      },
    }
  }
</script>

<style scoped>

</style>
