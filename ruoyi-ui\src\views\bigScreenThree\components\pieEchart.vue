<template>
  <div id="mainPie2" style="width: 100%; height: 100%"></div>
</template>

<script>
import request from "@/utils/request";
import * as echarts from "echarts";

export default {
  data() {
    return {
      myChart: null,
      option: {},
    };
  },
  mounted() {
    this.initChart();
    // this.init();
  },
  props: {
    valueObj: {
      type: Object,
      default: () => {
        return {
          xData: [],
          yData: [],
        };
      },
    },
  },
  watch: {
    valueObj(value) {
      if (value) {
        this.option.xAxis.data = value.xData;
        this.option.series = value.yData;
        this.myChart.setOption(this.option);
      }
    },
  },
  components: {},
  methods: {
    /**
     *
     */
    initChart() {
      let chartDom = document.getElementById("mainPie2");

      this.myChart = echarts.init(chartDom);
      this.option = {
        tooltip: {
          trigger: "item",
        },

        legend: {
          orient: "vertical",

          show: true, //是否显示
          textStyle: { color: "#fff" },
          padding: [25, 20],
          right: "right",
          icon: "circle",
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: "75%",
            data: [
              { value: 7, name: "威胁识别" },
              { value: 6.9, name: "漏洞管理" },
              { value: 9.5, name: "技术创新" },
              { value: 14.5, name: "政策标准" },
              { value: 18.4, name: "风险管理" },
              { value: 16, name: "其他" },
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      //随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
      setTimeout(() => {
        this.myChart.resize();
      }, 1);
      this.myChart.setOption(this.option);
      this.myChart.on("click", (params) => {
        this.$emit("openList");
      });
    },
  },
  beforeDestroy() {
    /* 页面组件销毁的时候，别忘了移除绑定的监听resize事件，否则的话，多渲染几次
        容易导致内存泄漏和额外CPU或GPU占用哦*/
    window.removeEventListener("resize", () => {
      this.myChart.resize();
    });
  },
};
</script>

<style lang="scss"></style>
