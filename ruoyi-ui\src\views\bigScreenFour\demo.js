export const demo = [
  { words: "人工智能", wordsNum: 2500 },
  { words: "机器学习", wordsNum: 653 },
  { words: "深度学习", wordsNum: 213 },
  { words: "神经网络", wordsNum: 399 },
  { words: "计算机视觉", wordsNum: 953 },
  { words: "自然语言处理", wordsNum: 124 },
  { words: "强化学习", wordsNum: 599 },
  { words: "生成对抗网络", wordsNum: 993 },
  { words: "专家系统", wordsNum: 117 },
  { words: "数据挖掘", wordsNum: 794 },
  { words: "语音识别", wordsNum: 587 },
  { words: "图像识别", wordsNum: 199 },
  { words: "无监督学习", wordsNum: 232 },
  { words: "监督学习", wordsNum: 463 },
  { words: "半监督学习", wordsNum: 392 },
  { words: "迁移学习", wordsNum: 123 },
  { words: "聚类", wordsNum: 188 },
  { words: "分类", wordsNum: 853 },
  { words: "回归", wordsNum: 799 },
  { words: "决策树", wordsNum: 210 },
  { words: "随机森林", wordsNum: 306 },
  { words: "支持向量机", wordsNum: 499 },
  { words: "逻辑回归", wordsNum: 190 },
  { words: "感知机", wordsNum: 883 },
  { words: "卷积神经网络", wordsNum: 298 },
  { words: "循环神经网络", wordsNum: 400 },
  { words: "长短期记忆网络", wordsNum: 415 },
  { words: "注意力机制", wordsNum: 513 },
  { words: "超参数", wordsNum: 749 },
  { words: "参数", wordsNum: 75 },
  { words: "特征工程", wordsNum: 232 },
  { words: "A/B测试", wordsNum: 124 },
  { words: "激活函数", wordsNum: 469 },
  { words: "主动学习", wordsNum: 425 },
  { words: "算法", wordsNum: 760 },
  { words: "标注", wordsNum: 699 },
  { words: "曲线下面积", wordsNum: 856 },
  { words: "人工神经网络", wordsNum: 759 },
  { words: "关联规则学习", wordsNum: 267 },
  { words: "自动解码器", wordsNum: 197 },
  { words: "自动语音识别", wordsNum: 321 },
  { words: "反向传播", wordsNum: 423 },
  { words: "批量", wordsNum: 422 },
  { words: "贝叶斯定理", wordsNum: 627 },
  { words: "归纳偏差", wordsNum: 97 },
  { words: "确认偏差", wordsNum: 629 },
  { words: "偏差与方差权衡", wordsNum: 398 },
  { words: "提升", wordsNum: 475 },
  { words: "边界框", wordsNum: 740 },
  { words: "聊天机器人", wordsNum: 406 },
  { words: "冷启动", wordsNum: 194 },
  { words: "协作过滤", wordsNum: 582 },
  { words: "置信区间", wordsNum: 303 },
  { words: "贡献者", wordsNum: 373 },
  { words: "卷积神经网络", wordsNum: 727 },
  { words: "生成模型", wordsNum: 298 },
  { words: "遗传算法", wordsNum: 891 },
  { words: "大型语言模型", wordsNum: 957 },
  { words: "启发式", wordsNum: 279 },
  { words: "超参数调整", wordsNum: 155 },
  { words: "图像标注", wordsNum: 563 },
  { words: "图片说明", wordsNum: 275 },
  { words: "图片分类", wordsNum: 460 },
  { words: "图像增强", wordsNum: 102 },
  { words: "图像预处理", wordsNum: 770 },
  { words: "图像修复", wordsNum: 826 },
  { words: "图像分割", wordsNum: 420 },
  { words: "推理", wordsNum: 264 },
  { words: "K均值", wordsNum: 639 },
  { words: "知识表示", wordsNum: 418 },
  { words: "语言模型", wordsNum: 199 },
  { words: "马尔可夫决策过程", wordsNum: 883 },
  { words: "马尔可夫性质", wordsNum: 566 },
  { words: "马尔可夫奖励过程", wordsNum: 136 },
  { words: "模型评估", wordsNum: 673 },
  { words: "模型部署", wordsNum: 993 },
  { words: "优化算法", wordsNum: 473 },
  { words: "过拟合", wordsNum: 439 },
  { words: "正则化", wordsNum: 66 },
  { words: "梯度下降", wordsNum: 772 },
  { words: "损失函数", wordsNum: 308 },
  { words: "逻辑回归", wordsNum: 182 },
  { words: "决策树", wordsNum: 811 },
  { words: "随机森林", wordsNum: 523 },
  { words: "主成分分析", wordsNum: 921 },
  { words: "聚类分析", wordsNum: 957 },
  { words: "降维", wordsNum: 802 },
  { words: "支持向量机", wordsNum: 203 },
  { words: "K近邻算法", wordsNum: 591 },
  { words: "迁移学习", wordsNum: 745 },
  { words: "自然语言处理", wordsNum: 310 },
  { words: "文本分类", wordsNum: 455 },
  { words: "命名实体识别", wordsNum: 220 },
  { words: "词性标注", wordsNum: 522 },
  { words: "语义分析", wordsNum: 524 },
  { words: "机器翻译", wordsNum: 991 },
  { words: "文本生成", wordsNum: 865 },
  { words: "文本摘要", wordsNum: 856 },
  { words: "强化学习", wordsNum: 891 },
  { words: "深度学习", wordsNum: 899 },
];

export const demo5 = [
  { words: "crowdstrike", wordsNum: 2500, url: '/crowdstrike/index.html' },
  { words: "IPV6入侵", wordsNum: 2500, url: '/crowdstrike/index.html' },
  { words: "网络安全", wordsNum: 1500 },
  { words: "信息安全", wordsNum: 520 },
  { words: "攻击者", wordsNum: 526 },
  { words: "恶意软件", wordsNum: 159 },
  { words: "木马", wordsNum: 682 },
  { words: "病毒", wordsNum: 130 },
  { words: "蠕虫病毒", wordsNum: 140 },
  { words: "索软件", wordsNum: 500 },
  { words: "谍软件", wordsNum: 552 },
  { words: "Rootkit", wordsNum: 626 },
  { words: "漏洞", wordsNum: 291 },
  { words: "零日漏洞", wordsNum: 520 },
  { words: "数据泄露", wordsNum: 431 },
  { words: "网络钓鱼", wordsNum: 752 },
  { words: "拒绝服务攻击", wordsNum: 299 },
  { words: "分布式拒绝服务攻击", wordsNum: 345 },
  { words: "SQL注入", wordsNum: 738 },
  { words: "跨站脚本攻击", wordsNum: 400 },
  { words: "端口扫描", wordsNum: 581 },
  { words: "防火墙", wordsNum: 344 },
  { words: "内容安全策略", wordsNum: 856 },
  { words: "数字证书", wordsNum: 694 },
  { words: "高级持久性威胁", wordsNum: 615 },
  { words: "入侵检测系统", wordsNum: 137 },
  { words: "数据丢失防护", wordsNum: 898 },
  { words: "安全信息和事件管理", wordsNum: 638 },
  { words: "震网病毒", wordsNum: 731 },
  { words: "挖矿木马", wordsNum: 581 },
  { words: "后门", wordsNum: 643 },
  { words: "弱口令", wordsNum: 349 },
  { words: "网络侦察", wordsNum: 263 },
  { words: "认证", wordsNum: 342 },
  { words: "权限提升", wordsNum: 483 },
  { words: "僵尸网络", wordsNum: 690 },
  { words: "加密", wordsNum: 317 },
  { words: "解密", wordsNum: 557 },
  { words: "双因素认证", wordsNum: 428 },
  { words: "安全配置", wordsNum: 431 },
  { words: "渗透测试", wordsNum: 870 },
  { words: "安全策略", wordsNum: 202 },
  { words: "网络隔离", wordsNum: 701 },
  { words: "无线安全", wordsNum: 858 },
  { words: "行为监控", wordsNum: 228 },
  { words: "自携设备办公", wordsNum: 908 },
  { words: "网络防火墙", wordsNum: 319 },
  { words: "虚拟私人网络", wordsNum: 866 },
  { words: "网络接入控制", wordsNum: 234 },
  { words: "安全审计", wordsNum: 421 },
  { words: "数据掩码", wordsNum: 291 },
  { words: "安全意识培训", wordsNum: 909 },
  { words: "安全运营中心", wordsNum: 309 },
  { words: "入侵预防系统", wordsNum: 138 },
  { words: "端点安全管理", wordsNum: 671 },
  { words: "物理安全", wordsNum: 851 },
  { words: "法医分析", wordsNum: 949 },
  { words: "应用白名单", wordsNum: 505 },
  { words: "黑帽搜索引擎优化", wordsNum: 324 },
  { words: "网络犯罪", wordsNum: 509 },
  { words: "恶意代码分析", wordsNum: 138 },
  { words: "应急响应计划", wordsNum: 640 },
  { words: "系统更新", wordsNum: 373 },
  { words: "安全漏洞扫描器", wordsNum: 665 },
  { words: "网络流量分析", wordsNum: 837 },
  { words: "加壳", wordsNum: 967 },
  { words: "安全策略实施", wordsNum: 481 },
  { words: "缓冲区溢出", wordsNum: 266 },
  { words: "密码破解", wordsNum: 777 },
  { words: "安全基线", wordsNum: 553 },
  { words: "威胁建模", wordsNum: 657 },
  { words: "安全控制", wordsNum: 668 },
  { words: "风险管理", wordsNum: 72 },
  { words: "漏洞利用", wordsNum: 831 },
  { words: "网络侵入检测", wordsNum: 542 },
  { words: "安全漏洞评估", wordsNum: 896 },
  { words: "逆向工程", wordsNum: 498 },
  { words: "代码审查", wordsNum: 786 },
  { words: "信息流控制", wordsNum: 461 },
  { words: "深度包检查", wordsNum: 761 },
  { words: "攻击特征", wordsNum: 590 },
  { words: "安全态势感知", wordsNum: 983 },
  { words: "防毒软件", wordsNum: 721 },
  { words: "加密算法", wordsNum: 203 },
  { words: "数字取证", wordsNum: 768 },
  { words: "红队演练", wordsNum: 768 },
  { words: "蓝队防守", wordsNum: 880 },
  { words: "白队监督", wordsNum: 402 },
  { words: "安全漏洞数据库", wordsNum: 296 },
  { words: "网络安全等级保护", wordsNum: 517 },
  { words: "安全培训与教育", wordsNum: 161 },
  { words: "网络安全合规性", wordsNum: 258 },
  { words: "隐私保护", wordsNum: 625 },
  { words: "密钥管理", wordsNum: 777 },
  { words: "网络安全审计", wordsNum: 981 },
  { words: "漏洞奖励计划", wordsNum: 504 },
  { words: "供应链安全", wordsNum: 163 },
  { words: "国家网络安全应急体系", wordsNum: 153 },
  { words: "网络空间安全法", wordsNum: 797 },
  { words: "网络安全基础设施", wordsNum: 932 },
  { words: "反恶意软件工具", wordsNum: 557 },
  { words: "安全漏洞通报机制", wordsNum: 831 },
];
export const demo1 = {
  atlasNodeInfos: [
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "两化融合管理体系",
      params: {},
      id: 800,
      entityName: "术语和定义",
      nodeType: 2,
      nodeName: "组织 organization",
      nodeColor: "#F29103",
      weights: 20,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "0",
      params: {},
      id: 801,
      entityName: "术语和定义",
      nodeType: 2,
      nodeName: "新型能力  enhanced capability",
      nodeColor: "#F29103",
      weights: 20,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "0",
      params: {},
      id: 802,
      entityName: "术语和定义",
      nodeType: 2,
      nodeName: "技术  technology",
      nodeColor: "#F29103",
      weights: 20,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "0",
      params: {},
      id: 803,
      entityName: "术语和定义",
      nodeType: 2,
      nodeName: "业务流程  business process",
      nodeColor: "#F29103",
      weights: 20,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "0",
      params: {},
      id: 804,
      entityName: "术语和定义",
      nodeType: 2,
      nodeName: "组织结构  organizational     structure",
      nodeColor: "#F29103",
      weights: 20,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "230",
      params: {},
      id: 100,
      entityName: "国家标准",
      nodeType: 2,
      nodeName: "两化融合管理体系 基础和术语",
      nodeColor: "#F66F6A",
      weights: 30,
      delFlag: 0,
      atlasContents: [
        {
          searchValue: null,
          createTime: null,
          updateTime: null,
          remark: null,
          params: {},
          id: 100,
          attributeName: "标准名称",
          attributeValue: "两化融合管理体系 基础和术语",
          attributeType: "string",
          field: null,
          delFlag: 0,
          count: null,
        },
        {
          searchValue: null,
          createTime: null,
          updateTime: null,
          remark: null,
          params: {},
          id: 101,
          attributeName: "标准类别",
          attributeValue: "国家标准",
          attributeType: "string",
          field: null,
          delFlag: 0,
          count: null,
        },
        {
          searchValue: null,
          createTime: null,
          updateTime: null,
          remark: null,
          params: {},
          id: 102,
          attributeName: "标准号/计划号",
          attributeValue: "GB/T 23000-2017",
          attributeType: "string",
          field: null,
          delFlag: 0,
          count: null,
        },
        {
          searchValue: null,
          createTime: null,
          updateTime: null,
          remark: null,
          params: {},
          id: 103,
          attributeName: "状态",
          attributeValue: "发布实施",
          attributeType: "string",
          field: null,
          delFlag: 0,
          count: null,
        },
        {
          searchValue: null,
          createTime: null,
          updateTime: null,
          remark: null,
          params: {},
          id: 104,
          attributeName: "发布时间",
          attributeValue: "2017-05-22",
          attributeType: "string",
          field: null,
          delFlag: 0,
          count: null,
        },
        {
          searchValue: null,
          createTime: null,
          updateTime: null,
          remark: null,
          params: {},
          id: 105,
          attributeName: "实施时间",
          attributeValue: "2017-05-22",
          attributeType: "string",
          field: null,
          delFlag: 0,
          count: null,
        },
        {
          searchValue: null,
          createTime: null,
          updateTime: null,
          remark: null,
          params: {},
          id: 106,
          attributeName: "归口单位",
          attributeValue: "TC573",
          attributeType: "string",
          field: null,
          delFlag: 0,
          count: null,
        },
      ],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "231",
      params: {},
      id: 101,
      entityName: "国家标准",
      nodeType: 2,
      nodeName: "两化融合管理体系 要求",
      nodeColor: "#F66F6A",
      weights: 30,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "0",
      params: {},
      id: 805,
      entityName: "术语和定义",
      nodeType: 2,
      nodeName: "业务流程职责  responsibility of business process",
      nodeColor: "#F29103",
      weights: 20,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "0",
      params: {},
      id: 806,
      entityName: "术语和定义",
      nodeType: 2,
      nodeName: "信息资源  information resources",
      nodeColor: "#F29103",
      weights: 20,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "232",
      params: {},
      id: 102,
      entityName: "国家标准",
      nodeType: 2,
      nodeName: "两化融合管理体系 实施指南",
      nodeColor: "#F66F6A",
      weights: 30,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "0",
      params: {},
      id: 807,
      entityName: "术语和定义",
      nodeType: 2,
      nodeName: "文件化信息  documented information",
      nodeColor: "#F29103",
      weights: 20,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "233",
      params: {},
      id: 103,
      entityName: "国家标准",
      nodeType: 2,
      nodeName: "两化融合管理体系 评定指南",
      nodeColor: "#F66F6A",
      weights: 30,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "100",
      params: {},
      id: 808,
      entityName: "术语和定义",
      nodeType: 2,
      nodeName: "系统性解决方案  systematic solution",
      nodeColor: "#F29103",
      weights: 20,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "234",
      params: {},
      id: 104,
      entityName: "国家标准",
      nodeType: 2,
      nodeName: "两化融合管理体系 咨询服务指南",
      nodeColor: "#F66F6A",
      weights: 30,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "100",
      params: {},
      id: 200,
      entityName: "标准体系分类一级",
      nodeType: 1,
      nodeName: "A 两化融合管理体系",
      nodeColor: "#9ED5FF",
      weights: 20,
      delFlag: 0,
      atlasContents: [],
      treeId: 9,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "100",
      params: {},
      id: 809,
      entityName: "术语和定义",
      nodeType: 2,
      nodeName: "符合  conformity",
      nodeColor: "#F29103",
      weights: 20,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "394",
      params: {},
      id: 105,
      entityName: "国家标准",
      nodeType: 2,
      nodeName: "两化融合 生态系统参考架构",
      nodeColor: "#F66F6A",
      weights: 30,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "395",
      params: {},
      id: 106,
      entityName: "国家标准",
      nodeType: 2,
      nodeName: "两化融合管理体系 新型能力分级要求",
      nodeColor: "#F66F6A",
      weights: 30,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "100",
      params: {},
      id: 810,
      entityName: "术语和定义",
      nodeType: 2,
      nodeName: "不符合  nonconformity",
      nodeColor: "#F29103",
      weights: 20,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "396",
      params: {},
      id: 107,
      entityName: "国家标准",
      nodeType: 2,
      nodeName: "两化融合管理体系 评定分级指南",
      nodeColor: "#F66F6A",
      weights: 30,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "546",
      params: {},
      id: 108,
      entityName: "国家标准",
      nodeType: 2,
      nodeName: "玻璃行业信息化和工业化融合评估规范",
      nodeColor: "#F66F6A",
      weights: 30,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "100",
      params: {},
      id: 816,
      entityName: "术语和定义",
      nodeType: 2,
      nodeName: "社会贡献率  social  contribution rate",
      nodeColor: "#F29103",
      weights: 20,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "",
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "254",
      params: {},
      id: 1200,
      entityName: "国家标准",
      nodeType: 2,
      nodeName: "两化融合管理体系 供应链数字化管理指南",
      nodeColor: "#F66F6A",
      weights: 30,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: null,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: null,
      remark: "100",
      params: {},
      id: 947,
      entityName: "归口单位",
      nodeType: 2,
      nodeName: "TC573",
      nodeColor: "#99CC49",
      weights: 20,
      delFlag: 0,
      atlasContents: [],
      treeId: null,
      atlasNum: "100",
      deflultValue: "默认显示",
    },
  ],
  atlasRelations: [
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 555,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 100,
      originNodeName: "两化融合管理体系 基础和术语",
      targetNodeId: 800,
      targetNodeName: "组织 organization",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 556,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 100,
      originNodeName: "两化融合管理体系 基础和术语",
      targetNodeId: 801,
      targetNodeName: "新型能力  enhanced capability",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 557,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 100,
      originNodeName: "两化融合管理体系 基础和术语",
      targetNodeId: 802,
      targetNodeName: "技术  technology",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 558,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 100,
      originNodeName: "两化融合管理体系 基础和术语",
      targetNodeId: 803,
      targetNodeName: "业务流程  business process",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 559,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 100,
      originNodeName: "两化融合管理体系 基础和术语",
      targetNodeId: 804,
      targetNodeName: "组织结构  organizational     structure",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 560,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 100,
      originNodeName: "两化融合管理体系 基础和术语",
      targetNodeId: 805,
      targetNodeName: "业务流程职责  responsibility of business process",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 561,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 100,
      originNodeName: "两化融合管理体系 基础和术语",
      targetNodeId: 806,
      targetNodeName: "信息资源  information resources",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 562,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 100,
      originNodeName: "两化融合管理体系 基础和术语",
      targetNodeId: 807,
      targetNodeName: "文件化信息  documented information",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 563,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 101,
      originNodeName: "两化融合管理体系 要求",
      targetNodeId: 800,
      targetNodeName: "组织 organization",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 564,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 101,
      originNodeName: "两化融合管理体系 要求",
      targetNodeId: 801,
      targetNodeName: "新型能力  enhanced capability",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 565,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 101,
      originNodeName: "两化融合管理体系 要求",
      targetNodeId: 802,
      targetNodeName: "技术  technology",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 566,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 101,
      originNodeName: "两化融合管理体系 要求",
      targetNodeId: 803,
      targetNodeName: "业务流程  business process",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 567,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 101,
      originNodeName: "两化融合管理体系 要求",
      targetNodeId: 804,
      targetNodeName: "组织结构  organizational     structure",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 568,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 101,
      originNodeName: "两化融合管理体系 要求",
      targetNodeId: 805,
      targetNodeName: "业务流程职责  responsibility of business process",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 569,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 101,
      originNodeName: "两化融合管理体系 要求",
      targetNodeId: 806,
      targetNodeName: "信息资源  information resources",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 570,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 101,
      originNodeName: "两化融合管理体系 要求",
      targetNodeId: 807,
      targetNodeName: "文件化信息  documented information",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 571,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 102,
      originNodeName: "两化融合管理体系 实施指南",
      targetNodeId: 800,
      targetNodeName: "组织 organization",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 572,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 102,
      originNodeName: "两化融合管理体系 实施指南",
      targetNodeId: 801,
      targetNodeName: "新型能力  enhanced capability",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 573,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 102,
      originNodeName: "两化融合管理体系 实施指南",
      targetNodeId: 802,
      targetNodeName: "技术  technology",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 574,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 102,
      originNodeName: "两化融合管理体系 实施指南",
      targetNodeId: 803,
      targetNodeName: "业务流程  business process",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 575,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 102,
      originNodeName: "两化融合管理体系 实施指南",
      targetNodeId: 804,
      targetNodeName: "组织结构  organizational     structure",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:05",
      remark: null,
      params: {},
      id: 576,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 102,
      originNodeName: "两化融合管理体系 实施指南",
      targetNodeId: 805,
      targetNodeName: "业务流程职责  responsibility of business process",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 577,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 102,
      originNodeName: "两化融合管理体系 实施指南",
      targetNodeId: 806,
      targetNodeName: "信息资源  information resources",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 578,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 102,
      originNodeName: "两化融合管理体系 实施指南",
      targetNodeId: 807,
      targetNodeName: "文件化信息  documented information",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 579,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 103,
      originNodeName: "两化融合管理体系 评定指南",
      targetNodeId: 800,
      targetNodeName: "组织 organization",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 580,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 103,
      originNodeName: "两化融合管理体系 评定指南",
      targetNodeId: 801,
      targetNodeName: "新型能力  enhanced capability",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 581,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 103,
      originNodeName: "两化融合管理体系 评定指南",
      targetNodeId: 802,
      targetNodeName: "技术  technology",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 582,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 103,
      originNodeName: "两化融合管理体系 评定指南",
      targetNodeId: 803,
      targetNodeName: "业务流程  business process",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 583,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 103,
      originNodeName: "两化融合管理体系 评定指南",
      targetNodeId: 804,
      targetNodeName: "组织结构  organizational     structure",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 584,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 103,
      originNodeName: "两化融合管理体系 评定指南",
      targetNodeId: 805,
      targetNodeName: "业务流程职责  responsibility of business process",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 585,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 103,
      originNodeName: "两化融合管理体系 评定指南",
      targetNodeId: 806,
      targetNodeName: "信息资源  information resources",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 586,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 103,
      originNodeName: "两化融合管理体系 评定指南",
      targetNodeId: 807,
      targetNodeName: "文件化信息  documented information",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 587,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 104,
      originNodeName: "两化融合管理体系 咨询服务指南",
      targetNodeId: 800,
      targetNodeName: "组织 organization",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 588,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 104,
      originNodeName: "两化融合管理体系 咨询服务指南",
      targetNodeId: 801,
      targetNodeName: "新型能力  enhanced capability",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 589,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 104,
      originNodeName: "两化融合管理体系 咨询服务指南",
      targetNodeId: 802,
      targetNodeName: "技术  technology",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 590,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 104,
      originNodeName: "两化融合管理体系 咨询服务指南",
      targetNodeId: 803,
      targetNodeName: "业务流程  business process",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 591,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 104,
      originNodeName: "两化融合管理体系 咨询服务指南",
      targetNodeId: 804,
      targetNodeName: "组织结构  organizational     structure",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 592,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 104,
      originNodeName: "两化融合管理体系 咨询服务指南",
      targetNodeId: 805,
      targetNodeName: "业务流程职责  responsibility of business process",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 593,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 104,
      originNodeName: "两化融合管理体系 咨询服务指南",
      targetNodeId: 806,
      targetNodeName: "信息资源  information resources",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 594,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 104,
      originNodeName: "两化融合管理体系 咨询服务指南",
      targetNodeId: 807,
      targetNodeName: "文件化信息  documented information",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 595,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 105,
      originNodeName: "两化融合 生态系统参考架构",
      targetNodeId: 800,
      targetNodeName: "组织 organization",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 596,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 105,
      originNodeName: "两化融合 生态系统参考架构",
      targetNodeId: 801,
      targetNodeName: "新型能力  enhanced capability",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 597,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 105,
      originNodeName: "两化融合 生态系统参考架构",
      targetNodeId: 802,
      targetNodeName: "技术  technology",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 598,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 105,
      originNodeName: "两化融合 生态系统参考架构",
      targetNodeId: 803,
      targetNodeName: "业务流程  business process",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 599,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 105,
      originNodeName: "两化融合 生态系统参考架构",
      targetNodeId: 804,
      targetNodeName: "组织结构  organizational     structure",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 600,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 105,
      originNodeName: "两化融合 生态系统参考架构",
      targetNodeId: 805,
      targetNodeName: "业务流程职责  responsibility of business process",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 601,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 105,
      originNodeName: "两化融合 生态系统参考架构",
      targetNodeId: 806,
      targetNodeName: "信息资源  information resources",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 602,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 105,
      originNodeName: "两化融合 生态系统参考架构",
      targetNodeId: 807,
      targetNodeName: "文件化信息  documented information",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 603,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 106,
      originNodeName: "两化融合管理体系 新型能力分级要求",
      targetNodeId: 808,
      targetNodeName: "系统性解决方案  systematic solution",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 604,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 107,
      originNodeName: "两化融合管理体系 评定分级指南",
      targetNodeId: 809,
      targetNodeName: "符合  conformity",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 605,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 107,
      originNodeName: "两化融合管理体系 评定分级指南",
      targetNodeId: 810,
      targetNodeName: "不符合  nonconformity",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 606,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 108,
      originNodeName: "玻璃行业信息化和工业化融合评估规范",
      targetNodeId: 811,
      targetNodeName:
        "工业企业信息化和工业化融合  integration of informatization and industrialization for industrial enterprises",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 607,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 108,
      originNodeName: "玻璃行业信息化和工业化融合评估规范",
      targetNodeId: 812,
      targetNodeName: "基础建设  infrastructure construction",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 608,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 108,
      originNodeName: "玻璃行业信息化和工业化融合评估规范",
      targetNodeId: 813,
      targetNodeName: "单项应用 domain   application",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 609,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 108,
      originNodeName: "玻璃行业信息化和工业化融合评估规范",
      targetNodeId: 814,
      targetNodeName: "综合集成  comprehensive  integration",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 610,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 108,
      originNodeName: "玻璃行业信息化和工业化融合评估规范",
      targetNodeId: 815,
      targetNodeName: "协同与创新  collaboration or innovation",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:06",
      remark: null,
      params: {},
      id: 611,
      atlasId: 100,
      relationName: "术语和定义",
      originNodeId: 108,
      originNodeName: "玻璃行业信息化和工业化融合评估规范",
      targetNodeId: 816,
      targetNodeName: "社会贡献率  social  contribution rate",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:07",
      remark: null,
      params: {},
      id: 640,
      atlasId: 100,
      relationName: "归口单位",
      originNodeId: 100,
      originNodeName: "两化融合管理体系 基础和术语",
      targetNodeId: 947,
      targetNodeName: "TC573",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:07",
      remark: null,
      params: {},
      id: 641,
      atlasId: 100,
      relationName: "归口单位",
      originNodeId: 101,
      originNodeName: "两化融合管理体系 要求",
      targetNodeId: 947,
      targetNodeName: "TC573",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:07",
      remark: null,
      params: {},
      id: 642,
      atlasId: 100,
      relationName: "归口单位",
      originNodeId: 102,
      originNodeName: "两化融合管理体系 实施指南",
      targetNodeId: 947,
      targetNodeName: "TC573",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:07",
      remark: null,
      params: {},
      id: 643,
      atlasId: 100,
      relationName: "归口单位",
      originNodeId: 103,
      originNodeName: "两化融合管理体系 评定指南",
      targetNodeId: 947,
      targetNodeName: "TC573",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:07",
      remark: null,
      params: {},
      id: 644,
      atlasId: 100,
      relationName: "归口单位",
      originNodeId: 104,
      originNodeName: "两化融合管理体系 咨询服务指南",
      targetNodeId: 947,
      targetNodeName: "TC573",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:07",
      remark: null,
      params: {},
      id: 645,
      atlasId: 100,
      relationName: "归口单位",
      originNodeId: 105,
      originNodeName: "两化融合 生态系统参考架构",
      targetNodeId: 947,
      targetNodeName: "TC573",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:08",
      remark: null,
      params: {},
      id: 646,
      atlasId: 100,
      relationName: "归口单位",
      originNodeId: 106,
      originNodeName: "两化融合管理体系 新型能力分级要求",
      targetNodeId: 947,
      targetNodeName: "TC573",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:08",
      remark: null,
      params: {},
      id: 647,
      atlasId: 100,
      relationName: "归口单位",
      originNodeId: 107,
      originNodeName: "两化融合管理体系 评定分级指南",
      targetNodeId: 947,
      targetNodeName: "TC573",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:08",
      remark: null,
      params: {},
      id: 648,
      atlasId: 100,
      relationName: "归口单位",
      originNodeId: 108,
      originNodeName: "玻璃行业信息化和工业化融合评估规范",
      targetNodeId: 947,
      targetNodeName: "TC573",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:08",
      remark: null,
      params: {},
      id: 1071,
      atlasId: 100,
      relationName: "标准体系",
      originNodeId: 200,
      originNodeName: "A 两化融合管理体系",
      targetNodeId: 100,
      targetNodeName: "两化融合管理体系 基础和术语",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:08",
      remark: null,
      params: {},
      id: 1072,
      atlasId: 100,
      relationName: "标准体系",
      originNodeId: 200,
      originNodeName: "A 两化融合管理体系",
      targetNodeId: 101,
      targetNodeName: "两化融合管理体系 要求",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:08",
      remark: null,
      params: {},
      id: 1073,
      atlasId: 100,
      relationName: "标准体系",
      originNodeId: 200,
      originNodeName: "A 两化融合管理体系",
      targetNodeId: 102,
      targetNodeName: "两化融合管理体系 实施指南",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:08",
      remark: null,
      params: {},
      id: 1074,
      atlasId: 100,
      relationName: "标准体系",
      originNodeId: 200,
      originNodeName: "A 两化融合管理体系",
      targetNodeId: 103,
      targetNodeName: "两化融合管理体系 评定指南",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:08",
      remark: null,
      params: {},
      id: 1075,
      atlasId: 100,
      relationName: "标准体系",
      originNodeId: 200,
      originNodeName: "A 两化融合管理体系",
      targetNodeId: 104,
      targetNodeName: "两化融合管理体系 咨询服务指南",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:08",
      remark: null,
      params: {},
      id: 1076,
      atlasId: 100,
      relationName: "标准体系",
      originNodeId: 200,
      originNodeName: "A 两化融合管理体系",
      targetNodeId: 105,
      targetNodeName: "两化融合 生态系统参考架构",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:08",
      remark: null,
      params: {},
      id: 1077,
      atlasId: 100,
      relationName: "标准体系",
      originNodeId: 200,
      originNodeName: "A 两化融合管理体系",
      targetNodeId: 106,
      targetNodeName: "两化融合管理体系 新型能力分级要求",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: null,
      updateTime: "2024-05-08 15:18:08",
      remark: null,
      params: {},
      id: 1078,
      atlasId: 100,
      relationName: "标准体系",
      originNodeId: 200,
      originNodeName: "A 两化融合管理体系",
      targetNodeId: 107,
      targetNodeName: "两化融合管理体系 评定分级指南",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
    {
      searchValue: null,
      createTime: "2024-05-08 11:07:36",
      updateTime: "2024-05-08 15:18:12",
      remark: null,
      params: {},
      id: 1229,
      atlasId: 100,
      relationName: "归口单位",
      originNodeId: 1200,
      originNodeName: "两化融合管理体系 供应链数字化管理指南",
      targetNodeId: 947,
      targetNodeName: "TC573",
      lineColor: "red",
      lineSize: 1,
      delFlag: 0,
    },
  ],
};

export const demo2 = {
  nodes: [
    {
      id: "0",
      name: "Myriel",
      symbolSize: 100,
      x: -266.82776,
      y: 299.6904,
      value: 28.685715,
      category: 0,
      label: {
        show: true,
      },
    },
    {
      id: "1",
      name: "Napoleon",
      symbolSize: 2.6666666666666665,
      x: -418.08344,
      y: 446.8853,
      value: 4,
      category: 0,
    },
    {
      id: "2",
      name: "MlleBaptistine",
      symbolSize: 6.323809333333333,
      x: -212.76357,
      y: 245.29176,
      value: 9.485714,
      category: 1,
    },
    {
      id: "3",
      name: "MmeMagloire",
      symbolSize: 6.323809333333333,
      x: -242.82404,
      y: 235.26283,
      value: 9.485714,
      category: 1,
    },
    {
      id: "4",
      name: "CountessDeLo",
      symbolSize: 2.6666666666666665,
      x: -379.30386,
      y: 429.06424,
      value: 4,
      category: 0,
    },
    {
      id: "5",
      name: "Geborand",
      symbolSize: 2.6666666666666665,
      x: -417.26337,
      y: 406.03506,
      value: 4,
      category: 0,
    },
    {
      id: "6",
      name: "Champtercier",
      symbolSize: 2.6666666666666665,
      x: -332.6012,
      y: 485.16974,
      value: 4,
      category: 0,
    },
    {
      id: "7",
      name: "Cravatte",
      symbolSize: 2.6666666666666665,
      x: -382.69568,
      y: 475.09113,
      value: 4,
      category: 0,
    },
    {
      id: "8",
      name: "Count",
      symbolSize: 2.6666666666666665,
      x: -320.384,
      y: 387.17325,
      value: 4,
      category: 0,
    },
    {
      id: "9",
      name: "OldMan",
      symbolSize: 2.6666666666666665,
      x: -344.39832,
      y: 451.16772,
      value: 4,
      category: 0,
    },
    {
      id: "10",
      name: "Labarre",
      symbolSize: 2.6666666666666665,
      x: -89.34107,
      y: 234.56128,
      value: 4,
      category: 1,
    },
    {
      id: "11",
      name: "Valjean",
      symbolSize: 66.66666666666667,
      x: -87.93029,
      y: -6.8120565,
      value: 100,
      category: 1,
    },
    {
      id: "12",
      name: "Marguerite",
      symbolSize: 4.495239333333333,
      x: -339.77908,
      y: -184.69139,
      value: 6.742859,
      category: 1,
    },
    {
      id: "13",
      name: "MmeDeR",
      symbolSize: 2.6666666666666665,
      x: -194.31313,
      y: 178.55301,
      value: 4,
      category: 1,
    },
    {
      id: "14",
      name: "Isabeau",
      symbolSize: 2.6666666666666665,
      x: -158.05168,
      y: 201.99768,
      value: 4,
      category: 1,
    },
    {
      id: "15",
      name: "Gervais",
      symbolSize: 2.6666666666666665,
      x: -127.701546,
      y: 242.55057,
      value: 4,
      category: 1,
    },
    {
      id: "16",
      name: "Tholomyes",
      symbolSize: 17.295237333333333,
      x: -385.2226,
      y: -393.5572,
      value: 25.942856,
      category: 2,
    },
    {
      id: "17",
      name: "Listolier",
      symbolSize: 13.638097333333334,
      x: -516.55884,
      y: -393.98975,
      value: 20.457146,
      category: 2,
    },
    {
      id: "18",
      name: "Fameuil",
      symbolSize: 13.638097333333334,
      x: -464.79382,
      y: -493.57944,
      value: 20.457146,
      category: 2,
    },
    {
      id: "19",
      name: "Blacheville",
      symbolSize: 13.638097333333334,
      x: -515.1624,
      y: -456.9891,
      value: 20.457146,
      category: 2,
    },
    {
      id: "20",
      name: "Favourite",
      symbolSize: 13.638097333333334,
      x: -408.12122,
      y: -464.5048,
      value: 20.457146,
      category: 2,
    },
    {
      id: "21",
      name: "Dahlia",
      symbolSize: 13.638097333333334,
      x: -456.44113,
      y: -425.13303,
      value: 20.457146,
      category: 2,
    },
    {
      id: "22",
      name: "Zephine",
      symbolSize: 13.638097333333334,
      x: -459.1107,
      y: -362.5133,
      value: 20.457146,
      category: 2,
    },
    {
      id: "23",
      name: "Fantine",
      symbolSize: 28.266666666666666,
      x: -313.42786,
      y: -289.44803,
      value: 42.4,
      category: 2,
    },
    {
      id: "24",
      name: "MmeThenardier",
      symbolSize: 20.95238266666667,
      x: 4.6313396,
      y: -273.8517,
      value: 31.428574,
      category: 7,
    },
    {
      id: "25",
      name: "Thenardier",
      symbolSize: 30.095235333333335,
      x: 82.80825,
      y: -203.1144,
      value: 45.142853,
      category: 7,
    },
    {
      id: "26",
      name: "Cosette",
      symbolSize: 20.95238266666667,
      x: 78.64646,
      y: -31.512747,
      value: 31.428574,
      category: 6,
    },
    {
      id: "27",
      name: "Javert",
      symbolSize: 31.923806666666668,
      x: -81.46074,
      y: -204.20204,
      value: 47.88571,
      category: 7,
    },
    {
      id: "28",
      name: "Fauchelevent",
      symbolSize: 8.152382000000001,
      x: -225.73984,
      y: 82.41631,
      value: 12.228573,
      category: 4,
    },
    {
      id: "29",
      name: "Bamatabois",
      symbolSize: 15.466666666666667,
      x: -385.6842,
      y: -20.206686,
      value: 23.2,
      category: 3,
    },
    {
      id: "30",
      name: "Perpetue",
      symbolSize: 4.495239333333333,
      x: -403.92447,
      y: -197.69823,
      value: 6.742859,
      category: 2,
    },
    {
      id: "31",
      name: "Simplice",
      symbolSize: 8.152382000000001,
      x: -281.4253,
      y: -158.45137,
      value: 12.228573,
      category: 2,
    },
    {
      id: "32",
      name: "Scaufflaire",
      symbolSize: 2.6666666666666665,
      x: -122.41348,
      y: 210.37503,
      value: 4,
      category: 1,
    },
    {
      id: "33",
      name: "Woman1",
      symbolSize: 4.495239333333333,
      x: -234.6001,
      y: -113.15067,
      value: 6.742859,
      category: 1,
    },
    {
      id: "34",
      name: "Judge",
      symbolSize: 11.809524666666666,
      x: -387.84915,
      y: 58.7059,
      value: 17.714287,
      category: 3,
    },
    {
      id: "35",
      name: "Champmathieu",
      symbolSize: 11.809524666666666,
      x: -338.2307,
      y: 87.48405,
      value: 17.714287,
      category: 3,
    },
    {
      id: "36",
      name: "Brevet",
      symbolSize: 11.809524666666666,
      x: -453.26874,
      y: 58.94648,
      value: 17.714287,
      category: 3,
    },
    {
      id: "37",
      name: "Chenildieu",
      symbolSize: 11.809524666666666,
      x: -386.44904,
      y: 140.05937,
      value: 17.714287,
      category: 3,
    },
    {
      id: "38",
      name: "Cochepaille",
      symbolSize: 11.809524666666666,
      x: -446.7876,
      y: 123.38005,
      value: 17.714287,
      category: 3,
    },
    {
      id: "39",
      name: "Pontmercy",
      symbolSize: 6.323809333333333,
      x: 336.49738,
      y: -269.55914,
      value: 9.485714,
      category: 6,
    },
    {
      id: "40",
      name: "Boulatruelle",
      symbolSize: 2.6666666666666665,
      x: 29.187843,
      y: -460.13132,
      value: 4,
      category: 7,
    },
    {
      id: "41",
      name: "Eponine",
      symbolSize: 20.95238266666667,
      x: 238.36697,
      y: -210.00926,
      value: 31.428574,
      category: 7,
    },
    {
      id: "42",
      name: "Anzelma",
      symbolSize: 6.323809333333333,
      x: 189.69513,
      y: -346.50662,
      value: 9.485714,
      category: 7,
    },
    {
      id: "43",
      name: "Woman2",
      symbolSize: 6.323809333333333,
      x: -187.00418,
      y: -145.02663,
      value: 9.485714,
      category: 6,
    },
    {
      id: "44",
      name: "MotherInnocent",
      symbolSize: 4.495239333333333,
      x: -252.99521,
      y: 129.87549,
      value: 6.742859,
      category: 4,
    },
    {
      id: "45",
      name: "Gribier",
      symbolSize: 2.6666666666666665,
      x: -296.07935,
      y: 163.11964,
      value: 4,
      category: 4,
    },
    {
      id: "46",
      name: "Jondrette",
      symbolSize: 2.6666666666666665,
      x: 550.3201,
      y: 522.4031,
      value: 4,
      category: 5,
    },
    {
      id: "47",
      name: "MmeBurgon",
      symbolSize: 4.495239333333333,
      x: 488.13535,
      y: 356.8573,
      value: 6.742859,
      category: 5,
    },
    {
      id: "48",
      name: "Gavroche",
      symbolSize: 41.06667066666667,
      x: 387.89572,
      y: 110.462326,
      value: 61.600006,
      category: 8,
    },
    {
      id: "49",
      name: "Gillenormand",
      symbolSize: 13.638097333333334,
      x: 126.4831,
      y: 68.10622,
      value: 20.457146,
      category: 6,
    },
    {
      id: "50",
      name: "Magnon",
      symbolSize: 4.495239333333333,
      x: 127.07365,
      y: -113.05923,
      value: 6.742859,
      category: 6,
    },
    {
      id: "51",
      name: "MlleGillenormand",
      symbolSize: 13.638097333333334,
      x: 162.63559,
      y: 117.6565,
      value: 20.457146,
      category: 6,
    },
    {
      id: "52",
      name: "MmePontmercy",
      symbolSize: 4.495239333333333,
      x: 353.66415,
      y: -205.89165,
      value: 6.742859,
      category: 6,
    },
    {
      id: "53",
      name: "MlleVaubois",
      symbolSize: 2.6666666666666665,
      x: 165.43939,
      y: 339.7736,
      value: 4,
      category: 6,
    },
    {
      id: "54",
      name: "LtGillenormand",
      symbolSize: 8.152382000000001,
      x: 137.69348,
      y: 196.1069,
      value: 12.228573,
      category: 6,
    },
    {
      id: "55",
      name: "Marius",
      symbolSize: 35.58095333333333,
      x: 206.44687,
      y: -13.805411,
      value: 53.37143,
      category: 6,
    },
    {
      id: "56",
      name: "BaronessT",
      symbolSize: 4.495239333333333,
      x: 194.82993,
      y: 224.78036,
      value: 6.742859,
      category: 6,
    },
    {
      id: "57",
      name: "Mabeuf",
      symbolSize: 20.95238266666667,
      x: 597.6618,
      y: 135.18481,
      value: 31.428574,
      category: 8,
    },
    {
      id: "58",
      name: "Enjolras",
      symbolSize: 28.266666666666666,
      x: 355.78366,
      y: -74.882454,
      value: 42.4,
      category: 8,
    },
    {
      id: "59",
      name: "Combeferre",
      symbolSize: 20.95238266666667,
      x: 515.2961,
      y: -46.167564,
      value: 31.428574,
      category: 8,
    },
    {
      id: "60",
      name: "Prouvaire",
      symbolSize: 17.295237333333333,
      x: 614.29285,
      y: -69.3104,
      value: 25.942856,
      category: 8,
    },
    {
      id: "61",
      name: "Feuilly",
      symbolSize: 20.95238266666667,
      x: 550.1917,
      y: -128.17537,
      value: 31.428574,
      category: 8,
    },
    {
      id: "62",
      name: "Courfeyrac",
      symbolSize: 24.609526666666667,
      x: 436.17184,
      y: -12.7286825,
      value: 36.91429,
      category: 8,
    },
    {
      id: "63",
      name: "Bahorel",
      symbolSize: 22.780953333333333,
      x: 602.55225,
      y: 16.421427,
      value: 34.17143,
      category: 8,
    },
    {
      id: "64",
      name: "Bossuet",
      symbolSize: 24.609526666666667,
      x: 455.81955,
      y: -115.45826,
      value: 36.91429,
      category: 8,
    },
    {
      id: "65",
      name: "Joly",
      symbolSize: 22.780953333333333,
      x: 516.40784,
      y: 47.242233,
      value: 34.17143,
      category: 8,
    },
    {
      id: "66",
      name: "Grantaire",
      symbolSize: 19.12381,
      x: 646.4313,
      y: -151.06331,
      value: 28.685715,
      category: 8,
    },
    {
      id: "67",
      name: "MotherPlutarch",
      symbolSize: 2.6666666666666665,
      x: 668.9568,
      y: 204.65488,
      value: 4,
      category: 8,
    },
    {
      id: "68",
      name: "Gueulemer",
      symbolSize: 19.12381,
      x: 78.4799,
      y: -347.15146,
      value: 28.685715,
      category: 7,
    },
    {
      id: "69",
      name: "Babet",
      symbolSize: 19.12381,
      x: 150.35959,
      y: -298.50797,
      value: 28.685715,
      category: 7,
    },
    {
      id: "70",
      name: "Claquesous",
      symbolSize: 19.12381,
      x: 137.3717,
      y: -410.2809,
      value: 28.685715,
      category: 7,
    },
    {
      id: "71",
      name: "Montparnasse",
      symbolSize: 17.295237333333333,
      x: 234.87747,
      y: -400.85983,
      value: 25.942856,
      category: 7,
    },
    {
      id: "72",
      name: "Toussaint",
      symbolSize: 6.323809333333333,
      x: 40.942253,
      y: 113.78272,
      value: 9.485714,
      category: 1,
    },
    {
      id: "73",
      name: "Child1",
      symbolSize: 4.495239333333333,
      x: 437.939,
      y: 291.58234,
      value: 6.742859,
      category: 8,
    },
    {
      id: "74",
      name: "Child2",
      symbolSize: 4.495239333333333,
      x: 466.04922,
      y: 283.3606,
      value: 6.742859,
      category: 8,
    },
    {
      id: "75",
      name: "Brujon",
      symbolSize: 13.638097333333334,
      x: 238.79364,
      y: -314.06345,
      value: 20.457146,
      category: 7,
    },
    {
      id: "76",
      name: "MmeHucheloup",
      symbolSize: 13.638097333333334,
      x: 712.18353,
      y: 4.8131495,
      value: 20.457146,
      category: 8,
    },
  ],
  links: [
    {
      source: "1",
      target: "0",
    },
    {
      source: "2",
      target: "0",
    },
    {
      source: "3",
      target: "0",
    },
    {
      source: "3",
      target: "2",
    },
    {
      source: "4",
      target: "0",
    },
    {
      source: "5",
      target: "0",
    },
    {
      source: "6",
      target: "0",
    },
    {
      source: "7",
      target: "0",
    },
    {
      source: "8",
      target: "0",
    },
    {
      source: "9",
      target: "0",
    },
    {
      source: "11",
      target: "0",
    },
    {
      source: "11",
      target: "2",
    },
    {
      source: "11",
      target: "3",
    },
    {
      source: "11",
      target: "10",
    },
    {
      source: "12",
      target: "11",
    },
    {
      source: "13",
      target: "11",
    },
    {
      source: "14",
      target: "11",
    },
    {
      source: "15",
      target: "11",
    },
    {
      source: "17",
      target: "16",
    },
    {
      source: "18",
      target: "16",
    },
    {
      source: "18",
      target: "17",
    },
    {
      source: "19",
      target: "16",
    },
    {
      source: "19",
      target: "17",
    },
    {
      source: "19",
      target: "18",
    },
    {
      source: "20",
      target: "16",
    },
    {
      source: "20",
      target: "17",
    },
    {
      source: "20",
      target: "18",
    },
    {
      source: "20",
      target: "19",
    },
    {
      source: "21",
      target: "16",
    },
    {
      source: "21",
      target: "17",
    },
    {
      source: "21",
      target: "18",
    },
    {
      source: "21",
      target: "19",
    },
    {
      source: "21",
      target: "20",
    },
    {
      source: "22",
      target: "16",
    },
    {
      source: "22",
      target: "17",
    },
    {
      source: "22",
      target: "18",
    },
    {
      source: "22",
      target: "19",
    },
    {
      source: "22",
      target: "20",
    },
    {
      source: "22",
      target: "21",
    },
    {
      source: "23",
      target: "11",
    },
    {
      source: "23",
      target: "12",
    },
    {
      source: "23",
      target: "16",
    },
    {
      source: "23",
      target: "17",
    },
    {
      source: "23",
      target: "18",
    },
    {
      source: "23",
      target: "19",
    },
    {
      source: "23",
      target: "20",
    },
    {
      source: "23",
      target: "21",
    },
    {
      source: "23",
      target: "22",
    },
    {
      source: "24",
      target: "11",
    },
    {
      source: "24",
      target: "23",
    },
    {
      source: "25",
      target: "11",
    },
    {
      source: "25",
      target: "23",
    },
    {
      source: "25",
      target: "24",
    },
    {
      source: "26",
      target: "11",
    },
    {
      source: "26",
      target: "16",
    },
    {
      source: "26",
      target: "24",
    },
    {
      source: "26",
      target: "25",
    },
    {
      source: "27",
      target: "11",
    },
    {
      source: "27",
      target: "23",
    },
    {
      source: "27",
      target: "24",
    },
    {
      source: "27",
      target: "25",
    },
    {
      source: "27",
      target: "26",
    },
    {
      source: "28",
      target: "11",
    },
    {
      source: "28",
      target: "27",
    },
    {
      source: "29",
      target: "11",
    },
    {
      source: "29",
      target: "23",
    },
    {
      source: "29",
      target: "27",
    },
    {
      source: "30",
      target: "23",
    },
    {
      source: "31",
      target: "11",
    },
    {
      source: "31",
      target: "23",
    },
    {
      source: "31",
      target: "27",
    },
    {
      source: "31",
      target: "30",
    },
    {
      source: "32",
      target: "11",
    },
    {
      source: "33",
      target: "11",
    },
    {
      source: "33",
      target: "27",
    },
    {
      source: "34",
      target: "11",
    },
    {
      source: "34",
      target: "29",
    },
    {
      source: "35",
      target: "11",
    },
    {
      source: "35",
      target: "29",
    },
    {
      source: "35",
      target: "34",
    },
    {
      source: "36",
      target: "11",
    },
    {
      source: "36",
      target: "29",
    },
    {
      source: "36",
      target: "34",
    },
    {
      source: "36",
      target: "35",
    },
    {
      source: "37",
      target: "11",
    },
    {
      source: "37",
      target: "29",
    },
    {
      source: "37",
      target: "34",
    },
    {
      source: "37",
      target: "35",
    },
    {
      source: "37",
      target: "36",
    },
    {
      source: "38",
      target: "11",
    },
    {
      source: "38",
      target: "29",
    },
    {
      source: "38",
      target: "34",
    },
    {
      source: "38",
      target: "35",
    },
    {
      source: "38",
      target: "36",
    },
    {
      source: "38",
      target: "37",
    },
    {
      source: "39",
      target: "25",
    },
    {
      source: "40",
      target: "25",
    },
    {
      source: "41",
      target: "24",
    },
    {
      source: "41",
      target: "25",
    },
    {
      source: "42",
      target: "24",
    },
    {
      source: "42",
      target: "25",
    },
    {
      source: "42",
      target: "41",
    },
    {
      source: "43",
      target: "11",
    },
    {
      source: "43",
      target: "26",
    },
    {
      source: "43",
      target: "27",
    },
    {
      source: "44",
      target: "11",
    },
    {
      source: "44",
      target: "28",
    },
    {
      source: "45",
      target: "28",
    },
    {
      source: "47",
      target: "46",
    },
    {
      source: "48",
      target: "11",
    },
    {
      source: "48",
      target: "25",
    },
    {
      source: "48",
      target: "27",
    },
    {
      source: "48",
      target: "47",
    },
    {
      source: "49",
      target: "11",
    },
    {
      source: "49",
      target: "26",
    },
    {
      source: "50",
      target: "24",
    },
    {
      source: "50",
      target: "49",
    },
    {
      source: "51",
      target: "11",
    },
    {
      source: "51",
      target: "26",
    },
    {
      source: "51",
      target: "49",
    },
    {
      source: "52",
      target: "39",
    },
    {
      source: "52",
      target: "51",
    },
    {
      source: "53",
      target: "51",
    },
    {
      source: "54",
      target: "26",
    },
    {
      source: "54",
      target: "49",
    },
    {
      source: "54",
      target: "51",
    },
    {
      source: "55",
      target: "11",
    },
    {
      source: "55",
      target: "16",
    },
    {
      source: "55",
      target: "25",
    },
    {
      source: "55",
      target: "26",
    },
    {
      source: "55",
      target: "39",
    },
    {
      source: "55",
      target: "41",
    },
    {
      source: "55",
      target: "48",
    },
    {
      source: "55",
      target: "49",
    },
    {
      source: "55",
      target: "51",
    },
    {
      source: "55",
      target: "54",
    },
    {
      source: "56",
      target: "49",
    },
    {
      source: "56",
      target: "55",
    },
    {
      source: "57",
      target: "41",
    },
    {
      source: "57",
      target: "48",
    },
    {
      source: "57",
      target: "55",
    },
    {
      source: "58",
      target: "11",
    },
    {
      source: "58",
      target: "27",
    },
    {
      source: "58",
      target: "48",
    },
    {
      source: "58",
      target: "55",
    },
    {
      source: "58",
      target: "57",
    },
    {
      source: "59",
      target: "48",
    },
    {
      source: "59",
      target: "55",
    },
    {
      source: "59",
      target: "57",
    },
    {
      source: "59",
      target: "58",
    },
    {
      source: "60",
      target: "48",
    },
    {
      source: "60",
      target: "58",
    },
    {
      source: "60",
      target: "59",
    },
    {
      source: "61",
      target: "48",
    },
    {
      source: "61",
      target: "55",
    },
    {
      source: "61",
      target: "57",
    },
    {
      source: "61",
      target: "58",
    },
    {
      source: "61",
      target: "59",
    },
    {
      source: "61",
      target: "60",
    },
    {
      source: "62",
      target: "41",
    },
    {
      source: "62",
      target: "48",
    },
    {
      source: "62",
      target: "55",
    },
    {
      source: "62",
      target: "57",
    },
    {
      source: "62",
      target: "58",
    },
    {
      source: "62",
      target: "59",
    },
    {
      source: "62",
      target: "60",
    },
    {
      source: "62",
      target: "61",
    },
    {
      source: "63",
      target: "48",
    },
    {
      source: "63",
      target: "55",
    },
    {
      source: "63",
      target: "57",
    },
    {
      source: "63",
      target: "58",
    },
    {
      source: "63",
      target: "59",
    },
    {
      source: "63",
      target: "60",
    },
    {
      source: "63",
      target: "61",
    },
    {
      source: "63",
      target: "62",
    },
    {
      source: "64",
      target: "11",
    },
    {
      source: "64",
      target: "48",
    },
    {
      source: "64",
      target: "55",
    },
    {
      source: "64",
      target: "57",
    },
    {
      source: "64",
      target: "58",
    },
    {
      source: "64",
      target: "59",
    },
    {
      source: "64",
      target: "60",
    },
    {
      source: "64",
      target: "61",
    },
    {
      source: "64",
      target: "62",
    },
    {
      source: "64",
      target: "63",
    },
    {
      source: "65",
      target: "48",
    },
    {
      source: "65",
      target: "55",
    },
    {
      source: "65",
      target: "57",
    },
    {
      source: "65",
      target: "58",
    },
    {
      source: "65",
      target: "59",
    },
    {
      source: "65",
      target: "60",
    },
    {
      source: "65",
      target: "61",
    },
    {
      source: "65",
      target: "62",
    },
    {
      source: "65",
      target: "63",
    },
    {
      source: "65",
      target: "64",
    },
    {
      source: "66",
      target: "48",
    },
    {
      source: "66",
      target: "58",
    },
    {
      source: "66",
      target: "59",
    },
    {
      source: "66",
      target: "60",
    },
    {
      source: "66",
      target: "61",
    },
    {
      source: "66",
      target: "62",
    },
    {
      source: "66",
      target: "63",
    },
    {
      source: "66",
      target: "64",
    },
    {
      source: "66",
      target: "65",
    },
    {
      source: "67",
      target: "57",
    },
    {
      source: "68",
      target: "11",
    },
    {
      source: "68",
      target: "24",
    },
    {
      source: "68",
      target: "25",
    },
    {
      source: "68",
      target: "27",
    },
    {
      source: "68",
      target: "41",
    },
    {
      source: "68",
      target: "48",
    },
    {
      source: "69",
      target: "11",
    },
    {
      source: "69",
      target: "24",
    },
    {
      source: "69",
      target: "25",
    },
    {
      source: "69",
      target: "27",
    },
    {
      source: "69",
      target: "41",
    },
    {
      source: "69",
      target: "48",
    },
    {
      source: "69",
      target: "68",
    },
    {
      source: "70",
      target: "11",
    },
    {
      source: "70",
      target: "24",
    },
    {
      source: "70",
      target: "25",
    },
    {
      source: "70",
      target: "27",
    },
    {
      source: "70",
      target: "41",
    },
    {
      source: "70",
      target: "58",
    },
    {
      source: "70",
      target: "68",
    },
    {
      source: "70",
      target: "69",
    },
    {
      source: "71",
      target: "11",
    },
    {
      source: "71",
      target: "25",
    },
    {
      source: "71",
      target: "27",
    },
    {
      source: "71",
      target: "41",
    },
    {
      source: "71",
      target: "48",
    },
    {
      source: "71",
      target: "68",
    },
    {
      source: "71",
      target: "69",
    },
    {
      source: "71",
      target: "70",
    },
    {
      source: "72",
      target: "11",
    },
    {
      source: "72",
      target: "26",
    },
    {
      source: "72",
      target: "27",
    },
    {
      source: "73",
      target: "48",
    },
    {
      source: "74",
      target: "48",
    },
    {
      source: "74",
      target: "73",
    },
    {
      source: "75",
      target: "25",
    },
    {
      source: "75",
      target: "41",
    },
    {
      source: "75",
      target: "48",
    },
    {
      source: "75",
      target: "68",
    },
    {
      source: "75",
      target: "69",
    },
    {
      source: "75",
      target: "70",
    },
    {
      source: "75",
      target: "71",
    },
    {
      source: "76",
      target: "48",
    },
    {
      source: "76",
      target: "58",
    },
    {
      source: "76",
      target: "62",
    },
    {
      source: "76",
      target: "63",
    },
    {
      source: "76",
      target: "64",
    },
    {
      source: "76",
      target: "65",
    },
    {
      source: "76",
      target: "66",
    },
  ],
  categories: ["1", "2"],
};

export const nameMap = {
  Canada: "加拿大",
  Turkmenistan: "土库曼斯坦",
  "Saint Helena": "圣赫勒拿",
  "Lao PDR": "老挝",
  Lithuania: "立陶宛",
  Cambodia: "柬埔寨",
  Ethiopia: "埃塞俄比亚",
  "Faeroe Is.": "法罗群岛",
  Swaziland: "斯威士兰",
  Palestine: "巴勒斯坦",
  Belize: "伯利兹",
  Argentina: "阿根廷",
  Bolivia: "玻利维亚",
  Cameroon: "喀麦隆",
  "Burkina Faso": "布基纳法索",
  Aland: "奥兰群岛",
  Bahrain: "巴林",
  "Saudi Arabia": "沙特阿拉伯",
  "Fr. Polynesia": "法属波利尼西亚",
  "Cape Verde": "佛得角",
  "W. Sahara": "西撒哈拉",
  Slovenia: "斯洛文尼亚",
  Guatemala: "危地马拉",
  Guinea: "几内亚",
  "Dem. Rep. Congo": "刚果（金）",
  Germany: "德国",
  Spain: "西班牙",
  Liberia: "利比里亚",
  Netherlands: "荷兰",
  Jamaica: "牙买加",
  "Solomon Is.": "所罗门群岛",
  Oman: "阿曼",
  Tanzania: "坦桑尼亚",
  "Costa Rica": "哥斯达黎加",
  "Isle of Man": "曼岛",
  Gabon: "加蓬",
  Niue: "纽埃",
  Bahamas: "巴哈马",
  "New Zealand": "新西兰",
  Yemen: "也门",
  Jersey: "泽西岛",
  Pakistan: "巴基斯坦",
  Albania: "阿尔巴尼亚",
  Samoa: "萨摩亚",
  "Czech Rep.": "捷克",
  "United Arab Emirates": "阿拉伯联合酋长国",
  Guam: "关岛",
  India: "印度",
  Azerbaijan: "阿塞拜疆",
  "N. Mariana Is.": "北马里亚纳群岛",
  Lesotho: "莱索托",
  Kenya: "肯尼亚",
  Belarus: "白俄罗斯",
  Tajikistan: "塔吉克斯坦",
  Turkey: "土耳其",
  Afghanistan: "阿富汗",
  Bangladesh: "孟加拉国",
  Mauritania: "毛里塔尼亚",
  "Dem. Rep. Korea": "朝鲜",
  "Saint Lucia": "圣卢西亚",
  "Br. Indian Ocean Ter.": "英属印度洋领地",
  Mongolia: "蒙古国",
  France: "法国",
  "Cura?ao": "库拉索岛",
  "S. Sudan": "南苏丹",
  Rwanda: "卢旺达",
  Slovakia: "斯洛伐克",
  Somalia: "索马里",
  Peru: "秘鲁",
  Vanuatu: "瓦努阿图",
  Norway: "挪威",
  Malawi: "马拉维",
  Benin: "贝宁",
  "St. Vin. and Gren.": "圣文森特和格林纳丁斯",
  Korea: "韩国",
  Singapore: "新加坡",
  Montenegro: "黑山",
  "Cayman Is.": "开曼群岛",
  Togo: "多哥",
  China: "中国",
  "Heard I. and McDonald Is.": "赫德岛和麦克唐纳群岛",
  Armenia: "亚美尼亚",
  "Falkland Is.": "马尔维纳斯群岛（福克兰）",
  Ukraine: "乌克兰",
  Ghana: "加纳",
  Tonga: "汤加",
  Finland: "芬兰",
  Libya: "利比亚",
  "Dominican Rep.": "多米尼加",
  Indonesia: "印度尼西亚",
  Mauritius: "毛里求斯",
  "Eq. Guinea": "赤道几内亚",
  Sweden: "瑞典",
  Vietnam: "越南",
  Mali: "马里",
  Russia: "俄罗斯",
  Bulgaria: "保加利亚",
  "United States": "美国",
  Romania: "罗马尼亚",
  Angola: "安哥拉",
  Chad: "乍得",
  "South Africa": "南非",
  Fiji: "斐济",
  Liechtenstein: "列支敦士登",
  Malaysia: "马来西亚",
  Austria: "奥地利",
  Mozambique: "莫桑比克",
  Uganda: "乌干达",
  Japan: "日本",
  Niger: "尼日尔",
  Brazil: "巴西",
  Kuwait: "科威特",
  Panama: "巴拿马",
  Guyana: "圭亚那合作共和国",
  Madagascar: "马达加斯加",
  Luxembourg: "卢森堡",
  "American Samoa": "美属萨摩亚",
  Andorra: "安道尔",
  Ireland: "爱尔兰",
  Italy: "意大利",
  Nigeria: "尼日利亚",
  "Turks and Caicos Is.": "特克斯和凯科斯群岛",
  Ecuador: "厄瓜多尔",
  "U.S. Virgin Is.": "美属维尔京群岛",
  Brunei: "文莱",
  Australia: "澳大利亚",
  Iran: "伊朗",
  Algeria: "阿尔及利亚",
  "El Salvador": "萨尔瓦多",
  "CÃ´te d'Ivoire": "科特迪瓦",
  Chile: "智利",
  "Puerto Rico": "波多黎各",
  Belgium: "比利时",
  Thailand: "泰国",
  Haiti: "海地",
  Iraq: "伊拉克",
  "S?o Tomé and Principe": "圣多美和普林西比",
  "Sierra Leone": "塞拉利昂",
  Georgia: "格鲁吉亚",
  Denmark: "丹麦",
  Philippines: "菲律宾",
  "S. Geo. and S. Sandw. Is.": "南乔治亚岛和南桑威奇群岛",
  Moldova: "摩尔多瓦",
  Morocco: "摩洛哥",
  Namibia: "纳米比亚",
  Malta: "马耳他",
  "Guinea-Bissau": "几内亚比绍",
  Kiribati: "基里巴斯",
  Switzerland: "瑞士",
  Grenada: "格林纳达",
  Seychelles: "塞舌尔",
  Portugal: "葡萄牙",
  Estonia: "爱沙尼亚",
  Uruguay: "乌拉圭",
  "Antigua and Barb.": "安提瓜和巴布达",
  Lebanon: "黎巴嫩",
  Uzbekistan: "乌兹别克斯坦",
  Tunisia: "突尼斯",
  Djibouti: "吉布提",
  Greenland: "丹麦",
  "Timor-Leste": "东帝汶",
  Dominica: "多米尼克",
  Colombia: "哥伦比亚",
  Burundi: "布隆迪",
  "Bosnia and Herz.": "波斯尼亚和黑塞哥维那",
  Cyprus: "塞浦路斯",
  Barbados: "巴巴多斯",
  Qatar: "卡塔尔",
  Palau: "帕劳",
  Bhutan: "不丹",
  Sudan: "苏丹",
  Nepal: "尼泊尔",
  Micronesia: "密克罗尼西亚",
  Bermuda: "百慕大",
  Suriname: "苏里南",
  Venezuela: "委内瑞拉",
  Israel: "以色列",
  "St. Pierre and Miquelon": "圣皮埃尔和密克隆群岛",
  "Central African Rep.": "中非共和国",
  Iceland: "冰岛",
  Zambia: "赞比亚",
  Senegal: "塞内加尔",
  "Papua New Guinea": "巴布亚新几内亚",
  "Trinidad and Tobago": "特立尼达和多巴哥",
  Zimbabwe: "津巴布韦",
  Jordan: "约旦",
  Gambia: "冈比亚",
  Kazakhstan: "哈萨克斯坦",
  Poland: "波兰",
  Eritrea: "厄立特里亚",
  Kyrgyzstan: "吉尔吉斯斯坦",
  Montserrat: "蒙特塞拉特",
  "New Caledonia": "新喀里多尼亚",
  Macedonia: "马其顿",
  Paraguay: "巴拉圭",
  Latvia: "拉脱维亚",
  Hungary: "匈牙利",
  Syria: "叙利亚",
  Honduras: "洪都拉斯",
  Myanmar: "缅甸",
  Mexico: "墨西哥",
  Egypt: "埃及",
  Nicaragua: "尼加拉瓜",
  Cuba: "古巴",
  Serbia: "塞尔维亚",
  Comoros: "科摩罗",
  "United Kingdom": "英国",
  "Fr. S. Antarctic Lands": "南极洲",
  Congo: "刚果（布）",
  Greece: "希腊",
  "Sri Lanka": "斯里兰卡",
  Croatia: "克罗地亚",
  Botswana: "博茨瓦纳",
  "Siachen Glacier": "锡亚琴冰川地区",
};
