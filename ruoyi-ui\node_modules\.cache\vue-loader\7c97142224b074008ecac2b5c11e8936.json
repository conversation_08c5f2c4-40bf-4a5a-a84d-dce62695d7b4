{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\InfoEscalation\\Wechat.vue?vue&type=template&id=692f5a28&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\InfoEscalation\\Wechat.vue", "mtime": 1754299716358}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgdi1sb2FkaW5nPSJnbG9iYWxMb2FkaW5nIiBlbGVtZW50LWxvYWRpbmctdGV4dD0i5pWw5o2u5Yqg6L295LitIj4KICA8c3BsaXRwYW5lcyBjbGFzcz0iZGVmYXVsdC10aGVtZSI+CiAgICA8cGFuZQogICAgICBjbGFzcz0ibGVmdExpbmsiCiAgICAgIHJlZj0ibGVmdExpbmsiCiAgICAgIG1pbi1zaXplPSIyMCIKICAgICAgbWF4LXNpemU9IjUwIgogICAgICBzaXplPSIyNiIKICAgID4KICAgICAgPFRyZWVUYWJsZQogICAgICAgIHJlZj0idHJlZVRhYmxlIgogICAgICAgIDpkYXRhPSJ0cmVlRGF0YVRyYW5zZmVyIgogICAgICAgIDp0b3RhbD0idHJlZVRvdGFsIgogICAgICAgIDpjdXJyZW50LXBhZ2U9InRyZWVDdXJyZW50UGFnZSIKICAgICAgICA6cGFnZS1zaXplPSJ0cmVlUGFnZVNpemUiCiAgICAgICAgOmxvYWRpbmc9ImxvYWRpbmciCiAgICAgICAgOnNlbGVjdGVkLXNvdXJjZXM9InNhdmVkQ2hlY2tib3hEYXRhIgogICAgICAgIHJvdy1rZXk9ImlkIgogICAgICAgIEBzZWxlY3Rpb24tY2hhbmdlPSJoYW5kbGVTZWxlY3Rpb25DaGFuZ2UiCiAgICAgICAgQHJlc2V0PSJoYW5kbGVSZXNldCIKICAgICAgICBAc2l6ZS1jaGFuZ2U9ImhhbmRsZVRyZWVQYWdlU2l6ZUNoYW5nZSIKICAgICAgICBAY3VycmVudC1jaGFuZ2U9ImhhbmRsZVRyZWVDdXJyZW50Q2hhbmdlIgogICAgICAgIEBmaWx0ZXItc2VhcmNoPSJoYW5kbGVGaWx0ZXJTZWFyY2giCiAgICAgICAgQGNsYXNzaWZ5LWNoYW5nZT0iaGFuZGxlQ2xhc3NpZnlDaGFuZ2UiCiAgICAgIC8+CiAgICA8L3BhbmU+CiAgICA8cGFuZSBtaW4tc2l6ZT0iNTAiIG1heC1zaXplPSI4MCIgc2l6ZT0iNzQiPgogICAgICA8ZGl2CiAgICAgICAgY2xhc3M9InJpZ2h0TWFpbiIKICAgICAgICBzdHlsZT0ibWFyZ2luLWxlZnQ6IDA7IG92ZXJmbG93LXk6IGF1dG8iCiAgICAgICAgcmVmPSJyaWdodE1haW4iCiAgICAgICAgdi1sb2FkaW5nPSJ0YWJsZUxvYWRpbmciCiAgICAgICAgZWxlbWVudC1sb2FkaW5nLXRleHQ9IuaVsOaNruWKoOi9veS4rSIKICAgICAgPgogICAgICAgIDxlbC1mb3JtCiAgICAgICAgICA6bW9kZWw9InF1ZXJ5UGFyYW1zIgogICAgICAgICAgcmVmPSJGb3JtIgogICAgICAgICAgbGFiZWwtd2lkdGg9IjkwcHgiCiAgICAgICAgICBAc3VibWl0Lm5hdGl2ZS5wcmV2ZW50CiAgICAgICAgPgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5Y+R5biD5pel5pyfOiIgcHJvcD0iZGF0ZVR5cGUiPgogICAgICAgICAgICA8ZWwtcmFkaW8tZ3JvdXAgdi1tb2RlbD0icXVlcnlQYXJhbXMuZGF0ZVR5cGUiIHNpemU9InNtYWxsIj4KICAgICAgICAgICAgICA8ZWwtcmFkaW8tYnV0dG9uIDpsYWJlbD0iMSI+5LuK5aSpPC9lbC1yYWRpby1idXR0b24+CiAgICAgICAgICAgICAgPGVsLXJhZGlvLWJ1dHRvbiA6bGFiZWw9IjIiPui/kTLlpKk8L2VsLXJhZGlvLWJ1dHRvbj4KICAgICAgICAgICAgICA8ZWwtcmFkaW8tYnV0dG9uIDpsYWJlbD0iNCI+6L+RN+WkqTwvZWwtcmFkaW8tYnV0dG9uPgogICAgICAgICAgICAgIDxlbC1yYWRpby1idXR0b24gOmxhYmVsPSI1Ij7ov5EzMOWkqTwvZWwtcmFkaW8tYnV0dG9uPgogICAgICAgICAgICAgIDxlbC1yYWRpby1idXR0b24gOmxhYmVsPSIxMCI+5YWo6YOoPC9lbC1yYWRpby1idXR0b24+CiAgICAgICAgICAgIDwvZWwtcmFkaW8tZ3JvdXA+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDxkaXYgc3R5bGU9ImRpc3BsYXk6IGZsZXgiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtCiAgICAgICAgICAgICAgbGFiZWw9IuWwj+S/oeS8mOmAiToiCiAgICAgICAgICAgICAgcHJvcD0iaXNUZWNobm9sb2d5IgogICAgICAgICAgICAgIHN0eWxlPSJtYXJnaW4tcmlnaHQ6IDIwcHgiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8ZWwtcmFkaW8tZ3JvdXAgdi1tb2RlbD0icXVlcnlQYXJhbXMuaXNUZWNobm9sb2d5IiBzaXplPSJzbWFsbCI+CiAgICAgICAgICAgICAgICA8ZWwtcmFkaW8tYnV0dG9uCiAgICAgICAgICAgICAgICAgIHYtZm9yPSJkaWN0IGluIGRpY3QudHlwZS5pc190ZWNobm9sb2d5IgogICAgICAgICAgICAgICAgICA6bGFiZWw9ImRpY3QudmFsdWUiCiAgICAgICAgICAgICAgICAgIDprZXk9Iidpc190ZWNobm9sb2d5JyArIGRpY3QudmFsdWUiCiAgICAgICAgICAgICAgICAgID57eyBkaWN0LmxhYmVsIH19PC9lbC1yYWRpby1idXR0b24KICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDxlbC1yYWRpby1idXR0b24gOmxhYmVsPSJudWxsIiA6a2V5PSInaXNfdGVjaG5vbG9neUFsbCciCiAgICAgICAgICAgICAgICAgID7lhajpg6g8L2VsLXJhZGlvLWJ1dHRvbgogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgIDwvZWwtcmFkaW8tZ3JvdXA+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlsI/kv6Hnsr7pgIk6IiBwcm9wPSJlbW90aW9uIj4KICAgICAgICAgICAgICA8ZWwtcmFkaW8tZ3JvdXAgdi1tb2RlbD0icXVlcnlQYXJhbXMuZW1vdGlvbiIgc2l6ZT0ic21hbGwiPgogICAgICAgICAgICAgICAgPGVsLXJhZGlvLWJ1dHRvbiA6bGFiZWw9IicxJyIgOmtleT0iJ2lzX2Vtb3Rpb24xJyIKICAgICAgICAgICAgICAgICAgPumAieS4rTwvZWwtcmFkaW8tYnV0dG9uCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8ZWwtcmFkaW8tYnV0dG9uIDpsYWJlbD0iJzAnIiA6a2V5PSInaXNfZW1vdGlvbjAnIgogICAgICAgICAgICAgICAgICA+5YWo6YOoPC9lbC1yYWRpby1idXR0b24KICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICA8L2VsLXJhZGlvLWdyb3VwPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5qOA57Si6K+N5bqTOiIgcHJvcD0idGFncyI+CiAgICAgICAgICAgIDxlbC1yYWRpby1ncm91cCB2LW1vZGVsPSJxdWVyeVBhcmFtcy50YWdzIiBzaXplPSJzbWFsbCI+CiAgICAgICAgICAgICAgPGVsLXJhZGlvIDpsYWJlbD0iJyciPuWFqOmDqDwvZWwtcmFkaW8+CiAgICAgICAgICAgICAgPGVsLXJhZGlvCiAgICAgICAgICAgICAgICB2LWZvcj0iaXRlbSBpbiB0YWdzTGlzdDEiCiAgICAgICAgICAgICAgICA6a2V5PSJpdGVtLmlkIgogICAgICAgICAgICAgICAgOmxhYmVsPSJpdGVtLmlkIgogICAgICAgICAgICAgICAgPnt7IGl0ZW0ubmFtZSB9fTwvZWwtcmFkaW8KICAgICAgICAgICAgICA+CiAgICAgICAgICAgIDwvZWwtcmFkaW8tZ3JvdXA+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDxlbC1mb3JtLWl0ZW0KICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDAlOyBvdmVyZmxvdzogYXV0byIKICAgICAgICAgICAgbGFiZWw9IiIKICAgICAgICAgICAgcHJvcD0idGFnc1N1YnNldCIKICAgICAgICAgICAgdi1pZj0icXVlcnlQYXJhbXMudGFncyAhPSAnJyIKICAgICAgICAgID4KICAgICAgICAgICAgPGVsLWNoZWNrYm94CiAgICAgICAgICAgICAgc3R5bGU9ImZsb2F0OiBsZWZ0OyBtYXJnaW4tcmlnaHQ6IDMwcHgiCiAgICAgICAgICAgICAgOmluZGV0ZXJtaW5hdGU9ImlzSW5kZXRlcm1pbmF0ZSIKICAgICAgICAgICAgICB2LW1vZGVsPSJjaGVja0FsbCIKICAgICAgICAgICAgICBAY2hhbmdlPSJoYW5kbGVDaGVja0FsbFRhZ3NTdWJzZXQiCiAgICAgICAgICAgICAgPuWFqOmAiTwvZWwtY2hlY2tib3gKICAgICAgICAgICAgPgogICAgICAgICAgICA8ZWwtY2hlY2tib3gtZ3JvdXAgdi1tb2RlbD0icXVlcnlQYXJhbXMudGFnc1N1YnNldCI+CiAgICAgICAgICAgICAgPGVsLWNoZWNrYm94CiAgICAgICAgICAgICAgICB2LWZvcj0iaXRlbSBpbiB0YWdzTGlzdCIKICAgICAgICAgICAgICAgIDprZXk9Iml0ZW0ubmFtZSIKICAgICAgICAgICAgICAgIDpsYWJlbD0iaXRlbS5uYW1lIgogICAgICAgICAgICAgID48L2VsLWNoZWNrYm94PgogICAgICAgICAgICA8L2VsLWNoZWNrYm94LWdyb3VwPgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGNsYXNzPSJrZXl3b3JkIiBsYWJlbD0i5YWz6ZSu6K+NOiIgcHJvcD0ia2V5d29yZHMiPgogICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICByZWY9ImtleXdvcmRSZWYiCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeWFs+mUruivjSzkvb/nlKjpgJflj7fliIblibIo6Iux5paHKSIKICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDQzMHB4IgogICAgICAgICAgICAgIHYtbW9kZWw9InF1ZXJ5UGFyYW1zLmtleXdvcmRzIgogICAgICAgICAgICAgIEBmb2N1cz0ic2hvd0hpc3RvcnlMaXN0KCkiCiAgICAgICAgICAgICAgQGJsdXI9ImhpZGVIaXN0b3J5TGlzdCgpIgogICAgICAgICAgICAgIEBrZXl1cC5lbnRlci5uYXRpdmU9ImhhbmRsZVNlYXJjaCgpIgogICAgICAgICAgICA+CiAgICAgICAgICAgIDwvZWwtaW5wdXQ+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9Imhpc3RvcnkiIHYtc2hvdz0ic2hvd0hpc3RvcnkiPgogICAgICAgICAgICAgIDxkaXYKICAgICAgICAgICAgICAgIGNsYXNzPSJoaXN0b3J5SXRlbSIKICAgICAgICAgICAgICAgIHYtZm9yPSIoaGlzdG9yeSwgaW5kZXgpIGluIGhpc3RvcnlMaXN0IgogICAgICAgICAgICAgICAgOmtleT0iaW5kZXgiCiAgICAgICAgICAgICAgICB2LWxvYWRpbmc9Imhpc3RvcnlMb2FkaW5nIgogICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDxkaXYgQGNsaWNrPSJrZXl3b3Jkc0NoYW5nZShoaXN0b3J5KSIgY2xhc3M9Imhpc3RvcnlUZXh0Ij4KICAgICAgICAgICAgICAgICAge3sgaGlzdG9yeS5rZXl3b3JkIH19CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgICAgICAgICAgQGNsaWNrPSJyZW1vdmVIaXN0b3J5KGhpc3RvcnksIDEpIgogICAgICAgICAgICAgICAgICBzdHlsZT0iY29sb3I6ICM5OTk7IGZvbnQtc2l6ZTogMTJweCIKICAgICAgICAgICAgICAgICAgPuWIoOmZpDwvZWwtYnV0dG9uCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaGlzdG9yeUl0ZW0iPgogICAgICAgICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJ0ZXh0IiBAY2xpY2s9Im1vcmVIaXN0b3J5KCkiPuabtOWkmjwvZWwtYnV0dG9uPgogICAgICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgICAgICB0eXBlPSJ0ZXh0IgogICAgICAgICAgICAgICAgICBAY2xpY2s9ImNsZWFySGlzdG9yeSgpIgogICAgICAgICAgICAgICAgICBzdHlsZT0iY29sb3I6ICM5OTk7IGZvbnQtc2l6ZTogMTJweCIKICAgICAgICAgICAgICAgICAgPua4heepujwvZWwtYnV0dG9uCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgICAgICAgICBzaXplPSJtaW5pIgogICAgICAgICAgICAgIHN0eWxlPSJtYXJnaW4tbGVmdDogMTBweDsgaGVpZ2h0OiAzNnB4IgogICAgICAgICAgICAgIEBjbGljaz0iaGFuZGxlU2VhcmNoKCkiCiAgICAgICAgICAgICAgPuaQnOe0ojwvZWwtYnV0dG9uCiAgICAgICAgICAgID4KICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPGRpdiBjbGFzcz0ia2V5d29yZC10aXAiPgogICAgICAgICAgICAq5pSv5oyB5oyJ54Wn5aSa5Liq5YWz6ZSu6K+N5qOA57Si77yM4oCc5LiO4oCd5L2/55So4oCcLOKAneWIhumalO+8jOKAnOaIluKAneS9v+eUqOKAnHzigJ3liIblibIKICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZWwtZm9ybT4KICAgICAgICA8ZGl2IGNsYXNzPSJUb3BCdG5Hcm91cCI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJUb3BCdG5Hcm91cF9sZWZ0Ij4KICAgICAgICAgICAgPGVsLWNoZWNrYm94IHYtbW9kZWw9ImNoZWNrZWQiIEBjaGFuZ2U9ImhhbmRsZUNoZWNrQWxsQ2hhbmdlIgogICAgICAgICAgICAgID7lhajpgIk8L2VsLWNoZWNrYm94CiAgICAgICAgICAgID4KICAgICAgICAgICAgPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICAgICAgPGkKICAgICAgICAgICAgICAgIHRpdGxlPSLmibnph4/liKDpmaTmlofnq6AiCiAgICAgICAgICAgICAgICBjbGFzcz0iaWNvbi1zaGFuY2h1IgogICAgICAgICAgICAgICAgQGNsaWNrPSJiYXRjaERlbGV0ZSIKICAgICAgICAgICAgICA+PC9pPgogICAgICAgICAgICA8L3A+CiAgICAgICAgICAgIDxwIGNsYXNzPSJ0b29sVGl0bGUiPgogICAgICAgICAgICAgIDxpCiAgICAgICAgICAgICAgICBjbGFzcz0iaWNvbi1zaHVheGluLWNvcHkiCiAgICAgICAgICAgICAgICB0aXRsZT0i5Yi35pawIgogICAgICAgICAgICAgICAgQGNsaWNrPSJoYW5kbGVSZWZyZXNoIgogICAgICAgICAgICAgID48L2k+CiAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgPCEtLSA8cCBjbGFzcz0idG9vbFRpdGxlIj4KICAgICAgICAgICAgPGkgY2xhc3M9Imljb24tLV90aWFuamlhZGFva3UiIHRpdGxlPSLmt7vliqDliLDmiqXlkYoiIEBjbGljaz0ib3BlblJlcG9ydCIgdi1oYXNQZXJtaT0iWydyZXN1bHQ6cmVwb3J0OmFkZCddIj48L2k+CiAgICAgICAgICA8L3A+IC0tPgogICAgICAgICAgICA8IS0tIDxwIGNsYXNzPSJ0b29sVGl0bGUiPgogICAgICAgICAgICA8aSB0aXRsZT0i5om56YeP55Sf5oiQ5b+r54WnIiBjbGFzcz0iaWNvbi1waW5nbXVrdWFpemhhbyIgc3R5bGU9ImNvbG9yOmdyZWVuIgogICAgICAgICAgICAgIHYtaGFzUGVybWk9IlsnYXJ0aWNsZTpjb2xsZWN0aW9uOnNuYXBzaG90J10iIEBjbGljaz0icmVzdWx0RXZlbnQoKSI+PC9pPgogICAgICAgICAgPC9wPiAtLT4KICAgICAgICAgICAgPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICAgICAgPGkKICAgICAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLWRvY3VtZW50IgogICAgICAgICAgICAgICAgc3R5bGU9ImZvbnQtc2l6ZTogMjRweCIKICAgICAgICAgICAgICAgIHRpdGxlPSLmt7vliqDliLDlt6XkvZzlj7DotKYiCiAgICAgICAgICAgICAgICBAY2xpY2s9Im9wZW5UYWl6aGFuZyIKICAgICAgICAgICAgICAgIHYtaGFzUGVybWk9IlsnYXJ0aWNsZTp3b3JrOmFkZCddIgogICAgICAgICAgICAgID48L2k+CiAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICAgICAgPGkKICAgICAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLWRvY3VtZW50LWFkZCIKICAgICAgICAgICAgICAgIHN0eWxlPSJmb250LXNpemU6IDI0cHgiCiAgICAgICAgICAgICAgICB0aXRsZT0i5Y+R5biD5Yiw5q+P5pel5pyA5paw54Ot54K5IgogICAgICAgICAgICAgICAgQGNsaWNrPSJwdWJsaXNoSG90IgogICAgICAgICAgICAgID48L2k+CiAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICAgICAgPGkKICAgICAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLWNoYXQtZG90LXJvdW5kIgogICAgICAgICAgICAgICAgc3R5bGU9ImZvbnQtc2l6ZTogMjRweCIKICAgICAgICAgICAgICAgIHRpdGxlPSJEZWVwc2Vla+a3seW6puino+ivuyIKICAgICAgICAgICAgICAgIEBjbGljaz0iYXJ0aWNsZUFpQ2hhdCIKICAgICAgICAgICAgICA+PC9pPgogICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJkZWVwc2Vlay10ZXh0IiBAY2xpY2s9ImFydGljbGVBaUNoYXQiCiAgICAgICAgICAgICAgICA+RGVlcHNlZWvmt7Hluqbop6Por7s8L3NwYW4KICAgICAgICAgICAgICA+CiAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICAgICAgPGkKICAgICAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLXBpZS1jaGFydCIKICAgICAgICAgICAgICAgIHN0eWxlPSJmb250LXNpemU6IDI0cHgiCiAgICAgICAgICAgICAgICB0aXRsZT0i55Sf5oiQRGVlcHNlZWvlm77ooajnnIvmnb8iCiAgICAgICAgICAgICAgICBAY2xpY2s9ImNoYXJ0QWlDaGF0IgogICAgICAgICAgICAgID48L2k+CiAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImRlZXBzZWVrLXRleHQiIEBjbGljaz0iY2hhcnRBaUNoYXQiCiAgICAgICAgICAgICAgICA+55Sf5oiQRGVlcHNlZWvlm77ooajnnIvmnb88L3NwYW4KICAgICAgICAgICAgICA+CiAgICAgICAgICAgIDwvcD4KICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPGRpdj4KICAgICAgICAgICAgPGVsLWNoZWNrYm94CiAgICAgICAgICAgICAgdi1tb2RlbD0ic2hvd1N1bW1hcnkiCiAgICAgICAgICAgICAgQGNoYW5nZT0iKGUpID0+IChzaG93U3VtbWFyeSA9IGUpIgogICAgICAgICAgICAgIHN0eWxlPSJtYXJnaW4tcmlnaHQ6IDEwcHgiCiAgICAgICAgICAgICAgPuaYr+WQpuaYvuekuuaRmOimgTwvZWwtY2hlY2tib3gKICAgICAgICAgICAgPgogICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC1zaXplOiAxNHB4Ij7mjpLluo/mlrnlvI86PC9zcGFuPiZuYnNwOwogICAgICAgICAgICA8ZWwtc2VsZWN0IHYtbW9kZWw9InF1ZXJ5UGFyYW1zLnNvcnRNb2RlIiBzaXplPSJtaW5pIj4KICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmjInlj5HluIPml7bpl7TlgJLluo/mjpLluo8iIDp2YWx1ZT0iJzAnIj48L2VsLW9wdGlvbj4KICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmjInlj5HluIPml7bpl7TmraPluo/mjpLluo8iIDp2YWx1ZT0iJzEnIj48L2VsLW9wdGlvbj4KICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmjInph4fpm4bml7bpl7TlgJLluo/mjpLluo8iIDp2YWx1ZT0iJzInIj48L2VsLW9wdGlvbj4KICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmjInph4fpm4bml7bpl7TmraPluo/mjpLluo8iIDp2YWx1ZT0iJzMnIj48L2VsLW9wdGlvbj4KICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmjInns7vnu5/mjqjojZDmjpLluo8iIDp2YWx1ZT0iJzQnIj48L2VsLW9wdGlvbj4KICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2Rpdj4KICAgICAgICA8IS0tIDxlbC1yb3cgOmd1dHRlcj0iMTAiIGNsYXNzPSJtYjgiPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMS41Ij4KICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgIHR5cGU9InByaW1hcnkiCiAgICAgICAgICAgICAgcGxhaW4KICAgICAgICAgICAgICBpY29uPSJlbC1pY29uLWVkaXQiCiAgICAgICAgICAgICAgc2l6ZT0ibWluaSIKICAgICAgICAgICAgICA6ZGlzYWJsZWQ9Im11bHRpcGxlIgogICAgICAgICAgICAgIEBjbGljaz0iaGFuZGxlVXBkYXRlMCIKICAgICAgICAgICAgICB2LWhhc1Blcm1pPSJbJ3dlY2hhdDp1c2VyOnJlbW92ZSddIgogICAgICAgICAgICAgID7orr7nva7kuLrnp5HmioDml6DlhbM8L2VsLWJ1dHRvbgogICAgICAgICAgICA+CiAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjEuNSI+CiAgICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgICB0eXBlPSJwcmltYXJ5IgogICAgICAgICAgICAgIHBsYWluCiAgICAgICAgICAgICAgaWNvbj0iZWwtaWNvbi1lZGl0IgogICAgICAgICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgICAgICAgOmRpc2FibGVkPSJtdWx0aXBsZSIKICAgICAgICAgICAgICBAY2xpY2s9ImhhbmRsZVVwZGF0ZTEiCiAgICAgICAgICAgICAgdi1oYXNQZXJtaT0iWyd3ZWNoYXQ6dXNlcjpyZW1vdmUnXSIKICAgICAgICAgICAgICA+6K6+572u5Li656eR5oqA5pyJ5YWzPC9lbC1idXR0b24KICAgICAgICAgICAgPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIxLjUiPgogICAgICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgICAgICAgICBwbGFpbgogICAgICAgICAgICAgIGljb249ImVsLWljb24tZWRpdCIKICAgICAgICAgICAgICBzaXplPSJtaW5pIgogICAgICAgICAgICAgIDpkaXNhYmxlZD0ibXVsdGlwbGUiCiAgICAgICAgICAgICAgQGNsaWNrPSJoYW5kbGVVcGRhdGUyIgogICAgICAgICAgICAgIHYtaGFzUGVybWk9Ilsnd2VjaGF0OnVzZXI6cmVtb3ZlJ10iCiAgICAgICAgICAgICAgPuiuvue9ruS4uuWFtuS7ljwvZWwtYnV0dG9uCiAgICAgICAgICAgID4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgIDwvZWwtcm93PiAtLT4KICAgICAgICA8ZWwtdGFibGUKICAgICAgICAgIDpkYXRhPSJBcnRpY2xlTGlzdCIKICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJTsgdXNlci1zZWxlY3Q6IHRleHQiCiAgICAgICAgICA6c2hvdy1oZWFkZXI9ImZhbHNlIgogICAgICAgICAgcmVmPSJ0YWJsZSIKICAgICAgICAgIDpoZWlnaHQ9IgogICAgICAgICAgICAnY2FsYygxMDB2aCAtICcgKwogICAgICAgICAgICAoMzc0ICsgKHF1ZXJ5UGFyYW1zLnRhZ3MgIT0gJycgPyA1MSA6IDApKSArCiAgICAgICAgICAgICdweCknCiAgICAgICAgICAiCiAgICAgICAgICBAc2VsZWN0aW9uLWNoYW5nZT0iaGFuZGxlVGFibGVTZWxlY3Rpb25DaGFuZ2UiCiAgICAgICAgPgogICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiB0eXBlPSJzZWxlY3Rpb24iIHdpZHRoPSIzNSIgYWxpZ249ImNlbnRlciIgLz4KICAgICAgICAgIDxlbC10YWJsZS1jb2x1bW4gd2lkdGg9IjUwIiBhbGlnbj0iY2VudGVyIj4KICAgICAgICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iY29sb3I6ICMwODA4MDg7IGZvbnQtc2l6ZTogMTVweCI+CiAgICAgICAgICAgICAgICB7ewogICAgICAgICAgICAgICAgICAocXVlcnlQYXJhbXMucGFnZU51bSAtIDEpICogcXVlcnlQYXJhbXMucGFnZVNpemUgKwogICAgICAgICAgICAgICAgICBzY29wZS4kaW5kZXggKwogICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICB9fQogICAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbgogICAgICAgICAgICBsYWJlbD0i57yW5Y+3IgogICAgICAgICAgICBhbGlnbj0iY2VudGVyIgogICAgICAgICAgICBrZXk9ImlkIgogICAgICAgICAgICBwcm9wPSJpZCIKICAgICAgICAgICAgd2lkdGg9IjEwMCIKICAgICAgICAgICAgdi1pZj0iZmFsc2UiCiAgICAgICAgICAvPgogICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJ0aXRsZSIgbGFiZWw9IuaXpeacnyIgbWluLXdpZHRoPSIxODAiPgogICAgICAgICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJhcnRpY2xlX3RpdGxlIiBAY2xpY2s9Im9wZW5OZXdWaWV3KHNjb3BlLnJvdykiPgogICAgICAgICAgICAgICAgPHNwYW4KICAgICAgICAgICAgICAgICAgc3R5bGU9ImNvbG9yOiAjMDgwODA4IgogICAgICAgICAgICAgICAgICB2LWh0bWw9InNjb3BlLnJvdy50aXRsZSB8fCBzY29wZS5yb3cuY25UaXRsZSIKICAgICAgICAgICAgICAgID48L3NwYW4+CiAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iY29sb3I6ICM1NTg5ZjUiPgogICAgICAgICAgICAgICAgICB7eyAiKCIgKyBzY29wZS5yb3cucHVibGlzaFRpbWUgKyAiKSIgfX0KICAgICAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgICAgIDxzcGFuPiZuYnNwO3x8Jm5ic3A7PC9zcGFuPgogICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImNvbG9yOiAjNTU4OWY1Ij4KICAgICAgICAgICAgICAgICAge3sgc2NvcGUucm93LnNvdXJjZU5hbWUgfX0KICAgICAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgICAgIDxzcGFuPiZuYnNwO3x8Jm5ic3A7PC9zcGFuPgogICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImNvbG9yOiAjNTU4OWY1Ij4KICAgICAgICAgICAgICAgICAg5aSn5qih5Z6L562b6YCJOnt7IGdldFRlY2hub2xvZ3lMYWJlbChzY29wZS5yb3cuaXNUZWNobm9sb2d5KSB9fQogICAgICAgICAgICAgICAgPC9zcGFuPgogICAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgICA8ZGl2CiAgICAgICAgICAgICAgICBjbGFzcz0iQXJ0aWNsTWFpbiIKICAgICAgICAgICAgICAgIHN0eWxlPSIKICAgICAgICAgICAgICAgICAgZGlzcGxheTogLXdlYmtpdC1ib3g7CiAgICAgICAgICAgICAgICAgIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7CiAgICAgICAgICAgICAgICAgIC13ZWJraXQtbGluZS1jbGFtcDogMjsKICAgICAgICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgICAgICAgICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7CiAgICAgICAgICAgICAgICAgIHdvcmQtYnJlYWs6IGJyZWFrLWFsbDsKICAgICAgICAgICAgICAgICIKICAgICAgICAgICAgICAgIHYtaWY9IgogICAgICAgICAgICAgICAgICBzaG93U3VtbWFyeSAmJgogICAgICAgICAgICAgICAgICBoYXNBY3R1YWxDb250ZW50KHNjb3BlLnJvdy5jblN1bW1hcnkgfHwgc2NvcGUucm93LnN1bW1hcnkpCiAgICAgICAgICAgICAgICAiCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImNvbG9yOiAjOWI5YjliIj7mkZjopoHvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICA8c3BhbgogICAgICAgICAgICAgICAgICBzdHlsZT0iY29sb3I6ICM0YjRiNGIiCiAgICAgICAgICAgICAgICAgIHYtaHRtbD0iY2hhbmdlQ29sb3IoZ2V0U2FmZVN1bW1hcnkoc2NvcGUucm93KSkiCiAgICAgICAgICAgICAgICAgIEBjbGljaz0ib3Blbk5ld1ZpZXcoc2NvcGUucm93KSIKICAgICAgICAgICAgICAgID48L3NwYW4+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICAgIDwhLS0gPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJwdWJsaXNoVGltZSIgbGFiZWw9IuWPkeW4g+aXtumXtCIgd2lkdGg9IjE4MCI+CiAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4gLS0+CiAgICAgICAgICA8IS0tIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuaTjeS9nCIgZml4ZWQ9InJpZ2h0Ij4KICAgICAgICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICAgICAgICA8aSBjbGFzcz0iaWNvbi0tX3RpYW5qaWFkYW9rdSIgdGl0bGU9Iua3u+WKoOWIsOaKpeWRiiIgQGNsaWNrPSJzZXBhcmF0ZUFkZChpdGVtKSIKICAgICAgICAgICAgICAgIHYtaGFzUGVybWk9IlsncmVzdWx0OnJlcG9ydDphZGQnXSI+PC9pPgogICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+IC0tPgogICAgICAgIDwvZWwtdGFibGU+CiAgICAgICAgPHBhZ2luYXRpb24KICAgICAgICAgIHYtc2hvdz0idG90YWwgPiAwIgogICAgICAgICAgOnRvdGFsPSJ0b3RhbCIKICAgICAgICAgIDpwYWdlLnN5bmM9InF1ZXJ5UGFyYW1zLnBhZ2VOdW0iCiAgICAgICAgICA6bGltaXQuc3luYz0icXVlcnlQYXJhbXMucGFnZVNpemUiCiAgICAgICAgICBAcGFnaW5hdGlvbj0iaGFuZGxlUGFnaW5hdGlvbiIKICAgICAgICAgIDphdXRvU2Nyb2xsPSJ0cnVlIgogICAgICAgIC8+CiAgICAgIDwvZGl2PgogICAgPC9wYW5lPgogIDwvc3BsaXRwYW5lcz4KICA8ZWwtZGlhbG9nCiAgICB0aXRsZT0i5re75Yqg5Yiw5oql5ZGKIgogICAgOnZpc2libGUuc3luYz0iZGlhbG9nVmlzaWJsZSIKICAgIHdpZHRoPSI1MDBweCIKICAgIDpiZWZvcmUtY2xvc2U9ImNsb3NlUmVwb3J0IgogICAgOmNsb3NlLW9uLWNsaWNrLW1vZGFsPSJmYWxzZSIKICA+CiAgICA8ZWwtcm93IHN0eWxlPSJsaW5lLWhlaWdodDogNTBweCI+CiAgICAgIDxlbC1jb2wgOnNwYW49IjE4Ij4KICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICB2LW1vZGVsPSJyZXBvcnRJZCIKICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fpgInmi6nmiqXlkYoiCiAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJSIKICAgICAgICA+CiAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgIHYtZm9yPSIoaXRlbSwga2V5KSBpbiByZXBvcnRPcHRpb25zIgogICAgICAgICAgICA6a2V5PSJrZXkiCiAgICAgICAgICAgIDpsYWJlbD0iaXRlbS50aXRsZSIKICAgICAgICAgICAgOnZhbHVlPSJpdGVtLmlkIgogICAgICAgICAgPjwvZWwtb3B0aW9uPgogICAgICAgIDwvZWwtc2VsZWN0PgogICAgICA8L2VsLWNvbD4KICAgIDwvZWwtcm93PgogICAgPHNwYW4gc2xvdD0iZm9vdGVyIiBjbGFzcz0iZGlhbG9nLWZvb3RlciI+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJjbG9zZVJlcG9ydCI+5Y+WIOa2iDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIEBjbGljaz0icmVwb3J0U3VibWl0Ij7noa4g5a6aPC9lbC1idXR0b24+CiAgICA8L3NwYW4+CiAgPC9lbC1kaWFsb2c+CgogIDxlbC1kaWFsb2cKICAgIHRpdGxlPSLlhbPplK7or43ljoblj7IiCiAgICA6dmlzaWJsZS5zeW5jPSJkaWFsb2dWaXNpYmxlMSIKICAgIHdpZHRoPSI1NzBweCIKICAgIDpjbG9zZS1vbi1jbGljay1tb2RhbD0iZmFsc2UiCiAgPgogICAgPGRpdiBjbGFzcz0iaGlzdG9yeSIgdi1sb2FkaW5nPSJoaXN0b3J5TG9hZGluZyI+CiAgICAgIDxkaXYKICAgICAgICBjbGFzcz0iaGlzdG9yeUl0ZW0iCiAgICAgICAgdi1mb3I9IihoaXN0b3J5LCBpbmRleCkgaW4gaGlzdG9yeUxpc3QxIgogICAgICAgIDprZXk9ImluZGV4IgogICAgICA+CiAgICAgICAgPGRpdiBAY2xpY2s9ImtleXdvcmRzQ2hhbmdlKGhpc3RvcnkpIiBjbGFzcz0iaGlzdG9yeVRleHQiPgogICAgICAgICAge3sgaGlzdG9yeS5rZXl3b3JkIH19CiAgICAgICAgPC9kaXY+CiAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJ0ZXh0IiBAY2xpY2s9InJlbW92ZUhpc3RvcnkoaGlzdG9yeSwgMikiCiAgICAgICAgICA+5Yig6ZmkPC9lbC1idXR0b24KICAgICAgICA+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgICA8cGFnaW5hdGlvbgogICAgICB2LXNob3c9InRvdGFsMSA+IDAiCiAgICAgIDp0b3RhbD0idG90YWwxIgogICAgICA6cGFnZS5zeW5jPSJxdWVyeVBhcmFtczEucGFnZU51bSIKICAgICAgOmxpbWl0LnN5bmM9InF1ZXJ5UGFyYW1zMS5wYWdlU2l6ZSIKICAgICAgOmJhY2tncm91bmQ9ImZhbHNlIgogICAgICBAcGFnaW5hdGlvbj0iaGFuZGxlSGlzdG9yeVBhZ2luYXRpb24iCiAgICAgIDpsYXlvdXQ9Iid0b3RhbCwgcHJldiwgcGFnZXIsIG5leHQnIgogICAgICA6YXV0b1Njcm9sbD0idHJ1ZSIKICAgIC8+CiAgPC9lbC1kaWFsb2c+CgogIDxlbC1kaWFsb2cKICAgIHRpdGxlPSJEZWVwc2Vla+a3seW6puino+ivuyIKICAgIDp2aXNpYmxlLnN5bmM9ImFpRGlhbG9nVmlzaWJsZSIKICAgIHdpZHRoPSIxMDAwcHgiCiAgICA6YmVmb3JlLWNsb3NlPSJjbG9zZUFpRGlhbG9nIgogICAgY3VzdG9tLWNsYXNzPSJhaS1kaWFsb2ciCiAgICA6Y2xvc2Utb24tY2xpY2stbW9kYWw9ImZhbHNlIgogID4KICAgIDxkaXYgY2xhc3M9ImFpLWNoYXQtY29udGFpbmVyIj4KICAgICAgPGRpdiBjbGFzcz0iY2hhdC1tZXNzYWdlcyIgcmVmPSJjaGF0TWVzc2FnZXMiPgogICAgICAgIDxkaXYKICAgICAgICAgIGNsYXNzPSJtZXNzYWdlIgogICAgICAgICAgdi1mb3I9IihtZXNzYWdlLCBpbmRleCkgaW4gY2hhdE1lc3NhZ2VzIgogICAgICAgICAgOmtleT0iaW5kZXgiCiAgICAgICAgICA6Y2xhc3M9IlsKICAgICAgICAgICAgJ21lc3NhZ2UnLAogICAgICAgICAgICBtZXNzYWdlLnJvbGUgPT09ICd1c2VyJyA/ICd1c2VyLW1lc3NhZ2UnIDogJ2FpLW1lc3NhZ2UnLAogICAgICAgICAgXSIKICAgICAgICA+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJhdmF0YXIiPgogICAgICAgICAgICA8aW1nCiAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAzMHB4OyBoZWlnaHQ6IDMwcHgiCiAgICAgICAgICAgICAgdi1pZj0ibWVzc2FnZS5yb2xlID09PSAndXNlciciCiAgICAgICAgICAgICAgOnNyYz0idXNlckF2YXRhciB8fCByZXF1aXJlKCdAL2Fzc2V0cy9pbWFnZXMvaG9tZS91c2VyLnBuZycpIgogICAgICAgICAgICAgIGFsdD0i55So5oi35aS05YOPIgogICAgICAgICAgICAvPgogICAgICAgICAgICA8aW1nIHYtZWxzZSBzcmM9IkAvYXNzZXRzL2ltYWdlcy9sb2dvMi5wbmciIGFsdD0iQUnlpLTlg48iIC8+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDxkaXYgY2xhc3M9Im1lc3NhZ2Utd3JhcHBlciI+CiAgICAgICAgICAgIDxkaXYKICAgICAgICAgICAgICB2LWlmPSIKICAgICAgICAgICAgICAgIG1lc3NhZ2Uucm9sZSA9PT0gJ2Fzc2lzdGFudCcgJiYgaXNUaGlua2luZyAmJiAhbWVzc2FnZS5jb250ZW50CiAgICAgICAgICAgICAgIgogICAgICAgICAgICAgIGNsYXNzPSJ0aGlua2luZy1hbmltYXRpb24iCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8c3Bhbj48L3NwYW4+CiAgICAgICAgICAgICAgPHNwYW4+PC9zcGFuPgogICAgICAgICAgICAgIDxzcGFuPjwvc3Bhbj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYKICAgICAgICAgICAgICB2LWVsc2UKICAgICAgICAgICAgICBjbGFzcz0ibWVzc2FnZS1jb250ZW50IgogICAgICAgICAgICAgIHYtaHRtbD0iCiAgICAgICAgICAgICAgICBtZXNzYWdlLnJvbGUgPT09ICdhc3Npc3RhbnQnCiAgICAgICAgICAgICAgICAgID8gbWVzc2FnZS5jb250ZW50CiAgICAgICAgICAgICAgICAgIDogbWVzc2FnZS5jb250ZW50CiAgICAgICAgICAgICAgIgogICAgICAgICAgICA+PC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICAgIDx0ZW1wbGF0ZSBzbG90PSJmb290ZXIiPgogICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iY2xvc2VBaURpYWxvZyI+5Y+WIOa2iDwvZWwtYnV0dG9uPgogICAgPC90ZW1wbGF0ZT4KICA8L2VsLWRpYWxvZz4KCiAgPGVsLWRpYWxvZwogICAgdGl0bGU9IkRlZXBzZWVr5Zu+6KGo55yL5p2/IgogICAgOnZpc2libGUuc3luYz0iY2hhcnREaWFsb2dWaXNpYmxlIgogICAgd2lkdGg9IjEyMDBweCIKICAgIDpiZWZvcmUtY2xvc2U9ImNsb3NlQ2hhcnREaWFsb2ciCiAgICBjdXN0b20tY2xhc3M9ImNoYXJ0LWRpYWxvZyIKICAgIGRlc3Ryb3ktb24tY2xvc2UKICAgIDpjbG9zZS1vbi1jbGljay1tb2RhbD0iZmFsc2UiCiAgPgogICAgPGRpdgogICAgICB2LWlmPSJjaGFydERpYWxvZ1Zpc2libGUiCiAgICAgIGNsYXNzPSJjaGFydC1jb250YWluZXIiCiAgICAgIHYtbG9hZGluZz0iY2hhcnRMb2FkaW5nIgogICAgICBlbGVtZW50LWxvYWRpbmctdGV4dD0i5q2j5Zyo55Sf5oiQ5Zu+6KGo55yL5p2/Li4uIgogICAgICBlbGVtZW50LWxvYWRpbmctc3Bpbm5lcj0iZWwtaWNvbi1sb2FkaW5nIgogICAgICBlbGVtZW50LWxvYWRpbmctYmFja2dyb3VuZD0icmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpIgogICAgPgogICAgICA8ZGl2IGNsYXNzPSJjaGFydC1jb250ZW50IiByZWY9ImNoYXJ0Q29udGVudCI+PC9kaXY+CiAgICA8L2Rpdj4KICAgIDxzcGFuIHNsb3Q9ImZvb3RlciIgY2xhc3M9ImRpYWxvZy1mb290ZXIiPgogICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iY2xvc2VDaGFydERpYWxvZyI+5YWz6ZetPC9lbC1idXR0b24+CiAgICA8L3NwYW4+CiAgPC9lbC1kaWFsb2c+CjwvZGl2Pgo="}, null]}