import request from '@/utils/request'

// 查询文章工作列表
export function listWork(query) {
  return request({
    url: '/article/work/list',
    method: 'get',
    params: query
  })
}

// 查询文章工作详细
export function getWork(id) {
  return request({
    url: '/article/work/' + id,
    method: 'get'
  })
}

// 新增文章工作
export function addWork(data) {
  return request({
    url: '/article/work',
    method: 'post',
    data: data
  })
}

// 查询简报列表（树形结构）
export function getBriefList() {
  return request({
    url: '/article/work/brief/list',
    method: 'get'
  })
}

// 查询简报中的文章列表
export function getBriefArticles(briefId) {
  return request({
    url: `/article/work/brief/${briefId}/articles`,
    method: 'get'
  })
}

// 批量添加文章到简报
export function addArticlesToBrief(data) {
  return request({
    url: '/article/work/brief/articles/batch',
    method: 'post',
    data: data
  })
}

// 批量删除简报中的文章
export function removeArticlesFromBrief(data) {
  return request({
    url: '/article/work/brief/articles/batch',
    method: 'delete',
    data: data
  })
}
