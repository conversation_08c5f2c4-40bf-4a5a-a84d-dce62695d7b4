<template>
  <div class="markmap-page">
    <div class="markmap-header">
      <div>
        <h1 class="markmap-title" style="margin-bottom: 10px">
          {{ articleTitle }}
        </h1>
        <h1 class="markmap-title" style="color: #777">{{ title }}</h1>
      </div>
      <span class="close-btn" @click="closeWindow">✕</span>
    </div>
    <split-pane 
      split="vertical" 
      :default-percent="30" 
      :min-percent="20"
      :max-percent="50"
      @resize="handleResize" 
      class="markmap-content"
    >
      <!-- 左侧Markdown文本显示区域 -->
      <template slot="paneL">
        <div class="markdown-text-container">
          <div class="markdown-content-text" v-html="formattedMarkdown"></div>
        </div>
      </template>

      <!-- 右侧思维导图显示区域 -->
      <template slot="paneR">
        <div class="markmap-wrapper">
          <div v-if="loading" class="thinking-container">
            <div class="thinking-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <div class="thinking-text">正在解读中...</div>
          </div>
          <svg v-show="!loading" ref="markmap" class="markmap-svg"></svg>
        </div>
      </template>
    </split-pane>
  </div>
</template>

<script>
import { Transformer } from "markmap-lib";
import { Markmap } from "markmap-view";
import { formatMarkdown } from "@/utils/markdownUtils";
import splitPane from 'vue-splitpane';

export default {
  name: "MarkMapView",
  components: {
    splitPane
  },
  data() {
    return {
      title: "思维导图",
      articleTitle: "",
      content: "",
      loading: true,
      renderRetries: 0,
      maxRetries: 3,
      resizeTimer: null,
      markmapInstance: null,
      isResizing: false,
      lastResizeTime: 0,
    };
  },
  computed: {
    formattedMarkdown() {
      return formatMarkdown(this.content);
    },
  },
  mounted() {
    // 从URL参数获取数据
    this.parseUrlParams();

    // 初始化思维导图
    this.$nextTick(() => {
      this.renderMarkmap();
    });
  },
  methods: {
    parseUrlParams() {
      const params = new URLSearchParams(window.location.search);
      this.title = params.get("title") || "思维导图";
      this.articleTitle = params.get("articleTitle") || "";

      // 尝试从localStorage获取内容
      let content = "";
      const storageKey = params.get("storageKey");

      if (storageKey) {
        // 从localStorage读取内容
        content = localStorage.getItem(storageKey);

        // 读取后立即删除，避免占用存储空间
        localStorage.removeItem(storageKey);
      } else {
        // 兼容旧的URL参数方式
        content = params.get("content");
        if (content) {
          try {
            // 尝试进行URL解码，处理可能的双重编码问题
            content = decodeURIComponent(content);
          } catch (e) {
            console.error("解码内容参数失败:", e);
          }
        }
      }

      this.content = content || "";

      // 设置页面标题
      document.title = this.articleTitle;

      // 参数检查
      if (!this.content) {
        this.$message.warning("未找到要显示的内容");
        this.loading = false;
      }
    },
    closeWindow() {
      window.close();
    },
    async renderMarkmap() {
      if (!this.content) {
        this.loading = false;
        return;
      }

      try {
        // 确保DOM完全渲染后再访问SVG元素
        this.loading = true;

        // 使用setTimeout确保在DOM更新循环结束后执行
        setTimeout(async () => {
          try {
            // 再次使用nextTick确保加载状态变化已应用到DOM
            await this.$nextTick();
            const svg = this.$refs.markmap;

            if (!svg) {
              console.error("SVG element still not found, retrying...");
              // 再次尝试渲染，增加延迟时间
              this.renderRetries++;
              if (this.renderRetries < this.maxRetries) {
                setTimeout(() => this.renderMarkmap(), 300);
              } else {
                console.error("Max retries reached, giving up SVG rendering");
                this.$message.error("思维导图加载失败，请刷新页面重试");
                this.loading = false;
              }
              return;
            }

            // 清空之前的内容
            svg.innerHTML = "";

            // 处理内容，移除 markdown 标记
            let processedContent = this.content
              .replace(/^```markdown\s*/i, "") // 移除开头的 ```markdown
              .replace(/\s*```\s*$/, ""); // 移除结尾的 ```

            const transformer = new Transformer();
            const { root } = transformer.transform(processedContent);

            // 获取容器尺寸
            const containerWidth = svg.clientWidth;
            const containerHeight = svg.clientHeight;

            // 创建思维导图选项
            const options = {
              autoFit: false, // 关闭自动适配，手动控制位置
              duration: 0, // 初始渲染不需要动画
              nodeMinHeight: 20,
              spacingVertical: 10,
              spacingHorizontal: 100,
              paddingX: 20,
              initialExpandLevel: -1, // 完全展开
              zoom: true,
              pan: true,
              color: (node) => {
                const colors = {
                  0: "#0052ff", // 亮蓝色
                  1: "#009600", // 亮绿色
                  2: "#ff6600", // 亮橙色
                  3: "#8000ff", // 亮紫色
                  4: "#ff0066", // 亮粉色
                };
                return colors[node.depth] || "#0052ff";
              },
              nodeFont: (node) => {
                const fonts = {
                  0: 'bold 20px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
                  1: '600 18px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
                  2: '500 16px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
                };
                return (
                  fonts[node.depth] ||
                  '400 14px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto'
                );
              },
              maxWidth: 300,
              linkShape: "diagonal",
              linkWidth: (node) => 2.5 - node.depth * 0.5,
              linkColor: (node) => {
                const colors = {
                  0: "rgba(0, 82, 255, 0.8)", // 亮蓝色
                  1: "rgba(0, 150, 0, 0.8)", // 亮绿色
                  2: "rgba(255, 102, 0, 0.8)", // 亮橙色
                };
                return colors[node.depth] || "rgba(128, 0, 255, 0.8)";
              },
            };

            // 创建思维导图
            const mm = Markmap.create(svg, options, root);
            this.markmapInstance = mm;

            // 设置思维导图位置 - 在创建后立即计算和设置中心位置
            // 使用requestAnimationFrame确保DOM已更新
            requestAnimationFrame(() => {
              // 先计算尺寸
              mm.fit(); // 触发布局计算

              // 获取思维导图尺寸
              const { minX, maxX, minY, maxY } = mm.state;
              const width = maxX - minX;
              const height = maxY - minY;

              // 适当的缩放比例
              const fitRatio = 0.9; // 留出一些边距
              const scale = Math.min(
                (containerWidth / width) * fitRatio,
                (containerHeight / height) * fitRatio
              );

              // 计算中心位置
              const centerX = (containerWidth - width * scale) / 2;
              const centerY = (containerHeight - height * scale) / 2;

              // 设置思维导图位置和缩放
              mm.setData(root, {
                initialScale: scale,
                initialPosition: [centerX, centerY],
              });

              // 延迟一点点再次适应窗口大小，确保位置正确
              setTimeout(() => {
                mm.setData(root, {
                  duration: 300, // 恢复动画时间，用于后续交互
                });

                // 添加轻微的缩放动画作为视觉反馈，表示加载完成
                setTimeout(() => {
                  // 添加检查，确保setScale方法存在
                  if (mm && typeof mm.setScale === 'function') {
                    const currentScale = mm.state.scale;
                    // 轻微放大再恢复
                    mm.setScale(currentScale * 1.05, { duration: 200 });
                    setTimeout(() => {
                      mm.setScale(currentScale, { duration: 200 });
                    }, 200);
                  } else {
                    // 如果setScale方法不存在，使用替代方案或忽略动画
                    console.log('mm.setScale方法不可用，跳过加载动画');
                  }
                }, 100);
              }, 50);
            });

            // 监听窗口大小变化
            const resizeHandler = () => {
              // 重新计算适配大小，但保持一定平滑过渡
              if (mm && mm.fit) {
                mm.fit();
              }
            };
            window.addEventListener("resize", resizeHandler);

            // 组件销毁时清理
            this.$once("hook:beforeDestroy", () => {
              window.removeEventListener("resize", resizeHandler);
            });

            // 设置加载状态为false
            this.loading = false;
          } catch (error) {
            console.error("Inner Markmap rendering error:", error);
            this.$message.error("思维导图渲染失败，请尝试刷新页面");
            this.loading = false;
          }
        }, 100);
      } catch (error) {
        console.error("Markmap rendering error:", error);
        this.$message.error("思维导图渲染失败");
        this.loading = false;
      }
    },
    // vue-splitpane的resize处理
    handleResize() {
      // 设置正在调整状态
      this.isResizing = true;
      
      // 使用节流来降低更新频率
      const now = Date.now();
      if (now - this.lastResizeTime < 30) { // 每30毫秒最多执行一次更新
        return;
      }
      
      this.lastResizeTime = now;
      
      // 如果有未执行的延迟timer，清除它
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }
      
      // 避免每次resize都重新创建markmapInstance
      if (this.markmapInstance && !this.loading) {
        // 轻量级更新 - 只调整视图而不重新渲染整个思维导图
        this.$nextTick(() => {
          try {
            // 执行轻量级适配
            this.markmapInstance.fit();
          } catch (error) {
            console.error("Resize rendering error:", error);
          }
        });
      }
      
      // 设置拖动结束后的处理
      this.resizeTimer = setTimeout(() => {
        this.isResizing = false;
        
        // 拖动完全结束后再执行一次完整的适配
        if (this.markmapInstance && !this.loading) {
          try {
            this.markmapInstance.fit();
          } catch (error) {
            console.error("Final resize rendering error:", error);
          }
        }
      }, 50);
    }
  },
  // 组件销毁时清理
  beforeDestroy() {
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    }
    this.markmapInstance = null;
  }
};
</script>

<style lang="scss" scoped>
.markmap-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;
}

.markmap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;

  .markmap-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin: 0;
  }

  .close-btn {
    cursor: pointer;
    font-size: 20px;
    color: #999;
    transition: color 0.3s;

    &:hover {
      color: #333;
    }
  }
}

.markmap-content {
  flex: 1;
  height: calc(100vh - 72px);
  overflow: hidden;
}

.markdown-text-container {
  height: 100%;
  padding: 10px;
  padding-right: 5px;
  background-color: #f9f9f9;
  overflow-y: auto;

  .markdown-content-text {
    height: 100%;
    overflow-y: auto;
    padding: 10px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar,
  .markdown-content-text::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track,
  .markdown-content-text::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb,
  .markdown-content-text::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover,
  .markdown-content-text::-webkit-scrollbar-thumb:hover {
    background-color: #ccc;
  }
}

.markmap-wrapper {
  height: 100%;
  position: relative;
  background-color: #fff;
  overflow: hidden;
}

.markmap-svg {
  width: 100%;
  height: 100%;
  display: block;
  transform-origin: center center;
}

.thinking-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 9999;

  .thinking-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;

    span {
      display: inline-block;
      width: 12px;
      height: 12px;
      margin: 0 6px;
      background-color: #10d8ff;
      border-radius: 50%;
      animation: thinking 1.4s infinite ease-in-out both;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }

  .thinking-text {
    color: #333;
    font-size: 18px;
  }
}

@keyframes thinking {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.3;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Markdown 内容样式 */
::v-deep .markdown-content-text {
  .md-h1,
  h1.md-h1 {
    font-size: 20px;
    font-weight: bold;
    margin: 16px 0 8px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    color: #333;
  }

  .md-h2,
  h2.md-h2 {
    font-size: 18px;
    font-weight: bold;
    margin: 14px 0 8px 0;
    color: #333;
  }

  .md-h3,
  h3.md-h3 {
    font-size: 16px;
    font-weight: bold;
    margin: 12px 0 8px 0;
    color: #333;
  }

  .md-paragraph {
    margin: 8px 0;
    line-height: 1.6;
    color: #333;
  }

  .md-paragraph-space {
    height: 8px;
  }

  .md-blockquote {
    border-left: 4px solid #ddd;
    padding: 5px 10px;
    margin: 10px 0;
    background-color: #f6f6f6;
    color: #555;
  }

  .md-code-block {
    background-color: #f6f8fa;
    border-radius: 3px;
    padding: 10px;
    margin: 10px 0;
    font-family: monospace;
    overflow-x: auto;
    font-size: 13px;
  }

  .md-code-inline {
    background-color: #f6f8fa;
    border-radius: 3px;
    padding: 2px 4px;
    font-family: monospace;
    font-size: 13px;
  }

  .md-ul,
  .md-ol {
    margin: 10px 0;
    padding-left: 25px;
  }

  .md-ul {
    list-style-type: disc;
  }

  .md-ol {
    list-style-type: decimal;
  }

  strong {
    font-weight: bold;
  }

  em {
    font-style: italic;
  }
}

// 节点样式
::v-deep .markmap-node {
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

::v-deep .markmap-node-circle {
  fill: transparent; // 修改节点背景为透明
  stroke-width: 2px;
}

::v-deep .markmap-node-text {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial;

  tspan {
    fill: #333 !important; // 修改文字颜色为深色
    font-size: 14px;
    font-weight: 500;
  }
}

::v-deep .markmap-link {
  fill: none;
  stroke-width: 2.5px; // 加粗连线
}

// 根节点样式
::v-deep .markmap-node[data-depth="0"] {
  .markmap-node-circle {
    stroke: #0052ff; // 亮蓝色
    stroke-width: 3px;
  }

  .markmap-node-text tspan {
    font-size: 20px !important;
    font-weight: bold !important;
    fill: #333 !important;
  }
}

// 二级节点样式
::v-deep .markmap-node[data-depth="1"] {
  .markmap-node-circle {
    stroke: #009600; // 亮绿色
    stroke-width: 2.5px;
  }

  .markmap-node-text tspan {
    font-size: 18px !important;
    font-weight: 600 !important;
    fill: #333 !important;
  }
}

/* 自定义 vue-splitpane 样式 */
::v-deep .splitter-pane-resizer.vertical {
    width: 16px !important;

    &:after {
      content: "";
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 2px;
      height: 30px;
      background-color: #fff;
      border-radius: 1px;
    }
}

/* 增强滚动性能 */
* {
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: subpixel-antialiased;
}
</style>
