<template>
  <div v-loading="loading" element-loading-text="数据加载中" element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)">
    <splitpanes class="default-theme">
      <pane class="leftLink" ref="leftLink" min-size="10" max-size="50" size="20">
        <div class="treeMain" style="width: 100%;margin:0;">
          <div style="display:flex;justify-content:space-between;align-items:center;gap:10px">
            <el-input placeholder="输入关键字进行过滤" v-model="filterText" clearable style="margin-bottom: 20px;"
              class="input_Fixed">
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
          </div>
          <div class="treeBox">
            <el-tree :data="treeData" ref="tree" show-checkbox node-key="id" :default-expanded-keys="[1000]"
              :expand-on-click-node="false" @check-change="checkChange" :filter-node-method="filterNode">
              <template slot-scope="scoped">
                <div v-if="scoped.data.label != '全部'"><span>{{ scoped.data.label }}</span><b>{{ `${scoped.data.count ?
                  `(${scoped.data.count})` : ''}`
                }}</b></div>
                <div v-else>
                  {{ scoped.data.label }}
                  <div style="position: absolute;z-index: 99;right: 10px;top: -5px;">
                    <el-tooltip class="item" effect="dark" content="重置" placement="top">
                      <i class="el-input__icon el-icon-refresh" @click="treeClear"></i>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="按数量倒向排序" placement="top">
                      <i class="el-input__icon el-icon-caret-bottom" @click="treeSlot(true)"></i>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="按数量正向排序" placement="top">
                      <i class="el-input__icon el-icon-caret-top" @click="treeSlot(false)"></i>
                    </el-tooltip>
                  </div>
                </div>
              </template>
            </el-tree>
          </div>
        </div>
      </pane>
      <pane min-size="30" max-size="90" size="80">
        <div class="rightMain" style="margin-left: 0;overflow: auto;">
          <el-form :model="queryParams" ref="Form" label-width="90px">
            <el-form-item label="时间范围:" prop="dateType">
              <el-radio-group v-model="queryParams.dateType" size="small">
                <el-radio-button :label="''">24小时</el-radio-button>
                <el-radio-button :label="1">今天</el-radio-button>
                <el-radio-button :label="2">昨天</el-radio-button>
                <el-radio-button :label="4">近7天</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="检索词库:" prop="tags">
              <el-radio-group v-model="queryParams.tags" size="small">
                <el-radio :label="''">全部</el-radio>
                <el-radio v-for="item in tagsList1" :key="item.id" :label="item.id">{{ item.name }}</el-radio>
                <!-- <el-radio :label="2">产业经济</el-radio>
                <el-radio :label="3">科技发展</el-radio>
                <el-radio :label="4">领域专题</el-radio> -->
              </el-radio-group>
            </el-form-item>
            <el-form-item style="width: 100%;overflow: auto;" label="" prop="tagsSubset" v-if="queryParams.tags != ''">
              <el-checkbox style="float: left;margin-right: 30px;" :indeterminate="isIndeterminate" v-model="checkAll"
                @change="handleCheckAllTagsSubset">全选</el-checkbox>
              <el-checkbox-group v-model="queryParams.tagsSubset" @change="handleCheckedChange">
                <el-checkbox v-for="item in tagsList" :key="item.name" :label="item.name"></el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
          <div class="TopBtnGroup">
            <el-checkbox v-model="checked" @change="handleCheckAllChange">全选</el-checkbox>
            <p class="toolTitle">
              <i class="icon-shuaxin-copy" title="刷新" @click="funEsSeach"></i>
            </p>
            <!-- <p class="toolTitle">
              <i class="icon--_tianjiadaoku" title="添加到报告" @click="openReport" v-hasPermi="['result:report:add']"></i>
            </p> -->
            <!-- <p class="toolTitle">
              <i title="批量生成快照" class="icon-pingmukuaizhao" style="color:green"
                v-hasPermi="['article:collection:snapshot']" @click="resultEvent()"></i>
            </p> -->
            <p class="toolTitle">
              <i class="el-icon-document" style="font-size:24px" title="添加到工作台账" @click="openTaizhang"
                v-hasPermi="['article:work:add']"></i>
            </p>
          </div>
          <el-table :data="ArticleList" style="width: 100%;user-select: text;" :show-header="false" ref="table"
            :height="`calc(100vh - ${270 + (queryParams.tags != '' ? 60 : 0)}px) `" @cell-click="openArticle"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="35" align="center" />
            <el-table-column prop="title" label="日期" min-width="180">
              <template slot-scope="scope">
                <span v-html="changeColor(scope.row.cnTitle || scope.row.title)"></span>
                {{ `(${scope.row.publishTime})` }}
              </template>
            </el-table-column>
            <!-- <el-table-column prop="publishTime" label="发布时间" width="180">
            </el-table-column> -->
            <!-- <el-table-column label="操作" fixed="right">
              <template slot-scope="scope">
                <i class="icon--_tianjiadaoku" title="添加到报告" @click="separateAdd(item)"
                  v-hasPermi="['result:report:add']"></i>
              </template>
            </el-table-column> -->
          </el-table>
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="funEsSeach" />
        </div>
      </pane>
    </splitpanes>
    <el-dialog title="添加到报告" :visible.sync="dialogVisible" width="500px" :before-close="closeReport" :close-on-click-modal="false">
      <el-row style="line-height: 50px">
        <el-col :span="18">
          <el-select v-model="reportId" placeholder="请选择报告" clearable style="width: 100%">
            <el-option v-for="(item, key) in reportOptions" :key="key" :label="item.title" :value="item.id"></el-option>
          </el-select>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeReport">取 消</el-button>
        <el-button type="primary" @click="reportSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/api/ScienceApi/index.js'
import { listWork, getWork, delWork, addWork, updateWork } from "@/api/article/work";
import { listKeywords } from "@/api/article/keywords";
import API from '@/api/ScienceApi/index.js'

import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'

export default {
  components: { Splitpanes, Pane },
  data() {
    return {
      loading: false,
      queryParams: {
        id: 100,
        pageNum: 1,
        pageSize: 50,
        dateType: '',
        tags: '',
        tagsSubset: []
      },
      total: 0,
      funEsSeach: null, // 防抖查询
      treeData: [ // 树状结构
        {
          id: 1000,
          label: '全部',
          children: []
        }
      ],
      treeDataTransfer: [], // 原始树形数据 
      filterText: '', // 左侧树搜索栏
      checkList: [], // 左侧勾选数据
      ArticleList: [], // 列表数据
      checked: false, // 全选
      ids: [], // 选中的数据
      dialogVisible: false, // 添加到报告弹框
      reportOptions: [], // 报告列表
      reportId: '', // 已选择的报告
      drawer: false, // 文章详情弹窗
      drawerInfo: {}, // 文章详情
      tagsList: [], // 检索词库二级列表
      tagsList1: [], // 检索词库一级列表
      checkAll: false, // 检索词库全选
      isIndeterminate: true, // 检索词库选了值
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    'queryParams.dateType': {
      handler(newVal, oldVal) {
        this.funEsSeach()
      },
      deep: true
    },
    'queryParams.tags': {
      handler(newVal, oldVal) {
        this.queryParams.tagsSubset = []
        this.checkAll = false;
        this.isIndeterminate = false;
        if (newVal != '') {
          listKeywords({ parentId: newVal, pageNum: 1, pageSize: 10 }).then(res => {
            this.tagsList = res.data
          })
        } else {
          this.funEsSeach()
        }
      },
      deep: true
    },
    dialogVisible(val) {
      if (val) {
        api.getNewBuilt({ sourceType: '1' }).then(data => {
          if (data.code == 200) {
            this.reportOptions = data.data
          } else {
            this.$message({ message: '报告列表获取失败了', type: 'error' })
            this.closeReport()
          }
        })
      }
    },
  },
  created() {
    this.getWechat()
    listKeywords({ parentId: 0, pageNum: 1, pageSize: 10 }).then(res => {
      this.tagsList1 = []
      res.data.map(res => {
        if (res.parentId == 0) {
          this.tagsList1.push(res)
        }
      })
    })
    this.funEsSeach = this.debounce(this.EsSearch, 500)
  },
  methods: {
    // 获取左侧树
    async getWechat() {
      this.loading = true
      await api.GetWechatList().then(res => {
        if (res.code == 200) {
          this.treeData = [{
            id: 1000, label: '全部',
            children: res.data.map((item, index) => { return { id: index + 1, label: item, count: 0 } })
          }]
          // 原始树形数据 
          this.treeDataTransfer = res.data.map((item, index) => { return { id: index + 1, label: item, count: 0 } })
        }
      })
      this.EsSearch()
    },
    // 查询文章列表
    async EsSearch(flag) {
      this.loading = true
      // console.log(this.checkList);
      let params = JSON.parse(JSON.stringify(this.queryParams))
      params.label = this.queryParams.tagsSubset.join(',')
      if (flag) {
        let data = this.checkList.map(item => {
          return item.label
        })
        params.weChatName = String(data)
         api.esRetrieval(params).then(res => {
          if (res.code == 200) {
            this.ArticleList = res.data.list
            this.total = res.data.total ? res.data.total : 0
            if (this.ArticleList.length == 0 && this.queryParams.pageSize * (this.queryParams.pageNum - 1) >= this.total && this.total != 0) {
              this.queryParams.pageNum = Math.trunc(this.total / this.queryParams.pageSize) + 1
              this.EsSearch('source')
            }
          }
          this.loading = false
        })
      } else {
         api.weChatList(params).then(res => {
          if (res.code == 200) {
            this.ArticleList = res.data.list
            this.total = res.data.total ? res.data.total : 0
            if (res.data.countBySourceName) {
              if (this.checkList.length) {
                let checkList = JSON.parse(JSON.stringify(this.checkList))
                let list = JSON.parse(JSON.stringify(this.treeData[0].children))
                list.map((row, index) => {
                  row.count = 0
                  this.$set(this.treeData[0].children, index, row)
                })
                Object.keys(res.data.countBySourceName).forEach(item => {
                  let spIndex = list.findIndex(row => { return row.label == item })
                  if (spIndex > -1) {
                    this.$set(this.treeData[0].children[spIndex], 'count', res.data.countBySourceName[item])
                  }
                })
                this.$refs.tree.setCheckedKeys([])
                let ids = checkList.map(item => {
                  return this.treeData[0].children.filter(row => { return row.label == item.label })[0].id
                })
                setTimeout(res => {
                  this.$refs.tree.setCheckedKeys(ids);
                }, 100)
              } else {
                let list = JSON.parse(JSON.stringify(this.treeDataTransfer))
                let list1 = []
                Object.keys(res.data.countBySourceName).forEach(item => {
                  let spIndex = list.findIndex(row => { return row.label == item })
                  if (spIndex >= 0) {
                    list.splice(spIndex, 1)
                  }
                  list1.push({
                    label: item,
                    count: res.data.countBySourceName[item]
                  })
                })
                list = list1.concat(list).sort((a, b) => b.count - a.count).map((item, index) => {
                  item.id = index + 1
                  return item
                })
                this.$set(this.treeData[0], 'children', list)
              }
            } else {
              let list = JSON.parse(JSON.stringify(this.treeDataTransfer))
              this.$set(this.treeData[0], 'children', list)
              let checkList = JSON.parse(JSON.stringify(this.checkList))
              this.$refs.tree.setCheckedKeys([])
              let ids = checkList.map(item => {
                return this.treeData[0].children.filter(row => { return row.label == item.label })[0].id
              })
              setTimeout(res => {
                this.$refs.tree.setCheckedKeys(ids);
              }, 100)
            }
            if (this.ArticleList.length == 0 && this.queryParams.pageSize * (this.queryParams.pageNum - 1) >= this.total && this.total != 0) {
              this.queryParams.pageNum = Math.trunc(this.total / this.queryParams.pageSize) + 1
              this.EsSearch()
            }
          }
          this.loading = false
        })
         api.wechatCountSourceName(params).then(res => {
          if (res.code == 200) {
            this.ArticleList = res.data.list
            this.total = res.data.total ? res.data.total : 0
            if (res.data.countBySourceName) {
              if (this.checkList.length) {
                let checkList = JSON.parse(JSON.stringify(this.checkList))
                let list = JSON.parse(JSON.stringify(this.treeData[0].children))
                list.map((row, index) => {
                  row.count = 0
                  this.$set(this.treeData[0].children, index, row)
                })
                Object.keys(res.data.countBySourceName).forEach(item => {
                  let spIndex = list.findIndex(row => { return row.label == item })
                  if (spIndex > -1) {
                    this.$set(this.treeData[0].children[spIndex], 'count', res.data.countBySourceName[item])
                  }
                })
                this.$refs.tree.setCheckedKeys([])
                let ids = checkList.map(item => {
                  return this.treeData[0].children.filter(row => { return row.label == item.label })[0].id
                })
                setTimeout(res => {
                  this.$refs.tree.setCheckedKeys(ids);
                }, 100)
              } else {
                let list = JSON.parse(JSON.stringify(this.treeDataTransfer))
                let list1 = []
                Object.keys(res.data.countBySourceName).forEach(item => {
                  let spIndex = list.findIndex(row => { return row.label == item })
                  if (spIndex >= 0) {
                    list.splice(spIndex, 1)
                  }
                  list1.push({
                    label: item,
                    count: res.data.countBySourceName[item]
                  })
                })
                list = list1.concat(list).sort((a, b) => b.count - a.count).map((item, index) => {
                  item.id = index + 1
                  return item
                })
                this.$set(this.treeData[0], 'children', list)
              }
            } else {
              let list = JSON.parse(JSON.stringify(this.treeDataTransfer))
              this.$set(this.treeData[0], 'children', list)
              let checkList = JSON.parse(JSON.stringify(this.checkList))
              this.$refs.tree.setCheckedKeys([])
              let ids = checkList.map(item => {
                return this.treeData[0].children.filter(row => { return row.label == item.label })[0].id
              })
              setTimeout(res => {
                this.$refs.tree.setCheckedKeys(ids);
              }, 100)
            }
            if (this.ArticleList.length == 0 && this.queryParams.pageSize * (this.queryParams.pageNum - 1) >= this.total && this.total != 0) {
              this.queryParams.pageNum = Math.trunc(this.total / this.queryParams.pageSize) + 1
              this.EsSearch()
            }
          }
        })
      }
    },
    // 左侧数组查询
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 勾选左侧树状栏
    checkChange(item, isCheck, sonCheck) {
      if (isCheck) {
        if (item.label !== '全部') {
          this.checkList.push(item)
        }
      } else {
        this.checkList.splice(this.checkList.findIndex(row => row.label == item.label), 1)
      }
      this.funEsSeach('source')
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      if (selection.length == this.ArticleList.length) {
        this.checked = true
      } else {
        this.checked = false
      }
    },
    // 全选 
    handleCheckAllChange(val) {
      if (val) {
        this.$refs['table'].toggleAllSelection()
      } else {
        this.$refs['table'].clearSelection()
      }
    },
    // 打开添加到报告
    openReport() {
      if (this.ids.length == 0) {
        return this.$message({ message: '请勾选要添加的数据', type: 'warning' })
      }
      this.dialogVisible = true
    },
    // 确定添加到报告 
    async reportSubmit() {
      if (!this.reportId) return this.$message({ message: '请选择要添加到的报告', type: 'warning' })
      let keyWordList = this.ids.map(item => {
        return { reportId: this.reportId, listId: item }
      })
      let res = await api.AddReport(keyWordList)
      if (res.code == 200) {
        this.$message({ message: '已添加到报告', type: 'success' })
        this.funEsSeach()
      } else {
        this.$message({ message: '添加到报告失败,请联系管理员', type: 'error' })
      }
      this.$refs['table'].clearSelection()
      this.checked = false
      this.closeReport()
    },
    // 关闭添加到报告
    closeReport() {
      this.reportId = ''
      this.dialogVisible = false
    },
    // 添加到台账
    openTaizhang() {
      if (this.ids.length == 0) {
        return this.$message({ message: '请勾选要添加的数据', type: 'warning' })
      }
      this.$confirm('是否确认添加已勾选的数据项到台账统计?').then(() => {
        addWork(this.ids).then(res => {
          this.$message({ type: 'success', message: '添加成功!' });
          this.funEsSeach()
        })
      }).catch(() => { });
    },
    // 文章详情
    openArticle(item, row) {
      window.open(`/expressDetails?id=${item.id}&docId=${item.docId}`, '_blank')
    },
    // 获取文章详情
    async openDrawer() {
      await api.AreaInfo(this.drawerInfo.id).then(res => {
        if (res.code == 200) {
          this.drawerInfo = res.data
          /* 将字符串中的\n替换为<br> */
          if (this.drawerInfo.cnContent || this.drawerInfo.content) {
            this.drawerInfo.cnContent = (this.drawerInfo.cnContent || this.drawerInfo.content).replace(/\\n/g, (a, b, c) => {
              return '<br>'
            })
            this.drawerInfo.cnContent = (this.drawerInfo.cnContent || this.drawerInfo.content).replace(/\${[^}]+}/g, '<br>')
            this.drawerInfo.cnContent = (this.drawerInfo.cnContent || this.drawerInfo.content).replace('|xa0', '')
          }
        }
      })
    },
    // 防抖
    debounce(fn, delay) {
      let timer;
      return function () {
        let context = this;
        let args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(context, args);
        }, delay);
      }
    },
    // 全选点击
    handleCheckAllTagsSubset(val) {
      this.queryParams.tagsSubset = val ? this.tagsList.map(item => item.name) : [];
      this.isIndeterminate = false;
      this.funEsSeach()
    },
    // 多选起点击
    handleCheckedChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.tagsList.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.tagsList.length;
      this.funEsSeach()
    },
    // 关键字替换
    changeColor(str) {
      const regex = /<img\b[^>]*>/gi;
      let Str = str && str.replace(regex, '')
      if (Str && ((this.queryParams.tagsSubset && this.queryParams.tagsSubset.length) || this.queryParams.keywords)) {
        let keywords = this.queryParams.tagsSubset
        keywords.map((keyitem, keyindex) => {
          if (keyitem && keyitem.length > 0) {
            // 匹配关键字正则
            let replaceReg = new RegExp(keyitem, "g");
            // 高亮替换v-html值
            let replaceString = '<span class="highlight"' + ' style="color: red;">' + keyitem + "</span>";
            Str = Str.replace(replaceReg, replaceString);
          }
        });
      }
      return Str
    },
    /* 快照生成 */
    resultEvent() {
      if (this.ids.length == 0) {
        return this.$message.warning('请先选择文章')
      }
      let ids = this.ids
      let zhuangtai = '生成'
      let url = ''
      if (ids.length == 1) {
        let row = this.ArticleList.filter(item => item.id == ids[0])
        if (row && row.snapshotUrl) zhuangtai = '查看'; url = row.snapshotUrl
      }
      if (zhuangtai == '生成') {
        this.$msgbox({
          title: '提示',
          message: '快照正在生成中，请稍后查看',
          showCancelButton: true,
          confirmButtonText: '关闭',
          cancelButtonText: '取消',
          showCancelButton: false,
          beforeClose: (action, instance, done) => {
            done()
          }
        })
        API.downLoadExportKe(ids).then(response => {
          if (response.code == 200) {
          } else {
            this.$message({ message: '申请失败，请联系管理员，确认采集器是否正常', type: 'error' })
          }
        }).catch(err => { })
      } else {
        url = url.replace(new RegExp('/home/<USER>/dpx/server-api/', "g"), '/')
        url = url.replace(new RegExp('/home/<USER>/dpx/', "g"), '/')
        window.open(window.location.origin + url, '_blank');
      }
    },
    // 左侧列表重置
    treeClear() {
      this.$refs.tree.setCheckedKeys([]);
    },
    // 左侧树排序
    treeSlot(type) {
      let checkList = JSON.parse(JSON.stringify(this.checkList))
      let list = JSON.parse(JSON.stringify(this.treeData[0].children))
      let list1 = list.sort((a, b) => {
        if (type) {
          return b.count - a.count
        } else {
          return a.count - b.count
        }
      }).map((item, index) => {
        item.id = index + 1
        return item
      })
      this.$set(this.treeData[0], 'children', list1)
      let ids = checkList.map(item => {
        return this.treeData[0].children.filter(row => { return row.label == item.label })[0].id
      })
      setTimeout(res => {
        this.$refs.tree.setCheckedKeys(ids);
      }, 100)
    }
  }
}
</script>

<style lang="scss" scoped>
.input_Fixed {
  // position: fixed;
  // top: 90px;
  // z-index: 2;
  // width: 12%;
}

.treeBox {
  // margin-top:70px;
  width: 100%;
  height: calc(100vh - 114px);
  overflow-y: scroll;
}

.rightMain {
  height: calc(100vh - 60px);
  overflow: hidden;

  .TopBtnGroup {
    display: flex;
    align-items: center;
    padding: 0 0 0 20px;
    background: #dbdbd8;
    margin-bottom: 20px;
    height: 40px;
    box-shadow: -1px 5px 6px 2px #dbdbd880;

    .toolTitle {
      margin: 0 10px;

      &:nth-of-type(1) {
        margin-left: 30px;
      }
    }
  }
}

::v-deep .drawer_Title {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

::v-deep .drawer_Style {
  z-index: 2;
  margin: 0 15px 0 15px;
  width: 661px;
  height: 80vh;

  .title {
    font-size: 16px;
    font-weight: 500px;
    text-align: center;
  }

  .source {
    color: #0798f8;
    text-align: center;
    font-size: 14px;
  }

  .time {
    font-size: 14px;
    text-align: center;
    margin-left: 10px;
    color: #9b9b9b;
  }
}

::v-deep .el-icon-document:before {
  color: #5589F5;
}

::v-deep .el-table td.el-table__cell div {
  padding-left: 10px;
}

::v-deep .el-table-column--selection .cell {
  padding-right: 0px;
  padding-left: 14px !important;
  margin-left: 5px;
}

::v-deep .el-form-item {
  margin-bottom: 10px;
}

.treeMain {
  position: relative;
}
</style>