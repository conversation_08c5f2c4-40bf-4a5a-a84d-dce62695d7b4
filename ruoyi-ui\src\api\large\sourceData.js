import request from '@/utils/request'

// 查询信息源归类柱状图内容列表
export function listSourceData(query) {
  return request({
    url: '/large/sourceData/list',
    method: 'get',
    params: query
  })
}

// 查询信息源归类柱状图内容详细
export function getSourceData(id) {
  return request({
    url: '/large/sourceData/' + id,
    method: 'get'
  })
}

// 新增信息源归类柱状图内容
export function addSourceData(data) {
  return request({
    url: '/large/sourceData',
    method: 'post',
    data: data
  })
}

// 修改信息源归类柱状图内容
export function updateSourceData(data) {
  return request({
    url: '/large/sourceData',
    method: 'put',
    data: data
  })
}

// 删除信息源归类柱状图内容
export function delSourceData(id) {
  return request({
    url: '/large/sourceData/' + id,
    method: 'delete'
  })
}
