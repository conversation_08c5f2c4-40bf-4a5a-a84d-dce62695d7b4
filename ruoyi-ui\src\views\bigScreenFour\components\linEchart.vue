<template>
  <div id="mainLine" style="width: 100%; height: 100%"></div>
</template>

<script>
import request from "@/utils/request";
import * as echarts from "echarts";

export default {
  data() {
    return {
      myChart: null,
      option: {},
    };
  },
  mounted() {
    this.initChart();
    // this.init();
  },
  props: {
    valueObj: {
      type: Object,
      default: () => {
        return {
          xData: [],
          yData: [],
        };
      },
    },
  },
  watch: {
    valueObj(value) {
      if (value) {
        this.option.xAxis.data = value.xData;
        this.option.series = value.yData;
        this.myChart.setOption(this.option);
      }
    },
  },
  components: {},
  methods: {
    initChart() {
      let chartDom = document.getElementById("mainLine");

      this.myChart = echarts.init(chartDom);
      this.option = {
        tooltip: {
          showContent: true,
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          show: true, //是否显示
          textStyle: { color: "#fff" },
          padding: [25, 10],
          x: "center",
          icon: "path://M616.920615 567.296c43.795692 0 87.591385-34.422154 131.387077-102.242462l-50.924307-37.572923c-30.562462 48.009846-57.028923 73.058462-80.46277 73.058462-19.377231 0-50.924308-15.675077-94.72-44.898462-44.819692-31.271385-83.534769-45.883077-115.12123-45.883077-44.819692 0-88.615385 33.398154-131.387077 102.242462l50.924307 36.509538c29.538462-47.970462 56.044308-71.995077 80.46277-71.995076 20.361846 0 51.987692 14.611692 96.768 44.898461 43.795692 30.247385 81.526154 45.883077 113.033846 45.883077z",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "5%",
          top: "15%",
          containLabel: true,
        },

        xAxis: {
          type: "category",
          data: [],
          axisLabel: {
            fontSize: "14px",
            color: "#fff",
            formatter: function (value) {
              if (value.length > 6) {
                return `${value.slice(0, 6)}...`;
              }
              return value;
            },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            lineStyle: {
              color: "#ffffff70",
              type: "dotted",
            },
          },
          axisLabel: {
            //侧边栏的标题字
            interval: 0, //显示不全
            fontSize: "14px",
            color: "#fff",
          },
        },
        series: [],
      };
      //随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
      setTimeout(() => {
        this.myChart.resize();
      }, 1);
      this.myChart.setOption(this.option);
      this.myChart.on("click", (params) => {
        console.log(params);
        // this.$emit("openList");
      });
    },
  },
  beforeDestroy() {
    window.removeEventListener("resize", () => {
      this.myChart.resize();
    });
  },
};
</script>

<style lang="scss">
.el-table .warning-row {
  background-color: #13436d;
}
.el-table .success-row {
  background-color: #113a65;
}
</style>
