<template>
  <div class="productBox">
    <div class="productList">
      <div
        class="product"
        :key="index"
        v-for="(item, index) in productList"
        @click="openList(item)"
      >
        <img
          :src="item.cover ? baseUrl + item.cover : ''"
          alt=""
          style="width: 100%; height: 100%"
        />
      </div>
    </div>
    <el-drawer
      :title="title"
      :visible.sync="drawer"
      direction="rtl"
      :before-close="handleClose1"
      append-to-body
      size="800px"
    >
      <div v-infinite-scroll="load" style="overflow: auto; height: 100%">
        <el-table
          :data="list"
          style="width: 100%"
          :show-header="false"
          ref="tableRef"
          @cell-click="openNewView"
        >
          <el-table-column
            prop="title"
            label="标题"
            width="450"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-html="changeColor(scope.row.title)"></span>
            </template>
          </el-table-column>
          <el-table-column
            prop="sourceName"
            label="数据源"
            width="100"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="发布时间"
            width="180"
            show-overflow-tooltip
          >
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>
    <el-dialog
      :title="drawerInfo.title"
      :visible.sync="articleDialogVisible"
      width="800px"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div
        v-loading="isLoading"
        style="width: 100%; height: 699px; background: rgba(0, 0, 0, 0.15)"
      >
        <div
          v-if="drawerInfo.fileUrl && drawerInfo.fileUrl.indexOf('doc') > -1"
          class="iframe-sty"
        >
          <div ref="word" class="docWrap"></div>
        </div>
        <iframe
          v-else-if="
            drawerInfo.fileUrl &&
            drawerInfo.fileUrl.indexOf('pdf') > -1 &&
            pdfUrl
          "
          class="prism-player"
          :src="pdfUrl + '#toolbar=1'"
          width="100%"
          height="699px"
          frameborder="0"
        ></iframe>
        <el-empty
          description="当前文章暂无数据"
          v-if="!drawerInfo.fileUrl"
        ></el-empty>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listProduct } from "@/api/large/product";
import {
  listProductData,
  courseStream,
  courseStreamPdf,
} from "@/api/large/productData";
var docx = require("docx-preview");
export default {
  data() {
    return {
      productList: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      drawerInfo: {},
      articleDialogVisible: false,
      queryParams: {
        pageNum: 1,
        pageSize: 12,
      },
      total: 0,
      drawer: false,
      title: "",
      list: [],
      query: {},
      pdfUrl: null,
      isLoading: false,
    };
  },
  components: {},
  mounted() {
    this.init();
  },
  beforeDestroy() {},
  methods: {
    init() {
      //左三
      listProduct({
        status: "0",
        pageNum: 1,
        pageSize: 6,
      }).then((res) => {
        // this.productList = this.fillArrayToSize(res.data[3], 12);
        this.productList = res.rows;
      });
    },
    fillArrayToSize(array, desiredSize) {
      while (array.length < desiredSize) {
        array.push({});
      }
      return array;
    },
    handleClose1() {
      this.title = "";
      this.list = [];
      this.query = {};
      this.drawer = false;
    },
    openList(item) {
      this.title = item.name;
      this.queryParams.pageNum = 1;
      this.list = [];
      this.query = item;
      this.getList();
    },
    async getList() {
      await listProductData({
        productId: this.query.id,
        status: "0",
        ...this.queryParams,
      }).then((res) => {
        this.total = res.total;
        this.list = this.list.concat(res.rows);
      });
      this.drawer = true;
    },
    async openNewView(item) {
      this.drawerInfo = item;
      this.articleDialogVisible = true;
      if (item.fileUrl.indexOf("doc") > -1) {
        setTimeout(() => {
          this.getWord(item.fileUrl);
        }, 1);
      } else {
        setTimeout(() => {
          this.pdfFun(item.fileUrl);
        }, 1);
      }
    },
    // 关键字替换
    changeColor(str) {
      let Str = str;
      return Str;
    },
    handleClose() {
      this.drawerInfo = {};
      this.articleDialogVisible = false;
    },
    load() {
      if (this.drawer == false) {
        return;
      }
      if (this.total === this.list.length) {
        return;
      } else {
        this.queryParams.pageNum = this.queryParams.pageNum + 1;
        this.getList();
      }
    },
    //word
    getWord(data) {
      this.isLoading = true;
      let formData = new FormData();
      formData.append(
        "fileUrl",
        data
        //  "/home/<USER>/dpx/2024/07/19/亚太瞭望-2024年第1期_20240719101844A006.docx"
      );
      courseStream(formData).then((res) => {
        docx.renderAsync(res, this.$refs.word, null, {
          // className: "docx", // 默认和文档样式类的类名/前缀
          // inWrapper: false, // 启用围绕文档内容渲染包装器
          ignoreWidth: false, // 禁止页面渲染宽度
          ignoreHeight: false, // 禁止页面渲染高度
          ignoreFonts: false, // 禁止字体渲染
          breakPages: false, // 在分页符上启用分页
          ignoreLastRenderedPageBreak: false, //禁用lastRenderedPageBreak元素的分页
          experimental: false, //启用实验性功能（制表符停止计算）
          trimXmlDeclaration: false, //如果为真，xml声明将在解析之前从xml文档中删除
          debug: false, // 启用额外的日志记录
        }); // 渲染到页面
        this.isLoading = false;
      });

      //docx.renderAsync(, this.$refs.word); // 渲染到页面
    },
    //pdf
    pdfFun(data) {
      this.pdfUrl = null;
      this.isLoading = true;
      let formData = new FormData();
      formData.append(
        "fileUrl",
        data
        //  "/home/<USER>/dpx/2024/07/19/亚太瞭望-2024年第1期_20240719101844A006.docx"
      );
      courseStreamPdf(formData).then((res) => {
        var binaryData = [];
        binaryData.push(res);
        this.pdfUrl = window.URL.createObjectURL(
          new Blob(binaryData, {
            type: "application/pdf",
          })
        );
        this.isLoading = false;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.productBox {
  width: 100%;
  height: 100%;
  border: 1px solid rgba(16, 216, 255, 0.4);
  background: rgba(0, 0, 0, 0.15);
  box-shadow: 0px 0px 8px 0px #0056ad;
  overflow: hidden;
  margin-bottom: 20px;

  .productList {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: flex-start;
    align-content: flex-start;

    .product {
      width: 162px;
      height: 190px;
      margin: 20px 5px 0px 5px;
      background: rgba(11, 111, 216, 0.15);
      border-radius: 4px;
      border: 1px solid rgba(11, 111, 216, 0.2);
    }
  }
}

::v-deep .el-dialog {
  background: url("../../../assets/bigScreenTwo/dialogBackground.png") no-repeat;
  background-size: 100% 100% !important;
  background-size: cover;
  height: 800px;

  .el-dialog__header {
    background-color: #1d233400;
    font-size: 30px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);
    height: 100px;
  }

  .el-dialog__body {
    background-color: #2a304000;
    color: #f2f2f2;
    padding: 1px;
    height: 700px;
    overflow: auto;
  }

  .el-button {
    background-color: #002766;
    color: #fff;
    border: 0px;
  }

  .el-dialog__headerbtn .el-dialog__close {
    background: url("../../../assets/bigScreenTwo/关闭小.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
    width: 31px;
    height: 31px;
    top: 16px;

    &::before {
      content: none;
    }
  }
}

::v-deep .el-drawer__open {
  .el-drawer {
    background: url("../../../assets/bigScreenTwo/drawerBackground.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
  }

  .el-dialog__close {
    background: url("../../../assets/bigScreenTwo/关闭小.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
    width: 31px;
    height: 31px;
    top: 18px;
    right: 10px;

    &::before {
      content: none;
    }
  }

  .el-drawer__header {
    background-color: #1d233400;
    font-size: 30px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);
    height: 100px;
  }
}

::v-deep .el-table {
  background-color: #2a304000;

  tr {
    color: #f2f2f2;
    background: url("../../../assets/bigScreenTwo/弹窗列表.png") no-repeat;
    background-size: 100% 100% !important;
    height: 68px;
    padding: 0 0 0 65px;
    margin-bottom: 10px;
    display: block;
    width: 790px;
  }

  td.el-table__cell {
    border-bottom: 1px solid #1d233400;
    height: 68px;
    line-height: 68px;
    font-size: 16px;
    text-shadow: 0px 0px 9px rgba(30, 198, 255, 0.8);
  }

  &::before {
    height: 0;
  }
}

::v-deep
  .el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell,
::v-deep .el-table__empty-block {
  background-color: #2a304000;
  color: #f2f2f2;
  cursor: pointer;
}

::v-deep .pagination-container {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 0;

  .el-select .el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }

  .el-pagination__editor.el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }
}

::v-deep .el-pagination__total,
::v-deep .el-pagination__jump {
  color: #f2f2f2;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination button:disabled {
  background-color: #ffffff00;
  color: #fff;
}

::v-deep .el-pager li {
  background: #ffffff00;
  color: #fff;

  &.active {
    color: #1890ff;
  }
}
.iframe-sty {
  .docWrap {
    // 指定样式宽度
    width: 100%;
    overflow-x: auto;
  }

  ::v-deep {
    .docx-wrapper {
      background: none;
      // padding: 30px 0;
      padding: 1px;
    }

    .docx-wrapper > section.docx {
      width: 100% !important;
      padding: 20px !important;
      box-shadow: 0px 0px 15px 10px rgb(0 0 0 / 50%);
      margin: 0 !important;
    }
  }
}
::v-deep {
  .el-loading-mask {
    background: rgba(0, 0, 0, 0.03);
  }
}
</style>
