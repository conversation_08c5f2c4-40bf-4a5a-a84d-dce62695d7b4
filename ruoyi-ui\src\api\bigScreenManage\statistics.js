import request from "@/utils/request";

// 查询关键词列表
export function listStatistics(query) {
  return request({
    url: "/screen/statistics/list",
    method: "get",
    params: query,
  });
}

// 查询关键词详细
export function getStatistics(id) {
  return request({
    url: "/screen/statistics/" + id,
    method: "get",
  });
}

// 新增关键词
export function addStatistics(data) {
  return request({
    url: "/screen/statistics",
    method: "post",
    data: data,
  });
}

// 修改关键词
export function updateStatistics(data) {
  return request({
    url: "/screen/statistics/edit",
    method: "post",
    data: data,
  });
}

// 删除关键词
export function delStatistics(id) {
  return request({
    url: "/screen/statistics/remove",
    method: "post",
    data: id,
  });
}
