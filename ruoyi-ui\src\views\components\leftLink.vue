<template>
  <div class="leftLink">
    <div class="keListConteneurs">
      <el-button icon="el-icon-plus" v-if="showAdd && type == 'KeQin'" style="width: 90%;" type="primary"
        @click="AddKeData(false)" v-hasPermi="['article:monitoring:add']">新建专题</el-button>
      <el-button icon="el-icon-plus" v-if="showAdd && type == 'specialSubject'" style="width: 90%;" type="primary"
        @click="AddKeData(false)" v-hasPermi="['article:special:add']">新建专题</el-button>
      <!-- <div @click="clickActive($event)" style="margin-top: 8px;"> -->
      <div style="margin-top: 8px;">
        <el-button @click="clickActive(index)" slot="reference" :dom-index="index" :title="item.title"
          v-for="(item, index) in KeList" :key="index" :class="{ active: isActive == index, textStyle: 'textStyle' }"
          :plain="isActive == index" :type="isActive == index ? 'warning' : ''">
          {{ item.id ? item.id + ' ' + item.title : item.title }}
          <el-popover style="width: 15%;position: absolute;right: 0;" placement="bottom-end" trigger="click"
            v-if="toolShow">
            <el-button size="mini" type="primary" @click="AddKeData(item)"
              v-hasPermi="['article:monitoring:edit', 'article:special:edit']">编辑</el-button>
            <el-button size="mini" type="danger" v-if="item.id !== 100 && item.id !== 1" @click="deleteEvetn(item)"
              v-hasPermi="['article:monitoring:remove', 'article:special:remove']">删除</el-button>
            <i class="el-icon-s-tools" slot="reference" v-show="isActive == index"></i>
          </el-popover>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    type: {
      required: true
    },
    isActive: {
      required: true
    },
    ActiveData: {
      required: true,
      type: Object
    },
    drawer: {
      required: true,
      type: Boolean
    },
    KeList: {
      required: true,
      type: Array
    },
    showAdd: {
      type: Boolean,
      default: true
    },
    toolShow: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      visible: false,
      KeData: {}
    }
  },
  created() { },
  methods: {
    /* 科情切换-事件委托 */
    // clickActive($event) {
    //   let index = $event.target.parentNode.getAttribute('dom-index')
    //   if ($event.target.tagName !== 'DIV') {
    //     if (!index || index == this.isActive) return
    //     this.KeData = this.KeList[this.isActive]
    //     this.$emit('activeLink', index)
    //   }
    // },
    clickActive(index) {
      index = index.toString()
      if (!index || index == this.isActive) return
      this.KeData = this.KeList[this.isActive]
      this.$emit('activeLink', index)
    },
    /* 新健科情 */
    AddKeData(item) {
      this.$emit('AddKeEvent', true, item)
      // drawer = true
    },
    /* 删除 */
    deleteEvetn(item) {
      this.$emit('deleteEvetn', item.id)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-popover {
  background: rgba(0, 0, 0, 0.26);
}

.leftLink {
  width: 10%;
  float: left;
  background-color: rgb(248, 248, 248);

  .keListConteneurs {
    width: 95%;
    height: 100%;
    text-align: center;
    margin-top: 20px;
    margin: 20px auto;

    p {
      color: rgb(43, 43, 43);
      font-size: 14px;
      height: 30px;
      line-height: 30px;

      span {
        text-align: left;
        padding-left: 5px;
        overflow: hidden;
        white-space: nowrap;
        /* 防止文本换行 */
        text-overflow: ellipsis;
        width: 100%;
      }
    }

    .textStyle {
      // display: flex;
      // justify-content: space-between;
      // align-items: center;
      text-align: left;
      position: relative;
      padding-right: 5px;
      margin: 7px 0 7px 5%;
      width: 90%;
      overflow: hidden;
      white-space: nowrap;
      /* 防止文本换行 */
      text-overflow: ellipsis;
      padding: 10px 10px;
      border-width: 0px;
    }

    .active {
      width: 90%;
      margin: 7px 0 7px 5%;
      // height: 35px;
      // line-height: 35px;
      // background-color: #ff9318;
      border-radius: 2px;
      // color: #fafafa;
      border-width: 1px;
    }
  }
}
</style>