import request from '@/utils/request'

// 查询信通院大屏-网络安全关键人物 检索词库列表
export function listNscKeywords(query) {
  return request({
    url: '/large/nscKeywords/list',
    method: 'get',
    params: query
  })
}

// 检索词库列表（排除节点）
export function excludeChild(deptId) {
  return request({
    url: '/large/nscKeywords/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询信通院大屏-网络安全关键人物 检索词库详细
export function getNscKeywords(id) {
  return request({
    url: '/large/nscKeywords/' + id,
    method: 'get'
  })
}

// 新增信通院大屏-网络安全关键人物 检索词库
export function addNscKeywords(data) {
  return request({
    url: '/large/nscKeywords',
    method: 'post',
    data: data
  })
}

// 修改信通院大屏-网络安全关键人物 检索词库
export function updateNscKeywords(data) {
  return request({
    url: '/large/nscKeywords/edit',
    method: 'post',
    data: data
  })
}

// 删除信通院大屏-网络安全关键人物 检索词库
export function delNscKeywords(id) {
  let data = new FormData()
  data.append('id', id)
  return request({
    url: '/large/nscKeywords/remove',
    method: 'post',
    data: data
  })
}
