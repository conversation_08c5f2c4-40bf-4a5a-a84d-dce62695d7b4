<template>
  <div ref="chartContainer"></div>
</template>

<script>
import * as echarts from "echarts";
import usaJson from "../../../assets/usa.json";
import { proposalsCount } from "@/api/bigScreen/sanhao.js";

export default {
  name: "UsaMap",
  props: {
    // 外部传入的地图数据，如果传入则不调用接口
    externalData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      myChart: null,
      option: null,
      loading: false,
      mapData: [],
    };
  },
  mounted() {
    this.myChart = echarts.init(this.$refs.chartContainer);
    echarts.registerMap("USA", usaJson, {
      Alaska: { left: -131, top: 25, width: 15 },
      Hawaii: { left: -110, top: 28, width: 5 },
      "Puerto Rico": { left: -76, top: 26, width: 2 },
    });

    this.initChart();

    // 如果有外部数据则使用外部数据，否则调用接口获取数据
    if (this.externalData) {
      this.processMapData(this.externalData);
    } else {
      this.fetchProposalsData();
    }

    // 监听窗口大小变化，调整图表大小
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
    // 销毁实例
    this.myChart && this.myChart.dispose();
  },
  watch: {
    // 监听外部数据变化
    externalData: {
      handler(newData) {
        if (newData) {
          this.processMapData(newData);
        }
      },
      deep: true,
    },
  },
  methods: {
    handleResize() {
      this.myChart && this.myChart.resize();
    },

    // 调用接口获取数据
    async fetchProposalsData() {
      try {
        this.loading = true;
        const response = await proposalsCount({
          projectSn: "1",
          screenSn: "1",
          columnSn: "1",
        });
        this.processMapData(response);
      } catch (error) {
        console.error("获取政策提案数据失败:", error);
      } finally {
        this.loading = false;
      }
    },

    // 处理接口返回的数据
    processMapData(data) {
      if (!data || !data.data) {
        console.warn("接口返回数据格式不正确");
        return;
      }

      // 转换数据格式为echarts需要的格式
      this.mapData = [];

      // 数据格式是 [{area: "州名", count: 数量}, ...]
      if (Array.isArray(data.data)) {
        // 合并相同州的数据（例如"加州"和"加利福尼亚州"）
        const stateMap = new Map();

        data.data.forEach((item) => {
          if (item.area && item.count) {
            // 处理特殊情况，例如"加州"和"加利福尼亚州"视为同一州
            let areaName = item.area;
            if (areaName === "加州") {
              areaName = "加利福尼亚州";
            }

            // 如果该州已存在，累加count值
            if (stateMap.has(areaName)) {
              stateMap.set(areaName, stateMap.get(areaName) + item.count);
            } else {
              stateMap.set(areaName, item.count);
            }
          }
        });

        // 将合并后的数据转换为数组
        stateMap.forEach((count, area) => {
          this.mapData.push({
            name: area,
            value: count,
          });
        });
      }

      // 更新图表数据
      this.updateChartData();
    },

    // 更新图表数据
    updateChartData() {
      if (!this.option || !this.mapData.length) return;

      // 计算数据的最小值和最大值
      const values = this.mapData.map((item) => item.value);
      const min = 0;
      const max = Math.max(...values) || 100;

      // 更新visualMap的范围
      this.option.visualMap.min = min;
      this.option.visualMap.max = max;

      // 更新系列数据
      this.option.series[0].data = this.mapData;

      // 重新设置图表选项
      this.myChart.setOption(this.option);
    },

    initChart() {
      this.option = {
        tooltip: {
          trigger: "item",
          showDelay: 0,
          transitionDuration: 0.2,
          formatter: function (params) {
            return params.name + ": " + (params.value || 0);
          },
        },
        visualMap: {
          left: "auto",
          right: "2%",
          min: 0,
          max: 5, // 初始值，会根据实际数据动态调整
          inRange: {
            color: ["#FAE4E4", "#E36B6B", "#D54545", "#AF3535", "#6B0606"],
          },
          text: ["高", "低"],
          calculable: true,
          textStyle: {
            color: "#fff",
          },
        },
        series: [
          {
            name: "提案数量",
            type: "map",
            map: "USA",
            left: "0",
            right: "10%",
            top: "middle",
            itemStyle: {
              normal: {
                areaColor: "#FAE4E4", // 无数据区域颜色
                borderColor: "#D5D6D8", // 区域边框颜色
                borderWidth: 2, // 边框宽度
              },
            },
            emphasis: {
              label: {
                show: true,
              },
            },
            nameMap: {
              Alabama: "阿拉巴马州",
              Alaska: "阿拉斯加州",
              Arizona: "亚利桑那州",
              Arkansas: "阿肯色州",
              California: "加利福尼亚州",
              Colorado: "科罗拉多州",
              Connecticut: "康涅狄格州",
              Delaware: "特拉华州",
              "District of Columbia": "哥伦比亚特区",
              Florida: "佛罗里达州",
              Georgia: "格鲁吉亚州",
              Hawaii: "夏威夷州",
              Idaho: "爱达荷州",
              Illinois: "伊利诺伊州",
              Indiana: "印第安纳州",
              Iowa: "爱荷华州",
              Kansas: "堪萨斯州",
              Kentucky: "肯塔基州",
              Louisiana: "路易斯安那州",
              Maine: "缅因州",
              Maryland: "马里兰州",
              Massachusetts: "麻萨诸塞州",
              Michigan: "密歇根州",
              Minnesota: "明尼苏达州",
              Mississippi: "密西西比州",
              Missouri: "密苏里州",
              Montana: "蒙大拿州",
              Nebraska: "内布拉斯加州",
              Nevada: "内华达州",
              "New Hampshire": "新罕布什尔州",
              "New Jersey": "新泽西州",
              "New Mexico": "新墨西哥州",
              "New York": "纽约州",
              "North Carolina": "北卡罗来纳州",
              "North Dakota": "北达科他州",
              Ohio: "俄亥俄州",
              Oklahoma: "俄克拉荷马州",
              Oregon: "俄勒冈州",
              Pennsylvania: "宾夕法尼亚州",
              "Rhode Island": "罗德岛州",
              "South Carolina": "南卡罗来纳州",
              "South Dakota": "南达科他州",
              Tennessee: "田纳西州",
              Texas: "德克萨斯州",
              Utah: "犹他州",
              Vermont: "佛蒙特州",
              Virginia: "弗吉尼亚州",
              Washington: "华盛顿州",
              "West Virginia": "西弗吉尼亚州",
              Wisconsin: "威斯康星州",
              Wyoming: "怀俄明州",
              "Puerto Rico": "波多黎各",
            },
            data: [], // 初始化为空数组，等待API数据
          },
        ],
      };
      this.myChart.setOption(this.option);
    },
  },
};
</script>

<style scoped>
/* 可以在这里添加样式 */
.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  z-index: 10;
}
</style>
