<template>
  <div id="mainLine" style="width: 100%; height: 100%"></div>
</template>
 
<script>
import request from "@/utils/request";
import * as echarts from "echarts";

export default {
  data() {
    return {
      myChart: null,
      option: {},
    };
  },
  mounted() {
    this.initChart();
    // this.init();
  },
  props: {
    valueObj: {
      type: Object,
      default: () => {
        return {
          xData: [],
          yData: [],
        };
      },
    },
  },
  watch: {
    valueObj(value) {
      if (value) {
        this.option.xAxis.data = value.xData;
        this.option.series = value.yData;
        this.myChart.setOption(this.option);
      }
    },
  },
  components: {},
  methods: {
    /**
     *
     */
    initChart() {
      let chartDom = document.getElementById("mainLine");

      this.myChart = echarts.init(chartDom);
      this.option = {
        tooltip: {
          showContent: true,
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          show: true, //是否显示
          textStyle: { color: "#fff" },
          padding: [15, 10],
          x: "right",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "15%",
          top: "15%",
          containLabel: true,
        },

        xAxis: {
          type: "category",
          data: [],
          axisLabel: {
            fontSize: "14px",
            color: "#fff",
            formatter: function (value) {
              if (value.length > 4) {
                return `${value.slice(0, 4)}...`;
              }
              return value;
            },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            lineStyle: {
              color: "#1BDCFF40",
            },
          },
          axisLabel: {
            //侧边栏的标题字
            interval: 0, //显示不全

            fontSize: "14px",
            color: "#fff",
            formatter: function (value) {
              if (value.length > 4) {
                return `${value.slice(0, 4)}...`;
              }
              return value;
            },
          },
        },
        series: [],
      };
      //随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
      setTimeout(() => {
        this.myChart.resize();
      }, 1);
      this.myChart.setOption(this.option);
    },
  },
  beforeDestroy() {
    /* 页面组件销毁的时候，别忘了移除绑定的监听resize事件，否则的话，多渲染几次
      容易导致内存泄漏和额外CPU或GPU占用哦*/
    window.removeEventListener("resize", () => {
      this.myChart.resize();
    });
  },
};
</script>

<style lang='scss'>
.el-table .warning-row {
  background-color: #13436d;
}
.el-table .success-row {
  background-color: #113a65;
}
</style>