import request from '@/utils/request'

// 查询政策库采集任务列表
export function listTask(query) {
  return request({
    url: '/policy/task/list',
    method: 'get',
    params: query
  })
}

// 查询政策库采集任务详细
export function getTask(id) {
  return request({
    url: '/policy/task/' + id,
    method: 'get'
  })
}

// 新增政策库采集任务
export function addTask(data) {
  return request({
    url: '/policy/task',
    method: 'post',
    data: data
  })
}

// 修改政策库采集任务
export function updateTask(data) {
  return request({
    url: '/policy/task/edit',
    method: 'post',
    data: data
  })
}

// 删除政策库采集任务
export function delTask(data) {
  return request({
    url: '/policy/task/remove',
    method: 'post',
    data: data
  })
}
