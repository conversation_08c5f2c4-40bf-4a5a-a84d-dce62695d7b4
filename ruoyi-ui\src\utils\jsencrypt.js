import JSEncrypt from "jsencrypt/bin/jsencrypt.min";

// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey =
  "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjrLes5Xue9sRJ1Fazq+eo+5ej1iIfa0MPBY2O0EzpQ+HSi30yJl4p6iD1OiWypr8RL4gFhkdz6BEt7+6lLA+VD+qAsmvlddnH/aFiokywQH+GjwWEiDCOymtWGmHRZjm3VMRJbT000XPW8tnuUSeDHmpygdxjXt7Q0wd4yBFOGbYnhC7MxSnF1X78MR0lvxdcf45iG/CZ2M3B6E+VOB3EFhkxoBISIDx+NuqUVe7B9Q+lHvKs5ckKH38S0LdrS4tSSzFQ0Y0Ojc3ockkWH0Tz1ji4Hi67z8x/zUhIvV06BlRGqMRm+LykbhF6uI5R7BEYptPAHHy40zocc8IzXJDvwIDAQAB";

const privateKey =
  "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCOst6zle572xEnUVrOr56j7l6PWIh9rQw8FjY7QTOlD4dKLfTImXinqIPU6JbKmvxEviAWGR3PoES3v7qUsD5UP6oCya+V12cf9oWKiTLBAf4aPBYSIMI7Ka1YaYdFmObdUxEltPTTRc9by2e5RJ4MeanKB3GNe3tDTB3jIEU4ZtieELszFKcXVfvwxHSW/F1x/jmIb8JnYzcHoT5U4HcQWGTGgEhIgPH426pRV7sH1D6Ue8qzlyQoffxLQt2tLi1JLMVDRjQ6NzehySRYfRPPWOLgeLrvPzH/NSEi9XToGVEaoxGb4vKRuEXq4jlHsERim08AcfLjTOhxzwjNckO/AgMBAAECggEAR8BG3HPiI2LmgCeXCmAh23nLEDbLAq5VfY0JncfOKQvi0fjDoW4RzfOcvhxpXdyKRiJ3TcU5SSHkC6SbM5Je9MvCN36kDCrwDpFNWnhcDdJrq1NVIa5ZG5h8Mf116BfW1aij8ZrIk8cBWb2wHOcUtV8jcZNGlIZnV1JZoM+kHpRP1yML4RmeikOLOhE1keXKjbleDWTjT9C2ENQEkFHgwkV+chesGE55ySpF91Z8FIuUgRwLP8gqXBrzqKJldL1Pcyn9nQyojIw+ENrPQBWiNnzkEjGjfSU6t5O0rZyFjecetq72iBwp5Fppo8uaDdvfm41o5KcqL2WnNypvJZRUGQKBgQC/+AapwuCfgkCsXqZHs61mViGsCJU9OKCR8Wl16Zx5xwNTI9SledevMcuj6Z8an0KV1LgkMpjU5GkGe75PHQbC+OvKR6kn3mUJaw7ul40uk9rA4/XDti5sKKN/x8DcrivC1XlZltq10SyIsSuVAMkXHXeEUpzg6bFPnNyDdcuHvQKBgQC+S7rR8a3mKZ52XNRSWvFViFvl5JMRCzX6oYBO0FkCvyRKswXEKn1l/OQbndeA8fu7V4M65N8gDcbTNlnSJjMFKYAcL4tymaNAz6durt4+Jrvtjh8YM9TmpYmmYU9fMYJf8oHUPhhfbWVOyOsupADDW12iMDp9tsSGc9+GLGxDKwKBgQCvYAe2TzKmhnhPAKkoGB3xv/5DDBRLOdB3g/S0pBcvqYCAE9vQ0N8aToOb23Je/9/0wcD4UMEOvRy1r5bM79edh4rh4VXH9oSXdhbOIeX5B1pxndCIW4dNiFLWCcdH1FZmSF/0WLy2HcJxnmoSVgVf+KuXV2G6l8RE1ykdcB/ggQKBgH0txeQrLjDUuee0ovrV1iFkxMcSuGeiz0xihsvRpGv5Hr+Ayk9DgB/h3pAIIeYxyF1xcOMCAYDNbzgrbaSe+jrwFNT1ta6aQFj3AHX/1DRgOIlJT00Vc3yR8l2fb534LII/PYZHnrLPfK/53TNeMR5RcOOrBB8EZnznpmdVu5XnAoGAHNH2jzQSiyCUPRd5AndHRCpAz58dE52CqMVsTqeDjO5urZPWqNjPXWtAh66x0EdyAqOgtlM/8GDtdz55aiDdiCJJBTsKD4Fs0ynWRIakywwgO+jZ7Ub6f0wyiO8C2yWq0vpXAU2bKZ5RDinZTfYeRXYj7NxDM9PGGvCpihWXsWw=";

// 加密
export function encrypt(txt) {
  const encryptor = new JSEncrypt();
  encryptor.setPublicKey(publicKey); // 设置公钥
  return encryptor.encrypt(txt); // 对数据进行加密
}

// 解密
export function decrypt(txt) {
  const encryptor = new JSEncrypt();
  encryptor.setPrivateKey(privateKey); // 设置私钥
  return encryptor.decrypt(txt); // 对数据进行解密
}
