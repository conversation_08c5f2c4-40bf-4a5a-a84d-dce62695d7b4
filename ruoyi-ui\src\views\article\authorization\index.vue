<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="80px"
      class="queryForm"
    >
      <el-row type="flex" justify="space-between">
        <el-col :span="6">
          <el-form-item label="应用ID" prop="appId">
            <el-input
              v-model="queryParams.appId"
              placeholder="请输入应用ID:dpx+15位随机数"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户名称" prop="authorizedCustomers">
            <el-input
              v-model="queryParams.authorizedCustomers"
              placeholder="请输入客户名称"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="queryParams.remark"
              placeholder="请输入备注"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6" style="padding-left: 20px">
          <el-form-item class="form-item-btn">
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-form-item label="应用KEY：由开发者手动填写或随机生成，将用作消息体加解密密钥" prop="appKey">
        <el-input v-model="queryParams.appKey" placeholder="请输入应用KEY：由开发者手动填写或随机生成，将用作消息体加解密密钥" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="token参数：Token可由开发者可以任意填写，用作生成签名" prop="token">
        <el-input v-model="queryParams.token" placeholder="请输入token参数：Token可由开发者可以任意填写，用作生成签名" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item> -->
      <!-- <el-form-item label="获取到的凭证：动态口令" prop="accessToken">
        <el-input v-model="queryParams.accessToken" placeholder="请输入获取到的凭证：动态口令" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item> -->
      <!-- <el-form-item label="凭证有效时间，单位：秒" prop="expiresIn">
        <el-input v-model="queryParams.expiresIn" placeholder="请输入凭证有效时间，单位：秒" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item> -->

      <!-- <el-form-item label="授权服务器地址：URL" prop="authorizedUrl">
        <el-input v-model="queryParams.authorizedUrl" placeholder="请输入授权服务器地址：URL" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="授权服务器地址：IP" prop="authorizedIp">
        <el-input v-model="queryParams.authorizedIp" placeholder="请输入授权服务器地址：IP" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="授权开始时间" prop="startTime">
        <el-date-picker clearable v-model="queryParams.startTime" type="date" value-format="yyyy-MM-dd"
          placeholder="请选择授权开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="授权结束时间" prop="endTime">
        <el-date-picker clearable v-model="queryParams.endTime" type="date" value-format="yyyy-MM-dd"
          placeholder="请选择授权结束时间">
        </el-date-picker> 
      </el-form-item> -->
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['article:authorization:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['article:authorization:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['article:authorization:remove']"
          >删除</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['article:authorization:export']">导出</el-button>
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="authorizationList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 230px)"
      ref="tableRef"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键ID" align="center" prop="id" /> -->
      <el-table-column label="应用ID" width="160" align="center" prop="appId" />
      <el-table-column
        label="应用KEY"
        width="350"
        align="center"
        prop="appKey"
      />
      <!-- <el-table-column label="token参数：Token可由开发者可以任意填写，用作生成签名" align="center" prop="token" /> -->
      <!-- <el-table-column label="获取到的凭证：动态口令" align="center" prop="accessToken" /> -->
      <el-table-column
        label="授权客户名称"
        width="100"
        align="center"
        prop="authorizedCustomers"
      />
      <el-table-column
        label="数据源"
        width="100"
        align="center"
        prop="authorizedCustomers"
      />
      <!-- <el-table-column label="授权限制类型：0.不限；1.URL；2.IP" align="center" prop="authorizedType" /> -->
      <!-- <el-table-column label="授权服务器地址" align="center" prop="authorizedUrl" /> -->
      <!-- <el-table-column label="授权服务器地址：IP" align="center" prop="authorizedIp" /> -->
      <!-- <el-table-column label="授权开始时间" align="center" prop="startTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="授权结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="授权1" align="center" prop="field1" />
      <el-table-column label="授权2" align="center" prop="field2" />
      <el-table-column label="授权3" align="center" prop="field3" /> -->
      <el-table-column label="状态" width="55" align="center" prop="status">
        <template slot-scope="scope">
          {{ scope.row.status == 0 ? "启用" : "停用" }}
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <!-- <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="单位ID" align="center" prop="deptId" /> -->
      <el-table-column
        label="操作"
        width="100"
        fixed="right"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <div
            style="
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
            "
          >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['article:authorization:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['article:authorization:remove']"
              style="margin-left: 0"
              >删除</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-operation"
              @click="openDistribution(scope.row)"
              v-hasPermi="['article:authorization:distribution']"
              style="margin-left: 0"
              >分配数据源</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改API授权对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-form-item label="应用ID" prop="appId" v-if="title == '修改API授权'">
          <el-input
            v-model="form.appId"
            placeholder="请输入应用ID:dpx+15位随机数"
            :disabled="true"
          />
        </el-form-item>
        <el-form-item label="应用KEY" prop="appKey">
          <el-input
            v-model="form.appKey"
            placeholder="请输入应用KEY:由开发者手动填写或随机生成，将用作消息体加解密密钥"
          />
          <el-button
            @click="generateRandomString"
            :disabled="!!form.appKey && form.appKey.length == 64"
            >一键生成</el-button
          >
        </el-form-item>
        <!-- <el-form-item label="token参数" prop="token">
          <el-input v-model="form.token" placeholder="请输入token参数:Token可由开发者可以任意填写,用作生成签名" />
        </el-form-item>
        <el-form-item label="动态口令" prop="accessToken">
          <el-input v-model="form.accessToken" placeholder="请输入获取到的凭证:动态口令" />
        </el-form-item> -->
        <!-- <el-form-item label="凭证有效时间，单位：秒" prop="expiresIn">
          <el-input v-model="form.expiresIn" placeholder="请输入凭证有效时间，单位：秒" />
        </el-form-item> -->
        <el-form-item label="客户名称" prop="authorizedCustomers">
          <el-input
            v-model="form.authorizedCustomers"
            placeholder="请输入授权客户名称"
          />
        </el-form-item>
        <el-form-item label="服务器URL" prop="authorizedUrl">
          <el-input
            v-model="form.authorizedUrl"
            placeholder="请输入授权服务器地址:URL"
          />
        </el-form-item>
        <el-form-item label="服务器IP" prop="authorizedIp">
          <el-input
            v-model="form.authorizedIp"
            placeholder="请输入授权服务器地址:IP"
          />
        </el-form-item>
        <!-- <el-form-item label="授权开始时间" prop="startTime">
          <el-date-picker clearable v-model="form.startTime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择授权开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="授权结束时间" prop="endTime">
          <el-date-picker clearable v-model="form.endTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择授权结束时间">
          </el-date-picker>
        </el-form-item> -->
        <!-- <el-form-item label="授权1" prop="field1">
          <el-input v-model="form.field1" placeholder="请输入授权1" />
        </el-form-item>
        <el-form-item label="授权2" prop="field2">
          <el-input v-model="form.field2" placeholder="请输入授权2" />
        </el-form-item>
        <el-form-item label="授权3" prop="field3">
          <el-input v-model="form.field3" placeholder="请输入授权3" />
        </el-form-item> -->
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <!-- <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="单位ID" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入单位ID" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="分配数据源"
      :visible.sync="dialogVisible"
      width="1000px"
      :before-close="closeDistribution"
      :close-on-click-modal="false"
    >
      <el-form
        ref="dialogForm"
        :model="dialogForm"
        :rules="dialogRules"
        label-width="80px"
      >
        <el-form-item label="应用ID" prop="appid">
          <el-input
            v-model="dialogForm.appid"
            :disabled="true"
            placeholder="请输入应用ID:dpx+15位随机数"
          />
        </el-form-item>
        <div style="display: flex">
          <el-form-item label="数据源" prop="snList">
            <!-- <el-select v-model="dialogForm.snList" filterable style="width: 100%" multiple clearable placeholder="请选择数据源"> -->
            <!-- <el-option v-for="item in snOption" :key="item.sn" :label="item.name" :value="item.sn"></el-option> -->
            <el-select
              v-model="leftFilter"
              placeholder="请选择"
              size="mini"
              @change="filterList(leftFilter)"
            >
              <el-option
                v-for="(item, index) in classList"
                :key="index"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核查状态" prop="isAccused">
            <el-input v-model="isAccused" placeholder="请输入" size="mini" />
          </el-form-item>
        </div>
        <el-form-item>
          <el-transfer
            ref="transfer"
            style="width: 100%"
            filterable
            :filter-method="filterMethod"
            filter-placeholder="请选择数据源"
            :titles="['未选择', '已选择']"
            v-model="dialogForm.snList"
            :data="snOption"
          >
          </el-transfer>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDistribution">取 消</el-button>
        <el-button type="primary" @click="submitDistribution">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  listAuthorization,
  getAuthorization,
  delAuthorization,
  addAuthorization,
  updateAuthorization,
  getSnList,
  getAuthorizationSn,
  changeAuthorizationSn,
} from "@/api/article/authorization";
import random from "string-random";
import { getSourceSelect } from "@/api/article/source";

export default {
  name: "Authorization",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // API授权表格数据
      authorizationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        appId: null,
        appKey: null,
        token: null,
        accessToken: null,
        expiresIn: null,
        authorizedCustomers: null,
        authorizedType: null,
        authorizedUrl: null,
        authorizedIp: null,
        startTime: null,
        endTime: null,
        field1: null,
        field2: null,
        field3: null,
        status: null,
        userId: null,
        deptId: null,
        remark: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        appId: [
          {
            required: true,
            message: "应用ID：dpx+15位随机数不能为空",
            trigger: "blur",
          },
        ],
        status: [
          {
            required: true,
            message: "状态(0正常 1停用)不能为空",
            trigger: "change",
          },
        ],
      },
      dialogVisible: false, // 分配数据源弹窗
      dialogForm: {
        // 分配数据源数据
        appid: "",
        snList: [],
      },
      dialogRules: {
        // 分配数据源表单校验
        appId: [
          {
            required: true,
            message: "应用ID：dpx+15位随机数不能为空",
            trigger: "blur",
          },
        ],
      },
      snOption: [], // 数据源列表
      leftFilter: "",
      isAccused: "",
      classList: [],
    };
  },
  created() {
    this.getList();
    getSourceSelect().then((Data) => {
      this.classList = Data.data;
    });
  },
  methods: {
    /** 查询API授权列表 */
    getList() {
      this.loading = true;
      listAuthorization(this.queryParams).then((response) => {
        this.authorizationList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.$nextTick(() => {
          this.scrollToTop();
        });
      });
    },
    // 表格滚动到顶部
    scrollToTop() {
      if (this.$refs.tableRef) {
        const tableEl = this.$refs.tableRef.$el.querySelector(
          ".el-table__body-wrapper"
        );
        if (tableEl) {
          tableEl.scrollTop = 0;
        }
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        appId: null,
        appKey: null,
        token: null,
        accessToken: null,
        expiresIn: null,
        authorizedCustomers: null,
        authorizedType: null,
        authorizedUrl: null,
        authorizedIp: null,
        startTime: null,
        endTime: null,
        field1: null,
        field2: null,
        field3: null,
        status: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加API授权";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getAuthorization(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改API授权";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateAuthorization(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAuthorization(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids;
      this.$modal
        .confirm('是否确认删除API授权编号为"' + ids + '"的数据项？')
        .then(function () {
          return delAuthorization(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "article/authorization/export",
        {
          ...this.queryParams,
        },
        `authorization_${new Date().getTime()}.xlsx`
      );
    },
    // 分配数据源
    async openDistribution(row) {
      this.loading = true;
      this.dialogForm.appid = row.appId;
      await getSnList().then((res) => {
        this.snOption = res.data.map((row, index) => {
          return {
            label: row.name,
            key: row.sn,
            type: row.type,
            isAccused: row.isAccused ? row.isAccused : null,
          };
        });
      });
      await getAuthorizationSn(row.appId).then((res) => {
        this.dialogForm.snList = res.data;
      });
      this.leftFilter = "";
      this.isAccused = "";
      this.dialogVisible = true;
      this.loading = false;
    },
    // 调用分配数据源接口
    submitDistribution() {
      changeAuthorizationSn(this.dialogForm).then((res) => {
        this.$modal.msgSuccess("更改成功");
        this.closeDistribution();
      });
    },
    // 关闭分配数据源
    closeDistribution() {
      this.$refs["dialogForm"].resetFields();
      this.dialogVisible = false;
    },
    // 过滤
    filterMethod(query, item) {
      const labelMatches = item.label.indexOf(query) > -1;
      const typeMatches = !this.leftFilter || item.type == this.leftFilter;
      const accusedMatches =
        !this.isAccused || String(item.isAccused) === String(this.isAccused);

      return labelMatches && typeMatches && accusedMatches;
    },
    filterList(value) {
      this.$refs.transfer.$children[0].query = "";
    },
    // 随机生成64位随机字母（大小写）+数字
    generateRandomString() {
      this.form.appKey = random(64, { numeric: true, letters: true });
    },
  },
};
</script>
<style>
.el-transfer-panel {
  width: 348px;
}
</style>
<style lang="scss" scoped>
::v-deep .queryForm {
  .el-form-item {
    width: 100%;
    margin-right: 0;
  }
  .el-form-item__content {
    width: calc(100% - 80px);
  }
  .form-item-btn {
    .el-form-item__content {
      width: 100%;
    }
  }
}
</style>
