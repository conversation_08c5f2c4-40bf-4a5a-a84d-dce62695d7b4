<template>
  <div style="width: 100%; height: 100%">
    <div id="mainSankey" style="width: 100%; height: 100%"></div>
    <el-dialog :title="drawerInfo.title" :visible.sync="dialogVisible" width="800px" append-to-body
      :before-close="handleClose" :close-on-click-modal="false">
      <div class="black content" v-html="drawerInfo.content"></div>
      <div class="black">应用领域</div>
      <div class="tagsBox">
        <div class="tags" v-for="item in drawerInfo.tags">{{ item }}</div>
      </div>
      <div class="black">关联企业</div>
      <div class="enterprisesBox">
        <div class="enterprises" v-for="item in drawerInfo.enterprises">
          <div class="enterprisesName">{{ item.name }}</div>
          <div class="enterprisesUrl">{{ item.url }}</div>
        </div>
      </div>
      <div class="black">核心技术</div>
      <div class="tagsBox">
        <div class="tags" v-for="item in drawerInfo.tags">{{ item }}</div>
      </div>
      <div class="black">专家</div>
      <div class="tagsBox">
        <div class="tags" v-for="item in drawerInfo.tags">{{ item }}</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { achievementsResearch } from "@/api/bigScreen/index1";

export default {
  data() {
    return {
      myChart: null,
      option: {},
      color: ['#f18bbf', '#0078D7', '#3891A7', '#0037DA', '#964305', '#FEB80A', "#D34817", "#F89746", "#485FB5", "#744DA9", '"#744DA9"'],
      dialogVisible: false,
      drawerInfo: {
        content: "",
        title: "",
        tags: [],
        enterprises: []
      }
    };
  },
  mounted() {
    this.initChart();
    // this.init();
  },
  props: {
  },
  watch: {
  },
  components: {},
  methods: {
    async initChart() {
      let chartDom = document.getElementById("mainSankey");
      let data = await achievementsResearch()
      const allItems = [...data.data.map(edge => edge.source), ...data.data.map(edge => edge.target)]
      const uniqueSet = Array.from(new Set(allItems))
      this.myChart = echarts.init(chartDom);
      this.option = {
        series: {
          type: "sankey",
          layout: "none",
          left: 10.0,
          top: 10.0,
          right: 10.0,
          bottom: 10.0,
          nodeGap: 1,
          draggable: false,
          emphasis: {
            focus: "adjacency",
          },
          data: uniqueSet.map(item => {
            let color = this.color[Math.floor(Math.random() * 10)]
            return {
              name: item,
              itemStyle: {
                color: color,
                borderColor: color,
              },
            }
          }),
          links: data.data,
          levels: [
            {
              depth: 0,
              itemStyle: {
                color: "#fbb4ae",
              },
              lineStyle: {
                color: "source",
                opacity: 0.6,
              },
            },
            {
              depth: 1,
              itemStyle: {
                color: "#b3cde3",
              },
              lineStyle: {
                color: "source",
                opacity: 0.6,
              },
            },
            {
              depth: 2,
              itemStyle: {
                color: "#ccebc5",
              },
              lineStyle: {
                color: "source",
                opacity: 0.6,
              },
              label: {
                position: "left",
              },
            },
            {
              depth: 3,
              itemStyle: {
                color: "#decbe4",
              },
              lineStyle: {
                color: "source",
                opacity: 0.6,
              },
            },
          ],
          lineStyle: {
            curveness: 0.5,
          },
          label: {
            color: "rgba(255, 255, 255, 1)",
            backgroundColor: '#0A183600',
            borderWidth: 0,
            fontSize: 14,
          }
        },
      };
      //随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
      setTimeout(() => {
        this.myChart.resize();
      }, 1);
      this.myChart.setOption(this.option);
      this.myChart.on("click", (params) => {
        if (this.isLastLevel(params)) {
          this.openDialog(params)
        }
      });
    },
    isLastLevel(params) {
      const targetIndex = this.myChart.getOption().series[0].links.findIndex(link => link.target === params.name);
      const sourceIndex = this.myChart.getOption().series[0].links.findIndex(link => link.source === params.name);
      return sourceIndex === -1 && targetIndex !== -1;
    },
    openDialog(params) {
      this.dialogVisible = true
      this.drawerInfo = {
        content: "",
        title: params.name,
        tags: [],
        enterprises: []
      }
    },
    handleClose() {
      this.dialogVisible = false
      this.drawerInfo = {
        content: "",
        title: "",
        tags: [],
        enterprises: []
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener("resize", () => {
      this.myChart.resize();
    });
  },
};
</script>

<style lang="scss" scoped>
.black {
  background-color: #2B3041;
  padding: 5px 10px;
  margin-bottom: 10px;
}

::v-deep .el-dialog {
  background-color: #1E2335;
  height: 800px;

  .el-dialog__header {
    background-color: #1E2335;
    font-size: 30px;
    color: #ffffff;
    line-height: 45px;
    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);
    height: 60px;
    padding-left: 30px;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .el-dialog__body {
    background-color: #1E2335;
    color: #f2f2f2;
    height: calc(100% - 90px);
    overflow: auto;
    padding: 10px 20px;

    .content {
      line-height: 1.5;
      padding: 20px 10px;
    }

    .tagsBox {
      display: flex;
      flex-wrap: wrap;
      padding: 10px 20px;

      .tags {
        border: 1px dashed #539FFF;
        padding: 5px 10px;
        color: #539FFF;
        max-width: 180px;
        margin-right: 10px;
        margin-bottom: 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .enterprisesBox {
      padding: 10px 10px;

      .enterprises {
        background-color: #252A3C;
        height: 30px;
        line-height: 30px;
        display: flex;
        justify-content: space-between;
        padding: 0 10px;
        margin-bottom: 10px;

        .enterprisesName {
          margin-right: 10px;
          width: 50%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .enterprisesUrl {
          color: #539FFF;
          width: 50%;
          text-align: right;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .el-dialog__footer {
    background-color: #1d233400;
    padding: 0px 30px 0px 0;
  }

  .el-button {
    background-color: #002766;
    color: #fff;
    border: 0px;
  }

  .el-dialog__headerbtn .el-dialog__close {
    background: url("../../../assets/bigScreenTwo/关闭小.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
    width: 31px;
    height: 31px;
    top: -5px;

    &::before {
      content: none;
    }
  }
}
</style>
