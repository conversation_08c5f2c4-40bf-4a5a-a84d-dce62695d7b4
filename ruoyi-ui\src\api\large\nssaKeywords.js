import request from '@/utils/request'

// 查询信通院大屏 网络安全态势感知 检索词库列表
export function listNssaKeywords(query) {
  return request({
    url: '/large/nssaKeywords/list',
    method: 'get',
    params: query
  })
}

// 检索词库列表（排除节点）
export function excludeChild(deptId) {
  return request({
    url: '/large/nssaKeywords/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询信通院大屏 网络安全态势感知 检索词库详细
export function getNssaKeywords(id) {
  return request({
    url: '/large/nssaKeywords/' + id,
    method: 'get'
  })
}

// 新增信通院大屏 网络安全态势感知 检索词库
export function addNssaKeywords(data) {
  return request({
    url: '/large/nssaKeywords',
    method: 'post',
    data: data
  })
}

// 修改信通院大屏 网络安全态势感知 检索词库
export function updateNssaKeywords(data) {
  return request({
    url: '/large/nssaKeywords/edit',
    method: 'post',
    data: data
  })
}

// 删除信通院大屏 网络安全态势感知 检索词库
export function delNssaKeywords(id) {
  let data = new FormData()
  data.append('id', id)
  return request({
    url: '/large/nssaKeywords/remove',
    method: 'post',
    data: data
  })
}
