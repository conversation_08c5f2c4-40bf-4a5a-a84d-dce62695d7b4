<template>
  <div class="work">
    <div class="topMain">
      <div class="toolBox">
        <div class="mainTool">
          <p>
            时间范围:
            <el-radio-group v-model="queryParams.dateType" size="small">
              <el-radio-button :label="0">24小时</el-radio-button>
              <el-radio-button :label="1">今天</el-radio-button>
              <el-radio-button :label="2">近2天</el-radio-button>
              <el-radio-button :label="4">近7天</el-radio-button>
            </el-radio-group>
          </p>
          <p>
            数据源名称:
            <el-input
              placeholder="请输入数据源名称"
              v-model="queryParams.sourceName"
              style="width: 200px"
            >
            </el-input>
          </p>
          <p>
            文章所属领域:
            <el-input
              placeholder="请输入文章所属领域"
              v-model="queryParams.domain"
              style="width: 200px"
            >
            </el-input>
          </p>
          <div class="keyword">
            <span
              style="
                width: 60px;
                display: inline-block;
                text-align: right;
                margin-right: 5px;
              "
              >关键词:</span
            >
            <el-input
              ref="keywordRef"
              placeholder="请输入关键词,使用逗号分割(英文)"
              style="width: 200px"
              v-model="queryParams.keywords"
              @focus="showHistoryList()"
              @blur="hideHistoryList()"
            >
            </el-input>
            <el-button
              type="primary"
              size="mini"
              @click="funEsSeach()"
              style="margin-left: 10px; height: 36px"
              >搜索</el-button
            >
            <div class="history" v-show="showHistory">
              <div
                class="historyItem"
                v-for="(history, index) in historyList"
                :key="index"
                v-loading="historyLoading"
              >
                <div @click="keywordsChange(history)" class="historyText">
                  {{ history.keyword }}
                </div>
                <el-button
                  type="text"
                  @click="removeHistory(history, 1)"
                  style="color: #999; font-size: 12px"
                  >删除</el-button
                >
              </div>
              <div class="historyItem">
                <el-button type="text" @click="moreHistory()">更多</el-button>
                <el-button
                  type="text"
                  @click="clearHistory()"
                  style="color: #999; font-size: 12px"
                  >清空</el-button
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <MainArticle
        ref="MainArticle"
        v-loading="tableLoading"
        :flag="'Wechat'"
        :currentPage="queryParams.pageNum"
        :pageSize="queryParams.pageSize"
        :total="total"
        :ArticleList="ArticleList"
        @handleCurrentChange="handleCurrentChange"
        @handleSizeChange="handleSizeChange"
        @Refresh="queryArticleList"
        :SeachData="SeachData"
        :sourceType="'1'"
      >
      </MainArticle>
    </div>
    <div class="botMain">
      <!-- 批量操作按钮区域 -->
      <div class="batch-operations">
        <el-button
          type="primary"
          size="small"
          :disabled="!selectedBrief || getSelectedArticles().length === 0"
          @click="handleBatchAdd"
        >
          批量添加
        </el-button>
        <el-button
          type="danger"
          size="small"
          :disabled="!selectedBrief || selectedBriefArticles.length === 0"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
      </div>

      <!-- 下方主要内容区域 -->
      <div class="bottom-content">
        <!-- 左侧简报树 -->
        <div class="left-panel">
          <div class="panel-title">简报列表</div>
          <el-tree
            ref="briefTree"
            :data="briefTreeData"
            :props="treeProps"
            node-key="id"
            :expand-on-click-node="false"
            :highlight-current="true"
            :default-expanded-keys="['root', 'unpublished', 'published']"
            @node-click="handleBriefSelect"
            v-loading="briefTreeLoading"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>{{ node.label }}</span>
            </span>
          </el-tree>
        </div>

        <!-- 右侧文章表格 -->
        <div class="right-panel">
          <div class="panel-title">
            {{
              selectedBrief ? `${selectedBrief.label} - 文章列表` : "请选择简报"
            }}
          </div>
          <el-table
            ref="briefArticleTable"
            :data="briefArticleList"
            v-loading="briefArticleLoading"
            @selection-change="handleBriefArticleSelectionChange"
            height="calc(100% - 40px)"
            style="width: 100%"
          >
            <el-table-column
              type="selection"
              width="55"
              align="center"
            ></el-table-column>
            <el-table-column
              label="序号"
              type="index"
              width="60"
              align="center"
            ></el-table-column>
            <el-table-column
              label="文章标题"
              prop="title"
              min-width="200"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span
                  class="article-title-link"
                  @click="viewArticle(scope.row)"
                >
                  {{ scope.row.title }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              label="作者"
              prop="author"
              width="120"
              align="center"
            ></el-table-column>
            <el-table-column
              label="发布时间"
              prop="publishTime"
              width="160"
              align="center"
            >
              <template slot-scope="scope">
                {{ formatDate(scope.row.publishTime) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <el-dialog
      title="关键词历史"
      :visible.sync="dialogVisible1"
      width="570px"
      :close-on-click-modal="false"
    >
      <div class="history" v-loading="historyLoading">
        <div
          class="historyItem"
          v-for="(history, index) in historyList1"
          :key="index"
        >
          <div @click="keywordsChange(history)" class="historyText">
            {{ history.keyword }}
          </div>
          <el-button type="text" @click="removeHistory(history, 2)"
            >删除</el-button
          >
        </div>
      </div>
      <pagination
        v-show="total1 > 0"
        :total="total1"
        :page.sync="queryParams1.pageNum"
        :limit.sync="queryParams1.pageSize"
        :background="false"
        @pagination="getArticleHistory1"
        :layout="'total, prev, pager, next'"
      />
    </el-dialog>
  </div>
</template>

<script>
import { listWork } from "@/api/article/work";
import API from "@/api/ScienceApi/briefing.js";
import MainArticle from "../../components/MainArticle.vue";
import { mapGetters } from "vuex";
import {
  listArticleHistory,
  delArticleHistory,
  addArticleHistory,
  cleanArticleHistory,
} from "@/api/article/articleHistory";

export default {
  components: { MainArticle },
  dicts: ["is_technology"],
  data() {
    return {
      loading: false,
      tableLoading: false, // 表格loading状态
      queryParams: {
        id: 100,
        pageNum: 1,
        pageSize: 50,
        dateType: 4,
        keywords: "",
        sourceName: "",
        domain: "",
      },
      SeachData: {
        sortMode: false,
      },
      total: 0,
      ArticleList: [], // 列表数据
      showSummary: true,
      /* 初始化完成标记 */
      initializationCompleted: false,
      /* 查询防抖 */
      queryDebounceTimer: null,
      /* 防止重复查询 */
      isQuerying: false,
      showHistory: false,
      historyList: [],
      historyTimeout: null,
      dialogVisible1: false,
      historyLoading: false,
      queryParams1: {
        pageNum: 1,
        pageSize: 10,
      },
      total1: 0,
      historyList1: [],

      // 简报相关数据
      briefTreeData: [],
      briefTreeLoading: false,
      treeProps: {
        children: "children",
        label: "label",
      },
      selectedBrief: null,

      // 简报文章列表相关数据
      briefArticleList: [],
      briefArticleLoading: false,
      selectedBriefArticles: [],
    };
  },
  watch: {
    // 监听筛选条件变化
    "queryParams.dateType": {
      handler(newVal, oldVal) {
        if (!this.initializationCompleted || newVal === oldVal) return;
        this.handleFilterChange();
      },
    },
  },
  computed: {
    ...mapGetters(["roles"]),
    // 获取当前登录用户信息
    currentUserInfo() {
      return JSON.parse(window.sessionStorage.getItem("userInfo") || "{}");
    },
    // 判断当前用户是否为主编
    isChiefEditor() {
      return this.roles.includes("chief");
    },
    // 判断当前用户是否为编辑
    isEditor() {
      return this.roles.includes("editor");
    },
  },
  async created() {
    try {
      this.getArticleHistory();
      // 加载树数据和内容数据
      await this.initializeData();
      // 加载简报树数据
      await this.getBriefTreeData();

      if (this.roles.includes("information")) {
        this.showSummary = false;
      }

      // 标记初始化完成，这样watch监听器才会开始工作
      this.initializationCompleted = true;
    } catch (error) {
      console.error("组件初始化失败:", error);
      this.$message.error("初始化失败，请刷新页面重试");
    }
  },

  mounted() {
    // TreeTable 组件不需要特殊的状态检查
  },
  methods: {
    // 初始化数据
    async initializeData() {
      try {
        // 再加载文章列表（内部已经处理了 tableLoading）
        await this.queryArticleList();
      } catch (error) {
        console.error("初始化数据失败:", error);
        this.$message.error("初始化失败，请刷新页面重试");
      }
    },

    // 处理筛选条件变化
    handleFilterChange() {
      // 重置分页到第一页
      this.queryParams.pageNum = 1;
      // 滚动到顶部
      this.scrollToTopImmediately();
      // 同时查询树和列表
      this.queryArticleList();
    },
    // 查询文章列表（带防抖）
    async queryArticleList() {
      // 防止重复查询
      if (this.isQuerying) {
        return;
      }

      // 立即显示loading状态
      this.tableLoading = true;

      // 清除之前的防抖定时器
      if (this.queryDebounceTimer) {
        clearTimeout(this.queryDebounceTimer);
      }

      // 设置防抖，300ms后执行查询
      this.queryDebounceTimer = setTimeout(async () => {
        try {
          this.isQuerying = true;

          const params = {
            ...this.queryParams,
            ...this.SeachData,
            platformType: 0,
          };
          if (params.keywords) {
            addArticleHistory({ keyword: params.keywords, type: 2 }).then(
              () => {
                this.getArticleHistory();
              }
            );
          }

          const res = await listWork(params);

          if (res.tableDataInfo && res.tableDataInfo.code == 200) {
            this.ArticleList = res.tableDataInfo.rows;
            this.total = res.tableDataInfo.total;

            // 处理分页为空的情况
            if (
              this.ArticleList.length == 0 &&
              this.queryParams.pageSize * (this.queryParams.pageNum - 1) >=
                this.total &&
              this.total != 0
            ) {
              this.queryParams.pageNum = Math.max(
                1,
                Math.ceil(this.total / this.queryParams.pageSize)
              );
              // 重新查询
              await this.queryArticleList();
              return; // 重新查询时不要关闭loading
            }
          } else {
            this.$message.error(res.msg || "获取数据失败");
          }
        } catch (error) {
          console.error("查询文章列表失败:", error);
          this.$message.error("查询失败，请重试");
        } finally {
          this.isQuerying = false;
          this.tableLoading = false; // 查询完成后关闭loading
        }
      }, 300);
    },
    // 滚动到顶部
    scrollToTopImmediately() {
      this.$nextTick(() => {
        // 尝试多种滚动方式确保滚动成功
        const scrollBoxElement = document.querySelector(".scollBox");
        if (scrollBoxElement) {
          scrollBoxElement.scrollTop = 0;
        }

        // 如果MainArticle组件有scroll引用，也尝试滚动它
        if (
          this.$refs.mainArticle &&
          this.$refs.mainArticle.$refs &&
          this.$refs.mainArticle.$refs.scroll
        ) {
          this.$refs.mainArticle.$refs.scroll.scrollTop = 0;
        }

        // 滚动整个右侧区域
        const rightMain = document.querySelector(".rightMain");
        if (rightMain) {
          rightMain.scrollTop = 0;
        }
      });
    },
    // 保留现有的分页处理方法
    handleCurrentChange(current) {
      try {
        this.queryParams.pageNum = current;
        // 仅加载内容，不更新左侧树
        this.queryArticleList();

        // 分页后也应该滚动到顶部
        this.$nextTick(() => {
          this.scrollToTopImmediately();
        });
      } catch (error) {
        console.error("处理页码变化时出错:", error);
        this.$message.error("页面切换失败，请重试");
      }
    },
    // 处理每页数量变化
    handleSizeChange(pageSize) {
      try {
        this.queryParams.pageSize = pageSize;
        this.queryParams.pageNum = 1; // 切换每页数量时，重置为第一页
        // 仅加载内容，不更新左侧树
        this.queryArticleList();

        // 分页后也应该滚动到顶部
        this.$nextTick(() => {
          this.scrollToTopImmediately();
        });
      } catch (error) {
        console.error("处理每页大小变化时出错:", error);
        this.$message.error("设置页面大小失败，请重试");
      }
    },
    async getArticleHistory() {
      try {
        const res = await listArticleHistory({
          pageNum: 1,
          pageSize: 5,
          type: 2,
        });
        if (res && res.code === 200) {
          this.historyList = res.rows || [];
        }
      } catch (error) {
        console.error("获取历史记录失败:", error);
      }
    },
    async removeHistory(item, type) {
      try {
        if (this.historyTimeout) {
          clearTimeout(this.historyTimeout);
        }

        if (item && item.id) {
          await delArticleHistory([item.id]);

          if (type == 1) {
            if (this.$refs["keywordRef"]) {
              this.$refs["keywordRef"].focus();
            }
            await this.getArticleHistory();
          } else {
            await this.getArticleHistory();
            await this.getArticleHistory1();
          }
        }
      } catch (error) {
        console.error("删除历史记录时出错:", error);
        this.$message.error("删除历史记录失败");
      }
    },

    showHistoryList() {
      try {
        this.showHistory = true;
      } catch (error) {
        console.error("显示历史列表时出错:", error);
      }
    },

    hideHistoryList() {
      try {
        if (this.historyTimeout) {
          clearTimeout(this.historyTimeout);
        }

        this.historyTimeout = setTimeout(() => {
          this.showHistory = false;
        }, 500);
      } catch (error) {
        console.error("隐藏历史列表时出错:", error);
        this.showHistory = false; // 确保在出错时也能隐藏列表
      }
    },

    // 关键词历史选择 - 直接从Wechat.vue复制
    keywordsChange(item) {
      this.queryParams.keywords = item.keyword;
      this.dialogVisible1 = false;
      this.scrollToTopImmediately();
      this.queryParams.pageNum = 1;
      this.queryArticleList();
    },

    async clearHistory() {
      try {
        if (this.historyTimeout) {
          clearTimeout(this.historyTimeout);
        }

        if (this.$refs["keywordRef"]) {
          this.$refs["keywordRef"].focus();
        }

        await cleanArticleHistory(2);
        await this.getArticleHistory();
      } catch (error) {
        console.error("清除历史记录时出错:", error);
        this.$message.error("清除历史记录失败");
      }
    },

    moreHistory() {
      try {
        this.historyLoading = true;
        this.getArticleHistory1();
        this.dialogVisible1 = true;
      } catch (error) {
        console.error("加载更多历史记录时出错:", error);
        this.historyLoading = false;
      }
    },

    async getArticleHistory1() {
      try {
        this.historyLoading = true;
        const response = await listArticleHistory({
          ...this.queryParams1,
          type: 2,
        });

        if (response) {
          this.historyList1 = response.rows || [];
          this.total1 = response.total || 0;
        }

        this.historyLoading = false;
      } catch (error) {
        console.error("获取文章历史记录时出错:", error);
        this.historyLoading = false;
        this.$message.error("获取搜索历史失败");
      }
    },
    funEsSeach() {
      this.scrollToTopImmediately();
      this.queryParams.pageNum = 1;
      this.queryArticleList();
    },

    // ========== 简报相关方法 ==========

    // 获取简报树数据
    async getBriefTreeData() {
      try {
        this.briefTreeLoading = true;

        // 构建查询参数
        const baseParams = {};

        // 只有当当前登录用户的角色是主编和编辑的情况下才需要传editors参数
        if (this.isChiefEditor || this.isEditor) {
          baseParams.editors = this.currentUserInfo.userId;
        }

        // 查询待发布简报（reportStatus="1"，不传pageNum和pageSize）
        const unpublishedParams = {
          ...baseParams,
          reportStatus: "1",
        };

        // 查询已发布简报（reportStatus="2"，pageNum=1，pageSize=10）
        const publishedParams = {
          ...baseParams,
          reportStatus: "2",
          pageNum: 1,
          pageSize: 10,
        };

        // 并行查询两个接口
        const [unpublishedRes, publishedRes] = await Promise.all([
          API.queryReportList(unpublishedParams),
          API.queryReportList(publishedParams),
        ]);

        // 处理待发布简报数据
        const unpublishedBriefs = [];
        if (unpublishedRes && unpublishedRes.code === 200) {
          unpublishedBriefs.push(
            ...(unpublishedRes.rows || []).map((item) => ({
              id: item.id,
              label: item.title,
              reportStatus: "1",
            }))
          );
        }

        // 处理已发布简报数据
        const publishedBriefs = [];
        if (publishedRes && publishedRes.code === 200) {
          publishedBriefs.push(
            ...(publishedRes.rows || []).map((item) => ({
              id: item.id,
              label: item.title,
              reportStatus: "2",
            }))
          );
        }

        // 构建树形数据
        const treeData = [
          {
            id: "root",
            label: "我的工作台",
            children: [
              {
                id: "unpublished",
                label: "待发布简报",
                children: unpublishedBriefs,
              },
              {
                id: "published",
                label: "已发布简报",
                children: publishedBriefs,
              },
            ],
          },
        ];

        this.briefTreeData = treeData;

        // 使用 default-expanded-keys 属性自动展开节点，不需要手动操作
      } catch (error) {
        console.error("获取简报树数据失败:", error);
        this.$message.error("获取简报列表失败");
      } finally {
        this.briefTreeLoading = false;
      }
    },

    // 处理简报选择
    handleBriefSelect(data) {
      // 只有叶子节点（具体的简报）才能被选中
      if (data.id && data.reportStatus) {
        this.selectedBrief = data;
        this.getBriefArticleList();
      }
    },

    // 获取简报文章列表
    async getBriefArticleList() {
      if (!this.selectedBrief) return;

      try {
        this.briefArticleLoading = true;

        // 真实API调用（目前使用模拟数据）
        // const res = await getBriefArticles(this.selectedBrief.id);
        // if (res && res.code === 200) {
        //   this.briefArticleList = res.data;
        // } else {
        //   throw new Error(res.msg || '获取数据失败');
        // }

        // 临时模拟数据 - 显示所有文章，不分页
        const mockArticles = [];
        const total = this.selectedBrief.articleCount || 0;

        for (let i = 0; i < total; i++) {
          mockArticles.push({
            id: i + 1,
            title: `${this.selectedBrief.label} - 文章标题 ${i + 1}`,
            author: `作者${(i % 5) + 1}`,
            publishTime: new Date(
              Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
            ).toISOString(),
          });
        }

        this.briefArticleList = mockArticles;
      } catch (error) {
        console.error("获取简报文章列表失败:", error);
        this.$message.error("获取文章列表失败");
      } finally {
        this.briefArticleLoading = false;
      }
    },

    // 处理简报文章选择变化
    handleBriefArticleSelectionChange(selection) {
      this.selectedBriefArticles = selection;
    },

    // ========== 批量操作方法 ==========

    // 批量添加文章到简报
    async handleBatchAdd() {
      if (!this.selectedBrief) {
        this.$message.warning("请先选择一个简报");
        return;
      }

      const selectedArticles = this.getSelectedArticles();
      if (selectedArticles.length === 0) {
        this.$message.warning("请先在上方勾选要添加的文章");
        return;
      }

      try {
        const articleIds = selectedArticles.map((article) => article.id);

        // 确认对话框
        const confirmResult = await this.$confirm(
          `确定要将选中的 ${articleIds.length} 篇文章添加到"${this.selectedBrief.label}"吗？`,
          "批量添加确认",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        );

        if (confirmResult) {
          // 真实API调用（目前使用模拟数据）
          // const res = await addArticlesToBrief({
          //   briefId: this.selectedBrief.id,
          //   articleIds: articleIds
          // });
          // if (res && res.code !== 200) {
          //   throw new Error(res.msg || '添加失败');
          // }

          // 模拟成功
          this.$message.success(`成功添加 ${articleIds.length} 篇文章到简报`);

          // 刷新简报文章列表
          await this.getBriefArticleList();
          // 清空上方文章选择
          this.clearTopArticleSelection();
          // 刷新简报树（更新文章数量）
          await this.getBriefTreeData();
        }
      } catch (error) {
        if (error !== "cancel") {
          console.error("批量添加文章失败:", error);
          this.$message.error("添加文章失败，请重试");
        }
      }
    },

    // 批量删除简报中的文章
    async handleBatchDelete() {
      if (!this.selectedBrief) {
        this.$message.warning("请先选择一个简报");
        return;
      }

      if (this.selectedBriefArticles.length === 0) {
        this.$message.warning("请先勾选要删除的文章");
        return;
      }

      try {
        const articleIds = this.selectedBriefArticles.map(
          (article) => article.id
        );

        // 确认对话框
        const confirmResult = await this.$confirm(
          `确定要从"${this.selectedBrief.label}"中删除选中的 ${articleIds.length} 篇文章吗？`,
          "批量删除确认",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        );

        if (confirmResult) {
          // 真实API调用（目前使用模拟数据）
          // const res = await removeArticlesFromBrief({
          //   briefId: this.selectedBrief.id,
          //   articleIds: articleIds
          // });
          // if (res && res.code !== 200) {
          //   throw new Error(res.msg || '删除失败');
          // }

          // 模拟成功
          this.$message.success(`成功删除 ${articleIds.length} 篇文章`);

          // 刷新简报文章列表
          await this.getBriefArticleList();
          // 刷新简报树（更新文章数量）
          await this.getBriefTreeData();
        }
      } catch (error) {
        if (error !== "cancel") {
          console.error("批量删除文章失败:", error);
          this.$message.error("删除文章失败，请重试");
        }
      }
    },

    // ========== 辅助方法 ==========

    // 获取上方文章列表中选中的文章
    getSelectedArticles() {
      if (this.$refs.MainArticle && this.$refs.MainArticle.checkedCities) {
        const selectedIds = this.$refs.MainArticle.checkedCities;
        return this.ArticleList.filter((article) =>
          selectedIds.includes(article.id)
        );
      }
      return [];
    },

    // 清空上方文章选择
    clearTopArticleSelection() {
      if (this.$refs.MainArticle) {
        this.$refs.MainArticle.checkedCities = [];
        this.$refs.MainArticle.checked = false;
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return "";
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    },

    // 查看文章详情
    viewArticle(article) {
      // 这里可以实现文章详情查看逻辑
      console.log("查看文章:", article);
      this.$message.info(`查看文章: ${article.title}`);
    },
  },
};
</script>

<style lang="scss" scoped>
.work {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100vh - 56px);
}

.topMain {
  flex: 1;
  overflow: hidden;
}

.botMain {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 10px;
}

.batch-operations {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.bottom-content {
  flex: 1;
  display: flex;
  gap: 10px;
  overflow: hidden;
}

.left-panel {
  width: 300px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.right-panel {
  flex: 1;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel-title {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e6e6e6;
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.left-panel .el-tree {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.article-count {
  color: #999;
  font-size: 12px;
}

.article-title-link {
  color: #409eff;
  cursor: pointer;
  text-decoration: none;
}

.article-title-link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

::v-deep .drawer_Title {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

::v-deep .drawer_Style {
  z-index: 2;
  margin: 0 15px 0 15px;
  width: 661px;
  height: 80vh;

  .title {
    font-size: 16px;
    font-weight: 500px;
    text-align: center;
  }

  .source {
    color: #0798f8;
    text-align: center;
    font-size: 14px;
  }

  .time {
    font-size: 14px;
    text-align: center;
    margin-left: 10px;
    color: #9b9b9b;
  }
}

::v-deep .el-icon-document:before {
  color: #5589f5;
}

::v-deep .el-table td.el-table__cell div {
  padding-left: 10px;
}

::v-deep .el-table-column--selection .cell {
  padding-right: 0px;
  padding-left: 14px !important;
  margin-left: 5px;
}

::v-deep .el-form-item {
  margin-bottom: 10px;
}

::v-deep .el-table--medium .el-table__cell {
  padding: 10px 0;
}

.article_title {
  margin-left: 10px;
  font-size: 15px;
}

.article_title:hover {
  color: #1889f3;
  border-bottom: solid 1px #0798f8;
  cursor: pointer;
}

.toolBox {
  width: 100%;

  .title {
    display: flex;
    justify-content: space-between;
    height: 70px;
    padding: 0 30px;
    font-size: 19px;
  }

  .mainTool {
    padding: 0 28px;
    font-size: 14px;
    color: rgb(58, 58, 58);
    display: flex;
    align-items: center;
    column-gap: 20px;
    width: 100%;
  }

  .mainToolOne {
    margin-top: 15px;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    // align-items: center;
  }

  .mainToolTwo {
    display: flex;
    align-items: center;
    height: 40px;

    p {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .btn {
    margin: 15px 0 0 25px;
  }
}

.keyword {
  position: relative;

  .history {
    width: 200px;
    position: absolute;
    background: #fff;
    z-index: 9999;
    left: 65px;
    border: 1px solid rgb(221, 219, 219);

    .historyItem {
      padding-left: 20px;

      .historyText {
        width: 450px;
        height: 34px;
        line-height: 34px;
      }

      &:nth-last-of-type(1) {
        padding-left: 0;

        ::v-deep .el-button--text {
          padding: 10px 20px;
        }
      }
    }
  }
}

.history {
  .historyItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    padding: 0 10px;
    overflow: hidden;

    .historyText {
      width: 350px;
      height: 34px;
      line-height: 34px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
  }
}
</style>
