/**
 * hex颜色转rgb颜色
 * @param { String } str 颜色值,如 #409EFF 或 409EFF
 * @returns rgb颜色
 */
export function hexToRgb(str) {
  str = str.replace("#", "");
  let hexs = str.match(/../g);
  for (let i = 0; i < 3; i++) {
    hexs[i] = parseInt(hexs[i], 16);
  }
  return hexs;
}

/**
 * rgb颜色转hex颜色
 * @param {Number} r 通道值r
 * @param {Number} g 通道值g
 * @param {Number} b 通道值b
 * @returns hex颜色
 */
export function rgbToHex(r, g, b) {
  let hexs = [r.toString(16), g.toString(16), b.toString(16)];
  for (let i = 0; i < 3; i++) {
    if (hexs[i].length == 1) {
      hexs[i] = `0${hexs[i]}`;
    }
  }
  return `#${hexs.join("")}`;
}

/**
 * 变浅颜色值
 * @param {String} color hex颜色
 * @param {Number} level 程度 0 - 1 之间
 * @returns hex颜色
 */
export function getLightColor(color, level) {
  let rgb = hexToRgb(color);
  for (let i = 0; i < 3; i++) {
    rgb[i] = Math.floor((255 - rgb[i]) * level + rgb[i]);
  }
  return rgbToHex(rgb[0], rgb[1], rgb[2]);
}

/**
 * 变深颜色值
 * @param {String} color hex颜色
 * @param {Number} level 程度 0 - 1 之间
 * @returns hex颜色
 */
export function getDarkColor(color, level) {
  let rgb = hexToRgb(color);
  for (let i = 0; i < 3; i++) {
    rgb[i] = Math.floor(rgb[i] * (1 - level));
  }
  return rgbToHex(rgb[0], rgb[1], rgb[2]);
}
