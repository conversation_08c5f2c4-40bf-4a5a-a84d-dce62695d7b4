import request from "@/utils/request";

// 查询资料模板列表
export function listTemplate(query) {
  return request({
    url: "/screen/template/list",
    method: "get",
    params: query,
  });
}

// 查询资料模板详细
export function getTemplate(id) {
  return request({
    url: "/screen/template/" + id,
    method: "get",
  });
}

// 新增资料模板
export function addTemplate(data) {
  return request({
    url: "/screen/template",
    method: "post",
    data: data,
  });
}

// 修改资料模板
export function updateTemplate(data) {
  return request({
    url: "/screen/template/edit",
    method: "post",
    data: data,
  });
}

// 删除资料模板
export function delTemplate(id) {
  return request({
    url: "/screen/template/remove",
    method: "post",
    data: id,
  });
}
