<template>
  <div
    :class="['title', { title2: type == 2 }]"
    :style="{ width: width, height: '32px' }"
  >
    {{ title }}
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    width: {
      default: "520px",
    },
    title: {
      required: true,
      default: "",
    },
    type: {
      default: 1,
    },
  },
  computed: {},
  watch: {},
  data() {
    return {};
  },
  created() {},

  methods: {},
};
</script>

<style lang="scss" scoped>
.title {
  background: url("../../../assets/bigScreenThree/title.png") -3px 0px no-repeat;
  background-size: 100% 100% !important;
  background-size: cover;
  font-family: "pingFangMedium";
  font-size: 18px;
  color: #ffffff;
  line-height: 32px;
  letter-spacing: 2px;
  text-align: left;
  font-style: normal;
  padding-left: 35px;
}
.title2 {
  background: url("../../../assets/bigScreenThree/title-long.png") -3px 0px no-repeat;
}
</style>
