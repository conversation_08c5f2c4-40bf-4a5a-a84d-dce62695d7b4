<template>
  <div class="table">
    <div class="table_main" ref="scrollTableBox">
      <div ref="scrollTable" :class="{ 'scoll-Table1': isScrollTable }"
        :style="{ '--dynamic-height': `-${dynamicHeight}px`, '--dynamic-time': `${dynamicHeight / 40}s` }">
        <div class="table_col" v-for="(item, index) in hotList" :key="index" @click="jumpFun(item.reportUrl)">
          <!-- @click="expertFun(item)" -->
          <el-tooltip class="item" effect="dark" :content="item.reportName" placement="top-start">
            <div class="last-child">
              {{ item.reportName }}
            </div>
          </el-tooltip>
          <div class="table-main-content">
            {{ item.viewpoint }}
          </div>
          <div class="item-bottom">
            <div style="width: 100px">{{ parseTime(item.reportDate, "{y}-{m}-{d}") }}</div>
            <div style="flex: 1; text-align: center">{{ item.name }}</div>
            <div style="width: 100px; text-align: right">
              {{ item.nickName }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isScrollTable: false,
      dynamicHeight: 0,
      hotList: [
        {
          reportName: "「财经分析」新型人机关系带来社会负面问题 需产业共同助推技术向善发展",
          viewpoint: '人工智能为人类未来的发展带来很多的想象，新技术为整个人类社会带来的最大的价值，是把人与知识之间的距离缩得更短了，并提升了弥合技术鸿沟的可能性。',
          reportDate: "2024-11-11 ",
          name: "新华财经",
          nickName: "陈妍",
          reportUrl: "https://baijiahao.baidu.com/s?id=1815417576499771752&wfr=spider&for=pc"
        },
        {
          reportName: "需要关注人工智能对宏观经济和金融稳定可能产生的系统性影响",
          viewpoint: '需要关注人工智能对宏观经济和金融稳定可能产生的系统性影响.采用“监管沙盒”模式，即允许机构在监管部门的监督下进行创新试验，并及时评估潜在风险。',
          reportDate: "2024-11-11 ",
          name: " 21世纪经济报道",
          nickName: "黄益平",
          reportUrl: "https://news.10jqka.com.cn/20241111/c663407993.shtml"
        },
        {
          reportName: "更好把握人工智能发展趋势",
          viewpoint: '我们应当积极推进国际交流与合作，携手应对人类共同挑战，让人工智能技术发展更安全、更可靠、更可信，推动人工智能更好增进人类福祉。',
          reportDate: "2024-11-04 ",
          name: "人民日报",
          nickName: "龚克",
          reportUrl: "https://baijiahao.baidu.com/s?id=1814740617189717257&wfr=spider&for=pc"
        },
        {
          reportName: "2024人工智能十大前沿技术趋势展望发布",
          viewpoint: '它们都充满了无限可能和潜力，不仅将带来更加便捷、高效的生活方式，还将推动各行各业的创新和发展。',
          reportDate: "2024-10-28 ",
          name: "广东省科学技术厅",
          nickName: "乔红",
          reportUrl: "https://gdstc.gd.gov.cn/kjzx_n/mtjj/content/post_4514234.html"
        },
      ], //滚动
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.isScrollTable = this.$refs.scrollTableBox.offsetHeight < this.$refs.scrollTable.offsetHeight;
      this.dynamicHeight = this.$refs.scrollTable.offsetHeight - this.$refs.scrollTableBox.offsetHeight + 20;
    });
  },

  props: {},
  watch: {},

  components: {},
  methods: {
    expertFun(item) {
      this.$emit("expertFun", item);
    },
    jumpFun(url) {
      if (url) {
        window.open(url, "_blank");
      }
    }
  },
  beforeDestroy() {

  },
};
</script>

<style lang="scss">
.table {
  padding: 12px 30px 30px 17px;
  width: 100%;
  margin: 0 auto;
  height: 580px;

  .table_main {
    width: 100%;
    height: calc(100%);
    overflow: hidden;
    z-index: 0;

    .table_col {
      width: 100%;
      // height: 80px;
      background: url("../../../assets/bigScreenFour/border.png") no-repeat 0px 0px !important;
      background-size: 100% 100% !important;
      padding: 0 31px 20px;
      font-size: 14px;
      margin-bottom: 10px;
      cursor: pointer;


      .last-child {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-top: 16px;
        font-size: 16px;
        font-weight: bold;

        color: #e6f7ff;
        font-style: normal;
      }

      .table-main-content {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        text-overflow: ellipsis;
        text-indent: 2em;
        margin-top: 5px;
        line-height: 1.5;
      }

      .item-bottom {
        margin-top: 8px;
        display: flex;
        font-family: "pingFangMedium";
        font-weight: 300;
        font-size: 14px;
        color: #e6f7ff;
        font-style: normal;
        justify-content: space-between;
      }
    }

    @keyframes scoll-Table1 {
      from {
        transform: translate(0, 0px);
      }

      to {
        transform: translate(0, calc(var(--dynamic-height)));
      }
    }

    .scoll-Table1 {
      animation: scoll-Table1 var(--dynamic-time) linear infinite;
      transition: all 1s ease-out;
      //  animation-timing-function: linear;
      // animation-fill-mode: forwards;
      /* 在动画结束后保持最后一个关键帧的状态 */
    }

    /* 鼠标进入 */
    .scoll-Table1:hover {
      animation-play-state: paused;
    }

    /* 鼠标离开 */
    .scoll-Table1:not(:hover) {
      animation-play-state: running;
    }
  }
}
</style>
