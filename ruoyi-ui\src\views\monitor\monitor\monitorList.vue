<template>
  <div>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="82px">
      <el-form-item v-if="title == '停更数据量' || title == '近5日未发布文章的公众号' || title == '公众号异常数量'" style="margin-left: 25px;"
        label="公众号名称" prop="sourceName">
        <el-input style="width: 160px;" v-model="queryParams.sourceName" placeholder="请输入公众号名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item v-if="title == '停更数据量' || title == '近5日未发布文章的公众号' || title == '公众号异常数量'" label="发布时间"
        prop="lastArticlePublishTime">
        <el-date-picker clearable v-model="queryParams.lastArticlePublishTime" type="daterange" value-format="yyyy-MM-dd"
          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px;">
        </el-date-picker>
      </el-form-item>
      <el-form-item v-if="title == '近5日未发布文章的公众号'" label="是否异常" prop="isException">
        <el-select style="width: 140px;" v-model="queryParams.isException" placeholder="请选择是否异常" clearable>
          <el-option :label="'异常'" :value="'1'" />
          <el-option :label="'正常'" :value="'0'" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="title == '公众号异常数量'" label="发布频次" prop="publishFrequency">
        <el-input style="width: 120px;" v-model="queryParams.publishFrequency" placeholder="请输入频次" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="采集时间" prop="lastArticleCollectionTime">
        <el-date-picker clearable v-model="queryParams.lastArticleCollectionTime" type="daterange"
          value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
          style="width: 240px;">
        </el-date-picker>
      </el-form-item> -->

      <el-form-item v-if="title != '最长发布间隔'">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="monitorList">
      <!-- <el-table-column width="80" label="序号" align="center" prop="id" /> -->
      <el-table-column label="公众号名称" prop="wechatName" min-width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span style="color: #40A9FF;;cursor: pointer;" @click="openWachatList(scope.row)">
            {{ scope.row.wechatName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="最新一篇文章" prop="title" min-width="300" show-overflow-tooltip>
        <template slot-scope="scope">
          <span style="color: #40A9FF;;cursor: pointer;" @click="openNewView(scope.row)">{{ scope.row.title }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文章发布时间" align="center" prop="publishTime" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="title != '停更数据量'" label="文章采集时间" align="center" prop="createTime" width="180"
        show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="title == '公众号异常数量'" label="官方发布频次" align="center" prop="publishFrequency"
        show-overflow-tooltip width="140" />
      <el-table-column v-if="title == '公众号异常数量'" label="日期间隔" align="center" prop="intervalDays" show-overflow-tooltip
        width="100" />
      <el-table-column v-if="title == '停更数据量'" label="状态" align="center" prop="status" show-overflow-tooltip width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.status == "1" ? "停更" : "正常" }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="title == '最长发布间隔' || title == '近5日未发布文章的公众号'" label="是否异常" align="center" prop="isException"
        show-overflow-tooltip width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.isException == "1" ? "异常" : "正常" }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { monitorStopUpdating, monitorStopIntervalDays, monitorUnpublished, monitorAbnormalWechatName } from "@/api/monitor/monitor";

export default {
  name: "Monitor",
  data() {
    return {
      loading: true,
      total: 0,
      monitorList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        wechatBiz: null,
        wechatName: null,
        intervalDays: null,
        publishFrequency: null,
        lastArticlePublishTime: null,
        lastArticleCollectionTime: null,
        lastArticleTitle: null,
        statisticalTime: null
      },
    };
  },
  props: {
    title: {
      type: String,
      default: null
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询微信公众号采集监控列表 */
    async getList() {
      var response
      this.loading = true
      let queryParams = JSON.parse(JSON.stringify(this.queryParams))
      queryParams.wechatBiz = this.id
      if (queryParams.lastArticlePublishTime && queryParams.lastArticlePublishTime.length) {
        [queryParams.publishTimeStart, queryParams.publishTimeEnd] = queryParams.lastArticlePublishTime
        delete queryParams.lastArticlePublishTime
      }
      if (queryParams.lastArticleCollectionTime && queryParams.lastArticleCollectionTime.length) {
        [queryParams.collectionTimeStart, queryParams.collectionTimeEnd] = queryParams.lastArticleCollectionTime
        delete queryParams.lastArticleCollectionTime
      }
      if (this.title == '停更数据量') {
        response = await monitorStopUpdating(queryParams)
        response.rows = response.rows.map(item => {
          return {
            title: item.title,
            wechatName: item.sourceName,
            publishTime: item.publishTime,
            id: item.articleId,
            status: item.status,
            wechatBiz: item.sourceSn,
          }
        })
      } else if (this.title == '最长发布间隔') {
        response = await monitorStopIntervalDays(queryParams)
        response.rows = response.rows.map(item => {
          return {
            title: item.lastArticleTitle,
            wechatName: item.wechatName,
            publishTime: item.lastArticlePublishTime,
            createTime: item.lastArticleCollectionTime,
            isException: item.isException,
            id: item.lastArticleId,
            wechatBiz: item.wechatBiz,
          }
        })
      } else if (this.title == '近5日未发布文章的公众号') {
        response = await monitorUnpublished(queryParams)
        response.rows = response.rows.map(item => {
          return {
            title: item.lastArticleTitle,
            wechatName: item.wechatName,
            publishTime: item.lastArticlePublishTime,
            createTime: item.lastArticleCollectionTime,
            isException: item.isException,
            id: item.lastArticleId,
            wechatBiz: item.wechatBiz,
          }
        })
      } else if (this.title == '公众号异常数量') {
        response = await monitorAbnormalWechatName(queryParams)
        response.rows = response.rows.map(item => {
          return {
            title: item.lastArticleTitle,
            wechatName: item.wechatName,
            publishTime: item.lastArticlePublishTime,
            createTime: item.lastArticleCollectionTime,
            isException: item.isException,
            id: item.lastArticleId,
            intervalDays: item.intervalDays,
            publishFrequency: item.publishFrequency,
            wechatBiz: item.wechatBiz,
          }
        })
      }
      this.monitorList = response.rows;
      this.total = response.total;
      this.loading = false;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    openWachatList(row) {
      this.$emit("openWachatList", row)
    },
    openNewView(item) {
      window.open(`/expressDetails?id=${item.id}&docId=${item.id}`, '_blank')
    },
  }
};
</script>
