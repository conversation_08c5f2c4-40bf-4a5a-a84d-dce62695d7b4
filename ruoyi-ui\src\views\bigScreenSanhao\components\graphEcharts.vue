<template>
  <div id="mainGraph" style="width: 100%; height: 100%"></div>
</template>

<script>
import * as echarts from "echarts";
import { technicalData } from "@/api/bigScreen/sanhao.js";

export default {
  name: "GraphEcharts",
  props: {
    sccenId: {
      type: Number,
      default: 1,
    },
    screenSn: {
      type: String,
      default: "1",
    },
  },
  data() {
    return {
      myChart: null,
      option: {},
      color: [
        "#E26B72FF",
        "#C1B973FF",
        "#D56149FF",
        "#4CB5F6FF",
        "#4C85F6FF",
        "#66C9D5FF",
        "#73C193FF",
        "#C1B973FF",
        "#AE66AEFF",
        "#E26B72FF",
        "#C1B973FF",
        "#D56149FF",
        "#4CB5F6FF",
        "#4C85F6FF",
        "#66C9D5FF",
        "#73C193FF",
        "#C1B973FF",
        "#AE66AEFF",
        "#E26B72FF",
        "#C1B973FF",
        "#D56149FF",
        "#4CB5F6FF",
        "#4C85F6FF",
        "#66C9D5FF",
        "#73C193FF",
        "#C1B973FF",
        "#AE66AEFF",
      ],
    };
  },
  mounted() {
    let chartDom = document.getElementById("mainGraph");
    this.myChart = echarts.init(chartDom);
    this.initChart();
  },
  watch: {
    screenSn: {
      handler() {
        this.initChart();
      },
      immediate: false,
    },
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
  },
  methods: {
    initChart() {
      console.log("graphEcharts 获取数据，screenSn:", this.screenSn);
      technicalData({ screenSn: this.screenSn })
        .then((res) => {
          const data = res.data.map((item, index) => {
            return {
              id: item.technicalSn,
              name: item.technicalName,
              value: item.totalArticles,
              summary: item.summary,
              symbolSize: item.nodeSize
                ? item.nodeSize * 5 + 25
                : Math.max(50, Math.min(item.totalArticles / 25, 80)),
              itemStyle: {
                color: item.backgroundColor
                  ? item.backgroundColor
                  : this.color[index],
              },
              label: {
                color: item.fontColor ? item.fontColor : "#fff",
              },
            };
          });
          this.option = {
            tooltip: {
              show: true,
            },
            grid: {
              left: "0%",
              right: "0%",
              bottom: "0%",
              top: "0%",
              containLabel: true,
            },
            series: [
              {
                type: "graph",
                layout: "force",
                force: {
                  repulsion: 110,
                  edgeLength: [0, 400],
                  gravity: 0.1,
                },
                // symbolSize: 50,
                roam: true,
                label: {
                  show: true,
                },
                edgeSymbol: ["circle", "arrow"],
                edgeSymbolSize: [4, 10],
                edgeLabel: {
                  fontSize: 20,
                },
                data: data,
              },
            ],
          };
          this.myChart.setOption(this.option);
          this.myChart.on("click", (params) => {
            this.$emit("openTechnologyDetails", { ...params });
          });
          setTimeout(() => {
            this.myChart?.resize();
          }, 1);
          console.log("graphEcharts 数据获取成功:", res.data);
        })
        .catch((error) => {
          console.error("graphEcharts 数据获取失败:", error);
        });
    },
  },
};
</script>

<style lang="scss" scoped></style>
