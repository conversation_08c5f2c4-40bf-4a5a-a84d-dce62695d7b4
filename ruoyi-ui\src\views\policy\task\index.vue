<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="98px">
      <el-form-item label="任务名称" prop="taskName">
        <el-input v-model="queryParams.taskName" placeholder="请输入任务名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="采集名称" prop="sourceName">
        <el-input v-model="queryParams.sourceName" placeholder="请输入政策网站采集来源名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="来源唯一标识" prop="sourceSn">
        <el-input v-model="queryParams.sourceSn" placeholder="请输入来源唯一标识" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['policy:task:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['policy:task:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['policy:task:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['policy:task:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" width="55" />
      <el-table-column label="任务名称" align="center" prop="taskName" width="120" show-overflow-tooltip />
      <el-table-column label="抓取的目标URL" align="center" prop="baseUrl" min-width="150" show-overflow-tooltip />
      <el-table-column label="自定义请求头" align="center" prop="headers" min-width="150" show-overflow-tooltip />
      <el-table-column label="可选代理配置" align="center" prop="proxies" min-width="150" show-overflow-tooltip />
      <el-table-column label="可选cookies配置" align="center" prop="cookies" min-width="150" show-overflow-tooltip />
      <el-table-column label="请求超时时间" align="center" prop="timeout" min-width="150" show-overflow-tooltip />
      <el-table-column label="请求失败时的重试次数" align="center" prop="retryOnFailure" min-width="150" show-overflow-tooltip />
      <el-table-column label="重试时的退避因子" align="center" prop="retryBackoffFactor" min-width="150" show-overflow-tooltip />
      <el-table-column label="采集数据项目配置" align="center" prop="dataExtraction" min-width="150" show-overflow-tooltip />
      <el-table-column label="分页配置" align="center" prop="pagination" min-width="150" show-overflow-tooltip />
      <el-table-column label="AJAX请求配置" align="center" prop="ajaxHandling" min-width="150" show-overflow-tooltip />
      <el-table-column label="采集来源名称" align="center" prop="sourceName" min-width="150" show-overflow-tooltip />
      <el-table-column label="来源唯一标识" align="center" prop="sourceSn" min-width="150" show-overflow-tooltip />
      <el-table-column label="状态" align="center" key="status" width="120">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
            @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['policy:task:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['policy:task:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改政策库采集任务对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="160px" :inline="false" class="form-inline">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="form.taskName" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="抓取的目标URL" prop="baseUrl">
              <el-input v-model="form.baseUrl" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="自定义请求头" prop="headers">
              <el-input v-model="form.headers" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="可选代理配置" prop="proxies">
              <el-input v-model="form.proxies" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="可选cookies配置" prop="cookies">
              <el-input v-model="form.cookies" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求超时时间" prop="timeout">
              <el-input v-model="form.timeout" placeholder="请输入请求超时时间" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求失败时的重试次数" prop="retryOnFailure">
              <el-input v-model="form.retryOnFailure" placeholder="请输入请求失败时的重试次数" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重试时的退避因子" prop="retryBackoffFactor">
              <el-input v-model="form.retryBackoffFactor" placeholder="请输入重试时的退避因子" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采集数据项目配置" prop="dataExtraction">
              <el-input v-model="form.dataExtraction" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分页配置" prop="pagination">
              <el-input v-model="form.pagination" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="AJAX请求配置" prop="ajaxHandling">
              <el-input v-model="form.ajaxHandling" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采集来源名称" prop="sourceName">
              <el-input v-model="form.sourceName" placeholder="请输入政策网站采集来源名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="来源唯一标识" prop="sourceSn">
              <el-input v-model="form.sourceSn" placeholder="请输入来源唯一标识" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTask, getTask, delTask, addTask, updateTask } from "@/api/policy/task";

export default {
  name: "Task",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 政策库采集任务表格数据
      taskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        taskName: null,
        baseUrl: null,
        headers: null,
        proxies: null,
        cookies: null,
        timeout: null,
        retryOnFailure: null,
        retryBackoffFactor: null,
        dataExtraction: null,
        pagination: null,
        ajaxHandling: null,
        sourceName: null,
        sourceSn: null,
        status: null,
        userId: null,
        deptId: null,
        deleteBy: null,
        deleteTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询政策库采集任务列表 */
    getList() {
      this.loading = true;
      listTask(this.queryParams).then(response => {
        this.taskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        taskName: null,
        baseUrl: null,
        headers: null,
        proxies: null,
        cookies: null,
        timeout: null,
        retryOnFailure: null,
        retryBackoffFactor: null,
        dataExtraction: null,
        pagination: null,
        ajaxHandling: null,
        sourceName: null,
        sourceSn: null,
        status: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null,
        delFlag: null,
        deleteBy: null,
        deleteTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加政策库采集任务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getTask(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改政策库采集任务";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateTask(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTask(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids;
      this.$modal.confirm('是否确认删除政策库采集任务编号为"' + ids + '"的数据项？').then(function () {
        return delTask(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('policy/task/export', {
        ...this.queryParams
      }, `task_${new Date().getTime()}.xlsx`)
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === '0' ? '启用' : '停用'
      this.$modal.confirm('确认要"' + text + '吗？').then(() => {
        return updateTask(row)
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
      }).catch(function () {
        row.status = row.status === '0' ? '1' : '0'
      })
    },
  }
};
</script>
