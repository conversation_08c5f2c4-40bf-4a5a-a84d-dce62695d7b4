<template>
  <div class="one">
    <div class="left" style="padding-bottom: 8px">
      <!-- <TitleComponent :title="'热点推荐'" style="width: 523px" />
      <myList style="height: 659px" @openNewView="openNewView"> </myList>
      <TitleComponent :title="'产品介绍'" style="width: 523px" />
      <product style="height: 229px"> </product> -->
      <div style="height: calc(100% - 282px)">
        <div style="position: relative">
          <TitleComponent :title="'热点推荐'" style="width: 523px" />
          <!-- <el-button
            type="primary"
            size="mini"
            icon="el-icon-chat-dot-round"
            style="
              height: 20px;
              padding: 0 8px;
              background: rgba(16, 216, 255, 0.2);
              border: 1px solid rgba(16, 216, 255, 0.4);
              box-shadow: 0 0 8px 0 #0056ad;
              position: absolute;
              right: 4px;
              top: 53%;
              transform: translateY(-50%);
            "
            @click="articleAiChat"
            >Deepseek深度解读</el-button
          > -->
        </div>
        <myList
          style="height: calc(100% - 32px)"
          @openNewView="openNewView"
          ref="myList"
        >
        </myList>
      </div>
      <div style="height: 262px">
        <TitleComponent :title="'产品介绍'" style="width: 523px" />
        <product style="height: 230px"> </product>
      </div>
    </div>
    <div class="center">
      <div class="top">
        <div class="top-content bg1">
          <div class="top-content-number">
            {{ padWithZeros(gatherTotal, 6) }}
          </div>
          <div class="top-content-name">有效采集量</div>
        </div>
        <div class="top-content bg2">
          <div class="top-content-number">
            {{ padWithZeros(gatherDayNumber, 6) }}
          </div>
          <div class="top-content-name">当日采集数量</div>
        </div>
      </div>
      <mymap @openList="openList" style="width: 855px; height: 725px"></mymap>
    </div>
    <div class="right">
      <div style="height: 50%">
        <TitleComponent :title="'境内信息源'" />
        <barEchart
          :idName="'barChart1'"
          :data="barChart1Data"
          :sourceType="1"
          @openList="openList"
          style="height: calc(100% - 20px); width: 520px"
        >
        </barEchart>
      </div>
      <div style="height: 50%">
        <TitleComponent :title="'境外信息源'" />
        <barEchart
          :idName="'barChart2'"
          :data="barChart2Data"
          :sourceType="2"
          @openList="openList"
          style="height: calc(100% - 20px); width: 520px"
        >
        </barEchart>
      </div>
      <TitleComponent :title="'采集趋势'" v-if="false" />
      <linEchart style="height: 439px; width: 520px" v-if="false"> </linEchart>
    </div>
    <el-dialog
      :title="drawerInfo.cnTitle || drawerInfo.title"
      :visible.sync="articleDialogVisible"
      width="65%"
      append-to-body
      :before-close="handleClose"
      custom-class="detail-dialog"
      :close-on-click-modal="false"
    >
      <!--span style="text-align: right">test</span-->
      <div style="display: flex; justify-content: space-between">
        <div>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-chat-dot-round"
            style="
              height: 30px;
              background: rgba(16, 216, 255, 0.2);
              border: 1px solid rgba(16, 216, 255, 0.4);
              box-shadow: 0 0 8px 0 #0056ad;
            "
            @click="markAiChat(0)"
            >Deepseek文章内容解读</el-button
          >
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-chat-dot-round"
            style="
              height: 30px;
              background: rgba(16, 216, 255, 0.2);
              border: 1px solid rgba(16, 216, 255, 0.4);
              box-shadow: 0 0 8px 0 #0056ad;
            "
            @click="markAiChat(1)"
            >Deepseek人物观点解读</el-button
          >
        </div>
        <div class="fz">
          <div class="text">字号：</div>
          <div class="btns">
            <div class="btn-minus" @click="decreaseFontSize">-</div>
            <div class="font-size">{{ fontSize }}px</div>
            <div class="btn-plus" @click="increaseFontSize">+</div>
          </div>
        </div>
      </div>

      <!--div style="line-height: 30px;font-size: 16px;" v-html="drawerInfo.cnContent"></div-->
      <div
        class="dialog-art"
        :style="{ fontSize: getFontSize }"
        v-html="drawerInfo.cnContent"
      ></div>
      <el-empty
        description="当前文章暂无数据"
        v-if="!drawerInfo.cnContent"
      ></el-empty>
      <!-- <span slot="footer" class="dialog-footer"> -->
      <!--el-button size="mini" @click="handleClose">关闭</el-button-->
      <!-- </span> -->
    </el-dialog>
    <el-drawer
      :title="title"
      :visible.sync="drawer"
      direction="rtl"
      :before-close="handleClose1"
      append-to-body
      size="800px"
    >
      <!-- <template slot="title">
        <div
          style="
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
          "
        >
          <div>{{ title }}</div>
        </div>
      </template> -->
      <el-table
        :data="list"
        style="width: 100%"
        :show-header="false"
        ref="table"
        @cell-click="openNewView"
      >
        <el-table-column
          prop="title"
          label="标题"
          width="510"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span
              v-html="changeColor(scope.row.cnTitle || scope.row.title)"
            ></span>
          </template>
        </el-table-column>
        <el-table-column
          prop="sourceName"
          label="数据源"
          width="100"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="publishTime"
          label="发布时间"
          width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{
              parseTime(
                scope.row.publishTime || scope.row.gatherTime,
                "{y}-{m}-{d}"
              )
            }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="drawer-more" @click="drawerMore" v-if="list.length > 0">
        >>更多>>
      </div>
      <!-- <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :background="false"
        @pagination="getList"
      /> -->
    </el-drawer>
    <el-dialog
      title="Deepseek深度解读"
      :visible.sync="aiDialogVisible"
      width="65%"
      :before-close="closeAiDialog"
      custom-class="detail-dialog"
      append-to-body
    >
      <div style="display: flex; justify-content: flex-end">
        <div class="fz">
          <div class="text">字号：</div>
          <div class="btns">
            <div class="btn-minus" @click="decreaseAiFontSize">-</div>
            <div class="font-size">{{ aiFontSize }}px</div>
            <div class="btn-plus" @click="increaseAiFontSize">+</div>
          </div>
        </div>
      </div>
      <div v-if="isThinking && !aiContent" class="thinking-container">
        <div class="thinking-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <div class="thinking-text">正在思考中...</div>
      </div>
      <div
        v-else
        class="dialog-articleAiThinking"
        ref="articleAiThinking"
        :style="{ fontSize: getAiFontSize }"
      >
        <div class="stream-content" v-html="renderedContent"></div>
      </div>
      <el-empty
        description="暂无解读内容"
        v-if="!aiContent && !isThinking"
      ></el-empty>
    </el-dialog>
    <markmap-dialog
      :visible.sync="markmapVisible"
      :content="markmapContent"
      :title="markmapTitle"
      :loading="aiLoading"
      @close="handleMarkmapClose"
    />
  </div>
</template>
<script>
import TitleComponent from "./components/titleVue.vue";
import mymap from "./echartsComputent/map.vue";
import myList from "./echartsComputent/list.vue";
import product from "./echartsComputent/product.vue";
import linEchart from "./echartsComputent/linEchart.vue";
import barEchart from "./echartsComputent/barEchart.vue";
import {
  largeSourceDataList,
  largeGatherQueryGatherData,
  largeRegionDataList,
  largeHotQueryById,
  largeRegionDataQueryById,
  largeSourceDataQueryById,
  largeSourceGetKeywords,
  saveScreenFont,
  getScreenFont,
} from "@/api/bigScreen/index1";
import { largeSourceList } from "@/api/bigScreen/index1";
import { deepseekAiQa, ollamaAiQa, difyAiQa } from "@/api/infoEscalation/ai";
import { getListByIds } from "@/api/article/articleHistory";
import MarkmapDialog from "./components/MarkmapDialog.vue";
import { marked } from "marked";
import { getConfigKey } from "@/api/system/config";

export default {
  data() {
    return {
      drawerInfo: {},
      articleDialogVisible: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      drawer: false,
      title: "",
      list: [],
      query: {},
      gatherTotal: 0,
      gatherDayNumber: 0,
      keywords: "",
      fontSize: 20,
      oriFontSize: 20,
      barChart1Data: [],
      barChart2Data: [],
      drawerSourceType: null,
      aiDialogVisible: false,
      chatMessages: [],
      isThinking: false,
      userAvatar: "",
      markmapVisible: false,
      markmapContent: "",
      aiLoading: false,
      aiFontSize: 20,
      aiContent: "",
      markdownOptions: {
        gfm: true,
        breaks: true,
        headerIds: true,
        mangle: false,
        headerPrefix: "",
        pedantic: false,
        sanitize: false,
        smartLists: true,
        smartypants: true,
        xhtml: true,
      },
      markmapTitle: "Deepseek文章内容解读",
      abortController: null,
      isAborted: false,
      currentRequestId: null,
      aiPlatform: "",
      articleAiPrompt: "",
      articleAiMarkPrompt: "",
      charAiMarkPrompt: "",
      difyApikey: {
        article: "",
        mark: "",
      },
    };
  },
  components: {
    TitleComponent,
    mymap,
    myList,
    product,
    linEchart,
    barEchart,
    MarkmapDialog,
  },
  mounted() {
    this.init();
  },
  beforeDestroy() {
    // 组件销毁时中断所有请求
    this.handleMarkmapClose();
  },
  computed: {
    getFontSize() {
      return this.fontSize + "px";
    },
    getAiFontSize() {
      return this.aiFontSize + "px";
    },
    renderedContent() {
      return this.aiContent ? marked(this.aiContent, this.markdownOptions) : "";
    },
  },
  methods: {
    init() {
      largeGatherQueryGatherData({}).then((res) => {
        this.gatherTotal = res.data.gatherTotal;
        this.gatherDayNumber = res.data.gatherDayNumber;
      });
      largeSourceList({}).then((res) => {
        this.barChart1Data = res.rows.filter((item) => item.sourceType === 1);
        this.barChart2Data = res.rows.filter((item) => item.sourceType === 2);
        console.log(this.barChart1Data);
        console.log(this.barChart2Data);
      });
      getScreenFont().then((res) => {
        this.fontSize = res.screenFont ? res.screenFont : 20;
      });
      getConfigKey("sys.ai.platform").then((res) => {
        if (res.code == 200) {
          this.aiPlatform = res.msg;
        }
      });
      getConfigKey("bigScreen.ai.articlePrompt").then((res) => {
        if (res.code == 200) {
          this.articleAiPrompt = res.msg;
        }
      });
      getConfigKey("bigScreen.ai.articleMarkPrompt").then((res) => {
        if (res.code == 200) {
          this.articleAiMarkPrompt = res.msg;
        }
      });
      getConfigKey("bigScreen.ai.charMarkPrompt").then((res) => {
        if (res.code == 200) {
          this.charAiMarkPrompt = res.msg;
        }
      });
    },
    padWithZeros(num, targetLength) {
      const numStr = num.toString();
      const padding = "0".repeat(targetLength - numStr.length);
      return `${padding}${numStr}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    async openNewView(item) {
      if (this.query.regionName) {
        await largeRegionDataQueryById(item.id).then((res) => {
          this.drawerInfo = res.data;
        });
      } else if (this.query.sourceId) {
        await largeSourceDataQueryById(item.id).then((res) => {
          this.drawerInfo = res.data;
        });
      } else {
        await largeHotQueryById(item.id).then((res) => {
          this.drawerInfo = res.data;
        });
      }
      let content = this.drawerInfo.article || this.drawerInfo.content;
      if (content) {
        content = content.replace(/\n/g, "<br>");
        content = content.replace(/\${[^}]+}/g, "<br>");
        content = content.replace("|xa0", "");
        content = content.replace("opacity: 0", "");
        content = content.replace(/<img\b[^>]*>/gi, "");
        content = content.replace(/ style="[^"]*"/g, "");
      }
      this.drawerInfo.cnContent = content;
      this.articleDialogVisible = true;
      this.oriFontSize = this.fontSize;
    },
    handleClose() {
      this.drawerInfo = {};
      this.articleDialogVisible = false;
      if (this.fontSize !== this.oriFontSize) {
        saveScreenFont(this.fontSize);
      }
    },
    handleClose1() {
      this.title = "";
      this.list = [];
      this.query = {};
      this.keywords = "";
      this.drawer = false;
      this.drawerSourceType = null;
    },
    openList({ query, title, sourceType }) {
      console.log(query, title);
      this.query = query;
      this.title = title;
      this.keywords = [title];
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.getList();
      this.drawerSourceType = sourceType;
    },
    async getList() {
      if (this.query.regionName) {
        await largeRegionDataList(this.query, this.queryParams).then((res) => {
          this.list = res.rows;
          this.total = res.total;
        });
      } else {
        await largeSourceGetKeywords(this.query.sourceName).then((res) => {
          this.keywords = res.data;
        });
        let query = {
          sourceId: this.query.sourceId,
        };
        await largeSourceDataList(query, this.queryParams).then((res) => {
          this.list = res.rows;
          this.total = res.total;
        });
      }
      this.drawer = true;
    },
    // 关键字替换
    changeColor(str) {
      let Str = str;
      if (Str) {
        let keywords = this.keywords;
        keywords.map((keyitem, keyindex) => {
          if (keyitem && keyitem.length > 0) {
            // 匹配关键字正则
            let replaceReg = new RegExp(keyitem, "g");
            // 高亮替换v-html值
            let replaceString =
              '<span class="highlight"' +
              ' style="color: #ff7500;">' +
              keyitem +
              "</span>";
            Str = Str.replace(replaceReg, replaceString);
          }
        });
      }
      return Str;
    },
    increaseFontSize() {
      if (this.fontSize < 30) {
        this.fontSize += 2;
      }
    },
    decreaseFontSize() {
      if (this.fontSize > 16) {
        this.fontSize -= 2;
      }
    },
    drawerMore() {
      if (this.drawerSourceType === 1) {
        const routeUrl = this.$router.resolve({
          path: "/InfoEscalation/WechatIntegration",
          query: {
            title: this.title,
            sourceId: this.query.sourceId,
          },
        });
        window.open(routeUrl.href, "_blank");
      } else {
        const routeUrl = this.$router.resolve({
          path: "/MonitorUse?id=1",
          query: {
            title: this.title,
            sourceId: this.query.sourceId,
          },
        });
        window.open(routeUrl.href, "_blank");
      }
    },
    // deepseek深度解读
    async deepseekArticleAiChat() {
      this.isAborted = false;
      this.currentRequestId = Date.now();
      const requestId = this.currentRequestId;

      this.aiDialogVisible = true;
      this.aiContent = "";
      this.isThinking = true;

      // 从 list 组件获取热点文章列表
      const hotArticles = this.$refs.myList.remengwenzhangList;
      if (!hotArticles || hotArticles.length === 0) {
        this.$message.error("暂无热点文章");
        return;
      }

      try {
        // 先获取文章内容
        const articlesResponse = await getListByIds(
          hotArticles.map((article) => article.id).join(",")
        );
        if (!articlesResponse.data || !articlesResponse.data.length) {
          throw new Error("Failed to get article contents");
        }

        // 构建文章内容字符串
        const contents = articlesResponse.data
          .map((article, index) => {
            return `【第 ${index + 1} 篇文章】《${article.title}》\n\n${
              article.content || ""
            }`;
          })
          .join("\n\n-------------------------------------------\n\n");

        const prompt =
          this.articleAiPrompt
            .replace(/articleLength/g, hotArticles.length)
            .replace(/\&gt;/g, ">") +
          `\n\n**以下是待处理的文章：**\n\n${contents}`;

        const response = await deepseekAiQa(prompt, true);

        if (response.ok) {
          const reader = response.body.getReader();
          const decoder = new TextDecoder();
          let buffer = "";
          let lastUpdateTime = Date.now();

          const updateContent = (newContent) => {
            if (this.isAborted || this.currentRequestId !== requestId) return;

            const currentTime = Date.now();
            if (currentTime - lastUpdateTime >= 50) {
              this.aiContent = newContent;
              lastUpdateTime = currentTime;
              // this.$nextTick(() => {
              //   const dialogArt = document.querySelector(
              //     ".dialog-articleAiThinking"
              //   );
              //   if (dialogArt) {
              //     dialogArt.scrollTop = dialogArt.scrollHeight;
              //   }
              // });
              this.$nextTick(() => {
                const articleAiThinking = this.$refs.articleAiThinking;
                if (articleAiThinking) {
                  articleAiThinking.scrollTop = articleAiThinking.scrollHeight;
                }
              });
            }
          };

          while (true) {
            if (this.isAborted || this.currentRequestId !== requestId) {
              reader.cancel();
              break;
            }

            const { done, value } = await reader.read();
            if (done) {
              if (
                buffer.length > 0 &&
                !this.isAborted &&
                this.currentRequestId === requestId
              ) {
                updateContent(buffer);
              }
              break;
            }

            const chunk = decoder.decode(value);
            try {
              const lines = chunk.split("\n");
              for (const line of lines) {
                if (this.isAborted || this.currentRequestId !== requestId)
                  break;
                if (!line.trim() || !line.startsWith("data: ")) continue;

                const data = line.slice(5);
                if (data === "[DONE]") break;

                try {
                  const jsonData = JSON.parse(data);
                  if (jsonData.choices?.[0]?.delta?.content) {
                    let content = jsonData.choices[0].delta.content;

                    // 跳过特殊字符
                    if (content === "```" || content === "markdown") {
                      continue;
                    }

                    buffer += content;
                    updateContent(buffer);
                  }
                } catch (parseError) {
                  console.error("Error parsing JSON:", parseError);
                }
              }
            } catch (e) {
              console.error("Error processing chunk:", e);
            }
          }
        } else {
          throw new Error("Request failed");
        }
      } catch (error) {
        if (!this.isAborted && this.currentRequestId === requestId) {
          console.error("AI Chat Error:", error);
          this.$message.error("AI解读失败，请稍后重试");
          this.aiContent = "抱歉，服务器繁忙，请稍后再试";
        }
      } finally {
        if (this.currentRequestId === requestId) {
          this.isThinking = false;
        }
      }
    },

    // ollama深度解读
    async ollamaArticleAiChat() {
      this.isAborted = false;
      this.currentRequestId = Date.now();
      const requestId = this.currentRequestId;

      this.aiDialogVisible = true;
      this.aiContent = "";
      this.isThinking = true;

      // 从 list 组件获取热点文章列表
      const hotArticles = this.$refs.myList.remengwenzhangList;
      if (!hotArticles || hotArticles.length === 0) {
        this.$message.error("暂无热点文章");
        return;
      }

      try {
        // 先获取文章内容
        const articlesResponse = await getListByIds(
          hotArticles.map((article) => article.id).join(",")
        );
        if (!articlesResponse.data || !articlesResponse.data.length) {
          throw new Error("Failed to get article contents");
        }

        // 构建文章内容字符串
        const contents = articlesResponse.data
          .map((article, index) => {
            return `【第 ${index + 1} 篇文章】《${article.title}》\n\n${
              article.content || ""
            }`;
          })
          .join("\n\n-------------------------------------------\n\n");

        const prompt =
          this.articleAiPrompt
            .replace(/articleLength/g, hotArticles.length)
            .replace(/\&gt;/g, ">") +
          `\n\n**以下是待处理的文章：**\n\n${contents}`;

        const response = await ollamaAiQa(prompt, true);
        if (!response.ok) {
          throw new Error("AI接口调用失败");
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = "";
        let lastUpdateTime = Date.now();
        let isThinkContent = false;
        let tempBuffer = "";

        const updateContent = (newContent) => {
          if (this.isAborted || this.currentRequestId !== requestId) return;

          const currentTime = Date.now();
          if (currentTime - lastUpdateTime >= 50) {
            this.aiContent = newContent;
            lastUpdateTime = currentTime;
            // this.$nextTick(() => {
            //   const dialogArt = document.querySelector(
            //     ".dialog-articleAiThinking"
            //   );
            //   if (dialogArt) {
            //     dialogArt.scrollTop = dialogArt.scrollHeight;
            //   }
            // });
            this.$nextTick(() => {
              const articleAiThinking = this.$refs.articleAiThinking;
              if (articleAiThinking) {
                articleAiThinking.scrollTop = articleAiThinking.scrollHeight;
              }
            });
          }
        };

        const processStream = async () => {
          try {
            while (true) {
              if (this.isAborted || this.currentRequestId !== requestId) {
                reader.cancel();
                break;
              }

              const { done, value } = await reader.read();
              if (done) {
                if (
                  buffer.length > 0 &&
                  !this.isAborted &&
                  this.currentRequestId === requestId
                ) {
                  updateContent(buffer);
                }
                break;
              }

              const chunk = decoder.decode(value);
              const lines = chunk.split("\n").filter((line) => line.trim());

              for (const line of lines) {
                if (this.isAborted || this.currentRequestId !== requestId)
                  break;

                try {
                  const jsonData = JSON.parse(line);
                  if (!jsonData.response) continue;

                  const response = jsonData.response;

                  // 跳过特殊字符
                  if (response === "```" || response === "markdown") {
                    continue;
                  }

                  tempBuffer += response;

                  while (
                    !this.isAborted &&
                    this.currentRequestId === requestId
                  ) {
                    const thinkStartIndex = tempBuffer.indexOf("<think>");
                    const thinkEndIndex = tempBuffer.indexOf("</think>");

                    if (thinkStartIndex === -1 && thinkEndIndex === -1) {
                      if (!isThinkContent) {
                        buffer += tempBuffer;
                        updateContent(marked(buffer, this.markdownOptions));
                      }
                      tempBuffer = "";
                      break;
                    } else if (thinkStartIndex !== -1 && thinkEndIndex === -1) {
                      isThinkContent = true;
                      if (thinkStartIndex > 0) {
                        buffer += tempBuffer.substring(0, thinkStartIndex);
                        updateContent(marked(buffer, this.markdownOptions));
                      }
                      tempBuffer = tempBuffer.substring(thinkStartIndex);
                      break;
                    } else if (thinkStartIndex === -1 && thinkEndIndex !== -1) {
                      isThinkContent = false;
                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);
                      continue;
                    } else {
                      if (thinkStartIndex > 0) {
                        buffer += tempBuffer.substring(0, thinkStartIndex);
                        updateContent(marked(buffer, this.markdownOptions));
                      }
                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);
                      isThinkContent = false;
                      continue;
                    }
                  }
                } catch (parseError) {
                  console.warn("无效的JSON行，已跳过", {
                    line,
                    error: parseError.message,
                  });
                }
              }
            }
          } catch (streamError) {
            if (!this.isAborted && this.currentRequestId === requestId) {
              console.error("处理流式响应时出错:", streamError);
              throw streamError;
            }
          }
        };

        await processStream();
      } catch (error) {
        if (!this.isAborted && this.currentRequestId === requestId) {
          console.error("AI Chat Error:", error);
          this.$message.error("AI解读失败，请稍后重试");
          this.aiContent = "抱歉，服务器繁忙，请稍后再试";
        }
      } finally {
        if (this.currentRequestId === requestId) {
          this.isThinking = false;
        }
      }
    },
    // dify深度解读
    async difyArticleAiChat() {
      // 如果有正在进行的请求，中断它
      if (this.isRequesting) {
        this.isAborted = true;
        if (this.currentReader) {
          try {
            await this.currentReader.cancel();
          } catch (e) {
            console.log("中断之前的请求失败", e);
          }
        }
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      this.isRequesting = true;
      this.isAborted = false;
      this.aiDialogVisible = true;
      this.aiContent = "";
      this.isThinking = true;

      // 从 list 组件获取热点文章列表
      const hotArticles = this.$refs.myList.remengwenzhangList.slice(0, 30);
      if (!hotArticles || hotArticles.length === 0) {
        this.$message.error("暂无热点文章");
        return;
      }

      try {
        // 先获取文章内容
        const articlesResponse = await getListByIds(
          hotArticles.map((article) => article.id).join(",")
        );
        if (!articlesResponse.data || !articlesResponse.data.length) {
          throw new Error("Failed to get article contents");
        }

        // 构建文章内容字符串
        const contents = articlesResponse.data
          .map((article, index) => {
            return `【第 ${index + 1} 篇文章】《${article.title}》\n\n${
              article.content || ""
            }`;
          })
          .join("\n\n-------------------------------------------\n\n");

        const prompt =
          this.articleAiPrompt
            .replace(/articleLength/g, hotArticles.length)
            .replace(/\&gt;/g, ">") +
          `\n\n**以下是待处理的文章：**\n\n${contents}`;

        // 调用AI接口
        const response = await difyAiQa(
          contents,
          "streaming",
          "dify.article.apikey"
        );
        if (!response.ok) {
          throw new Error("AI接口调用失败");
        }

        // 处理流式响应
        const reader = response.body.getReader();
        this.currentReader = reader;
        const decoder = new TextDecoder();
        let buffer = "";
        let pendingBuffer = ""; // 用于存储待处理的不完整数据
        let isInThinkTag = false; // 新增：标记是否在think标签内

        const decodeUnicode = (str) => {
          return str.replace(/\\u[\dA-Fa-f]{4}/g, (match) => {
            return String.fromCharCode(parseInt(match.replace(/\\u/g, ""), 16));
          });
        };

        // 更新内容的函数
        const updateContent = (newContent) => {
          try {
            const renderedContent = marked(newContent, this.markdownOptions);
            this.aiContent = renderedContent;

            // 确保消息容器滚动到底部
            // this.$nextTick(() => {
            //   const dialogArt = document.querySelector(
            //     ".dialog-articleAiThinking"
            //   );
            //   if (dialogArt) {
            //     dialogArt.scrollTop = dialogArt.scrollHeight;
            //   }
            // });
            this.$nextTick(() => {
              const articleAiThinking = this.$refs.articleAiThinking;
              if (articleAiThinking) {
                articleAiThinking.scrollTop = articleAiThinking.scrollHeight;
              }
            });
          } catch (error) {
            console.error("渲染内容时出错:", error);
          }
        };

        // 处理流式响应
        while (true) {
          // 检查是否已中断
          if (this.isAborted) {
            throw new Error("AbortError");
          }

          const { done, value } = await reader.read();

          if (done) {
            // 处理最后可能剩余的数据
            if (pendingBuffer) {
              try {
                const lastData = JSON.parse(pendingBuffer);
                if (lastData.answer) {
                  const decodedAnswer = decodeUnicode(lastData.answer);
                  buffer += decodedAnswer;
                  updateContent(buffer);
                }
              } catch (e) {
                console.warn("处理最后的数据时出错:", e);
              }
            }
            break;
          }

          const chunk = decoder.decode(value);
          pendingBuffer += chunk;

          // 处理完整的数据行
          while (pendingBuffer.includes("\n")) {
            const newlineIndex = pendingBuffer.indexOf("\n");
            const line = pendingBuffer.slice(0, newlineIndex).trim();
            pendingBuffer = pendingBuffer.slice(newlineIndex + 1);

            if (!line || line === "data:" || !line.startsWith("data:")) {
              continue;
            }

            try {
              const data = line.slice(5).trim();
              if (data === "[DONE]") {
                continue;
              }

              const jsonData = JSON.parse(data);
              if (!jsonData.answer) {
                continue;
              }

              // 跳过特殊字符
              if (jsonData.answer === "```" || jsonData.answer === "markdown") {
                continue;
              }

              let answer = decodeUnicode(jsonData.answer);

              // 检查是否包含<think>开始标签
              if (answer.includes("<think>")) {
                isInThinkTag = true;
                continue; // 跳过包含<think>的部分
              }

              // 检查是否包含</think>结束标签
              if (answer.includes("</think>")) {
                isInThinkTag = false;
                continue; // 跳过包含</think>的部分
              }

              // 只有不在think标签内的内容才会被添加到buffer中
              if (!isInThinkTag && answer) {
                buffer += answer;
                updateContent(buffer);
              }
            } catch (parseError) {
              console.warn("解析数据行时出错:", {
                line,
                error: parseError.message,
                pendingBuffer,
              });
              continue;
            }
          }
        }
      } catch (error) {
        console.error("AI Chat Error:", error);
        this.$message.error("AI解读失败，请稍后重试");
        this.aiContent = "抱歉，服务器繁忙，请稍后再试";
      } finally {
        this.currentReader = null;
        if (this.aiDialogVisible) {
          this.isThinking = false;
          this.isRequesting = false;
        }
      }
    },
    closeAiDialog() {
      this.isAborted = true;
      this.currentRequestId = null;
      this.aiDialogVisible = false;
      this.aiContent = "";
      this.isThinking = false;
    },
    formatMessage(content) {
      if (!content) return "";

      let formattedContent = content
        // 处理换行
        .replace(/\n/g, "<br/>")
        // 处理空格
        .replace(/\s/g, "&nbsp;")
        // 清理所有的 #、*、- 号
        .replace(/[#*-]/g, "");

      return formattedContent;
    },
    // deepseek思维导图
    async deepseekMarkAiChat(e) {
      this.isAborted = false;
      this.currentRequestId = Date.now();
      const requestId = this.currentRequestId;

      try {
        const content = this.drawerInfo.cnContent || this.drawerInfo.content;
        const title = this.drawerInfo.cnTitle || this.drawerInfo.title;

        if (!content) {
          this.$message.error("文章内容为空");
          return;
        }

        // 先设置标题和打开弹窗
        if (e === 0) {
          this.markmapTitle = "Deepseek文章内容解读" + "《" + title + "》";
        } else {
          this.markmapTitle = "Deepseek人物观点解读" + "《" + title + "》";
        }
        this.markmapVisible = true;
        this.aiLoading = true;

        const prompt =
          e === 0
            ? this.articleAiMarkPrompt.replace(/articleTitle/g, title)
            : this.charAiMarkPrompt.replace(/articleTitle/g, title);

        const response = await deepseekAiQa(
          prompt + `\n\n文章内容：\n\n${content}`,
          false
        );

        // 如果当前请求已被中断，直接返回
        if (this.isAborted || this.currentRequestId !== requestId) {
          console.log("Request cancelled");
          return;
        }

        if (response.ok) {
          const data = await response.json();

          // 再次检查请求是否被中断
          if (this.isAborted || this.currentRequestId !== requestId) {
            console.log("Request cancelled");
            return;
          }

          if (data.choices && data.choices[0] && data.choices[0].message) {
            let content = data.choices[0].message.content;

            // 清理markdown标记和其他特殊字符
            content = content
              // 移除markdown代码块标记
              .replace(/```markdown\s*|```\s*/g, "")
              // 移除可能存在的其他markdown语言标记，如```json等
              .replace(/```[a-zA-Z]*\s*/g, "")
              // 移除多余的空行
              .replace(/\n\s*\n\s*\n/g, "\n\n")
              // 移除行首行尾空白字符
              .trim();

            // 确保内容非空
            if (!content) {
              throw new Error("Invalid content format");
            }

            // 最后一次检查请求是否被中断
            if (!this.isAborted && this.currentRequestId === requestId) {
              console.log("AI Response:", content);
              this.markmapContent = content;
            }
          } else {
            throw new Error("Invalid response format");
          }
        } else {
          throw new Error("Request failed");
        }
      } catch (error) {
        // 只处理未中断请求的错误
        if (!this.isAborted && this.currentRequestId === requestId) {
          console.error("AI Chat Error:", error);
          this.$message.error("AI解读失败，请稍后重试");
          this.markmapVisible = false;
        }
      } finally {
        // 只处理未中断请求的状态更新
        if (!this.isAborted && this.currentRequestId === requestId) {
          this.aiLoading = false;
        }
      }
    },
    // ollama思维导图
    async ollamaMarkAiChat(e) {
      this.isAborted = false;
      this.currentRequestId = Date.now();
      const requestId = this.currentRequestId;

      try {
        const content = this.drawerInfo.cnContent || this.drawerInfo.content;
        const title = this.drawerInfo.cnTitle || this.drawerInfo.title;

        if (!content) {
          this.$message.error("文章内容为空");
          return;
        }

        // 先设置标题和打开弹窗
        if (e === 0) {
          this.markmapTitle = "Deepseek文章内容解读" + "《" + title + "》";
        } else {
          this.markmapTitle = "Deepseek人物观点解读" + "《" + title + "》";
        }
        this.markmapVisible = true;
        this.aiLoading = true;

        const prompt =
          e === 0
            ? this.articleAiMarkPrompt.replace(/articleTitle/g, title)
            : this.charAiMarkPrompt.replace(/articleTitle/g, title);

        const response = await ollamaAiQa(
          prompt + `\n\n文章内容：\n\n${content}`,
          false
        );

        // 如果当前请求已被中断，直接返回
        if (this.isAborted || this.currentRequestId !== requestId) {
          console.log("Request cancelled");
          return;
        }

        if (response.ok) {
          const data = await response.json();

          // 再次检查请求是否被中断
          if (this.isAborted || this.currentRequestId !== requestId) {
            console.log("Request cancelled");
            return;
          }

          if (data.response) {
            const content = data.response;
            let content2 = "";

            // 处理思考标记
            const thinkStartIndex = content.indexOf("<think>");
            const thinkEndIndex = content.indexOf("</think>");

            // 提取有效内容
            if (thinkStartIndex !== -1 && thinkEndIndex !== -1) {
              // 如果存在思考标记，只取</think>后面的内容
              content2 = content.substring(thinkEndIndex + 8).trim();
            } else {
              // 如果不存在思考标记，使用完整内容
              content2 = content.trim();
            }

            // 清理markdown标记和其他特殊字符
            content2 = content2
              // 移除markdown代码块标记
              .replace(/```markdown\s*|```\s*/g, "")
              // 移除可能存在的其他markdown语言标记，如```json等
              .replace(/```[a-zA-Z]*\s*/g, "")
              // 移除多余的空行
              .replace(/\n\s*\n\s*\n/g, "\n\n")
              // 移除行首行尾空白字符
              .trim();

            // 确保内容非空
            if (!content2) {
              throw new Error("Invalid content format");
            }

            // 最后一次检查请求是否被中断
            if (!this.isAborted && this.currentRequestId === requestId) {
              console.log("AI Response:", content2);
              this.markmapContent = content2;
            }
          } else {
            throw new Error("Invalid response format");
          }
        } else {
          throw new Error("Request failed");
        }
      } catch (error) {
        // 只处理未中断请求的错误
        if (!this.isAborted && this.currentRequestId === requestId) {
          console.error("AI Chat Error:", error);
          this.$message.error("AI解读失败，请稍后重试");
          this.markmapVisible = false;
        }
      } finally {
        // 只处理未中断请求的状态更新
        if (!this.isAborted && this.currentRequestId === requestId) {
          this.aiLoading = false;
        }
      }
    },
    // dify思维导图
    async difyMarkAiChat(e) {
      this.isAborted = false;
      this.currentRequestId = Date.now();
      const requestId = this.currentRequestId;

      try {
        const content = this.drawerInfo.cnContent || this.drawerInfo.content;
        const title = this.drawerInfo.cnTitle || this.drawerInfo.title;

        if (!content) {
          this.$message.error("文章内容为空");
          return;
        }

        // 先设置标题和打开弹窗
        if (e === 0) {
          this.markmapTitle = "Deepseek文章内容解读" + "《" + title + "》";
        } else {
          this.markmapTitle = "Deepseek人物观点解读" + "《" + title + "》";
        }
        this.markmapVisible = true;
        this.aiLoading = true;

        const prompt =
          e === 0
            ? this.articleAiMarkPrompt.replace(/articleTitle/g, title)
            : this.charAiMarkPrompt.replace(/articleTitle/g, title);

        const response = await difyAiQa(
          prompt + `\n\n文章内容：\n\n${content}`,
          "blocking",
          "dify.mark.apikey"
        );

        // 如果当前请求已被中断，直接返回
        if (this.isAborted || this.currentRequestId !== requestId) {
          console.log("Request cancelled");
          return;
        }

        if (response.ok) {
          const data = await response.json();
          console.log(data);

          // 再次检查请求是否被中断
          if (this.isAborted || this.currentRequestId !== requestId) {
            console.log("Request cancelled");
            return;
          }

          if (data.answer) {
            const content = JSON.parse(data.answer).answer;
            let content2 = "";

            // 处理思考标记
            const thinkStartIndex = content.indexOf("<think>");
            const thinkEndIndex = content.indexOf("</think>");

            // 提取有效内容
            if (thinkStartIndex !== -1 && thinkEndIndex !== -1) {
              // 如果存在思考标记，只取</think>后面的内容
              content2 = content.substring(thinkEndIndex + 8).trim();
            } else {
              // 如果不存在思考标记，使用完整内容
              content2 = content.trim();
            }

            // 清理markdown标记和其他特殊字符
            content2 = content2
              // 移除markdown代码块标记
              .replace(/```markdown\s*|```\s*/g, "")
              // 移除可能存在的其他markdown语言标记，如```json等
              .replace(/```[a-zA-Z]*\s*/g, "")
              // 移除多余的空行
              .replace(/\n\s*\n\s*\n/g, "\n\n")
              // 移除行首行尾空白字符
              .trim();

            // 确保内容非空
            if (!content2) {
              throw new Error("Invalid content format");
            }

            // 最后一次检查请求是否被中断
            if (!this.isAborted && this.currentRequestId === requestId) {
              console.log("AI Response:", content2);
              this.markmapContent = content2;
            }
          } else {
            throw new Error("Invalid response format");
          }
        } else {
          throw new Error("Request failed");
        }
      } catch (error) {
        // 只处理未中断请求的错误
        if (!this.isAborted && this.currentRequestId === requestId) {
          console.error("AI Chat Error:", error);
          this.$message.error("AI解读失败，请稍后重试");
          this.markmapVisible = false;
        }
      } finally {
        // 只处理未中断请求的状态更新
        if (!this.isAborted && this.currentRequestId === requestId) {
          this.aiLoading = false;
        }
      }
    },
    decreaseAiFontSize() {
      if (this.aiFontSize > 16) {
        this.aiFontSize -= 2;
      }
    },
    increaseAiFontSize() {
      if (this.aiFontSize < 30) {
        this.aiFontSize += 2;
      }
    },
    // 添加 markmap 弹窗关闭处理方法
    handleMarkmapClose() {
      // 标记当前请求为已中断
      this.isAborted = true;
      this.currentRequestId = null;
      this.markmapContent = "";
      this.aiLoading = false;
      this.markmapVisible = false;
    },
    articleAiChat() {
      if (this.aiPlatform === "dify") {
        this.difyArticleAiChat();
      } else if (this.aiPlatform === "ollama") {
        this.ollamaArticleAiChat();
      } else if (this.aiPlatform === "deepseek") {
        this.deepseekArticleAiChat();
      }
    },
    markAiChat(e) {
      if (this.aiPlatform === "dify") {
        this.difyMarkAiChat(e);
      } else if (this.aiPlatform === "ollama") {
        this.ollamaMarkAiChat(e);
      } else if (this.aiPlatform === "deepseek") {
        this.deepseekMarkAiChat(e);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.one {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;

  .on {
    font-weight: bold;
  }

  .left {
    width: 520px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .center {
    width: 855px;

    .top {
      height: 232px;
      display: flex;
      align-items: flex-end;
      justify-content: center;
      padding-bottom: 20px;

      .top-content {
        position: relative;
        width: 324px;
        height: 112px;
        text-align: center;
        margin: 0 30px;

        .top-content-number {
          width: 170px;
          position: absolute;
          left: 126px;
          top: 16px;
          font-size: 40px;
          line-height: 47px;
          font-weight: bold;
          background: linear-gradient(180deg, #ffffff 0%, #05d5ff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .top-content-name {
          width: 170px;
          position: absolute;
          left: 126px;
          top: 68px;
          font-size: 20px;
          line-height: 28px;
          letter-spacing: 1px;
          font-weight: bold;
          background: linear-gradient(180deg, #ffffff 0%, #05d5ff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .bg1 {
        background: url("../../assets/bigScreenTwo/121.png") no-repeat;
        background-size: 100% 100% !important;
        background-size: cover;
      }

      .bg2 {
        background: url("../../assets/bigScreenTwo/131.png") no-repeat;
        background-size: 100% 100% !important;
        background-size: cover;
      }
    }
  }

  .right {
    width: 520px;
  }
}

::v-deep .el-dialog {
  background: url("../../assets/bigScreenTwo/dialogBackground.png") no-repeat;
  background-size: 100% 100% !important;
  background-size: cover;
  height: 800px;

  .el-dialog__header {
    background-color: #1d233400;
    font-size: 30px;
    color: #ffffff;
    line-height: 100px;
    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);
    height: 100px;

    .el-dialog__title {
      display: inline-block;
      width: calc(100% - 100px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .el-dialog__body {
    background-color: #2a304000;
    color: #f2f2f2;
    height: calc(100% - 100px);
    overflow: hidden;
    padding: 20px 30px;
  }

  .el-dialog__footer {
    background-color: #1d233400;
    padding: 18px 20px;
  }

  .el-button {
    background-color: #002766;
    color: #fff;
    border: 0px;
  }

  .el-dialog__headerbtn .el-dialog__close {
    background: url("../../assets/bigScreenTwo/关闭小.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
    width: 31px;
    height: 31px;
    top: 16px;

    &::before {
      content: none;
    }
  }
}

::v-deep .ai-dialog {
  .el-dialog__body {
    padding: 30px !important;
  }
}

::v-deep .el-drawer__open {
  .el-drawer {
    background: url("../../assets/bigScreenTwo/drawerBackground.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
  }

  .el-dialog__close {
    background: url("../../assets/bigScreenTwo/关闭小.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
    width: 31px;
    height: 31px;
    top: 18px;
    right: 10px;

    &::before {
      content: none;
    }
  }

  .el-drawer__header {
    background-color: #1d233400;
    font-size: 30px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);
    height: 100px;
  }

  .el-drawer__body {
    padding-bottom: 30px;
  }
}

::v-deep .el-table {
  background-color: #2a304000;

  tr {
    color: #f2f2f2;
    background: url("../../assets/bigScreenTwo/table_tr.png") no-repeat;
    background-size: 100% 100% !important;
    height: 52px;
    padding: 0 0 0 65px;
    margin-bottom: 10px;
    display: block;
    width: 790px;
  }

  td.el-table__cell {
    border-bottom: 1px solid #1d233400;
    height: 52px;
    line-height: 52px;
    font-size: 16px;
    text-shadow: 0px 0px 9px rgba(30, 198, 255, 0.8);
  }

  &::before {
    height: 0;
  }
}

::v-deep
  .el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell,
::v-deep .el-table__empty-block {
  background-color: #2a304000;
  color: #f2f2f2;
  cursor: pointer;
}

::v-deep .pagination-container {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 0;

  .el-select .el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }

  .el-pagination__editor.el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }
}

::v-deep .el-pagination__total,
::v-deep .el-pagination__jump {
  color: #f2f2f2;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination button:disabled {
  background-color: #ffffff00;
  color: #fff;
}

::v-deep .el-pager li {
  background: #ffffff00;
  color: #fff;

  &.active {
    color: #1890ff;
  }
}

.dialog-art {
  background: #1d293b;
  padding: 20px;
  height: 590px;
  line-height: 1.5;
  position: relative;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(16, 216, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background-color: rgba(16, 216, 255, 0.5);
    }
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

.dialog-articleAiThinking {
  background: #1d293b;
  padding: 20px;
  height: 590px;
  line-height: 1.5;
  position: relative;
  overflow-y: auto;

  .stream-content {
    word-break: break-all;
    word-wrap: break-word;
    color: #fff;
    white-space: pre-wrap;
    padding-right: 10px;

    ::v-deep {
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin: 0.05em 0 0.02em 0;
        font-weight: 600;
        line-height: 1;
      }

      h1 {
        font-size: 1.6em;
      }

      h2 {
        font-size: 1.4em;
      }

      h3 {
        font-size: 1.2em;
      }

      p {
        margin: 0;
        line-height: 1;
      }

      strong {
        font-weight: 600;
      }

      em {
        font-style: italic;
      }

      ul,
      ol {
        margin: 0;
        padding-left: 1em;

        li {
          margin: 0;
          line-height: 1;
          p {
            margin: 0;
            line-height: 1;
          }
        }
      }

      blockquote {
        border-left: 4px solid rgba(255, 255, 255, 0.2);
        margin: 0.1em 0;
        padding: 0.05em 0 0.05em 0.8em;
        background: rgba(255, 255, 255, 0.05);
      }

      code {
        background: rgba(255, 255, 255, 0.1);
        padding: 2px 4px;
        border-radius: 3px;
      }

      hr {
        border: none;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        margin: 0.2em 0;
      }
    }
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(16, 216, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background-color: rgba(16, 216, 255, 0.5);
    }
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

.fz {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 20px;

  .text {
    font-weight: 400;
    font-size: 20px;
    color: #ffffff;
    margin-right: 10px;
  }

  .btns {
    display: flex;
    align-items: center;
    background: #1d293b;
    border-radius: 14px;
    padding: 0 10px;
    height: 28px;

    .btn-minus,
    .btn-plus {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 20px;
      color: #ffffff;

      &:hover {
        color: #2f7cfe;
      }
    }

    .font-size {
      margin: 0 15px;
      color: #ffffff;
      font-size: 16px;
      min-width: 45px;
      text-align: center;
    }
  }
}

.drawer-more {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  width: 100%;
  height: 50px;
  color: #fff;
  background: linear-gradient(
    270deg,
    rgba(59, 114, 207, 0) 0%,
    #3b72cf 58%,
    rgba(60, 114, 209, 0) 100%
  );
  border-radius: 5px;
  border: 1px solid;
  border-image: linear-gradient(
      90deg,
      rgba(235, 245, 255, 0),
      rgba(210, 231, 253, 1),
      rgba(210, 231, 253, 0)
    )
    1 1;
  cursor: pointer;
}

.thinking-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 590px;
  background: #1d293b;
  padding: 20px;

  .thinking-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;

    span {
      display: inline-block;
      width: 12px;
      height: 12px;
      margin: 0 6px;
      background-color: #10d8ff;
      border-radius: 50%;
      animation: thinking 1.4s infinite ease-in-out both;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }

  .thinking-text {
    color: #ffffff;
    font-size: 18px;
    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);
  }
}

@keyframes thinking {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.3;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

::v-deep .el-button--mini {
  padding: 7px 15px;

  .el-icon-chat-dot-round {
    margin-right: 3px;
    font-size: 12px;
    color: #1bdcff;
  }

  &:hover {
    background: rgba(16, 216, 255, 0.3);
    border-color: rgba(16, 216, 255, 0.6);
  }

  span {
    font-size: 12px;
    color: rgba(216, 240, 255, 0.8);
  }
}

.ai-chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  // background: #fff;

  .chat-messages {
    flex: 1;
    overflow-y: auto;

    .message {
      margin-bottom: 28px;
      display: flex;
      align-items: flex-start;

      .avatar {
        width: 42px;
        height: 42px;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;
        border: 2px solid #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          background-color: #fff;
        }
      }

      .message-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
      }

      .message-content {
        padding: 12px 16px;
        border-radius: 12px;
        font-size: 14px;
        line-height: 1.6;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        position: relative;

        &::before {
          content: "";
          position: absolute;
          top: 14px;
          width: 0;
          height: 0;
          border: 6px solid transparent;
        }
      }
    }

    .user-message {
      flex-direction: row-reverse;

      .message-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
      }

      .message-content {
        background-color: #e6f3ff;
        color: #2d2d2d;

        &::before {
          right: -12px;
          border-left-color: #e6f3ff;
        }
      }
    }

    .ai-message {
      .message-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }

      .message-content {
        background-color: #fff;
        color: #2d2d2d;

        &::before {
          left: -12px;
          border-right-color: #fff;
        }
      }
    }
  }

  .thinking-animation {
    display: inline-flex;
    align-items: center;
    padding: 12px 16px;
    min-height: 45px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: 14px;
      left: -12px;
      width: 0;
      height: 0;
      border: 6px solid transparent;
      border-right-color: #fff;
    }

    span {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin: 0 3px;
      background-color: #409eff;
      border-radius: 50%;
      opacity: 0.7;
      animation: thinking 1.4s infinite ease-in-out both;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }

  .message-content {
    min-height: 45px;
    display: flex;
    align-items: flex-start;
    white-space: pre-wrap;

    ::v-deep p {
      margin: 0;
      line-height: 1.6;
    }
  }
}

.chat-messages {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(192, 196, 204, 0.5);
    border-radius: 3px;

    &:hover {
      background-color: rgba(192, 196, 204, 0.8);
    }
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}
</style>
