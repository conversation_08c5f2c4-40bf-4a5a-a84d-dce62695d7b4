{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\components\\TreeTable\\index.vue?vue&type=style&index=0&id=efa2b55a&lang=scss&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\components\\TreeTable\\index.vue", "mtime": 1754287187220}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnRyZWUtdGFibGUtY29udGFpbmVyIHsNCiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gNThweCk7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQp9DQoNCi5hY3Rpb24tY29udGFpbmVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KDQouZmlsdGVyLWNvbnRhaW5lciB7DQogIGZsZXg6IDAgMCBhdXRvOw0KICBwYWRkaW5nLWJvdHRvbTogMDsNCn0NCg0KLmJ1dHRvbi1jb250YWluZXIgew0KICBmbGV4OiAwIDAgYXV0bzsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLnNlbGVjdGVkLXNvdXJjZXMtaW5mbyB7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIHBhZGRpbmc6IDhweCAxMnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjBmOWZmOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZTFmNWZlOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg0KLnNlbGVjdGVkLXRleHQgew0KICBjb2xvcjogIzE5NzZkMjsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLnRhYmxlLWNvbnRhaW5lciB7DQogIGZsZXg6IDE7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5uYW1lLWNlbGwgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nLWxlZnQ6IDEwcHg7DQp9DQoNCi5uYW1lLXRleHQgew0KICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KfQ0KDQo6OnYtZGVlcCAuY291bnRyeS10ZXh0IHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWxlZnQ6IDRweDsNCg0KICAuY291bnRyeS10YWcgew0KICAgIHNwYW4gew0KICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgICB9DQogIH0NCn0NCg0KLmNvdW50LXRleHQgew0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIG1hcmdpbi1sZWZ0OiA0cHg7DQogIHdoaXRlLXNwYWNlOiBub3dyYXA7DQp9DQoNCi50cmVlVGFibGUtcGFnaW5hdGlvbiB7DQogIGZsZXg6IDAgMCBhdXRvOw0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDEwcHggMDsNCn0NCg0KLmxpbmstaWNvbiB7DQogIGNvbG9yOiAjNDA5ZWZmOw0KICBjdXJzb3I6IHBvaW50ZXI7DQogIG1hcmdpbi1sZWZ0OiA0cHg7DQogIGZvbnQtc2l6ZTogMThweDsNCn0NCg0KLmxpbmstaWNvbjpob3ZlciB7DQogIGNvbG9yOiAjNjZiMWZmOw0KfQ0KDQo6OnYtZGVlcCAuZWwtcGFnaW5hdGlvbl9fc2l6ZXMgew0KICBtYXJnaW4tdG9wOiAtM3B4Ow0KfQ0KDQo6OnYtZGVlcCAuZWwtdGFibGVfX2NlbGwgLmNlbGwgew0KICBwYWRkaW5nOiAwICFpbXBvcnRhbnQ7DQogIG1hcmdpbjogMCAhaW1wb3J0YW50Ow0KICBwYWRkaW5nLWxlZnQ6IDAgIWltcG9ydGFudDsNCn0NCg0KOjp2LWRlZXAgLmNsYXNzaWZ5LXNlbGVjdCB7DQogIC5lbC1pbnB1dF9faW5uZXIgew0KICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgfQ0KfQ0KDQo6OnYtZGVlcCAuY291bnRyeS1zZWxlY3Qgew0KICAuZWwtaW5wdXRfX2lubmVyIHsNCiAgICBmb250LXNpemU6IDE0cHg7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAioBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/TreeTable", "sourcesContent": ["<template>\r\n  <div\r\n    class=\"tree-table-container\"\r\n    v-loading=\"loading\"\r\n    element-loading-text=\"数据加载中\"\r\n  >\r\n    <!-- 搜索框 -->\r\n    <div class=\"search-container\">\r\n      <el-input\r\n        placeholder=\"输入关键字进行过滤\"\r\n        v-model=\"filterText\"\r\n        clearable\r\n        class=\"input_Fixed\"\r\n        style=\"margin-bottom: 10px\"\r\n      >\r\n        <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n      </el-input>\r\n    </div>\r\n\r\n    <!-- 操作按钮行 -->\r\n    <div class=\"action-container\">\r\n      <!-- 数据源分类筛选 -->\r\n      <div class=\"filter-container\">\r\n        <el-select\r\n          v-model=\"selectedClassify\"\r\n          placeholder=\"数据源分类\"\r\n          clearable\r\n          size=\"mini\"\r\n          style=\"width: 120px\"\r\n          @change=\"handleClassifyChange\"\r\n          class=\"classify-select\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.think_tank_class\"\r\n            :label=\"dict.label\"\r\n            :key=\"'think_tank_class' + dict.value\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n\r\n        <!-- 按国家筛选 - 仅在MonitorUse页面且id=1时显示 -->\r\n        <!-- <el-select\r\n          v-if=\"showCountryFilter\"\r\n          v-model=\"selectedCountry\"\r\n          placeholder=\"国家\"\r\n          clearable\r\n          size=\"mini\"\r\n          style=\"width: 100px; margin-left: 5px\"\r\n          @change=\"handleCountryChange\"\r\n          class=\"country-select\"\r\n        >\r\n          <el-option\r\n            v-for=\"(dict, index) in dict.type.country\"\r\n            :label=\"dict.label\"\r\n            :key=\"'country' + dict.value\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select> -->\r\n      </div>\r\n\r\n      <!-- 全选、取消选中和重置按钮 -->\r\n      <div class=\"button-container\">\r\n        <el-tooltip\r\n          class=\"item\"\r\n          effect=\"dark\"\r\n          content=\"全选当前页\"\r\n          placement=\"top\"\r\n        >\r\n          <el-button\r\n            icon=\"el-icon-check\"\r\n            @click=\"handleSelectAll\"\r\n            type=\"text\"\r\n            style=\"color: #409eff; padding: 0\"\r\n          >\r\n            全选\r\n          </el-button>\r\n        </el-tooltip>\r\n        <el-tooltip\r\n          class=\"item\"\r\n          effect=\"dark\"\r\n          content=\"取消所有选中\"\r\n          placement=\"top\"\r\n        >\r\n          <el-button\r\n            icon=\"el-icon-close\"\r\n            @click=\"handleClearAll\"\r\n            type=\"text\"\r\n            style=\"color: #f56c6c; padding: 0\"\r\n          >\r\n            取消选中\r\n          </el-button>\r\n        </el-tooltip>\r\n        <el-tooltip class=\"item\" effect=\"dark\" content=\"重置\" placement=\"top\">\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"handleReset\"\r\n            type=\"text\"\r\n            style=\"color: #666; padding: 0\"\r\n          >\r\n            重置\r\n          </el-button>\r\n        </el-tooltip>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 已勾选数据源显示行 -->\r\n    <div\r\n      class=\"selected-sources-info\"\r\n      v-if=\"selectedSources && selectedSources.length > 0\"\r\n    >\r\n      <span class=\"selected-text\">\r\n        {{ getSelectedSourcesText }}\r\n      </span>\r\n    </div>\r\n\r\n    <!-- 表格 -->\r\n    <div class=\"table-container\">\r\n      <el-table\r\n        :data=\"tableData\"\r\n        style=\"font-size: 16px\"\r\n        :height=\"tableHeight\"\r\n        :show-header=\"false\"\r\n        @row-click=\"handleRowClick\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        ref=\"table\"\r\n        :row-key=\"rowKey\"\r\n        size=\"small\"\r\n        border\r\n      >\r\n        <!-- 序号列 -->\r\n        <el-table-column type=\"index\" label=\"序号\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <!-- 复选框列 -->\r\n        <el-table-column\r\n          type=\"selection\"\r\n          width=\"30\"\r\n          align=\"center\"\r\n        ></el-table-column>\r\n\r\n        <!-- 名称+数量+链接列 -->\r\n        <el-table-column label=\"名称\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"name-cell\">\r\n              <span class=\"name-text\">{{ scope.row.label }}</span>\r\n              <div\r\n                class=\"country-text\"\r\n                v-if=\"scope.row.country && scope.row.country !== '0'\"\r\n              >\r\n                <div>[</div>\r\n                <dict-tag\r\n                  :options=\"dict.type.country\"\r\n                  :value=\"scope.row.country\"\r\n                  class=\"country-tag\"\r\n                />\r\n                <div>]</div>\r\n              </div>\r\n              <span class=\"count-text\" v-if=\"scope.row.count !== undefined\">\r\n                ({{ scope.row.count }})\r\n              </span>\r\n              <el-tooltip\r\n                v-if=\"scope.row.url\"\r\n                content=\"打开数据源链接\"\r\n                placement=\"top-start\"\r\n                effect=\"light\"\r\n              >\r\n                <i\r\n                  class=\"el-icon-connection link-icon\"\r\n                  @click.stop=\"openUrl(scope.row.url)\"\r\n                ></i>\r\n              </el-tooltip>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n\r\n    <!-- 分页 -->\r\n    <div class=\"treeTable-pagination\">\r\n      <el-pagination\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"currentPage\"\r\n        :pager-count=\"5\"\r\n        :page-sizes=\"[50, 100, 150, 200]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\"total, sizes, prev, pager, next\"\r\n        :total=\"total\"\r\n        small\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"SimpleTreeTable\",\r\n  props: {\r\n    data: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n    rowKey: {\r\n      type: String,\r\n      default: \"id\",\r\n    },\r\n    currentPage: {\r\n      type: Number,\r\n      default: 1,\r\n    },\r\n    pageSize: {\r\n      type: Number,\r\n      default: 20,\r\n    },\r\n    total: {\r\n      type: Number,\r\n      default: 0,\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    // 已勾选的数据源列表，用于显示勾选信息\r\n    selectedSources: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n  },\r\n  dicts: [\"country\", \"think_tank_class\"],\r\n  data() {\r\n    return {\r\n      filterText: \"\",\r\n      selectedData: [], // 简单的选中数据数组\r\n      isPagingOperation: false, // 标记是否是分页操作\r\n      searchDebounceTimer: null, // 搜索防抖定时器\r\n      selectedClassify: null, // 选中的数据源分类\r\n      selectedCountry: null, // 选中的国家\r\n      dynamicTableHeight: null, // 动态计算的表格高度\r\n    };\r\n  },\r\n  computed: {\r\n    tableData() {\r\n      // 直接返回传入的数据，过滤由后端接口处理\r\n      return this.data;\r\n    },\r\n    // 判断是否显示国家筛选下拉框\r\n    showCountryFilter() {\r\n      // 当当前页面是MonitorUse并且地址上的id参数等于1时才显示\r\n      return (\r\n        this.$route.path == \"/MonitorUse\" &&\r\n        !this.$route.query.menuType &&\r\n        this.$route.query.id === \"1\"\r\n      );\r\n    },\r\n    // 获取已勾选数据源的显示文本\r\n    getSelectedSourcesText() {\r\n      if (!this.selectedSources || this.selectedSources.length === 0) {\r\n        return \"\";\r\n      }\r\n\r\n      const totalCount = this.selectedSources.length;\r\n\r\n      if (totalCount <= 3) {\r\n        // 小于等于3个时，显示所有名称，不显示\"等X个数据源\"\r\n        const names = this.selectedSources.map(\r\n          (item) => item.label || item.name\r\n        );\r\n        return `当前已勾选${names.join(\"、\")}`;\r\n      } else {\r\n        // 超过3个时，显示前3个名称加上剩余数量\r\n        const names = this.selectedSources\r\n          .slice(0, 3)\r\n          .map((item) => item.label || item.name);\r\n        const remainingCount = totalCount - 3;\r\n        return `当前已勾选${names.join(\"、\")}等${remainingCount}个数据源`;\r\n      }\r\n    },\r\n    // 计算表格高度\r\n    tableHeight() {\r\n      return this.dynamicTableHeight || \"auto\";\r\n    },\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler() {\r\n        // 数据变化时，只滚动到顶部，不清空选择\r\n        this.$nextTick(() => {\r\n          // 滚动到顶部\r\n          this.scrollToTop();\r\n        });\r\n        // 重新计算表格高度\r\n        this.updateTableHeight();\r\n      },\r\n      immediate: true,\r\n    },\r\n    currentPage: {\r\n      handler() {\r\n        // 分页变化时滚动到顶部\r\n        this.$nextTick(() => {\r\n          this.scrollToTop();\r\n        });\r\n      },\r\n    },\r\n    filterText: {\r\n      handler(newVal) {\r\n        // 清除之前的防抖定时器\r\n        if (this.searchDebounceTimer) {\r\n          clearTimeout(this.searchDebounceTimer);\r\n        }\r\n\r\n        // 设置防抖，500ms后执行搜索\r\n        this.searchDebounceTimer = setTimeout(() => {\r\n          this.handleFilterSearch(newVal);\r\n        }, 500);\r\n      },\r\n    },\r\n    // 监听已选择数据源变化，重新计算高度\r\n    selectedSources: {\r\n      handler() {\r\n        this.updateTableHeight();\r\n      },\r\n      deep: true,\r\n    },\r\n  },\r\n  methods: {\r\n    // 处理行点击 - 重写版本（点击行是单选，直接替换保存勾选数据）\r\n    handleRowClick(row, column) {\r\n      // 如果点击的是复选框列，不处理\r\n      if (column && column.type === \"selection\") {\r\n        return;\r\n      }\r\n\r\n      console.log(\"行点击（单选）:\", row.label);\r\n\r\n      // 检查当前行是否已选中\r\n      const isSelected = this.selectedData.some(\r\n        (item) => item[this.rowKey] === row[this.rowKey]\r\n      );\r\n\r\n      if (isSelected) {\r\n        // 如果已选中，则取消选中\r\n        this.selectedData = this.selectedData.filter(\r\n          (item) => item[this.rowKey] !== row[this.rowKey]\r\n        );\r\n        // 直接操作表格取消选中\r\n        this.$refs.table.toggleRowSelection(row, false);\r\n      } else {\r\n        // 如果未选中，则清空其他选择，只选中当前行（单选）\r\n        this.selectedData = [{ ...row }];\r\n        // 直接操作表格：先清空，再选中当前行\r\n        this.$refs.table.clearSelection();\r\n        this.$refs.table.toggleRowSelection(row, true);\r\n      }\r\n\r\n      console.log(\r\n        \"行点击后选中数据:\",\r\n        this.selectedData.map((item) => item.label)\r\n      );\r\n\r\n      // 触发父组件事件（行点击是单选，直接替换）\r\n      this.$emit(\"selection-change\", this.selectedData, \"row-click\");\r\n    },\r\n\r\n    // 处理复选框选择变化（点击勾选框是多选，往里面push或删除）\r\n    handleSelectionChange(selection) {\r\n      this.selectedData = selection.map((item) => ({ ...item }));\r\n\r\n      // 如果是分页操作导致的选择变化，不触发父组件事件\r\n      if (!this.isPagingOperation) {\r\n        console.log(\"复选框变化触发父组件事件（这会更新保存的勾选数据）\");\r\n        this.$emit(\"selection-change\", this.selectedData, \"checkbox-change\");\r\n      }\r\n    },\r\n\r\n    // 处理重置（重置才更新保存的勾选数据）\r\n    handleReset() {\r\n      // 清空搜索关键字（会触发 watch 调用后端接口）\r\n      this.filterText = \"\";\r\n      this.selectedClassify = null; // 重置数据源分类选择\r\n      this.selectedCountry = null; // 重置国家筛选选择\r\n      this.selectedData = [];\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n      console.log(\"重置，触发父组件事件（这会更新保存的勾选数据）\");\r\n      this.$emit(\"reset\");\r\n      // 滚动到顶部\r\n      this.scrollToTop();\r\n    },\r\n\r\n    // 处理全选当前页（全选是把当页所有数据都push进去）\r\n    handleSelectAll() {\r\n      if (this.tableData && this.tableData.length > 0) {\r\n        // 选中当前页所有数据\r\n        this.selectedData = this.tableData.map((item) => ({ ...item }));\r\n\r\n        // 更新表格选中状态\r\n        if (this.$refs.table) {\r\n          this.$refs.table.clearSelection();\r\n          this.tableData.forEach((row) => {\r\n            this.$refs.table.toggleRowSelection(row, true);\r\n          });\r\n        }\r\n\r\n        console.log(\"全选当前页，触发父组件事件（这会更新保存的勾选数据）\");\r\n        // 触发父组件事件（全选需要追加数据）\r\n        this.$emit(\"selection-change\", this.selectedData, \"select-all\");\r\n      }\r\n    },\r\n\r\n    // 处理取消所有选中（取消选中是当页所有数据都从保存勾选数据中删除）\r\n    handleClearAll() {\r\n      // 清空选中数据\r\n      this.selectedData = [];\r\n\r\n      // 清空表格选中状态\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n\r\n      console.log(\"取消所有选中，触发父组件事件（这会更新保存的勾选数据）\");\r\n      // 触发父组件事件（取消选中是直接替换为空）\r\n      this.$emit(\"selection-change\", this.selectedData, \"clear-all\");\r\n    },\r\n\r\n    // 处理数据源分类变化（不更新保存的勾选数据）\r\n    handleClassifyChange(value) {\r\n      this.isPagingOperation = true;\r\n      // 清空选中数据（仅清空界面显示，不更新保存的勾选数据）\r\n      this.selectedData = [];\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n\r\n      console.log(\"数据源分类变化，不更新保存的勾选数据\");\r\n      // 触发父组件事件，只更新左侧列表，不更新右侧列表\r\n      this.$emit(\"classify-change\", value);\r\n      // 滚动到顶部\r\n      this.scrollToTop();\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          this.isPagingOperation = false;\r\n        }, 100);\r\n      });\r\n    },\r\n\r\n    // 处理国家筛选变化（不更新保存的勾选数据）\r\n    handleCountryChange(value) {\r\n      this.isPagingOperation = true;\r\n      // 清空选中数据（仅清空界面显示，不更新保存的勾选数据）\r\n      this.selectedData = [];\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n\r\n      console.log(\"国家筛选变化，不更新保存的勾选数据\");\r\n      // 触发父组件事件，只更新左侧列表，不更新右侧列表\r\n      this.$emit(\"country-change\", value);\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          this.isPagingOperation = false;\r\n        }, 100);\r\n      });\r\n    },\r\n\r\n    // 处理分页大小变化（不更新保存的勾选数据）\r\n    handleSizeChange(size) {\r\n      this.isPagingOperation = true;\r\n      this.selectedData = [];\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n      console.log(\"分页大小变化，不更新保存的勾选数据\");\r\n      this.$emit(\"size-change\", size);\r\n      // 滚动到顶部\r\n      this.scrollToTop();\r\n      // 延迟重置标记\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          this.isPagingOperation = false;\r\n        }, 100);\r\n      });\r\n    },\r\n\r\n    // 处理当前页变化（不更新保存的勾选数据）\r\n    handleCurrentChange(page) {\r\n      this.isPagingOperation = true;\r\n      this.selectedData = [];\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n      console.log(\"当前页变化，不更新保存的勾选数据\");\r\n      this.$emit(\"current-change\", page);\r\n      // 滚动到顶部\r\n      this.scrollToTop();\r\n      // 延迟重置标记\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          this.isPagingOperation = false;\r\n        }, 100);\r\n      });\r\n    },\r\n\r\n    // 打开链接\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTop() {\r\n      this.$nextTick(() => {\r\n        if (this.$refs.table && this.$refs.table.bodyWrapper) {\r\n          this.$refs.table.bodyWrapper.scrollTop = 0;\r\n        }\r\n        // 如果表格的 bodyWrapper 不存在，尝试其他方式\r\n        else if (this.$refs.table && this.$refs.table.$el) {\r\n          const tableBody = this.$refs.table.$el.querySelector(\r\n            \".el-table__body-wrapper\"\r\n          );\r\n          if (tableBody) {\r\n            tableBody.scrollTop = 0;\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 处理过滤搜索（不更新保存的勾选数据）\r\n    handleFilterSearch(keyword) {\r\n      // 触发父组件的过滤搜索事件，传递 filterwords 参数\r\n      this.isPagingOperation = true;\r\n      this.selectedData = [];\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n      console.log(\"关键字过滤，不更新保存的勾选数据\");\r\n      this.$emit(\"filter-search\", keyword);\r\n      // 滚动到顶部\r\n      this.scrollToTop();\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          this.isPagingOperation = false;\r\n        }, 100);\r\n      });\r\n    },\r\n\r\n    // 恢复选中状态（供父组件调用）\r\n    restoreSelection(itemsToSelect) {\r\n      if (!itemsToSelect || itemsToSelect.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // 更新内部选中数据\r\n      this.selectedData = itemsToSelect.map((item) => ({ ...item }));\r\n\r\n      // 等待表格渲染完成后设置选中状态\r\n      this.$nextTick(() => {\r\n        if (this.$refs.table) {\r\n          // 先清空选择\r\n          this.$refs.table.clearSelection();\r\n\r\n          // 逐个设置选中状态\r\n          itemsToSelect.forEach((item) => {\r\n            // 在当前表格数据中查找对应的行\r\n            const tableRow = this.tableData.find(\r\n              (row) => row[this.rowKey] === item[this.rowKey]\r\n            );\r\n            if (tableRow) {\r\n              this.$refs.table.toggleRowSelection(tableRow, true);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 静默恢复选中状态（不触发 selection-change 事件）\r\n    restoreSelectionSilently(itemsToSelect) {\r\n      if (!itemsToSelect || itemsToSelect.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // 设置标记，避免触发 selection-change 事件\r\n      this.isPagingOperation = true;\r\n\r\n      // 更新内部选中数据\r\n      this.selectedData = itemsToSelect.map((item) => ({ ...item }));\r\n\r\n      // 等待表格渲染完成后设置选中状态\r\n      this.$nextTick(() => {\r\n        if (this.$refs.table) {\r\n          // 先清空选择\r\n          this.$refs.table.clearSelection();\r\n\r\n          // 逐个设置选中状态\r\n          itemsToSelect.forEach((item) => {\r\n            // 在当前表格数据中查找对应的行\r\n            const tableRow = this.tableData.find(\r\n              (row) => row[this.rowKey] === item[this.rowKey]\r\n            );\r\n            if (tableRow) {\r\n              this.$refs.table.toggleRowSelection(tableRow, true);\r\n            }\r\n          });\r\n        }\r\n\r\n        // 延迟重置标记\r\n        setTimeout(() => {\r\n          this.isPagingOperation = false;\r\n        }, 100);\r\n      });\r\n    },\r\n\r\n    // 计算并更新表格高度\r\n    updateTableHeight() {\r\n      this.$nextTick(() => {\r\n        const tableContainer = this.$el.querySelector(\".table-container\");\r\n        if (tableContainer) {\r\n          const containerHeight = tableContainer.clientHeight;\r\n          this.dynamicTableHeight =\r\n            containerHeight > 0 ? containerHeight : \"auto\";\r\n        }\r\n      });\r\n    },\r\n  },\r\n  mounted() {\r\n    // 初始化表格高度\r\n    this.updateTableHeight();\r\n\r\n    // 监听窗口大小变化\r\n    window.addEventListener(\"resize\", this.updateTableHeight);\r\n  },\r\n  beforeDestroy() {\r\n    // 清理事件监听器\r\n    window.removeEventListener(\"resize\", this.updateTableHeight);\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tree-table-container {\r\n  height: calc(100vh - 58px);\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.action-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.filter-container {\r\n  flex: 0 0 auto;\r\n  padding-bottom: 0;\r\n}\r\n\r\n.button-container {\r\n  flex: 0 0 auto;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.selected-sources-info {\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background-color: #f0f9ff;\r\n  border: 1px solid #e1f5fe;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n.selected-text {\r\n  color: #1976d2;\r\n  font-weight: 500;\r\n}\r\n\r\n.table-container {\r\n  flex: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  padding-left: 10px;\r\n}\r\n\r\n.name-text {\r\n  white-space: nowrap;\r\n}\r\n\r\n::v-deep .country-text {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-left: 4px;\r\n\r\n  .country-tag {\r\n    span {\r\n      white-space: nowrap;\r\n    }\r\n  }\r\n}\r\n\r\n.count-text {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin-left: 4px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.treeTable-pagination {\r\n  flex: 0 0 auto;\r\n  text-align: center;\r\n  padding: 10px 0;\r\n}\r\n\r\n.link-icon {\r\n  color: #409eff;\r\n  cursor: pointer;\r\n  margin-left: 4px;\r\n  font-size: 18px;\r\n}\r\n\r\n.link-icon:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n::v-deep .el-pagination__sizes {\r\n  margin-top: -3px;\r\n}\r\n\r\n::v-deep .el-table__cell .cell {\r\n  padding: 0 !important;\r\n  margin: 0 !important;\r\n  padding-left: 0 !important;\r\n}\r\n\r\n::v-deep .classify-select {\r\n  .el-input__inner {\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n::v-deep .country-select {\r\n  .el-input__inner {\r\n    font-size: 14px;\r\n  }\r\n}\r\n</style>\r\n"]}]}