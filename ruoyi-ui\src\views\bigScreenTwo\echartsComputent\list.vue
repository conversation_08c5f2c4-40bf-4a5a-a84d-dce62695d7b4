<template>
  <div class="remengwenzhang-box">
    <div class="remengwenzhang-box-dynamic" style="height: 100%" :style="`margin-top: -${listHeight}px;`">
      <div class="remengwenzhang-list" v-for="(item, index) in remengwenzhangList" @click="openNewView(item)">
        <div class="block" :style="{ 'background': item.color ? item.color : '#1bdcff' }"></div>
        <div class="title">{{ item.title }}</div>
        <div class="sourceName">
          {{ item.sourceName }}
        </div>
        <div class="time">
          {{ parseTime(item.publishTime, "{y}-{m}-{d}") }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { largeHotList } from "@/api/bigScreen/index1";

export default {
  data() {
    return {
      remengwenzhangList: [],
      listHeight: 0,
    };
  },
  components: {
  },
  mounted() {
    this.init();
  },
  beforeDestroy() { },
  methods: {
    init() {
      largeHotList({}, { pageNum: 1, pageSize: 100 }).then((res) => {
        this.$set(this, "remengwenzhangList", res.rows);
        let max = res.rows.length * 44 - 400;
        if (max > 0) {
          // 定时器
          this.listHeight = 0;
          let timer = setInterval(() => {
            this.listHeight += 1;
            if (this.listHeight >= max) {
              // clearInterval(timer)
              this.listHeight = 0;
            }
          }, 100);
        }
      });
    },
    openNewView(item) {
      this.$emit('openNewView', item)
    }
  },
};
</script>
<style lang="scss" scoped>
.remengwenzhang-box {
  width: 100%;
  height: 100%;
  padding: 20px;
  border: 1px solid rgba(16, 216, 255, 0.4);
  background: rgba(0, 0, 0, 0.15);
  box-shadow: 0px 0px 8px 0px #0056AD;
  overflow: hidden;
  margin-bottom: 20px;

  .remengwenzhang-box-dynamic {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .remengwenzhang-list {
    position: relative;
    height: 20px;
    margin: 12px 0px 12px 20px;
    padding-right: 15px;
    width: 100%;
    display: flex;
    justify-content: space-around;

    .title {
      width: 480px;
      overflow: hidden;
      color: rgba(216, 240, 255, 0.8);
      text-overflow: ellipsis;
      font-family: "Source Han Sans CN";
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .time {
      width: 150px;
      color: rgba(216, 240, 255, 0.8);
      text-align: right;
      font-family: "Source Han Sans CN";
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .sourceName {
      width: 150px;
      color: rgba(216, 240, 255, 0.8);
      text-align: right;
      font-family: "Source Han Sans CN";
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .block {
      position: absolute;
      left: -20px;
      top: 6px;
      width: 10px;
      height: 10px;
      border-radius: 1px;
      background: #1bdcff;
    }
  }
}
</style>
    