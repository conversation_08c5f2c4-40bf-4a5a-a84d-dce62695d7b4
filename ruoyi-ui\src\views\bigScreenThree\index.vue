<template>
  <!-- <v-scale-screen width="1920" height="1080"> -->
  <div class="bigMap">
    <div class="bigMap-bg">
      <!-- 上下结构 -->
      <!-- 上 -->
      <el-row class="top">
        <el-col :span="8" class="center-left">
          <img src="@/assets/bigScreenThree/logo.png" alt="" />
        </el-col>
        <el-col :span="8" class="center-content">
          全球开源科技情报地平线扫描分析平台
        </el-col>
        <el-col :span="8">
          <div class="right-content">
            <div class="h1" style="margin-right: 32px">
              {{ currentTime ? parseTime(currentTime, "{y}-{m}-{d}") : "" }}
            </div>
            <div class="h1">
              {{ currentTime ? parseTime(currentTime, "{h}:{i}") : "" }}
            </div>
            <div
              style="
                width: 50px;
                height: 100%;
                display: flex;
                justify-content: center;
              "
            >
              <el-tooltip
                class="item"
                effect="dark"
                content="登出"
                placement="bottom"
              >
                <i
                  class="el-icon-switch-button"
                  style="font-size: 26px; margin-top: 20px"
                  @click="logout"
                ></i>
              </el-tooltip>
            </div>
          </div>
        </el-col>
      </el-row>
      <!-- 下 -->
      <el-row class="bottom">
        <!-- <div class="tabs-all">
            <div v-for="(item, index) in tabList" :key="index" :class="['tabs', { active: index === activeKey }]"
              @click="changeTabsFun(index)">
              <span>{{ item.name }}</span>
            </div>
          </div> -->
        <tabOne v-if="activeKey == 0" />
        <tabTwo v-else-if="activeKey == 1" />
        <tabThree v-else />
        <div class="bottom-border"></div>
      </el-row>
    </div>
  </div>
  <!-- </v-scale-screen> -->
</template>
<script>
import tabOne from "./tabOne";
import tabTwo from "./tabTwo";
import tabThree from "./tabThree";
import autofit from "autofit.js";

export default {
  data() {
    return {
      activeKey: 0,
      currentTime: "",
      timer: null,
      tabList: [
        {
          name: "智能分析",
          index: 1,
        },
        {
          name: "人工智能",
          index: 2,
        },
        {
          name: "网络安全",
          index: 3,
        },
        {
          name: "经济安全",
          index: 4,
        },
      ],
    };
  },
  components: { tabThree, tabOne, tabTwo },
  created() {
    this.timer = setInterval(() => {
      this.currentTime = new Date();
      let weekDays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      this.week = weekDays[this.currentTime.getDay()];
    }, 1000);
  },
  mounted() {
    autofit.init({
      el: "#app",
      dw: 1920,
      dh: 919,
      resize: true,
      // ignore: [
      //   {
      //     el: ".top-content", //必填
      //     // height: "300px", //可选，需注明单位
      //     // width: "80%", //可选，需注明单位
      //     // scale: 1.1, //可选：回放程度，基于主元素缩放后的大小
      //     // fontSize: 26, //可选，如果自定义缩放后文字大小不合适，可以在这里设置文字大小
      //   },
      //   {
      //     el: ".mapBox", //必填
      //     // height: "300px", //可选，需注明单位
      //     // width: "80%", //可选，需注明单位
      //     // scale: 1.2, //可选：回放程度，基于主元素缩放后的大小
      //     // fontSize: 26, //可选，如果自定义缩放后文字大小不合适，可以在这里设置文字大小
      //   },
      // ],
    });
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  methods: {
    //tabs点击事件
    changeTabsFun(index) {
      // this.activeKey = index;
    },
    async logout() {
      this.$confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.dispatch("LogOut").then(() => {
            location.href = "/index";
          });
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.screen-box {
  background: #091c3c !important;
  // background: url("../../assets/bigScreenThree/bg.png") 0px 0px no-repeat !important;
  // background-color: #091C3C !important;
}
.bigMap {
  background: #081f46;
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0px;
  margin: 0px;
  font-family: "pingFangMedium";
  display: flex;
  flex-direction: column;

  .bigMap-bg {
    background: url("../../assets/bigScreenThree/bg.png") 0px 0px no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
    font-size: 14px;
    color: #ffffff;
    position: relative;
    width: 100%;
    height: 100%;
    padding: 0px;
    margin: 0px;
    font-family: "pingFangMedium";
    display: flex;
    flex-direction: column;

    .top {
      height: 82px;
      background: url("../../assets/bigScreenThree/bg-top.png") no-repeat 0px -3px !important;
      background-size: 100% 100% !important;

      //左边
      .center-left {
        padding-left: 20px;
        height: 82px;

        img {
          width: 182.88px;
          display: inline-block;
          margin-top: 16px;
        }
      }

      //中间
      .center-content {
        text-align: center;
        font-size: 34px;
        color: linear-gradient(270deg, #87daff 0%, #ffffff 100%);
        background: linear-gradient(270deg, #87daff 0%, #ffffff 100%);
        /* 使用渐变背景时，通常需要设置这些值以确保文字可读 */
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-family: "pingFangMedium";
        height: 82px;
        line-height: 75px;
        font-weight: bold;
        letter-spacing: 3px;
      }

      //右边
      .right-content {
        display: flex;
        font-family: "pingFangMedium";
        justify-content: end;
        padding-right: 24px;

        .h1 {
          width: 126px;
          height: 36px;
          text-align: center;
          line-height: 36px;
          background: url("../../assets/bigScreenThree/time.png") no-repeat 0px
            0px !important;
          background-size: 100% 100% !important;

          color: #fff;
          display: inline-block;
          font-size: 16px;
          margin-left: 24px;
          font-weight: bold;
          margin-top: 16px;
        }
      }
    }

    .bottom {
      flex: 1;
      height: calc(100% - 81px);
      position: relative;
      padding: 0 10px 11px 10px;

      .tabs-all {
        .tabs {
          writing-mode: vertical-rl;
          /* 文字从上到下，从右到左 */
          height: 143px;
          width: 46px;
          font-weight: 600;
          font-size: 18px;
          color: #ffffff;
          line-height: 44px;
          text-align: center;
          font-style: normal;
          background: url("../../assets/bigScreenThree/tab.png") no-repeat 0px
            0px !important;
          background-size: 100% 100% !important;
          letter-spacing: 2px;
          margin-bottom: 7px;
          cursor: pointer;
        }

        .active {
          background: url("../../assets/bigScreenThree/tab-active.png")
            no-repeat 0px 0px !important;
          background-size: 100% 100% !important;
        }

        position: absolute;
        right: 540px;
        top: 20px;
        z-index: 99;
      }

      .bottom-border {
        background: url("../../assets/bigScreenTwo/底部.png") no-repeat;
        position: fixed;
        width: 100%;
        height: 42px;
        background-size: 100% 100% !important;
        left: calc(1980px / 2 - 1901px / 2);
        bottom: 0;
      }
    }
  }
}
</style>
