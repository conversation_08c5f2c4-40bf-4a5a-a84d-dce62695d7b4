{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\KeDrawer.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\KeDrawer.vue", "mtime": 1754357440985}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRMaXN0Q2xhc3NpZnkgfSBmcm9tICJAL2FwaS9hcnRpY2xlL2NsYXNzaWZ5IjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBwcm9wczogew0KICAgIHRpdGxlOiB7DQogICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICB9LA0KICAgIHZpc2JsZTogew0KICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgfSwNCiAgICBlZGl0RGF0YTogew0KICAgICAgcmVxdWlyZWQ6IGZhbHNlLA0KICAgIH0sDQogICAgY291bnRyeUxpc3Q6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgIH0sDQogICAgYXJlYUxpc3Q6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgIH0sDQogIH0sDQogIGRpY3RzOiBbInRoaW5rX3RhbmtfY2xhc3MiXSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdGltZXI6IG51bGwsDQogICAgICBGb3JtTW9kZWw6IHsNCiAgICAgICAgS2VOYW1lOiAiIiAvKiDnp5Hmg4XlkI3np7AgKi8sDQogICAgICAgIHBsYXRmb3JtVHlwZTogW10gLyog5bmz5Y+w57G75Z6LICovLA0KICAgICAgICByZWdpb246IDAgLyog5Zyw5Z+f6K+NICovLA0KICAgICAgICBtYWluQm9keTogIiIgLyog5Li75L2T6K+NICovLA0KICAgICAgICBhbmFseXNpczogIiIgLyog5YiG5p6Q6K+NICovLA0KICAgICAgICBleGNsdWRlOiAiIiAvKiDmjpLpmaTor40gKi8sDQogICAgICAgIGNvdW50cnlPckNyZWE6ICIiLA0KICAgICAgfSwNCiAgICAgIEZvcm1SdWxlczogew0KICAgICAgICBLZU5hbWU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl56eR5oOF5ZCN56ewIiB9XSwNCiAgICAgICAgcGxhdGZvcm1UeXBlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeW5s+WPsOexu+WeiyIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0sDQogICAgICAgIF0sDQogICAgICB9LA0KICAgICAgY3JlYUxpc3Q6IFsi5YyX5Lqs5biCIiwgIuWkqea0peW4giIsICLmsrPljJfnnIEiXSwNCiAgICAgIGNvdW50cnk6IFsi5L+E572X5pavIiwgIuiLseWbvSIsICLlvrflm70iXSwNCiAgICAgIHNvdXJjZVR5cGVMaXN0OiBbXSwNCiAgICB9Ow0KICB9LA0KICB3YXRjaDogew0KICAgICJGb3JtTW9kZWwucmVnaW9uIjogew0KICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgICBpZiAob2xkVmFsKSB7DQogICAgICAgICAgdGhpcy5Gb3JtTW9kZWwuY291bnRyeU9yQ3JlYSA9ICIiOw0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgZGVlcDogdHJ1ZSwNCiAgICB9LA0KICAgIGVkaXREYXRhOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICAgIGlmIChuZXdWYWwpIHsNCiAgICAgICAgICBjb25zb2xlLmxvZyhuZXdWYWwpOw0KICAgICAgICAgIHRoaXMuRm9ybU1vZGVsID0gew0KICAgICAgICAgICAgaWQ6IG5ld1ZhbC5pZCwNCiAgICAgICAgICAgIEtlTmFtZTogbmV3VmFsLnRpdGxlIC8qIOenkeaDheWQjeensCAqLywNCiAgICAgICAgICAgIHBsYXRmb3JtVHlwZTogbmV3VmFsLnR5cGUNCiAgICAgICAgICAgICAgPyBuZXdWYWwudHlwZS5zcGxpdCgiLCIpDQogICAgICAgICAgICAgIDogbmV3VmFsLnR5cGUgLyog5bmz5Y+w57G75Z6LICovLA0KICAgICAgICAgICAgcmVnaW9uOiBOdW1iZXIobmV3VmFsLmFyZWFUeXBlKSAvKiDlnLDln5/or40gKi8sDQogICAgICAgICAgICBtYWluQm9keTogbmV3VmFsLmtleXdvcmRzIC8qIOS4u+S9k+ivjSAqLywNCiAgICAgICAgICAgIGV4Y2x1ZGU6IG5ld1ZhbC5leGNsdXNpb25zIC8qIOaOkumZpOivjSAqLywNCiAgICAgICAgICAgIGNvdW50cnlPckNyZWE6IG5ld1ZhbC5wdWJsaXNoQXJlYQ0KICAgICAgICAgICAgICA/IG5ld1ZhbC5wdWJsaXNoQXJlYS5zcGxpdCgiLCIpDQogICAgICAgICAgICAgIDogW25ld1ZhbC5wdWJsaXNoQXJlYV0sDQogICAgICAgICAgfTsNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIGRlZXA6IHRydWUsDQogICAgfSwNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICBnZXRMaXN0Q2xhc3NpZnkoKS50aGVuKChyZXMpID0+IHsNCiAgICAgIHRoaXMuc291cmNlVHlwZUxpc3QgPSByZXMuZGF0YTsNCiAgICB9KTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy4kZW1pdCgiYmVmb3JDbG9lIiwgZmFsc2UpOw0KICAgICAgdGhpcy5Gb3JtTW9kZWwgPSB7DQogICAgICAgIEtlTmFtZTogIiIgLyog56eR5oOF5ZCN56ewICovLA0KICAgICAgICBwbGF0Zm9ybVR5cGU6ICIiIC8qIOW5s+WPsOexu+WeiyAqLywNCiAgICAgICAgcmVnaW9uOiAiIiAvKiDlnLDln5/or40gKi8sDQogICAgICAgIG1haW5Cb2R5OiAiIiAvKiDkuLvkvZPor40gKi8sDQogICAgICAgIGV4Y2x1ZGU6ICIiIC8qIOaOkumZpOivjSAqLywNCiAgICAgICAgY291bnRyeU9yQ3JlYTogIiIsDQogICAgICB9Ow0KICAgICAgdGhpcy4kcmVmcy5mb3JtLnJlc2V0RmllbGRzKCk7DQogICAgfSwNCiAgICAvKiDnoa7orqTmj5DkuqQgKi8NCiAgICBzYXZlRXZlbnQoKSB7DQogICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUoKHZhbGQpID0+IHsNCiAgICAgICAgaWYgKHZhbGQpIHsNCiAgICAgICAgICB0aGlzLiRlbWl0KCJzdWJtaXRLZSIsIHRoaXMuRm9ybU1vZGVsKTsNCiAgICAgICAgICB0aGlzLnRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRyZWZzLmZvcm0ucmVzZXRGaWVsZHMoKTsNCiAgICAgICAgICB9LCAxMDApOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICB9LA0KICBiZWZvcmVEZXN0cm95KCkgew0KICAgIGNsZWFyVGltZW91dCh0aGlzLnRpbWVyKTsNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["KeDrawer.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2JA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "KeDrawer.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-drawer\r\n      :title=\"title\"\r\n      :visible.sync=\"visble\"\r\n      direction=\"rtl\"\r\n      :before-close=\"handleClose\"\r\n      size=\"800px\"\r\n    >\r\n      <el-form\r\n        :model=\"FormModel\"\r\n        size=\"mini\"\r\n        ref=\"form\"\r\n        class=\"formSytle\"\r\n        label-width=\"120px\"\r\n        label-position=\"top\"\r\n        :rules=\"FormRules\"\r\n      >\r\n        <el-form-item\r\n          :label=\"\r\n            title == '修改科情' || title == '新增科情' ? '科情名称' : '专题名称'\r\n          \"\r\n          prop=\"KeName\"\r\n        >\r\n          <el-input\r\n            v-model=\"FormModel.KeName\"\r\n            :placeholder=\"\r\n              title == '修改科情' || title == '新增科情'\r\n                ? '请输入科情名称'\r\n                : '请输入专题名称'\r\n            \"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"平台类型\" prop=\"platformType\">\r\n          <el-select\r\n            placeholder=\"请选择平台类型\"\r\n            v-model=\"FormModel.platformType\"\r\n            style=\"width: 100%\"\r\n            multiple\r\n          >\r\n            <el-option\r\n              v-for=\"(item, index) in sourceTypeList\"\r\n              :key=\"index\"\r\n              :label=\"item.name\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"发布地区\" prop=\"region\">\r\n          <el-radio-group v-model=\"FormModel.region\" style=\"margin-right: 20px\">\r\n            <el-radio :label=\"0\">全部</el-radio>\r\n            <el-radio :label=\"1\">国内</el-radio>\r\n            <el-radio :label=\"2\">境外</el-radio>\r\n          </el-radio-group>\r\n          <el-select\r\n            v-model=\"FormModel.countryOrCrea\"\r\n            placeholder=\"请选择地区\"\r\n            v-if=\"FormModel.region == 1\"\r\n            multiple\r\n            collapse-tags\r\n          >\r\n            <el-option\r\n              v-for=\"(item, index) in areaList\"\r\n              :key=\"index\"\r\n              :label=\"item.regionName\"\r\n              :value=\"item.regionName\"\r\n            ></el-option>\r\n          </el-select>\r\n          <el-select\r\n            v-model=\"FormModel.countryOrCrea\"\r\n            placeholder=\"请选择国家\"\r\n            v-if=\"FormModel.region == 2\"\r\n            multiple\r\n            collapse-tags\r\n          >\r\n            <el-option\r\n              v-for=\"(item, index) in countryList\"\r\n              :key=\"index\"\r\n              :label=\"item.regionName\"\r\n              :value=\"item.regionName\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"关键词\" prop=\"mainBody\">\r\n          <el-input\r\n            v-model=\"FormModel.mainBody\"\r\n            placeholder=\"请输入关键词,多个关键词之间用','分割\"\r\n            type=\"textarea\"\r\n            rows=\"3\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <!-- <el-form-item label=\"分析词\" prop=\"analysis\">\r\n          <el-input v-model=\"FormModel.analysis\" placeholder=\"请输入分析词,多个分析词之间用','分割\"></el-input>\r\n        </el-form-item>-->\r\n        <el-form-item label=\"排除词\" prop=\"exclude\">\r\n          <el-input\r\n            v-model=\"FormModel.exclude\"\r\n            placeholder=\"请输入排除词,多个排除词之间用','分割\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"数据源分类\" prop=\"classify\">\r\n          <el-select\r\n            v-model=\"FormModel.classify\"\r\n            placeholder=\"数据源分类\"\r\n            style=\"width: 100%\"\r\n            multiple\r\n          >\r\n            <el-option\r\n              v-for=\"dict in dict.type.think_tank_class\"\r\n              :label=\"dict.label\"\r\n              :key=\"'think_tank_class' + dict.value\"\r\n              :value=\"dict.value\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"数据源SN\" prop=\"sn\">\r\n          <el-input\r\n            v-model=\"FormModel.sn\"\r\n            placeholder=\"请输入数据源SN,多个数据源SN之间用','分割\"\r\n            type=\"textarea\"\r\n            rows=\"3\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"相关标签\" prop=\"tags\">\r\n          <el-input\r\n            v-model=\"FormModel.tags\"\r\n            placeholder=\"请输入标签,多个标签之间用','分割\"\r\n            type=\"textarea\"\r\n            rows=\"3\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button\r\n            type=\"primary\"\r\n            style=\"margin-left: 38%; width: 150px\"\r\n            v-if=\"title == '修改科情' || title == '新增科情'\"\r\n            @click=\"saveEvent\"\r\n            v-hasPermi=\"['article:monitoring:add']\"\r\n            >保存</el-button\r\n          >\r\n          <el-button\r\n            type=\"primary\"\r\n            style=\"margin-left: 38%; width: 150px\"\r\n            v-else\r\n            @click=\"saveEvent\"\r\n            v-hasPermi=\"['article:special:add']\"\r\n            >保存</el-button\r\n          >\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getListClassify } from \"@/api/article/classify\";\r\n\r\nexport default {\r\n  props: {\r\n    title: {\r\n      required: true,\r\n    },\r\n    visble: {\r\n      required: true,\r\n    },\r\n    editData: {\r\n      required: false,\r\n    },\r\n    countryList: {\r\n      type: Array,\r\n    },\r\n    areaList: {\r\n      type: Array,\r\n    },\r\n  },\r\n  dicts: [\"think_tank_class\"],\r\n  data() {\r\n    return {\r\n      timer: null,\r\n      FormModel: {\r\n        KeName: \"\" /* 科情名称 */,\r\n        platformType: [] /* 平台类型 */,\r\n        region: 0 /* 地域词 */,\r\n        mainBody: \"\" /* 主体词 */,\r\n        analysis: \"\" /* 分析词 */,\r\n        exclude: \"\" /* 排除词 */,\r\n        countryOrCrea: \"\",\r\n      },\r\n      FormRules: {\r\n        KeName: [{ required: true, message: \"请输入科情名称\" }],\r\n        platformType: [\r\n          { required: true, message: \"请选择平台类型\", trigger: \"change\" },\r\n        ],\r\n      },\r\n      creaList: [\"北京市\", \"天津市\", \"河北省\"],\r\n      country: [\"俄罗斯\", \"英国\", \"德国\"],\r\n      sourceTypeList: [],\r\n    };\r\n  },\r\n  watch: {\r\n    \"FormModel.region\": {\r\n      handler(newVal, oldVal) {\r\n        if (oldVal) {\r\n          this.FormModel.countryOrCrea = \"\";\r\n        }\r\n      },\r\n      deep: true,\r\n    },\r\n    editData: {\r\n      handler(newVal, oldVal) {\r\n        if (newVal) {\r\n          console.log(newVal);\r\n          this.FormModel = {\r\n            id: newVal.id,\r\n            KeName: newVal.title /* 科情名称 */,\r\n            platformType: newVal.type\r\n              ? newVal.type.split(\",\")\r\n              : newVal.type /* 平台类型 */,\r\n            region: Number(newVal.areaType) /* 地域词 */,\r\n            mainBody: newVal.keywords /* 主体词 */,\r\n            exclude: newVal.exclusions /* 排除词 */,\r\n            countryOrCrea: newVal.publishArea\r\n              ? newVal.publishArea.split(\",\")\r\n              : [newVal.publishArea],\r\n          };\r\n        }\r\n      },\r\n      deep: true,\r\n    },\r\n  },\r\n  created() {\r\n    getListClassify().then((res) => {\r\n      this.sourceTypeList = res.data;\r\n    });\r\n  },\r\n  methods: {\r\n    handleClose() {\r\n      this.$emit(\"beforCloe\", false);\r\n      this.FormModel = {\r\n        KeName: \"\" /* 科情名称 */,\r\n        platformType: \"\" /* 平台类型 */,\r\n        region: \"\" /* 地域词 */,\r\n        mainBody: \"\" /* 主体词 */,\r\n        exclude: \"\" /* 排除词 */,\r\n        countryOrCrea: \"\",\r\n      };\r\n      this.$refs.form.resetFields();\r\n    },\r\n    /* 确认提交 */\r\n    saveEvent() {\r\n      this.$refs.form.validate((vald) => {\r\n        if (vald) {\r\n          this.$emit(\"submitKe\", this.FormModel);\r\n          this.timer = setTimeout(() => {\r\n            this.$refs.form.resetFields();\r\n          }, 100);\r\n        }\r\n      });\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    clearTimeout(this.timer);\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped></style>\r\n"]}]}