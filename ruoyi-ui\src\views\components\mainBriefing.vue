<!-- 简报主体内容 -->
<template>
  <div>
    <template v-if="!preview">
      <div v-show="switchView == '图片视图'" class="mainBriefing_imgView">
        <div class="credStyle" v-for="(item, key) in tableData" :key="key">
          <el-tag
            class="tag_custom"
            :type="item.reportStatus == 2 ? 'success' : 'warning'"
            >{{ item.reportStatus == 2 ? "报告" : "草稿" }}</el-tag
          >
          <div @mouseleave="submitTitle(item)" @click="edit_Title(item)">
            <p class="title" v-show="!item.inputShow">{{ item.title }}</p>
            <el-input
              class="title"
              style="width: 80%"
              v-model="editTitle"
              size="mini"
              v-show="item.inputShow"
              placeholder="请输入要修改的简报名称"
              :key="key"
            ></el-input>
          </div>

          <div class="info">
            <div class="imgStyle">
              <div class="image-container">
                <!-- <img :src="item.coverImage || img" /> -->
                <!-- <div class="image-overlay" @click.stop="handleUploadClick(item)">
                  <i class="el-icon-picture-outline"></i>
                  <p>替换封面</p>
                </div> -->
                <div class="image-text1">简 报</div>
                <div class="image-text2">{{ item.title }}</div>
                <div class="image-text3">
                  {{ item.author ? item.author : "刘丹" }}
                </div>
              </div>
            </div>
            <div class="information">
              <p>
                <span class="importantInfo">
                  类型:
                  {{ item.type | typeReturn }}
                </span>
              </p>
              <p>
                <span class="importantInfo"
                  >作者:{{ item.author ? item.author : "刘丹" }}</span
                >
              </p>
              <p>
                <span class="importantInfo">时间:{{ item.createTime }}</span>
              </p>
              <p class="font_icon">
                <el-tooltip
                  class="item"
                  v-if="item.reportStatus == 2"
                  effect="dark"
                  content="预览报告"
                  placement="bottom"
                >
                  <i
                    class="icon-yulan"
                    style="color: rgb(255, 178, 78)"
                    @click="PreViewBrie(item)"
                    v-hasPermi="[
                      'article:report:Preview',
                      'article:report:list',
                    ]"
                  ></i>
                  <i
                    class="icon-yulan"
                    style="color: rgb(255, 178, 78)"
                    @click="PreViewBrie(item)"
                    v-hasPermi="['result:report:preview']"
                  ></i>
                </el-tooltip>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="文章列表"
                  placement="bottom"
                >
                  <i
                    class="icon-liebiao"
                    style="color: rgb(255, 178, 78)"
                    @click="StatisticalList(item)"
                  ></i>
                </el-tooltip>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="下载报告"
                  placement="bottom"
                >
                  <i
                    class="icon-xiazai1"
                    style="color: rgb(0, 182, 238)"
                    @click="downLoadReport(item)"
                  ></i>
                </el-tooltip>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="删除报告"
                  placement="bottom"
                >
                  <i
                    class="icon-shanchu"
                    style="color: red"
                    @click="deleteBrie(item)"
                    v-hasPermi="['article:report:remove']"
                  ></i>
                </el-tooltip>
              </p>
            </div>
          </div>
        </div>
        <div class="paginationStyle">
          <el-pagination
            @size-change="handleSizeChange"
            :hide-on-single-page="true"
            @current-change="handleCurrentChange"
            :current-page="pageCurrent"
            :page-sizes="[10, 20, 30, 40, 50]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
      <div v-show="switchView == '列表视图'" class="mainBriefing">
        <el-table
          :data="tableData"
          size="mini"
          style="width: 100%; margin-top: 25x"
          :header-cell-style="{
            textAlign: 'center',
            background: 'rgb(32, 160, 255',
            color: '#fff',
          }"
          border
          :cell-style="{ textAlign: 'center' }"
          height="calc(100vh - 240px)"
          ref="tableRef"
        >
          <el-table-column width="50" align="center" label="序号">
            <template slot-scope="scope">
              <span>
                {{ (pageCurrent - 1) * pageSize + scope.$index + 1 }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="title" label="报告名称">
            <template slot-scope="{ row }">
              <div @click="edit_Title(row)" @mouseleave="submitTitle(row)">
                <p v-show="!row.inputShow">{{ row.title }}</p>
                <el-input
                  class="title"
                  style="width: 80%"
                  v-model="editTitle"
                  size="mini"
                  v-show="row.inputShow"
                  placeholder="请输入要修改的简报名称"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="生成时间"></el-table-column>
          <el-table-column prop="status" label="报告状态">
            <template slot-scope="scope">
              <p style="color: red" v-if="scope.row.status == 1">生成失败</p>
              <p style="color: rgb(1, 180, 40)" v-if="scope.row.status == 2">
                生成成功
              </p>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template slot-scope="{ row }">
              <p class="font_icon">
                <el-tooltip
                  class="item"
                  v-if="row.reportStatus == 2"
                  effect="dark"
                  content="预览报告"
                  placement="bottom"
                >
                  <i
                    class="icon-yulan"
                    style="color: rgb(255, 178, 78)"
                    @click="PreViewBrie(row)"
                    v-hasPermi="[
                      'article:report:Preview',
                      'article:report:list',
                    ]"
                  ></i>
                  <i
                    class="icon-yulan"
                    style="color: rgb(255, 178, 78)"
                    @click="PreViewBrie(row)"
                    v-hasPermi="['result:report:preview']"
                  ></i> </el-tooltip
                >&nbsp;&nbsp;&nbsp;
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="文章列表"
                  placement="bottom"
                >
                  <i
                    class="icon-liebiao"
                    style="color: rgb(255, 178, 78)"
                    @click="StatisticalList(row)"
                  ></i> </el-tooltip
                >&nbsp;&nbsp;&nbsp;
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="下载报告"
                  placement="bottom"
                >
                  <i
                    class="icon-xiazai1"
                    style="color: rgb(0, 182, 238)"
                    @click="downLoadReport(row)"
                  ></i> </el-tooltip
                >&nbsp;&nbsp;&nbsp;
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="删除报告"
                  placement="bottom"
                >
                  <i
                    class="icon-shanchu"
                    style="color: red"
                    @click="deleteBrie(row)"
                    v-hasPermi="['article:report:remove']"
                  ></i>
                </el-tooltip>
              </p>
            </template>
          </el-table-column>
        </el-table>
        <div class="paginationStyle">
          <el-pagination
            @size-change="handleSizeChange"
            :hide-on-single-page="true"
            @current-change="handleCurrentChange"
            :current-page="pageCurrent"
            :page-sizes="[10, 20, 30, 40, 50]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
      <div v-show="switchView == '定时任务'" class="mainBriefing">
        <div class="timeOut" v-for="(item, key) in timeData" :key="key">
          <div class="title">
            <span>定时任务</span>
            <el-switch
              v-hasPermi="['article:cron:initiate']"
              v-model="item.taskStatus"
              active-value="0"
              inactive-value="1"
              active-color="#13ce66"
              inactive-color="#ff4949"
              @change="switchChange(item)"
            ></el-switch>
          </div>
          <div class="info">
            <p>
              <span>ID:{{ item.id }}</span>
              <span
                >下次发送时间:{{
                  item.nextValidTime ? item.nextValidTime : ""
                }}</span
              >
            </p>
            <p>定时任务名称:{{ item.title }}</p>
            <p>发送频率:{{ item.frequency }}</p>
            <p>创建时间:{{ item.createTime }}</p>
          </div>
          <div class="toolGroup">
            <!-- <i class="icon--xiugaineirong" @click="editTime(item)"></i> -->
            <i class="icon-yulan" @click="editTime(item, false)"></i>
            <i
              class="icon-shanchu"
              @click="deleteBrie(item)"
              v-hasPermi="['article:cron:remove']"
            ></i>
          </div>
        </div>
        <div class="paginationStyle">
          <el-pagination
            @size-change="handleSizeChange"
            :hide-on-single-page="true"
            @current-change="handleCurrentChange"
            :current-page="pageCurrent"
            :page-sizes="[10, 20, 30, 40, 50]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
      <div
        v-if="
          (tableData.length == 0 && switchView == '图片视图') ||
          (switchView == '定时任务' && timeData.length == 0)
        "
        class="empty"
      >
        <el-empty :image-size="200"></el-empty>
      </div>
      <el-dialog
        title="文章列表"
        :visible.sync="dialogTableVisible"
        :close-on-click-modal="false"
      >
        <el-table
          :data="gridData"
          size="mini"
          border
          :header-cell-style="{
            textAlign: 'center',
          }"
        >
          <el-table-column
            type="index"
            label="序号"
            width="80px"
            align="center"
          ></el-table-column>
          <el-table-column
            property="cnTitle"
            label="文章标题"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="scope">
              <div @click="openNewView(scope.row)" class="hoverStyle">
                <span :title="scope.row.cnTitle">{{
                  scope.row.cnTitle || scope.row.title
                }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            property="publishTime"
            label="发布时间"
            width="200"
            align="center"
          ></el-table-column>
          <el-table-column
            property="keywords"
            label="操作"
            width="200"
            align="center"
            v-if="reportStatus == 1"
          >
            <template slot-scope="scope">
              <el-popconfirm
                title="确定要删除这条数据吗?"
                @confirm="dangerEvent(scope.row)"
              >
                <el-button slot="reference" type="danger" size="mini"
                  >删除</el-button
                >
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <div style="margin-top: 15px; text-align: right">
          <el-pagination
            @size-change="sizeChange"
            @current-change="currentChange"
            :current-page="pageNum1"
            :page-sizes="[10, 20, 30, 40, 50]"
            :page-size="pageSize1"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total1"
          ></el-pagination>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" @click="dialogTableVisible = false"
            >关 闭</el-button
          >
          <el-button
            size="mini"
            type="primary"
            @click="ReportPriview"
            v-hasPermi="['result:report:preview']"
            >报告预览</el-button
          >
          <el-button
            size="mini"
            type="primary"
            @click="ReportStatisics"
            :disabled="reportStatus != 1"
            v-hasPermi="['article:report:add']"
            >生成报告</el-button
          >
        </div>
      </el-dialog>
    </template>
    <briedingPreview
      v-else
      :preViewData="preViewData"
      @switchPreview="switchPreview"
      @ReportStatisics="ReportStatisics"
    >
    </briedingPreview>

    <!-- 上传图片对话框 -->
    <el-dialog
      title="替换封面图片"
      :visible.sync="uploadDialogVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="upload-container">
        <el-upload
          class="avatar-uploader"
          action="#"
          :http-request="handleUpload"
          :show-file-list="false"
          :before-upload="beforeUpload"
          accept="image/*"
        >
          <img v-if="imageUrl" :src="imageUrl" class="avatar" />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </div>
      <div class="upload-tip">只能上传jpg/png格式文件，且不超过2MB</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelUpload">取 消</el-button>
        <el-button type="primary" @click="confirmUpload" :disabled="!imageUrl"
          >确 认</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import API from "@/api/ScienceApi/briefing.js";
import briedingPreview from "@/views/components/briefingPreview.vue";
export default {
  props: {
    switchView: {
      required: false,
      default: "图片视图",
    },
    total: {
      required: true,
    },
    pageCurrent: {
      required: true,
    },
    pageSize: {
      required: true,
      default: 50,
    },
    tableData: {
      required: false,
      default: () => {
        return [];
      },
    },
    preview: {
      required: true,
      type: Boolean,
    },
    timeData: {
      required: false,
    },
  },
  components: {
    briedingPreview,
  },
  data() {
    return {
      preViewData: {
        briefingId: "" /* 简报id */,
        isWechat: "" /* 是否是微信报告模板 */,
        reportName: "" /* 报告名称 */,
        flag: "" /* 是否通过下载按钮 */,
        previewData: {} /* 预览数据 */,
        preview: false,
      },
      editTitle: "",
      inputShow: false,
      img: require("@/assets/images/muban.png"),
      dialogTableVisible: false,
      gridData: [],
      pageNum1: 1,
      pageSize1: 50,
      total1: 0,
      statisticsId: "",
      reportStatus: "",
      num: 0,
      itemData: {},
      // 上传图片相关
      uploadDialogVisible: false,
      imageUrl: "",
      currentItem: null,
      uploadLoading: false,
    };
  },

  filters: {
    typeReturn(type) {
      let str;
      switch (type) {
        case "1":
          str = "日报";
          break;
        case "2":
          str = "周报";
          break;
        case "3":
          str = "月报";
          break;
        case "4":
          str = "自定义报告";
          break;
        case "5":
          str = "新建简报";
          break;
        default:
          break;
      }
      return str;
    },
  },
  created() {},
  methods: {
    // 处理点击上传图标
    handleUploadClick(item) {
      this.currentItem = item;
      this.uploadDialogVisible = true;
      this.imageUrl = "";
    },

    // 上传前的检查
    beforeUpload(file) {
      const isJPG = file.type === "image/jpeg" || file.type === "image/png";
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传图片只能是 JPG/PNG 格式!");
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
        return false;
      }
      return true;
    },

    // 处理图片上传
    handleUpload(options) {
      const file = options.file;
      // 验证文件类型和大小
      if (!this.beforeUpload(file)) {
        return;
      }

      this.uploadLoading = true;
      // 使用FileReader预览图片
      const reader = new FileReader();
      reader.onload = (e) => {
        this.imageUrl = e.target.result;
        this.uploadLoading = false;
      };
      reader.readAsDataURL(file);
    },

    // 确认上传图片
    confirmUpload() {
      if (!this.imageUrl) {
        this.$message.warning("请先选择图片");
        return;
      }

      // 这里将来可替换为实际的上传API调用
      // API.uploadCoverImage({id: this.currentItem.id, image: 图片文件})
      //   .then(res => {
      //     if (res.code === 200) {
      //       this.$message.success('上传成功');
      //       this.$set(this.currentItem, 'coverImage', res.data.url);
      //       this.uploadDialogVisible = false;
      //     }
      //   })

      // 模拟上传成功
      this.$message({
        message: "上传成功",
        type: "success",
      });

      // 将上传的图片设置为当前项的封面图
      if (this.currentItem) {
        this.$set(this.currentItem, "coverImage", this.imageUrl);
      }

      // 关闭对话框
      this.uploadDialogVisible = false;
      this.imageUrl = "";
      this.currentItem = null;
    },

    // 取消上传
    cancelUpload() {
      this.uploadDialogVisible = false;
      this.imageUrl = "";
    },

    PreViewBrie(item) {
      this.preViewData = {
        briefingId: item.id /* 简报id */,
        isWechat: item.templateId /* 是否是微信报告模板 */,
        reportName: item.title /* 报告名称 */,
        flag: "" /* 是否通过下载按钮 */,
        previewData: "" /* 预览数据 */,
        preview: false,
      };
      this.$parent._data.preview = true;
    },
    /* 下载报告 */
    downLoadReport(item) {
      if (item.reportStatus == 1) {
        this.$message({
          message: "当前报告未生成,请先生成报告",
          type: "warning",
        });
        return;
      }
      this.preViewData = {
        briefingId: item.id /* 简报id */,
        isWechat: item.templateId /* 是否是微信报告模板 */,
        reportName: item.title /* 报告名称 */,
        flag: "download" /* 是否通过下载按钮 */,
        previewData: "" /* 预览数据 */,
        preview: false,
      };
      this.$parent._data.preview = true;
    },
    /* 删除 */
    deleteBrie(item) {
      this.$emit("deleteBrie", item.id);
    },
    /* 修改 */
    editTime(item) {
      this.$emit("editTime", item);
    },
    handleCurrentChange(current) {
      this.$emit("handleCurrentChange", current);
      document.querySelector(".mainBriefing_imgView") &&
        document.querySelector(".mainBriefing_imgView").scrollTo(0, 0);
      document.querySelector(".mainBriefing") &&
        document.querySelector(".mainBriefing").scrollTo(0, 0);
      if (this.switchView === "列表视图" && this.$refs.tableRef) {
        this.$refs.tableRef.bodyWrapper.scrollTop = 0;
      }
    },
    handleSizeChange(size) {
      this.$emit("handleSizeChange", size);
      document.querySelector(".mainBriefing_imgView") &&
        document.querySelector(".mainBriefing_imgView").scrollTo(0, 0);
      document.querySelector(".mainBriefing") &&
        document.querySelector(".mainBriefing").scrollTo(0, 0);
      if (this.switchView === "列表视图" && this.$refs.tableRef) {
        this.$refs.tableRef.bodyWrapper.scrollTop = 0;
      }
    },
    /* 文章统计列表 */
    async StatisticalList(item) {
      let data = Object.keys(this.itemData).length;
      if (!data) {
        this.itemData = item;
      }
      if (item && item.id) {
        this.statisticsId = item.id;
        this.reportStatus = item.reportStatus;
      }
      this.dialogTableVisible = true;
      let res = await API.statistics({
        pageSize: this.pageSize1,
        pageNum: this.pageNum1,
        reportId: this.statisticsId,
      });
      if (res.code == 200) {
        this.gridData = res.rows;
        this.total1 = res.total;
      }
    },
    /* size变化 */
    sizeChange(size) {
      this.pageSize1 = size;
      this.StatisticalList();
    },
    /* 页码变化 */
    currentChange(current) {
      this.pageNum1 = current;
      this.StatisticalList();
    },
    /* 删除报告关联文章 */
    async dangerEvent(tableData) {
      let res = await API.removeReport([tableData.id]);
      if (res.code == 200) {
        this.$message({ message: "删除成功", type: "success" });
        this.StatisticalList();
      } else {
        this.$message({ message: "删除失败,请联系管理员", type: "error" });
      }
      // this.dialogTableVisible = false
    },
    /* 报告统计 */
    async ReportStatisics() {
      this.dialogTableVisible = false;
      let res = await API.reportStatistics(this.statisticsId);
      if (res.code == 200) {
        this.$message({
          message: "统计成功,即将跳转详情数据",
          type: "success",
        });
        this.handleCurrentChange(1);
        this.preViewData = {
          briefingId: this.statisticsId /* 简报id */,
          isWechat: this.itemData.templateId /* 是否是微信报告模板 */,
          reportName: this.itemData.title /* 报告名称 */,
          flag: "" /* 是否通过下载按钮 */,
          previewData: res.data /* 预览数据 */,
          preview: false,
        };
        this.$parent._data.preview = true;
      } else {
        this.$message({ message: "统计失败,请联系管理员", type: "error" });
      }
    },
    async ReportPriview() {
      this.dialogTableVisible = false;
      if (this.reportStatus == 1) {
        await API.reportPriview(this.statisticsId).then((response) => {
          if (response.code == 200) {
            this.preViewData = {
              reportStatus: this.reportStatus,
              briefingId: this.statisticsId /* 简报id */,
              isWechat: this.itemData.templateId /* 是否是微信报告模板 */,
              reportName: this.itemData.title /* 报告名称 */,
              flag: "" /* 是否通过下载按钮 */,
              previewData: response.data /* 预览数据 */,
              preview: true,
            };
            this.$parent._data.preview = true;
          }
        });
      } else {
        this.preViewData = {
          reportStatus: this.reportStatus,
          briefingId: this.statisticsId /* 简报id */,
          isWechat: this.itemData.templateId /* 是否是微信报告模板 */,
          reportName: this.itemData.title /* 报告名称 */,
          flag: "" /* 是否通过下载按钮 */,
          previewData: "" /* 预览数据 */,
          preview: false,
        };
        this.$parent._data.preview = true;
      }
    },
    /* 打开外网链接 */
    openNewView(item) {
      // this.num += 1
      // if (this.num >= 3)
      // {
      //   this.$message({ message: '请勿频繁点击', type: 'error' })
      //   this.num = 0
      //   return
      // }
      if (item.sourceType == 1) {
        if (item.shortUrl) {
          window.open(item.shortUrl);
          return;
        }
        this.$message({ message: "该文章没有原文链接" });
      } else if (item.sourceType == 2) {
        if (item.originalUrl) {
          window.open(item.originalUrl);
          return;
        }
        this.$message({ message: "该文章没有原文链接" });
      }
    },
    async switchChange(status) {
      if (status.taskStatus == 0) {
        await API.firingTimeOut(status.id).then((Data) => {
          if (Data.code == 200) {
            this.$message({ message: "启动定时任务成功", type: "success" });
          }
        });
      } else {
        await API.stopTImeOut(status.id).then((Data) => {
          this.$message({ message: "暂停定时任务成功", type: "success" });
        });
      }
    },
    submitTitle(item) {
      if (item.inputShow) {
        item.inputShow = false;
        API.editBireFing({ id: item.id, title: this.editTitle }).then((res) => {
          if (res.code == 200) {
            this.$message({ message: "修改成功", type: "success" });
            this.handleCurrentChange(1);
          } else {
            this.$message({ message: "修改失败,请联系管理员", type: "error" });
          }
        });
      }
    },
    edit_Title(item) {
      item.inputShow = true;
      this.editTitle = item.title;
    },
    switchPreview() {
      this.$parent._data.preview = false;
      this.$store.commit("app/set_Loding", false);
    },
  },
};
</script>

<style lang="scss" scoped>
.mainBriefing_imgView {
  overflow-y: auto;
  padding-left: 10px;
  width: 100%;
  height: calc(100vh - 240px);
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
  padding-bottom: 60px;
  display: flex;
  justify-content: flex-start;
}

.mainBriefing {
  padding-left: 10px;
  width: 100%;
  height: auto;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.credStyle {
  width: 330px;
  height: 260px;
  padding: 10px;
  box-shadow: 2px 1px 12px 2px rgb(216, 216, 216);

  .title {
    height: 20px;
    width: 100%;
    margin: 0;
    text-align: center;
    // display: block;
    border-bottom: solid 1px #d8d7d7;
    padding-bottom: 30px;

    .tag_Style {
      float: right;
      margin: -5px;
      display: inline;
    }
  }

  h3 {
    text-align: center;
  }

  .info {
    width: 100%;
    display: flex;
    justify-content: center;
    position: relative;
  }

  .imgStyle {
    width: 45%;
  }

  .image-container {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    margin-top: 10px;
    text-align: center;
    background: url("../../assets/images/reportPng.png") no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.6);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      opacity: 0;
      transition: opacity 0.3s;
      cursor: pointer;

      &:hover {
        opacity: 1;
      }

      i {
        font-size: 24px;
        color: #fff;
      }

      p {
        color: #fff;
        margin-top: 8px;
      }
    }

    .image-text1 {
      font-size: 20px;
      font-weight: 600;
      margin-top: 24px;
      color: #6784eb;
    }

    .image-text2 {
      font-size: 14px;
      font-weight: 600;
      padding: 0 10px;
      color: #fff;
      font-family: Microsoft YaHei, Microsoft YaHei;
    }

    .image-text3 {
      font-size: 14px;
      color: #fff;
      margin-bottom: 6px;
      font-family: Microsoft YaHei, Microsoft YaHei;
    }
  }

  .information {
    display: flex;
    flex-direction: column;
    // justify-content: space-around;
    margin-left: 10px;
    width: 55%;
    padding-top: 10px;

    .importantInfo {
      font-size: 14px;
      color: rgb(78, 78, 78);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    p {
      font-size: 15px;
      font-weight: 500;
      color: rgb(138, 138, 138);
    }

    .font_icon {
      width: 90%;
      font-size: 27px;
      display: flex;
      justify-content: space-between;
    }
  }
}

.paginationStyle {
  width: 100%;
  position: fixed;
  bottom: 0px;
  left: 0;
  background: #fbfaf7;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.timeOut {
  width: 330px;
  min-height: 230px;
  padding: 10px;
  box-shadow: 2px 1px 12px 2px rgb(216, 216, 216);

  .title {
    height: 30px;
    font-size: 14px;
    font-weight: 500;
    border-bottom: solid 1px #efefef;
    display: flex;
    justify-content: space-between;
  }

  .info {
    height: calc(100% - 75px);
    font-size: 14px;
    color: #949393;
    border-bottom: solid 1px #efefef;

    :first-child {
      display: flex;
      justify-content: space-between;
    }
  }

  .toolGroup {
    min-height: 40px;
    color: #949393;
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: 26px;
  }
}

.empty {
  width: 100%;
  height: 700px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hoverStyle {
  color: black;
  overflow: hidden;
  text-overflow: ellipsis;
  line-clamp: 2;
}

.hoverStyle > span:hover {
  color: #0798f8;
  border-bottom: solid 1px #0798f8;
}

.tag_custom {
  float: right;
}

/* 上传相关样式 */
.upload-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.upload-tip {
  margin-top: 10px;
  text-align: center;
  font-size: 12px;
  color: #909399;
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 180px;
  height: 267px;
}

::v-deep .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

::v-deep .avatar-uploader .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 180px;
  height: 267px;
  line-height: 267px;
  text-align: center;
}

::v-deep .avatar-uploader .avatar {
  width: 180px;
  height: 267px;
  display: block;
  object-fit: cover;
}
</style>
