/**
 * 将markdown文本格式化为HTML
 * @param {string} markdownText - 要格式化的markdown文本
 * @returns {string} 格式化后的HTML
 */
export function formatMarkdown(markdownText) {
  if (!markdownText) return "";

  // 预处理markdown文本，处理转义字符
  // 将\\n替换为\n (双反斜杠换行符)
  markdownText = markdownText.replace(/\\n/g, "\n");
  // 将\\\n替换为\n (三反斜杠换行符)
  markdownText = markdownText.replace(/\\\n/g, "\n"); 
  // 将\r\n替换为\n (Windows风格换行符)
  markdownText = markdownText.replace(/\r\n/g, "\n");

  // 对markdown文本进行处理，使其在HTML中显示更美观

  // 先分行处理，保留原始的行结构
  const lines = markdownText.split("\n");
  let html = "";

  // 用于判断是否在列表或代码块中
  let inCodeBlock = false;
  let codeBlockContent = "";

  // 列表处理变量
  let listStack = []; // 存储当前活动的列表 [{type: 'ul'/'ol', indent: Number, html: String}]

  // 添加日志输出查看预处理后的行数
  console.log(`预处理后共${lines.length}行文本`);

  lines.forEach((line, index) => {
    // 处理代码块
    if (line.match(/^```/)) {
      if (!inCodeBlock) {
        // 开始代码块
        inCodeBlock = true;
        codeBlockContent = "";
      } else {
        // 结束代码块
        html += `<pre class="md-code-block">${escapeHtml(
          codeBlockContent
        )}</pre>`;
        inCodeBlock = false;
      }
      return;
    }

    if (inCodeBlock) {
      codeBlockContent += line + "\n";
      return;
    }

    // 处理标题 - 忽略h1标题
    const h1Match = line.match(/^# (.*?)$/);
    const h2Match = line.match(/^## (.*?)$/);
    const h3Match = line.match(/^### (.*?)$/);

    // 标题行之前清理列表栈
    if (h1Match || h2Match || h3Match) {
      // 关闭所有打开的列表
      while (listStack.length > 0) {
        const list = listStack.pop();
        html += `</${list.type}>`;
      }

      // 跳过h1标题，不处理
      if (h1Match) {
        return;
      }

      if (h2Match) {
        html += `<h2 class="md-h2">${formatInlineMarkdown(
          h2Match[1]
        )}</h2>`;
        return;
      }

      if (h3Match) {
        html += `<h3 class="md-h3">${formatInlineMarkdown(
          h3Match[1]
        )}</h3>`;
        return;
      }

      return;
    }

    // 处理列表 - 识别缩进来确定嵌套层级
    const ulMatch = line.match(/^(\s*)- (.*?)$/);
    const olMatch = line.match(/^(\s*)(\d+)\. (.*?)$/);

    if (ulMatch || olMatch) {
      let indentSize = 0;
      let listContent = "";
      let listType = "";

      if (ulMatch) {
        indentSize = ulMatch[1].length;
        listContent = ulMatch[2];
        listType = "ul";
      } else {
        indentSize = olMatch[1].length;
        listContent = olMatch[3];
        listType = "ol";
      }

      // 关闭当前级别之后的所有列表
      while (
        listStack.length > 0 &&
        listStack[listStack.length - 1].indent >= indentSize
      ) {
        const list = listStack.pop();
        html += `</${list.type}>`;
      }

      // 如果栈为空或者新的缩进大于栈顶的缩进，则开始新的列表
      if (
        listStack.length === 0 ||
        indentSize > listStack[listStack.length - 1].indent
      ) {
        html += `<${listType} class="md-${listType}">`;
        listStack.push({ type: listType, indent: indentSize });
      }

      // 添加列表项
      html += `<li>${formatInlineMarkdown(listContent)}</li>`;

      return;
    }

    // 如果遇到非列表行，关闭所有列表
    if (!ulMatch && !olMatch && line.trim() !== "") {
      while (listStack.length > 0) {
        const list = listStack.pop();
        html += `</${list.type}>`;
      }
    }

    // 处理引用
    const quoteMatch = line.match(/^\s*> (.*?)$/);
    if (quoteMatch) {
      html += `<blockquote class="md-blockquote">${formatInlineMarkdown(
        quoteMatch[1]
      )}</blockquote>`;
      return;
    }

    // 处理普通段落，空行就忽略
    if (line.trim() === "") {
      // 不添加空行间隔，完全忽略空行
    } else {
      html += `<p class="md-paragraph">${formatInlineMarkdown(
        line
      )}</p>`;
    }
  });

  // 确保代码块和列表都已正确关闭
  if (inCodeBlock) {
    html += `<pre class="md-code-block">${escapeHtml(
      codeBlockContent
    )}</pre>`;
  }

  // 关闭所有剩余的列表
  while (listStack.length > 0) {
    const list = listStack.pop();
    html += `</${list.type}>`;
  }

  return html;
}

/**
 * 处理inline markdown格式
 * @param {string} text - 要处理的文本
 * @returns {string} 处理后的HTML
 */
export function formatInlineMarkdown(text) {
  if (!text) return "";

  return (
    text
      // 处理粗体
      .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
      // 处理斜体
      .replace(/\*(.*?)\*/g, "<em>$1</em>")
      // 处理行内代码
      .replace(/`(.*?)`/g, '<code class="md-code-inline">$1</code>')
  );
}

/**
 * 转义HTML特殊字符
 * @param {string} unsafe - 包含HTML特殊字符的字符串
 * @returns {string} 转义后的字符串
 */
export function escapeHtml(unsafe) {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
} 