<template>
  <div>
    <div class="bg-box">
      <div class="bg-box-title">
        <span>{{ title }}</span>
        <div class="date-picker-container">
          <!-- 日期区间选择器 -->
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="onDateRangeChange"
            size="small"
            class="title-date-picker"
          />
        </div>
      </div>
      <div class="bg-box-content">
        <div
          class="sankey-container"
          :style="{ width: width, height: hasData ? containerHeight : '100px' }"
        >
          <div
            ref="sankeyChart"
            class="sankey-chart"
            :style="{ height: dynamicHeight }"
            v-show="hasData"
          ></div>
          <div v-show="!hasData" class="no-data-container">
            <div class="no-data-text">暂无数据</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { zcfxSankey } from "@/api/bigScreen/sanhao.js";

export default {
  name: "SankeyChart2",
  props: {
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "100%",
    },
    title: {
      type: String,
      default: "关系图",
    },
    // 接口参数类型：proposalsTitle、proposalsExperts、enterpriseName
    paramType: {
      type: String,
      required: true,
    },
    // 参数值
    paramValue: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      chart: null,
      loading: false,
      sankeyData: {
        nodes: [],
        links: [],
      },
      // 原始数据，包含所有节点和连接
      originalData: {
        nodes: [],
        links: [],
      },
      // 记录哪些子标签节点已展开企业
      expandedSubLabels: new Set(),
      // ResizeObserver 实例
      resizeObserver: null,
      // 防抖定时器
      resizeTimer: null,
      // 日期范围
      dateRange: [],
      // 是否有数据
      hasData: true,
      // 基础高度
      baseHeight: 600,
      // 每个展开企业增加的高度
      heightPerExpansion: 50,
    };
  },
  computed: {
    // 动态计算桑葚图高度
    dynamicHeight() {
      // 计算当前显示的企业节点数量
      const enterpriseCount = this.sankeyData.nodes.filter(
        (node) => node.category === "企业"
      ).length;

      // 基础高度 + 企业数量 * 每个企业增加的高度
      const calculatedHeight =
        this.baseHeight + enterpriseCount * this.heightPerExpansion;
      return `${calculatedHeight}px`;
    },

    // 动态计算容器高度（用于滚动）
    containerHeight() {
      // 容器高度固定为基础高度，超出部分显示滚动条
      return `${this.baseHeight}px`;
    },
  },
  mounted() {
    this.initDefaultDateRange();
    this.initChart();
    this.fetchSankeyData();
  },

  watch: {
    // 监听宽度和高度变化
    width() {
      this.handleResize();
    },
    height() {
      this.handleResize();
    },
    // 监听参数变化
    paramValue() {
      this.fetchSankeyData();
    },
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    // 清理定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    }
    // 清理窗口大小变化监听
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    // 初始化默认日期范围（最近半年）
    initDefaultDateRange() {
      const today = new Date();
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(today.getMonth() - 6);

      this.dateRange = [this.formatDate(sixMonthsAgo), this.formatDate(today)];
    },

    // 格式化日期为 yyyy-MM-dd 格式
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    // 日期范围变化处理
    onDateRangeChange() {
      this.fetchSankeyData();
    },

    // 更新日期范围（由父组件调用）
    updateDateRange() {
      this.fetchSankeyData();
    },

    // 获取桑葚图数据
    async fetchSankeyData() {
      try {
        this.loading = true;

        // 构建请求参数
        const params = {
          screenSn: "1",
        };

        // 根据参数类型添加对应的参数
        if (this.paramType && this.paramValue) {
          params[this.paramType] = this.paramValue;
        }

        // 如果有选择日期范围，添加日期参数
        if (this.dateRange && this.dateRange.length === 2) {
          params.startDate = this.dateRange[0];
          params.endDate = this.dateRange[1];
        }

        const response = await zcfxSankey(params);

        if (response && response.data) {
          console.log("桑葚图数据:", response.data);
          // 处理节点数据
          const processedNodes = this.processNodes(response.data.nodes || []);
          // 处理连接数据
          const processedLinks = this.processLinks(response.data.links || []);

          // 保存原始数据
          this.originalData = {
            nodes: processedNodes,
            links: processedLinks,
          };

          // 初始化时隐藏企业节点
          this.sankeyData = this.filterEnterpriseNodes(this.originalData);

          if (this.sankeyData.links.length === 0) {
            this.hasData = false;
            this.sankeyData.nodes = [];
          } else {
            this.hasData = true;
            // 如果有数据但图表还没初始化，则初始化图表
            if (!this.chart) {
              this.$nextTick(() => {
                this.initChart();
              });
            }
          }

          // 更新图表
          this.updateChart();
        }
      } catch (error) {
        console.error("获取桑葚图数据失败:", error);
      } finally {
        this.loading = false;
      }
    },

    // 处理节点数据
    processNodes(nodes) {
      const colors = ["#dd79ff", "#58d9f9", "#4992ff"];

      // category到层级的映射
      const categoryToDepth = {
        党派: 0,
        专家: 1,
        提案: 2,
        父标签: 3,
        子标签: 4,
        企业: 5,
      };

      return nodes.map((node, index) => {
        const depth =
          categoryToDepth[node.category] !== undefined
            ? categoryToDepth[node.category]
            : 0;

        return {
          id: node.id,
          name: node.name,
          category: node.category,
          depth: depth,
          itemStyle: {
            color: colors[index % colors.length],
          },
        };
      });
    },

    // 过滤企业节点，根据展开状态决定是否显示
    filterEnterpriseNodes(data) {
      // 如果没有展开任何子标签，则隐藏所有企业节点
      if (this.expandedSubLabels.size === 0) {
        const filteredNodes = data.nodes.filter(
          (node) => node.category !== "企业"
        );
        const filteredLinks = data.links.filter((link) => {
          const sourceExists = filteredNodes.find((n) => n.id === link.source);
          const targetExists = filteredNodes.find((n) => n.id === link.target);
          return sourceExists && targetExists;
        });

        return {
          nodes: filteredNodes,
          links: filteredLinks,
        };
      }

      // 如果有展开的子标签，则显示对应的企业节点
      const filteredNodes = data.nodes.filter((node) => {
        if (node.category === "企业") {
          // 查找连接到此企业的子标签节点
          const connectedToSubLabel = data.links.some((link) => {
            // 检查企业是否与已展开的子标签相连
            if (link.target === node.id) {
              // 企业是目标，检查源是否是已展开的子标签
              const sourceNode = data.nodes.find((n) => n.id === link.source);
              return (
                sourceNode &&
                sourceNode.category === "子标签" &&
                this.expandedSubLabels.has(sourceNode.id)
              );
            }
            if (link.source === node.id) {
              // 企业是源，检查目标是否是已展开的子标签
              const targetNode = data.nodes.find((n) => n.id === link.target);
              return (
                targetNode &&
                targetNode.category === "子标签" &&
                this.expandedSubLabels.has(targetNode.id)
              );
            }
            return false;
          });

          return connectedToSubLabel;
        }
        return true; // 非企业节点都显示
      });

      const filteredLinks = data.links.filter((link) => {
        const sourceExists = filteredNodes.find((n) => n.id === link.source);
        const targetExists = filteredNodes.find((n) => n.id === link.target);
        return sourceExists && targetExists;
      });

      return {
        nodes: filteredNodes,
        links: filteredLinks,
      };
    },

    // 处理连接数据
    processLinks(links) {
      return links.map((link) => {
        return {
          source: link.source,
          target: link.target,
          value: link.value || 1, // 如果没有value，默认为1
        };
      });
    },

    // 更新图表
    updateChart() {
      if (this.chart && this.hasData) {
        const option = this.getChartOption();
        // 使用 notMerge: false 来避免完全重新渲染，提高性能
        this.chart.setOption(option, true);
      }
    },

    // 获取图表配置
    getChartOption() {
      return {
        backgroundColor: "transparent",
        title: {
          text: "",
          textStyle: {
            color: "#ffffff",
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: "item",
          triggerOn: "mousemove",
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          borderColor: "#0ec2f4",
          borderWidth: 1,
          textStyle: {
            color: "#ffffff",
          },
          formatter: function (params) {
            if (params.dataType === "edge") {
              return `${params.data.source} → ${params.data.target}<br/>影响度: ${params.data.value}`;
            } else {
              const depthMap = {
                0: "第1级",
                1: "第2级",
                2: "第3级",
                3: "第4级",
                4: "第5级",
                5: "第6级",
              };
              const levelText = depthMap[params.data.depth] || "未知层级";
              return `${params.data.name}<br/>类别: ${
                params.data.category || "未知"
              }<br/>层级: ${levelText}`;
            }
          },
        },
        series: [
          {
            type: "sankey",
            layout: "none",
            emphasis: {
              focus: "adjacency",
            },
            data: this.sankeyData.nodes,
            links: this.sankeyData.links,
            // orient: 'vertical',
            // nodeAlign: 'justify',
            nodeGap: 10,
            nodeWidth: 40,
            layoutIterations: 0,
            left: "0",
            right: "20%",
            top: "0.2%",
            bottom: "0.2%",
            label: {
              show: true,
              position: "right",
              color: "#ffffff",
              fontSize: 14,
              formatter: function (params) {
                return params.name.length > 12
                  ? params.name.substring(0, 12) + "..."
                  : params.name;
              },
            },
            lineStyle: {
              color: "source",
              // curveness: 0.2,
              // opacity: 0.7
            },
            // itemStyle: {
            //   borderWidth: 1,
            //   borderColor: '#0ec2f4'
            // }
          },
        ],
      };
    },

    initChart() {
      if (!this.hasData) {
        return; // 没有数据时不初始化图表
      }

      // 设置图表容器的尺寸 - 初始化时使用基础高度
      this.$refs.sankeyChart.style.width = "100%";
      this.$refs.sankeyChart.style.height = `${this.baseHeight}px`;

      this.chart = echarts.init(this.$refs.sankeyChart);

      // 初始化时设置空的图表配置
      const option = this.getChartOption();
      this.chart.setOption(option);

      // 添加节点点击事件
      this.chart.on("click", (params) => {
        if (params.dataType === "node" && params.data.category === "子标签") {
          this.toggleEnterpriseNodes(params.data.id);
        }
      });

      // 设置尺寸变化监听
      this.setupResizeListeners();
    },

    // 切换企业节点的显示/隐藏
    toggleEnterpriseNodes(subLabelId) {
      if (this.expandedSubLabels.has(subLabelId)) {
        // 如果已展开，则收起
        this.expandedSubLabels.delete(subLabelId);
      } else {
        // 如果未展开，则展开
        this.expandedSubLabels.add(subLabelId);
      }

      // 重新过滤数据
      const newSankeyData = this.filterEnterpriseNodes(this.originalData);

      // 计算新高度
      const enterpriseCount = newSankeyData.nodes.filter(
        (node) => node.category === "企业"
      ).length;
      const newHeight = `${
        this.baseHeight + enterpriseCount * this.heightPerExpansion
      }px`;

      // 同时更新数据、高度和图表，确保完全同步
      this.sankeyData = newSankeyData;
      if (this.$refs.sankeyChart) {
        this.$refs.sankeyChart.style.height = newHeight;
      }

      // 如果没有任何展开的子领域（所有企业都收起），回到顶部并恢复初始高度
      if (this.expandedSubLabels.size === 0) {
        // 先强制设置高度为基础高度
        if (this.$refs.sankeyChart) {
          this.$refs.sankeyChart.style.height = `${this.baseHeight}px`;
          // 强制浏览器重新计算布局
          this.$refs.sankeyChart.offsetHeight;
        }

        // 立即更新图表以确保高度生效
        if (this.chart) {
          this.chart.resize();
        }

        // 然后滚动到顶部
        const container = this.$refs.sankeyChart.parentElement;
        if (container) {
          // 强制触发重绘
          container.offsetHeight;
          container.scrollTop = 0;
          // 使用 scrollTo 确保滚动生效
          container.scrollTo({ top: 0, behavior: "instant" });
        }
      }

      // 立即更新图表
      this.updateChart();
      if (this.chart) {
        this.chart.resize();
      }
    },

    // 设置尺寸变化监听
    setupResizeListeners() {
      // 创建 ResizeObserver 监听容器尺寸变化
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(() => {
          // 使用防抖处理，避免频繁触发
          clearTimeout(this.resizeTimer);
          this.resizeTimer = setTimeout(() => {
            this.handleResize();
          }, 100);
        });

        // 监听图表容器的尺寸变化
        this.resizeObserver.observe(this.$refs.sankeyChart);

        // 也监听父容器的尺寸变化
        const parentContainer = this.$refs.sankeyChart.parentElement;
        if (parentContainer) {
          this.resizeObserver.observe(parentContainer);
        }
      }

      // 监听窗口大小变化（作为备用方案）
      this.handleResize = this.handleResize.bind(this);
      window.addEventListener("resize", this.handleResize);
    },

    // 处理尺寸变化
    handleResize() {
      if (this.chart) {
        // 延迟执行 resize，确保 DOM 更新完成
        this.$nextTick(() => {
          this.chart.resize();
        });
      }
    },

    // 手动触发图表重新调整大小（供父组件调用）
    resizeChart() {
      this.handleResize();
    },
  },
};
</script>

<style lang="scss" scoped>
.bg-box {
  position: relative;
  background: #1b283b;
  border-radius: 8px;
  padding: 8px 16px 16px;
  margin-bottom: 20px;

  .bg-box-title {
    font-weight: 800;
    font-size: 18px;
    color: #ffffff;
    height: 30px;
    line-height: 30px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .bg-box-content {
    font-size: 16px;
    color: #ffffff;
    white-space: pre-wrap;
  }
}

.date-picker-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.title-date-picker {
  width: 240px;
}

.date-picker-container ::v-deep .el-input__inner {
  background: rgba(27, 40, 59, 0.8) !important;
  border: 1px solid rgba(14, 194, 244, 0.3) !important;
  color: #ffffff !important;
  font-size: 12px !important;
}

.date-picker-container ::v-deep .el-input__inner::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

.date-picker-container ::v-deep .el-range-separator {
  color: #ffffff !important;
}

.date-picker-container ::v-deep .el-range-input {
  background: transparent !important;
  color: #ffffff !important;
}

.date-picker-container ::v-deep .el-range-input::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

// 额外的强制样式覆盖
::v-deep .el-date-editor.el-input {
  .el-input__wrapper {
    background: rgba(27, 40, 59, 0.8) !important;
    border: 1px solid rgba(14, 194, 244, 0.3) !important;
  }

  .el-input__inner {
    background: rgba(27, 40, 59, 0.8) !important;
    border: 1px solid rgba(14, 194, 244, 0.3) !important;
    color: #ffffff !important;
  }
}

.sankey-container {
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden;
  position: relative;
}

.sankey-chart {
  width: 100%;
  min-height: 600px; /* 设置最小高度，确保图表有足够空间渲染 */
  /* 完全移除过渡动画，实现瞬间变化 */
}

.no-data-container {
  width: 100%;
  height: 100px; /* 无数据时的较小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-data-text {
  color: #fff;
  font-size: 16px;
  text-align: center;
}

/* 自定义滚动条样式 */
.sankey-container::-webkit-scrollbar,
.sankey-fullscreen-container::-webkit-scrollbar {
  width: 8px;
}

.sankey-container::-webkit-scrollbar-track,
.sankey-fullscreen-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.sankey-container::-webkit-scrollbar-thumb,
.sankey-fullscreen-container::-webkit-scrollbar-thumb {
  background: rgba(14, 194, 244, 0.6);
  border-radius: 4px;
}

.sankey-container::-webkit-scrollbar-thumb:hover,
.sankey-fullscreen-container::-webkit-scrollbar-thumb:hover {
  background: rgba(14, 194, 244, 0.8);
}
</style>
