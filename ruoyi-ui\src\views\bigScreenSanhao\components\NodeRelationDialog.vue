<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div
      v-show="show"
      class="custom-dialog"
      :class="{ 'node-relation-fullscreen': isFullscreen }"
      :style="
        isFullscreen
          ? {}
          : { width: typeof width === 'number' ? width + 'px' : width }
      "
      @click.stop
    >
      <div class="custom-dialog-header">
        <span
          >{{ title }} -
          {{ (nodeData && nodeData.node && nodeData.node.name) || "" }}</span
        >
        <div style="display: flex; align-items: center">
          <div
            @click="handleScreen"
            :title="isFullscreen ? '退出全屏' : '全屏'"
            style="
              margin-right: 20px;
              cursor: pointer;
              color: #ffffff;
              font-size: 20px;
            "
          >
            <i
              :class="isFullscreen ? 'el-icon-rank' : 'el-icon-full-screen'"
              style="width: 20px; height: 20px"
            ></i>
          </div>
          <div class="custom-dialog-close" @click="closeDialog"></div>
        </div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="bg-box-content">
            <div
              class="relation-container"
              :style="{ height: containerHeight }"
            >
              <div
                ref="relationChart"
                class="relation-chart"
                :style="{ height: dynamicHeight }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "NodeRelationDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "节点关系分析",
    },
    nodeData: {
      type: Object,
      default: null,
    },
    closeOnClickMask: {
      type: Boolean,
      default: false,
    },
    width: {
      type: [String, Number],
      default: "80%",
    },
  },
  data() {
    return {
      show: false,
      chart: null,
      loading: false,
      // 记录哪些子标签节点已展开企业
      expandedSubLabels: new Set(),
      // 处理后的显示数据
      displayData: {
        nodes: [],
        links: [],
      },
      // 全屏状态
      isFullscreen: false,
      // ResizeObserver 实例
      resizeObserver: null,
      // 防抖定时器
      resizeTimer: null,
      // 基础高度
      baseHeight: 800,
      // 每个展开企业增加的高度
      heightPerExpansion: 50,
    };
  },
  computed: {
    // 动态计算桑葚图高度
    dynamicHeight() {
      // 计算当前显示的企业节点数量
      const enterpriseCount = this.displayData.nodes.filter(
        (node) => node.category === "企业"
      ).length;

      // 基础高度 + 企业数量 * 每个企业增加的高度
      const calculatedHeight =
        this.baseHeight + enterpriseCount * this.heightPerExpansion;
      return `${calculatedHeight}px`;
    },

    // 动态计算容器高度（用于滚动）
    containerHeight() {
      // 容器高度固定为基础高度，超出部分显示滚动条
      return `${this.baseHeight}px`;
    },
  },
  mounted() {
    // 添加ESC键监听
    document.addEventListener("keydown", this.handleKeydown);
    // 添加窗口大小变化监听
    window.addEventListener("resize", this.handleWindowResize);
  },

  beforeDestroy() {
    this.destroyChart();
    // 移除ESC键监听
    document.removeEventListener("keydown", this.handleKeydown);
    // 移除窗口大小变化监听
    window.removeEventListener("resize", this.handleWindowResize);
  },

  watch: {
    visible(newVal) {
      if (newVal) {
        this.show = true;
        // 重置全屏状态
        this.isFullscreen = false;
        this.$nextTick(
          function () {
            this.initChart();
            this.processDisplayData();
            this.updateChart();
          }.bind(this)
        );
      } else {
        this.show = false;
        this.destroyChart();
      }
    },
    nodeData: {
      handler() {
        if (this.visible && this.chart) {
          this.processDisplayData();
          this.updateChart();
        }
      },
      deep: true,
    },
  },
  methods: {
    // 关闭弹窗的方法
    closeDialog() {
      this.$emit("update:visible", false);
    },

    // 处理遮罩层点击事件
    handleMaskClick() {
      if (this.closeOnClickMask) {
        this.closeDialog();
      }
    },

    // 初始化图表
    initChart() {
      if (this.$refs.relationChart) {
        // 设置图表容器的尺寸 - 初始化时使用基础高度
        this.$refs.relationChart.style.width = "100%";
        this.$refs.relationChart.style.height = `${this.baseHeight}px`;

        this.chart = echarts.init(this.$refs.relationChart);

        // 添加节点点击事件
        this.chart.on(
          "click",
          function (params) {
            if (
              params.dataType === "node" &&
              params.data.category === "子标签"
            ) {
              this.toggleEnterpriseNodes(params.data.id);
            }
          }.bind(this)
        );

        // 设置尺寸变化监听
        this.setupResizeListeners();
      }
    },

    // 更新图表
    updateChart() {
      if (this.chart && this.displayData.nodes.length > 0) {
        const option = this.getChartOption();
        // 使用 notMerge: false 来避免完全重新渲染，提高性能
        this.chart.setOption(option, true);
      }
    },

    // 获取图表配置
    getChartOption() {
      if (
        !this.nodeData ||
        !this.nodeData.node ||
        this.displayData.nodes.length === 0
      ) {
        return {};
      }

      const nodes = this.displayData.nodes;
      const links = this.displayData.links;
      const targetNodeId = this.nodeData.node.id;

      // 为节点添加样式，突出显示目标节点
      const processedNodes = nodes.map(function (node) {
        return Object.assign({}, node, {
          itemStyle: {
            color:
              node.id === targetNodeId
                ? "#ff6b6b"
                : (node.itemStyle && node.itemStyle.color) || "#4992ff",
            borderColor: node.id === targetNodeId ? "#ff4757" : "#0ec2f4",
            borderWidth: node.id === targetNodeId ? 3 : 1,
          },
          label: {
            show: true,
            color: "#ffffff",
            fontSize: node.id === targetNodeId ? 18 : 14,
            fontWeight: node.id === targetNodeId ? "bold" : "normal",
          },
        });
      });

      return {
        backgroundColor: "transparent",
        title: {
          text: "",
          textStyle: {
            color: "#ffffff",
            fontSize: 18,
          },
          left: "center",
          top: 20,
        },
        tooltip: {
          trigger: "item",
          triggerOn: "mousemove",
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          borderColor: "#0ec2f4",
          borderWidth: 1,
          textStyle: {
            color: "#ffffff",
          },
          formatter: function (params) {
            if (params.dataType === "edge") {
              return (
                params.data.source +
                " → " +
                params.data.target +
                "<br/>影响度: " +
                params.data.value
              );
            } else {
              const depthMap = {
                0: "第1级",
                1: "第2级",
                2: "第3级",
                3: "第4级",
                4: "第5级",
                5: "第6级",
              };
              const levelText = depthMap[params.data.depth] || "未知层级";
              return (
                params.data.name +
                "<br/>类别: " +
                (params.data.category || "未知") +
                "<br/>层级: " +
                levelText
              );
            }
          },
        },
        series: [
          {
            type: "sankey",
            layout: "none",
            emphasis: {
              focus: "adjacency",
            },
            data: processedNodes,
            links: links,
            nodeGap: 10,
            nodeWidth: 40,
            layoutIterations: 0,
            left: "2%",
            right: "16%",
            top: "0.2%",
            bottom: "0.2%",
            label: {
              show: true,
              position: "right",
              color: "#ffffff",
              fontSize: 14,
              formatter: function (params) {
                return params.name.length > 12
                  ? params.name.substring(0, 12) + "..."
                  : params.name;
              },
            },
            lineStyle: {
              color: "source",
              opacity: 0.6,
            },
          },
        ],
      };
    },

    // 销毁图表
    destroyChart() {
      if (this.chart) {
        // 清理 ResizeObserver
        if (this.resizeObserver) {
          this.resizeObserver.disconnect();
        }
        // 清理定时器
        if (this.resizeTimer) {
          clearTimeout(this.resizeTimer);
        }
        // 清理窗口大小变化监听
        window.removeEventListener("resize", this.handleResize);
        this.chart.dispose();
        this.chart = null;
      }
    },

    // 设置尺寸变化监听
    setupResizeListeners() {
      // 创建 ResizeObserver 监听容器尺寸变化
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(() => {
          // 使用防抖处理，避免频繁触发
          clearTimeout(this.resizeTimer);
          this.resizeTimer = setTimeout(() => {
            this.handleResize();
          }, 100);
        });

        // 监听图表容器的尺寸变化
        this.resizeObserver.observe(this.$refs.relationChart);

        // 也监听父容器的尺寸变化
        const parentContainer = this.$refs.relationChart.parentElement;
        if (parentContainer) {
          this.resizeObserver.observe(parentContainer);
        }
      }

      // 监听窗口大小变化（作为备用方案）
      this.handleResize = this.handleResize.bind(this);
      window.addEventListener("resize", this.handleResize);
    },

    // 处理尺寸变化
    handleResize() {
      if (this.chart) {
        // 延迟执行 resize，确保 DOM 更新完成
        this.$nextTick(() => {
          this.chart.resize();
        });
      }
    },

    // 手动触发图表重新调整大小（供父组件调用）
    resizeChart() {
      this.handleResize();
    },

    // 全屏切换
    handleScreen() {
      this.isFullscreen = !this.isFullscreen;

      // 延迟调整图表大小，确保DOM更新完成
      this.$nextTick(() => {
        setTimeout(() => {
          // 强制重新计算容器尺寸
          if (this.$refs.relationChart) {
            this.$refs.relationChart.style.height = this.isFullscreen
              ? "100%"
              : "800px";
          }
          this.handleResize();
        }, 300); // 等待CSS动画完成
      });
    },

    // 处理键盘事件
    handleKeydown(event) {
      // 按ESC键退出全屏
      if (event.key === "Escape" && this.isFullscreen && this.visible) {
        this.isFullscreen = false;
      }
    },

    // 处理窗口大小变化
    handleWindowResize() {
      if (this.isFullscreen) {
        // 重新调整图表大小
        this.handleResize();
      }
    },

    // 处理显示数据（根据双击节点类型决定是否隐藏企业）
    processDisplayData() {
      if (!this.nodeData || !this.nodeData.relations) {
        this.displayData = { nodes: [], links: [] };
        return;
      }

      // 重置展开状态
      this.expandedSubLabels.clear();

      // 如果双击的不是企业节点，默认隐藏所有企业节点
      if (this.nodeData.node.category !== "企业") {
        this.displayData = this.filterEnterpriseNodes(this.nodeData.relations);
      } else {
        // 如果双击的是企业节点，显示所有相关节点
        this.displayData = {
          nodes: this.nodeData.relations.nodes,
          links: this.nodeData.relations.links,
        };
      }
    },

    // 过滤企业节点，根据展开状态决定是否显示
    filterEnterpriseNodes(data) {
      // 如果没有展开任何子标签，则隐藏所有企业节点
      if (this.expandedSubLabels.size === 0) {
        const filteredNodes = data.nodes.filter(function (node) {
          return node.category !== "企业";
        });
        const filteredLinks = data.links.filter(function (link) {
          const sourceExists = filteredNodes.find(function (n) {
            return n.id === link.source;
          });
          const targetExists = filteredNodes.find(function (n) {
            return n.id === link.target;
          });
          return sourceExists && targetExists;
        });

        return {
          nodes: filteredNodes,
          links: filteredLinks,
        };
      }

      // 如果有展开的子标签，则显示对应的企业节点
      const filteredNodes = data.nodes.filter(
        function (node) {
          if (node.category === "企业") {
            // 查找连接到此企业的子标签节点
            const connectedToSubLabel = data.links.some(
              function (link) {
                // 检查企业是否与已展开的子标签相连
                if (link.target === node.id) {
                  // 企业是目标，检查源是否是已展开的子标签
                  const sourceNode = data.nodes.find(function (n) {
                    return n.id === link.source;
                  });
                  return (
                    sourceNode &&
                    sourceNode.category === "子标签" &&
                    this.expandedSubLabels.has(sourceNode.id)
                  );
                }
                if (link.source === node.id) {
                  // 企业是源，检查目标是否是已展开的子标签
                  const targetNode = data.nodes.find(function (n) {
                    return n.id === link.target;
                  });
                  return (
                    targetNode &&
                    targetNode.category === "子标签" &&
                    this.expandedSubLabels.has(targetNode.id)
                  );
                }
                return false;
              }.bind(this)
            );

            return connectedToSubLabel;
          }
          return true; // 非企业节点都显示
        }.bind(this)
      );

      const filteredLinks = data.links.filter(function (link) {
        const sourceExists = filteredNodes.find(function (n) {
          return n.id === link.source;
        });
        const targetExists = filteredNodes.find(function (n) {
          return n.id === link.target;
        });
        return sourceExists && targetExists;
      });

      return {
        nodes: filteredNodes,
        links: filteredLinks,
      };
    },

    // 切换企业节点的显示/隐藏
    toggleEnterpriseNodes(subLabelId) {
      if (this.expandedSubLabels.has(subLabelId)) {
        // 如果已展开，则收起
        this.expandedSubLabels.delete(subLabelId);
      } else {
        // 如果未展开，则展开
        this.expandedSubLabels.add(subLabelId);
      }

      // 重新过滤数据
      const newDisplayData = this.filterEnterpriseNodes(
        this.nodeData.relations
      );

      // 计算新高度
      const enterpriseCount = newDisplayData.nodes.filter(
        (node) => node.category === "企业"
      ).length;
      const newHeight = `${
        this.baseHeight + enterpriseCount * this.heightPerExpansion
      }px`;

      // 同时更新数据、高度和图表，确保完全同步
      this.displayData = newDisplayData;
      if (this.$refs.relationChart) {
        this.$refs.relationChart.style.height = newHeight;
      }

      // 如果没有任何展开的子领域（所有企业都收起），回到顶部并恢复初始高度
      if (this.expandedSubLabels.size === 0) {
        // 先强制设置高度为基础高度
        if (this.$refs.relationChart) {
          this.$refs.relationChart.style.height = `${this.baseHeight}px`;
          // 强制浏览器重新计算布局
          this.$refs.relationChart.offsetHeight;
        }

        // 立即更新图表以确保高度生效
        if (this.chart) {
          this.chart.resize();
        }

        // 然后滚动到顶部
        const container = this.$refs.relationChart.parentElement;
        if (container) {
          // 强制触发重绘
          container.offsetHeight;
          container.scrollTop = 0;
          // 使用 scrollTo 确保滚动生效
          container.scrollTo({ top: 0, behavior: "instant" });
        }
      }

      // 立即更新图表
      this.updateChart();
      if (this.chart) {
        this.chart.resize();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001; // 比主弹窗层级高一点

  // 确保在所有分辨率下都能正确覆盖
  min-width: 100%;
  min-height: 100%;
  overflow: hidden;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 80%;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("../../../assets/bigScreenSanhao/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;
    // transition: all 0.3s ease;

    &.node-relation-fullscreen {
      width: calc(100vw - 40px) !important;
      height: calc(100vh - 40px) !important;
      max-width: none !important;
      max-height: none !important;
      margin: 0 !important;
      // 确保在所有分辨率下都能正确显示
      min-width: calc(100% - 40px) !important;
      min-height: calc(100% - 40px) !important;

      .custom-dialog-body {
        height: calc(100% - 80px); // 减去边距和header高度
        max-height: calc(100% - 80px);
        overflow-y: auto;
        overflow-x: hidden;

        .bg-box-content {
          height: 100%;

          .relation-container {
            height: 100%; /* 减去边距、header、padding和bg-box的padding */

            .relation-chart {
              min-height: 100%;
              height: 100%;
            }
          }
        }
      }
    }

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("../../../assets/bigScreenSanhao/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 20px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("../../../assets/bigScreenSanhao/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px;
        padding: 16px;
        height: 100%;
      }
    }
  }
}

/* 关系图容器样式 */
.relation-container {
  width: 100%;
  height: 800px;
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden;
  position: relative;
}

.relation-chart {
  width: 100%;
  min-height: 800px; /* 设置最小高度，确保图表有足够空间渲染 */
  /* 完全移除过渡动画，实现瞬间变化 */
}

/* 自定义滚动条样式 */
.relation-container::-webkit-scrollbar {
  width: 8px;
}

.relation-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.relation-container::-webkit-scrollbar-thumb {
  background: rgba(14, 194, 244, 0.6);
  border-radius: 4px;
}

.relation-container::-webkit-scrollbar-thumb:hover {
  background: rgba(14, 194, 244, 0.8);
}
</style>
