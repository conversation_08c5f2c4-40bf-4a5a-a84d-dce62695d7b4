<template>
  <div id="app">
    <router-view />
    <theme-picker />

    <!-- 全局文章通知弹框 -->
    <!-- <article-notification
      ref="articleNotification"
      :articles="notificationArticles"
      @close="handleNotificationClose"
      @view-article="handleViewArticle"
      @view-all="handleViewAllArticles"
    /> -->
  </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker";
import ArticleNotification from "@/components/ArticleNotification";
import articleNotificationManager from "@/utils/articleNotificationManager";

export default {
  name: "App",
  components: {
    ThemePicker,
    ArticleNotification,
  },
  data() {
    return {
      apptitle: process.env.VUE_APP_TITLE,
      notificationArticles: [],
    };
  },
  created() {
    document.referrerPolicy = "no-referrer";
    // this.initNotification();
  },
  beforeDestroy() {
    // 销毁通知管理器
    // articleNotificationManager.destroy();
  },
  methods: {
    // async initNotification() {
    //   // 初始化文章通知管理器
    //   await articleNotificationManager.init(this.showNotification);

    //   // 监听路由变化来判断是否登录
    //   this.$router.afterEach((to) => {
    //     this.checkLoginStatus(to.path);
    //   });

    //   // 初始检查登录状态
    //   this.$nextTick(() => {
    //     this.checkLoginStatus(this.$route.path);
    //   });
    // },

    // async checkLoginStatus(currentPath) {
    //   const isLoginPage = currentPath === "/login";
    //   const hasToken = this.$store.getters.token;
    //   const isLoggedIn = !isLoginPage && hasToken;

    //   console.log("检查登录状态:", {
    //     currentPath,
    //     isLoginPage,
    //     hasToken: !!hasToken,
    //     isLoggedIn,
    //   });

    //   await articleNotificationManager.setLoginStatus(isLoggedIn);
    // },

    // // 显示文章通知
    // showNotification(articles) {
    //   this.notificationArticles = articles;
    //   this.$nextTick(() => {
    //     if (this.$refs.articleNotification) {
    //       this.$refs.articleNotification.show();
    //     }
    //   });
    // },

    // // 处理通知关闭
    // handleNotificationClose() {
    //   this.notificationArticles = [];
    // },

    // // 处理查看单篇文章
    // handleViewArticle(article) {
    //   console.log("查看文章:", article);

    //   window.open(`/expressDetails?id=${article.id}`, "_blank");
    // },

    // // 处理查看全部文章
    // handleViewAllArticles() {
    //   console.log("查看全部文章");

    //   // 跳转到文章列表页面（根据实际路由调整）
    //   this.$router.push("/articles").catch(() => {
    //     // 如果路由不存在，显示提示信息
    //     this.$message.info("文章列表页面正在开发中");
    //   });
    // },
  },
  metaInfo() {
    return {
      title:
        this.$store.state.settings.dynamicTitle &&
        this.$store.state.settings.title,
      titleTemplate: this.apptitle,
      // titleTemplate: title => {
      //   return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      // }
    };
  },
};
</script>
<style scoped>
#app .theme-picker {
  display: none;
}
</style>
