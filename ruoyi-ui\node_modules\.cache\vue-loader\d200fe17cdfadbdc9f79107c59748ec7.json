{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\expressDetails\\index2.vue?vue&type=template&id=3b0dc51a&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\expressDetails\\index2.vue", "mtime": 1754294018383}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}