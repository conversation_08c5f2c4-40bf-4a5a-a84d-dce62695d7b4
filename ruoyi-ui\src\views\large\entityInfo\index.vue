<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="实体名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入实体名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['large:entityInfo:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['large:entityInfo:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['large:entityInfo:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['large:entityInfo:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="entityInfoList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index" />
      <el-table-column label="实体名称" align="center" prop="name" />
      <el-table-column label="实体类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.large_entity_type"
            :value="scope.row.type"
          />
        </template>
      </el-table-column>
      <el-table-column label="图片地址" align="center" prop="imgUrl" />
      <!-- <el-table-column label="文章地址" align="center" prop="articleUrl" /> -->

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['large:entityInfo:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['large:entityInfo:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改大屏-实体信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="80%" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="实体名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入实体名称" />
        </el-form-item>
        <el-form-item label="上传图片" prop="imgUrl">
          <ImageUpload :limit="1" :value="form.imgUrl" @input="imageFun" />
        </el-form-item>
        <el-form-item label="实体类型" prop="type">
          <el-select
            v-model="form.type"
            placeholder="实体类型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in dict.type.large_entity_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-divider content-position="center">保留实体内容信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAddXyLargeScreenEntityData"
              >添加</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              @click="handleDeleteXyLargeScreenEntityData"
              >删除</el-button
            >
          </el-col>
        </el-row>
        <el-table
          :data="xyLargeScreenEntityDataList"
          :row-class-name="rowXyLargeScreenEntityDataIndex"
          @selection-change="handleXyLargeScreenEntityDataSelectionChange"
          ref="xyLargeScreenEntityData"
        >
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column
            label="序号"
            align="center"
            prop="index"
            width="50"
          />
          <el-table-column label="标签名" prop="tag" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.tag" placeholder="请输入标签名" />
            </template>
          </el-table-column>
          <el-table-column label="文章内容" prop="article">
            <template slot-scope="scope">
              <editor v-model="scope.row.article" :min-height="192" />
            </template>
          </el-table-column>
          <el-table-column label="文章文件地址" prop="articleUrl" width="220">
            <template slot-scope="scope">
              <FileUpload
                :limit="1"
                :fileType="['doc', 'pdf', 'docx']"
                :value="scope.row.articleUrl"
                @input="(data) => articleFun(data, scope.$index, scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="排序" prop="sort" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.sort" placeholder="请输入排序" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listEntityInfo,
  getEntityInfo,
  delEntityInfo,
  addEntityInfo,
  updateEntityInfo,
} from "@/api/large/entityInfo";

export default {
  name: "EntityInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedXyLargeScreenEntityData: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 大屏-实体信息表格数据
      entityInfoList: [],
      // 大屏-实体数据表格数据
      xyLargeScreenEntityDataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        name: null,
        type: null,
      },
      // 表单参数
      form: {
        type: "1",
        imgUrl: null,
      },
      // 表单校验
      rules: {
        type: [
          { required: true, message: "实体类型不能为空", trigger: "change" },
        ],
      },
    };
  },
  dicts: ["large_entity_type"],
  created() {
    this.getList();
  },
  methods: {
    /** 查询大屏-实体信息列表 */
    getList() {
      this.loading = true;
      listEntityInfo(this.queryParams).then((response) => {
        this.entityInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        type: "1",
        imgUrl: null,
        createTime: null,
        updateTime: null,
      };
      this.xyLargeScreenEntityDataList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加实体信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getEntityInfo(id).then((response) => {
        this.form = {
          ...response.data,
          type: response.data.type ? String(response.data.type) : "",
        };
        this.xyLargeScreenEntityDataList =
          response.data.xyLargeScreenEntityDataList;
        this.open = true;
        this.title = "修改实体信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.xyLargeScreenEntityDataList =
            this.xyLargeScreenEntityDataList;
          if (this.form.id != null) {
            updateEntityInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEntityInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids;
      this.$modal
        .confirm('是否确认删除大屏-实体信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delEntityInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 大屏-实体数据序号 */
    rowXyLargeScreenEntityDataIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 大屏-实体数据添加按钮操作 */
    handleAddXyLargeScreenEntityData() {
      let obj = {};
      obj.tag = "";
      obj.article = "";
      obj.articleUrl = "";
      obj.sort = "";
      this.xyLargeScreenEntityDataList.push(obj);
    },
    /** 大屏-实体数据删除按钮操作 */
    handleDeleteXyLargeScreenEntityData() {
      if (this.checkedXyLargeScreenEntityData.length == 0) {
        this.$modal.msgError("请先选择要删除的大屏-实体数据数据");
      } else {
        const xyLargeScreenEntityDataList = this.xyLargeScreenEntityDataList;
        const checkedXyLargeScreenEntityData =
          this.checkedXyLargeScreenEntityData;
        this.xyLargeScreenEntityDataList = xyLargeScreenEntityDataList.filter(
          function (item) {
            return checkedXyLargeScreenEntityData.indexOf(item.index) == -1;
          }
        );
      }
    },
    /** 复选框选中数据 */
    handleXyLargeScreenEntityDataSelectionChange(selection) {
      this.checkedXyLargeScreenEntityData = selection.map((item) => item.index);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "large/entityInfo/export",
        {
          ...this.queryParams,
        },
        `entityInfo_${new Date().getTime()}.xlsx`
      );
    },
    //图片回传
    imageFun(data) {
      this.form.imgUrl = data;
    },
    //附件上传回传
    articleFun(data, index, row) {
      this.$set(this.xyLargeScreenEntityDataList, index, {
        ...row,
        articleUrl: data,
      });
    },
  },
};
</script>
