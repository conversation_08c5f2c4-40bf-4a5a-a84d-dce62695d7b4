<template>
  <div v-if="funEsSeach">
    <splitpanes class="default-theme">
      <pane
        class="leftLink"
        ref="leftLink"
        min-size="20"
        max-size="50"
        size="26"
      >
        <TreeTable
          ref="treeTable"
          :data="treeDataTransfer"
          :total="treeTotal"
          :current-page="treeCurrentPage"
          :page-size="treePageSize"
          :loading="loading"
          :selected-sources="checkList"
          row-key="id"
          @selection-change="handleSelectionChange"
          @reset="handleReset"
          @size-change="handleTreePageSizeChange"
          @current-change="handleTreeCurrentChange"
          @filter-search="handleFilterSearch"
          @classify-change="handleClassifyChange"
        />
      </pane>
      <pane min-size="50" max-size="80" size="74">
        <div
          class="rightMain"
          style="margin-left: 0; overflow-y: auto"
          ref="rightMain"
        >
          <div class="toolBox">
            <!-- <div class="title" :style="{ height: ActiveData.title ? '' : '50px' }">
              <p v-if="ActiveData.title">{{ ActiveData.title }}</p>
              <p v-else></p>
            </div> -->
            <div class="mainTool">
              <p>
                发布日期:
                <el-date-picker
                  value-format="yyyy-MM-dd HH:mm:ss"
                  v-model="SeachData.customDay"
                  style="display: inline-block; width: 320px; margin-left: 10px"
                  size="mini"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  unlink-panels
                  clearable
                ></el-date-picker>
              </p>
              <p>
                创建日期:
                <el-date-picker
                  value-format="yyyy-MM-dd HH:mm:ss"
                  v-model="SeachData.collectionTime"
                  style="display: inline-block; width: 320px; margin-left: 10px"
                  size="mini"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  unlink-panels
                  clearable
                ></el-date-picker>
              </p>
              <div class="keyword">
                <span
                  style="
                    width: 60px;
                    display: inline-block;
                    text-align: right;
                    margin-right: 5px;
                  "
                  >关键词:</span
                >
                <el-input
                  ref="keywordRef"
                  placeholder="请输入关键词,使用逗号分割(英文)"
                  style="width: 430px"
                  v-model="SeachData.keyword"
                  @focus="showHistoryList()"
                  @blur="hideHistoryList()"
                >
                </el-input>
                <el-button
                  type="primary"
                  size="mini"
                  @click="funEsSeach()"
                  :loading="buttonDisabled"
                  style="margin-left: 10px; height: 36px"
                  >搜索</el-button
                >
                <div class="history" v-show="showHistory">
                  <div
                    class="historyItem"
                    v-for="(history, index) in historyList"
                    :key="index"
                    v-loading="historyLoading"
                  >
                    <div @click="keywordsChange(history)" class="historyText">
                      {{ history.keyword }}
                    </div>
                    <el-button
                      type="text"
                      @click="removeHistory(history, 1)"
                      style="color: #999; font-size: 12px"
                      >删除</el-button
                    >
                  </div>
                  <div class="historyItem">
                    <el-button type="text" @click="moreHistory()"
                      >更多</el-button
                    >
                    <el-button
                      type="text"
                      @click="clearHistory()"
                      style="color: #999; font-size: 12px"
                      >清空</el-button
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
          <MainArticle
            v-loading="tableLoading"
            :flag="'zhikubaogao'"
            :currentPage="currentPage"
            :pageSize="pageSize"
            :total="total"
            :ArticleList="ArticleList"
            :keywords="SeachData.keyword"
            @handleCurrentChange="handleCurrentChange"
            @handleSizeChange="handleSizeChange"
            @Refresh="funEsSeach()"
            :SeachData="SeachData"
            ref="mainArticle"
          ></MainArticle>
        </div>
      </pane>
    </splitpanes>

    <el-dialog
      title="关键词历史"
      :visible.sync="dialogVisible1"
      width="570px"
      :close-on-click-modal="false"
    >
      <div class="history" v-loading="historyLoading">
        <div
          class="historyItem"
          v-for="(history, index) in historyList1"
          :key="index"
        >
          <div @click="keywordsChange(history)" class="historyText">
            {{ history.keyword }}
          </div>
          <el-button type="text" @click="removeHistory(history, 2)"
            >删除</el-button
          >
        </div>
      </div>
      <pagination
        v-show="total1 > 0"
        :total="total1"
        :page.sync="queryParams1.pageNum"
        :limit.sync="queryParams1.pageSize"
        :background="false"
        @pagination="getArticleHistory1"
        :layout="'total, prev, pager, next'"
      />
    </el-dialog>
  </div>
</template>

<script>
import api from "@/api/ScienceApi/index.js";
import topSeach from "@/views/components/topSeach.vue";
import MainArticle from "../components/MainArticle.vue";
import {
  listArticleHistory,
  delArticleHistory,
  addArticleHistory,
  cleanArticleHistory,
} from "@/api/article/articleHistory";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
import TreeTable from "@/components/TreeTable/index.vue";

export default {
  components: { topSeach, MainArticle, Splitpanes, Pane, TreeTable },
  dicts: ["is_technology"],
  data() {
    return {
      width: "258",
      isReSize: false,
      /* 文章主体组件数据 */
      currentPage: 1,
      pageSize: 50,
      total: 0,
      ArticleList: [],
      treeDataTransfer: [],
      checkList: [],
      /* 树形分页数据 */
      treeCurrentPage: 1,
      treePageSize: 100,
      treeTotal: 0,

      /* 搜索组件数据 */
      SeachData: {
        metaMode: "" /* 匹配模式 */,
        keyword: "" /* 关键词 */,
        timeRange: 4 /* 时间范围 */,
        customDay: [] /* 自定义天 */,
        collectionDateType: null /* 时间范围 */,
        collectionTime: [] /* 自定义天 */,
        isTechnology: "1",
        sortMode: "0",
        emotion: "0",
        hasCache: "0",
      } /* 搜索条件 */,
      /* 排序模式 - 单独提取，避免触发SeachData的深度监听 */
      buttonDisabled: false /* 按钮防抖 */,
      ActiveData: {},
      seniorSerchFlag: false /* 普通检索或高级检索 */,
      areaList: [] /* 国内地区 */,
      countryList: [] /* 国家或地区 */,
      KeList: [],
      funEsSeach: null,
      treeQuery: {
        filterwords: "", // 添加树搜索关键字
      },
      domainList: [],
      industryList: [],
      showHistory: false,
      historyList: [],
      historyTimeout: null,
      dialogVisible1: false,
      historyLoading: false,
      queryParams1: {
        pageNum: 1,
        pageSize: 10,
      },
      total1: 0,
      historyList1: [],
      initializationCompleted: false, // 标记初始化是否完成
      // 从Wechat.vue同步的属性
      loading: false, // 树组件loading状态
      tableLoading: false, // 表格loading状态
      isQuerying: false, // 查询防抖
      queryDebounceTimer: null, // 查询防抖定时器
      isRightFilter: false, // 标记右侧筛选条件是否发生变化
      isLeftReset: false, // 标记左侧树是否重置
      selectedClassify: null, // 选中的数据源分类
    };
  },
  async created() {
    try {
      // 初始化funEsSeach方法
      this.funEsSeach = this.EsSeach;

      // 先加载基础数据
      this.getArticleHistory();

      // 加载树数据和内容数据
      await this.initializeData();

      // 标记初始化完成，这样watch监听器才会开始工作
      this.initializationCompleted = true;
    } catch (error) {
      console.error("组件初始化失败:", error);
      this.$message.error("初始化失败，请刷新页面重试");
    }
  },

  mounted() {
    // TreeTable 组件不需要特殊的状态检查
  },
  watch: {
    // 监听筛选条件变化
    "SeachData.customDay": {
      handler(newVal, oldVal) {
        if (!this.initializationCompleted || newVal === oldVal) return;
        this.handleFilterChange();
      },
    },
    "SeachData.collectionTime": {
      handler(newVal, oldVal) {
        if (!this.initializationCompleted || newVal === oldVal) return;
        this.handleFilterChange();
      },
    },
  },
  methods: {
    // 初始化数据
    async initializeData() {
      try {
        // 加载文章列表（内部已经处理了 tableLoading）
        this.queryArticleList();
        // 加载树数据
        await this.queryTreeData();
        // 等待树组件完全渲染
        await this.$nextTick();
      } catch (error) {
        console.error("初始化数据失败:", error);
        this.$message.error("初始化失败，请刷新页面重试");
      }
    },

    // TreeTable 组件事件处理方法

    // 处理选择变化
    handleSelectionChange(selectedData) {
      this.checkList = selectedData;

      // 重置页码并查询内容
      this.currentPage = 1;
      this.scrollToTopImmediately();
      if (!this.isRightFilter) {
        this.queryArticleList();
      }
    },

    // 处理重置
    handleReset() {
      // 先清空过滤关键字，避免触发 handleFilterSearch
      this.treeQuery.filterwords = "";

      // 然后设置重置标记
      this.isLeftReset = true;

      // 清空选中状态
      this.checkList = [];

      // 重置页码并查询列表数据
      this.currentPage = 1;
      this.treeCurrentPage = 1;
      this.SeachData.hasCache = "1";
      this.scrollToTopImmediately();

      // 重新查询树和列表
      this.queryTreeAndList();
    },

    // 处理筛选条件变化 - 来自右侧筛选条件的变化
    handleFilterChange() {
      this.isRightFilter = true; // 标记右侧筛选条件发生变化

      // 清空左侧树选中状态
      this.checkList = [];

      // 重置分页到第一页
      this.currentPage = 1;
      this.treeCurrentPage = 1;
      this.SeachData.hasCache = "0";

      // 滚动到顶部
      this.scrollToTopImmediately();

      // 同时查询树和列表
      this.queryTreeAndList();
    },

    // 同时查询树和列表 - 直接从Wechat.vue复制
    async queryTreeAndList() {
      try {
        await Promise.all([
          this.queryTreeData(),
          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading
        ]);
        // 查询完成后重置右侧筛选标记
        this.isRightFilter = false;
        setTimeout(() => {
          this.isLeftReset = false;
        }, 300);
      } catch (error) {
        console.error("同时查询树和列表失败:", error);
        this.$message.error("查询失败，请重试");
        // 即使出错也要重置标记
        this.isRightFilter = false;
        setTimeout(() => {
          this.isLeftReset = false;
        }, 300);
      }
    },

    // 分页处理
    handleCurrentChange(current) {
      this.currentPage = current;
      this.scrollToTopImmediately();
      this.queryArticleList();
    },

    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.scrollToTopImmediately();
      this.queryArticleList();
    },

    // 查询树数据
    async queryTreeData() {
      this.loading = true;
      try {
        const params = {
          pageNum: this.treeCurrentPage,
          pageSize: this.treePageSize,
          startPublishTime: this.SeachData.customDay
            ? this.SeachData.customDay[0]
            : "",
          endPublishTime: this.SeachData.customDay
            ? this.SeachData.customDay[1]
            : "",
          startCreateTime: this.SeachData.collectionTime
            ? this.SeachData.collectionTime[0]
            : "",
          endCreateTime: this.SeachData.collectionTime
            ? this.SeachData.collectionTime[1]
            : "",
          keywords: this.SeachData.keyword,
          // 添加关键字过滤参数
          filterwords: this.treeQuery.filterwords || "",
          // 添加数据源分类参数
          thinkTankClassification: this.selectedClassify,
          hasCache: this.SeachData.hasCache,
        };

        if (this.$route.query.menuType) {
          params.menuType = this.$route.query.menuType;
        }

        const res = await api.monitoringMedium(params);

        if (res.code === 200) {
          const dataList = res.rows || [];
          const total = res.total || 0;

          const mapData = (data) =>
            data.map((item, index) => ({
              id: `${
                item.sourceSn || "unknown"
              }_${index}_${Date.now()}_${Math.random()
                .toString(36)
                .substring(2, 11)}`, // 确保绝对唯一性
              label: item.cnName,
              count: item.articleCount || 0,
              orderNum: item.orderNum,
              country: item.countryOfOrigin || null,
              sourceSn: item.sourceSn,
              url: item.url || null,
            }));

          this.treeDataTransfer = mapData(dataList);
          this.treeTotal = total;
        }
      } catch (error) {
        console.error("查询树数据失败:", error);
        this.$message.error("获取数据源失败");
      } finally {
        this.loading = false;
      }
    },

    // 查询文章列表（带防抖）
    async queryArticleList() {
      // 防止重复查询
      if (this.isQuerying) {
        return;
      }

      // 立即显示loading状态
      this.tableLoading = true;

      // 清除之前的防抖定时器
      if (this.queryDebounceTimer) {
        clearTimeout(this.queryDebounceTimer);
      }

      // 设置防抖，300ms后执行查询
      this.queryDebounceTimer = setTimeout(async () => {
        try {
          this.isQuerying = true;

          const params = {
            pageNum: this.currentPage,
            pageSize: this.pageSize,
            startPublishTime: this.SeachData.customDay
              ? this.SeachData.customDay[0]
              : "",
            endPublishTime: this.SeachData.customDay
              ? this.SeachData.customDay[1]
              : "",
            startCreateTime: this.SeachData.collectionTime
              ? this.SeachData.collectionTime[0]
              : "",
            endCreateTime: this.SeachData.collectionTime
              ? this.SeachData.collectionTime[1]
              : "",
            keywords: this.SeachData.keyword,
            isSort: this.SeachData.sortMode,
          };
          // 如果有选中的数据源，添加数据源参数 - 直接从Wechat.vue复制
          if (this.checkList && this.checkList.length > 0) {
            const data = this.checkList.map((item) => item.label);
            const sourceSn = data
              .map((item) => {
                const foundItem = this.treeDataTransfer.find(
                  (row) => row.label === item
                );
                return foundItem ? foundItem.sourceSn : null;
              })
              .filter((sn) => sn !== null);

            params.weChatName = String(data);
            params.sourceSn = String(sourceSn);
          }

          // 记录关键词历史
          if (params.keywords) {
            addArticleHistory({ keyword: params.keywords, type: 2 }).then(
              () => {
                this.getArticleHistory();
              }
            );
          }

          const res = await api.getReportsList({ ...params });

          if (res.code == 200) {
            this.ArticleList = res.rows || [];
            this.total = res.total || 0;

            // 处理分页为空的情况
            if (
              this.ArticleList.length == 0 &&
              this.pageSize * (this.currentPage - 1) >= this.total &&
              this.total != 0
            ) {
              this.currentPage = Math.max(
                1,
                Math.ceil(this.total / this.pageSize)
              );
              // 重新查询
              await this.queryArticleList();
              return; // 重新查询时不要关闭loading
            }
          } else {
            this.$message.error(res.msg || "获取数据失败");
          }
        } catch (error) {
          console.error("查询文章列表失败:", error);
          this.$message.error("查询失败，请重试");
        } finally {
          this.isQuerying = false;
          this.tableLoading = false; // 查询完成后关闭loading
          this.buttonDisabled = false;
        }
      }, 300);
    },

    // 滚动到顶部
    scrollToTopImmediately() {
      this.$nextTick(() => {
        // 尝试多种滚动方式确保滚动成功
        const scrollBoxElement = document.querySelector(".scollBox");
        if (scrollBoxElement) {
          scrollBoxElement.scrollTop = 0;
        }

        // 如果MainArticle组件有scroll引用，也尝试滚动它
        if (
          this.$refs.mainArticle &&
          this.$refs.mainArticle.$refs &&
          this.$refs.mainArticle.$refs.scroll
        ) {
          this.$refs.mainArticle.$refs.scroll.scrollTop = 0;
        }

        // 滚动整个右侧区域
        const rightMain = document.querySelector(".rightMain");
        if (rightMain) {
          rightMain.scrollTop = 0;
        }
      });
    },
    async getArea() {
      try {
        const Response = await api.getAreaList();
        if (Response && Response.code == 200 && Response.data) {
          this.areaList = Response.data[0] || [];
          this.countryList = Response.data[1] || [];
        } else {
          console.warn("获取地区数据失败或数据为空");
        }
      } catch (err) {
        console.error("获取区域数据失败:", err);
        this.$message.error("地区数据获取失败");
      }
    },

    async getArticleHistory() {
      try {
        const res = await listArticleHistory({
          pageNum: 1,
          pageSize: 5,
          type: 2,
        });
        if (res && res.code === 200) {
          this.historyList = res.rows || [];
        }
      } catch (error) {
        console.error("获取历史记录失败:", error);
      }
    },

    // 树节点过滤方法
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    // 添加EsSeach方法以兼容原有调用
    EsSeach(flag) {
      if (flag === "filter") {
        // 筛选变化时同时查询树和列表
        this.queryTreeAndList();
      } else {
        // 其他情况只查询列表
        this.queryArticleList();
      }
    },
    async removeHistory(item, type) {
      try {
        if (this.historyTimeout) {
          clearTimeout(this.historyTimeout);
        }

        if (item && item.id) {
          await delArticleHistory([item.id]);

          if (type == 1) {
            if (this.$refs["keywordRef"]) {
              this.$refs["keywordRef"].focus();
            }
            await this.getArticleHistory();
          } else {
            await this.getArticleHistory();
            await this.getArticleHistory1();
          }
        }
      } catch (error) {
        console.error("删除历史记录时出错:", error);
        this.$message.error("删除历史记录失败");
      }
    },

    showHistoryList() {
      try {
        this.showHistory = true;
      } catch (error) {
        console.error("显示历史列表时出错:", error);
      }
    },

    hideHistoryList() {
      try {
        if (this.historyTimeout) {
          clearTimeout(this.historyTimeout);
        }

        this.historyTimeout = setTimeout(() => {
          this.showHistory = false;
        }, 500);
      } catch (error) {
        console.error("隐藏历史列表时出错:", error);
        this.showHistory = false; // 确保在出错时也能隐藏列表
      }
    },

    // 关键词历史选择 - 直接从Wechat.vue复制
    keywordsChange(item) {
      this.SeachData.keyword = item.keyword;
      this.dialogVisible1 = false;
      this.scrollToTopImmediately();
      this.currentPage = 1;
      this.queryTreeAndList();
    },

    async clearHistory() {
      try {
        if (this.historyTimeout) {
          clearTimeout(this.historyTimeout);
        }

        if (this.$refs["keywordRef"]) {
          this.$refs["keywordRef"].focus();
        }

        await cleanArticleHistory(2);
        await this.getArticleHistory();
      } catch (error) {
        console.error("清除历史记录时出错:", error);
        this.$message.error("清除历史记录失败");
      }
    },

    moreHistory() {
      try {
        this.historyLoading = true;
        this.getArticleHistory1();
        this.dialogVisible1 = true;
      } catch (error) {
        console.error("加载更多历史记录时出错:", error);
        this.historyLoading = false;
      }
    },

    async getArticleHistory1() {
      try {
        this.historyLoading = true;
        const response = await listArticleHistory({
          ...this.queryParams1,
          type: 2,
        });

        if (response) {
          this.historyList1 = response.rows || [];
          this.total1 = response.total || 0;
        }

        this.historyLoading = false;
      } catch (error) {
        console.error("获取文章历史记录时出错:", error);
        this.historyLoading = false;
        this.$message.error("获取搜索历史失败");
      }
    },

    openUrl(url) {
      window.open(url, "_blank");
    },

    // 处理过滤搜索（来自 TreeTable 组件）
    handleFilterSearch(keyword) {
      if (this.isLeftReset) {
        return;
      }
      // 更新查询参数中的 filterwords
      this.treeQuery.filterwords = keyword || "";

      // 重置到第一页
      this.treeCurrentPage = 1;
      this.SeachData.hasCache = "1";

      // 调用树数据查询接口
      this.queryTreeData();
    },

    // 处理数据源分类变化（来自 TreeTable 组件）
    handleClassifyChange(classifyValue) {
      // 更新选中的分类
      this.selectedClassify = classifyValue;

      // 重置到第一页
      this.treeCurrentPage = 1;
      this.SeachData.hasCache = "1";

      // 只调用树数据查询接口，不更新右侧列表
      this.queryTreeData();
    },

    // 处理树形分页页码变化
    handleTreeCurrentChange(page) {
      this.treeCurrentPage = page;
      this.SeachData.hasCache = "1";
      this.queryTreeData();
    },

    // 处理树形分页每页大小变化
    handleTreePageSizeChange(size) {
      this.treePageSize = size;
      this.treeCurrentPage = 1;
      this.SeachData.hasCache = "1";
      this.queryTreeData();
    },

    // 添加缺失的openNewView方法
    openNewView(item) {
      window.open(
        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,
        "_blank"
      );
    },

    // 添加缺失的handleHistoryPagination方法
    handleHistoryPagination() {
      this.getArticleHistory1();
    },
  },
};
</script>

<style lang="scss" scoped>
.treeBox {
  width: 100%;
  height: calc(100vh - 176px);
  overflow-y: auto;
}

.tree-pagination {
  padding: 10px;
  border-top: 1px solid #ebeef5;
  background-color: #fff;
  text-align: center;

  ::v-deep .el-pagination {
    .el-pagination__sizes {
      margin-top: -2px;
    }
  }
}

.treeMain {
  position: relative;
}

.treeQuery {
  ::v-deep .el-input--mini .el-input__inner {
    height: 24px;
    line-height: 24px;
    padding: 0 4px;
  }

  ::v-deep .el-input__suffix {
    // height: 20px;
    right: -2px;
    // top: 5px;
  }
}

.toolBox {
  min-height: 130px;
  height: auto;
  padding-bottom: 15px;
  background-color: rgb(255, 255, 255);
  // box-shadow: -1px 2px 15px #cecdcd;
  border-left: solid 1px rgb(221, 219, 219);

  .title {
    display: flex;
    justify-content: space-between;
    height: 70px;
    padding: 0 30px;
    font-size: 19px;
  }

  .mainTool {
    padding: 0 28px;
    font-size: 14px;
    color: rgb(58, 58, 58);
  }

  .mainToolOne {
    margin-top: 15px;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    // align-items: center;
  }

  .mainToolTwo {
    display: flex;
    align-items: center;
    height: 40px;

    p {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .btn {
    margin: 15px 0 0 25px;
  }
}

.keyword {
  width: 100%;
  position: relative;

  .history {
    width: 430px;
    position: absolute;
    background: #fff;
    z-index: 9999;
    left: 65px;
    border: 1px solid rgb(221, 219, 219);

    .historyItem {
      padding-left: 20px;

      .historyText {
        width: 450px;
        height: 34px;
        line-height: 34px;
      }

      &:nth-last-of-type(1) {
        padding-left: 0;

        ::v-deep .el-button--text {
          padding: 10px 20px;
        }
      }
    }
  }
}

.history {
  width: 530px;

  .historyItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    padding: 0 10px;
    overflow: hidden;

    .historyText {
      width: 350px;
      height: 34px;
      line-height: 34px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
  }
}
</style>
