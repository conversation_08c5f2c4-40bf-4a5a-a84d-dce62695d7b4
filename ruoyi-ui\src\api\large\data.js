import request from '@/utils/request'

// 查询大屏-区域分析文章数据列表
export function listData(query) {
  return request({
    url: '/large/data/list',
    method: 'get',
    params: query
  })
}

// 查询大屏-区域分析文章数据详细
export function getData(id) {
  return request({
    url: '/large/data/' + id,
    method: 'get'
  })
}

// 新增大屏-区域分析文章数据
export function addData(data) {
  return request({
    url: '/large/data',
    method: 'post',
    data: data
  })
}

// 修改大屏-区域分析文章数据
export function updateData(data) {
  return request({
    url: '/large/data',
    method: 'put',
    data: data
  })
}

// 删除大屏-区域分析文章数据
export function delData(id) {
  return request({
    url: '/large/data/' + id,
    method: 'delete'
  })
}
