<template>
  <div style="width: 100%; height: 100%; position: relative">
    <div id="mainPie" style="width: 100%; height: 100%"></div>
    <el-row class="pie-number">
      <el-col :span="24">总计</el-col>
      <el-col :span="24"> 57</el-col>
    </el-row>
  </div>
</template>
 
<script>
import request from "@/utils/request";
import * as echarts from "echarts";

export default {
  data() {
    return {
      myChart: null,
      option: {},
    };
  },
  mounted() {
    this.initChart();
    // this.init();
  },

  components: {},
  methods: {
    init() {
      return new Promise((resolve, reject) => {
        request({
          url: "/xy/getDeve",
          method: "get",
          data: {},
        }).then((res) => {
          this.mapDataArr = res;
          this.initChart();
        });
      });
    },
    /**
     *
     */
    initChart() {
      let chartDom = document.getElementById("mainPie");

      let myChart = echarts.init(chartDom);
      let option = {
        grid: {
          left: "3%",
          right: "4%",
          bottom: "15%",
          top: "8%",
          containLabel: true,
        },
        tooltip: {
          trigger: "item",
        },
        series: [
          {
            type: "pie",
            radius: ["60%", "80%"],
            avoidLabelOverlap: false,

            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              show: false,
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 1048, name: "Search Engine" },
              { value: 735, name: "Direct" },
              { value: 580, name: "Email" },
              { value: 484, name: "Union Ads" },
              { value: 300, name: "Video Ads" },
            ],
          },
        ],
      };
      //随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        myChart.resize();
      });
      setTimeout(() => {
        myChart.resize();
      }, 1);
      myChart.setOption(option);
    },
  },
  beforeDestroy() {
    /* 页面组件销毁的时候，别忘了移除绑定的监听resize事件，否则的话，多渲染几次
      容易导致内存泄漏和额外CPU或GPU占用哦*/
    window.removeEventListener("resize", () => {
      this.myChart.resize();
    });
  },
};
</script>

<style lang='scss'>
.pie-number {
  font-weight: bold;
  color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  text-align: center;
  transform: translate(-50%, -50%);
  font-size: 25px;
}
</style>