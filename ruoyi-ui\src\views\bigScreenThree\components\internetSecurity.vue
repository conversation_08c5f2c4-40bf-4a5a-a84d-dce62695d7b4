<template>
  <div class="table-three">
    <div class="table_main" ref="scrollTableBox">
      <div ref="scrollTable" :class="{ 'scoll-Table2': isScrollTable }"
        :style="{ '--dynamic-height': `-${dynamicHeight}px`, '--dynamic-time': `${dynamicHeight / 40}s` }">
        <div class="table_col" v-for="(item, index) in hotList" :key="index" @click="expertFun">
          <div class="item-bottom">
            <el-tooltip class="item" effect="dark" :content="item.reportName" placement="top-start">
              <div class="last-child" @click="jumpFun(item.reportUrl)">
                {{ item.reportName }}
              </div>
            </el-tooltip>

            <div style="width: 100px; text-align: right">
              {{ item.reportDate }}
            </div>
          </div>
          <img class="bottom-border" :src="require('@/assets/bigScreenThree/bottom-border.png')" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isScrollTable: false,
      dynamicHeight: 0,
      hotList: [
        {
          reportName: "CISA 对主动利用 Palo Alto Networks 重大漏洞发出警告",
          reportDate: "2024-11",
        },
        {
          reportName: "黑客可通过系统漏洞访问马自达汽车控制装置...",
          reportDate: " 2024-11",
        },
        {
          reportName: "Veeam Backup Replication 漏洞在新的 Frag 勒索软件攻击中被重复使用",
          reportDate: " 2024-11",
        },
        {
          reportName: "美国机构提醒员工限制使用电话因为 Salt Typhoon 黑客攻击了电信提供商",
          reportDate: " 2024-11",
        },
        {
          reportName: "Spynote 恶意软件虚假防病毒软件在复杂的新活动中以 Android 用户为目标",
          reportDate: " 2024-11",
        },
        {
          reportName: "Idc报告解读实用型靶场将成为下一代网络靶场的必然方向",
          reportDate: " 2024-11",
        },
        {
          reportName: "360教育行业屡获大单力抗国家级黑客与勒索双重威胁",
          reportDate: " 2024-11",
        },
        {
          reportName: "黑客在加密勒索软件攻击中索要价值 125 万美元的法国面包",
          reportDate: " 2024-11",
        },
        {
          reportName: "新型 Steelfox 恶意软件冒充流行软件窃取浏览器数据",
          reportDate: " 2024-11",
        },
        {
          reportName: "Cisa 扩展了 Kev 目录增加了四个被积极利用的漏洞",
          reportDate: " 2024-11",
        },
        {
          reportName: "马格努斯行动 警方摧毁 Redline 和 Meta 信息窃取基础设施",
          reportDate: " 2024-11",
        },
        {
          reportName: "正式发布360深度参编终端安全国家标准",
          reportDate: " 2024-11",
        },
        {
          reportName: "Ai 如何改变网络风险量化",
          reportDate: " 2024-11",
        },
        {
          reportName: "美国足协披露数据安全事件",
          reportDate: " 2024-11 ",
        },
        {
          reportName: "Schneider Electric 在报告黑客索赔后调查安全事件",
          reportDate: " 2024-11 ",
        },
        {
          reportName: "Veildrive 攻击利用微软服务逃避检测并传播恶意软件",
          reportDate: "  2024-11",
        },
        {
          reportName: "Intelbroker 声称在 Breachforums 上出售诺基亚的源代码",
          reportDate: " 2024-11 ",
        },
        {
          reportName: "5亿美元索赔达美航空怒告crowd Strike网安故障谁来买单",
          reportDate: " 2024-11 ",
        },
        {
          reportName: "黑客攻击意大利政府核心部门",
          reportDate: " 2024-11 ",
        },
        {
          reportName: "360漏洞云亮相看雪峰会携手行业精英共话安全创新",
          reportDate: " 2024-10 ",
        },
      ], //滚动
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.isScrollTable = this.$refs.scrollTableBox.offsetHeight < this.$refs.scrollTable.offsetHeight;
      this.dynamicHeight = this.$refs.scrollTable.offsetHeight - this.$refs.scrollTableBox.offsetHeight + 20;
    });
  },

  props: {},
  watch: {},

  components: {},
  methods: {
    expertFun(item) {
      // this.$emit("expertFun", item);
    },
  },
  beforeDestroy() {

  },
};
</script>

<style lang="scss">
.table-three {
  padding: 10px 33px 10px 40px;
  width: 100%;
  margin: 0 auto;
  height: 100%;

  .table_main {
    width: 100%;
    height: calc(100% - 0px);
    overflow: hidden;
    z-index: 0;

    .table_col {
      position: relative;
      width: 100%;
      height: 60px;
      position: relative;
      padding: 0 2px 0 24px;
      font-size: 14px;
      line-height: 52px;

      .last-child {
        height: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 16px;
        font-weight: bold;
        font-weight: 500;
        color: #e6f7ff;
        font-style: normal;
        flex: 1;
      }

      .item-bottom {
        height: 60px;

        display: flex;
        font-family: "pingFangMedium";
        font-weight: 300;
        font-size: 14px;
        color: #e6f7ff;
        font-style: normal;
        justify-content: space-between;
      }
    }

    .bottom-border {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 485px;
      display: inline-block;
    }

    @keyframes scoll-Table2 {
      from {
        transform: translate(0, 0px);
      }

      to {
        transform: translate(0, calc(var(--dynamic-height)));
      }
    }

    .scoll-Table2 {
      animation: scoll-Table2 var(--dynamic-time) linear infinite;
      transition: all 1s ease-out;
      //  animation-timing-function: linear;
      // animation-fill-mode: forwards;
      /* 在动画结束后保持最后一个关键帧的状态 */
    }

    /* 鼠标进入 */
    .scoll-Table2:hover {
      animation-play-state: paused;
    }

    /* 鼠标离开 */
    .scoll-Table2:not(:hover) {
      animation-play-state: running;
    }
  }
}
</style>
