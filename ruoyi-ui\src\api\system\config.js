import request from "@/utils/request";

// 查询参数列表
export function listConfig(query) {
  return request({
    url: "/system/config/list",
    method: "get",
    params: query,
  });
}

// 查询参数详细
export function getConfig(configId) {
  return request({
    url: "/system/config/" + configId,
    method: "get",
  });
}

// 根据参数键名查询参数值
export function getConfigKey(configKey) {
  return request({
    url: "/system/config/configKey/" + configKey,
    method: "get",
  });
}

// 新增参数配置
export function addConfig(data) {
  return request({
    url: "/system/config",
    method: "post",
    data: data,
  });
}

// 修改参数配置
export function updateConfig(data) {
  return request({
    url: "/system/config/edit",
    method: "post",
    data: data,
  });
}

// 删除参数配置
export function delConfig(configId) {
  return request({
    url: "/system/config/remove",
    method: "post",
    data: configId,
  });
}

// 刷新参数缓存
export function refreshCache(params) {
  return request({
    url: "/system/config/refreshCache",
    method: "get",
    params: params,
  });
}
/* 获取系统配置列表 */
export function SystemConfig(params) {
  return request({
    url: "/system/config/xy/list",
    method: "get",
    params: params,
  });
}
/* 获取手机验证码 */
export function phoneCode(params) {
  return request({
    url: "/login/sms/code",
    method: "post",
    data: params,
  });
}
/* 验证码是否显示 */
export const codeShow = (params) => {
  return request({
    url: `/system/config/configKey/${params}`,
    method: "get",
  });
};
