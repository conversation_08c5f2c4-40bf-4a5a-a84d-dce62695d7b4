<template>
  <div>
    <!-- <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="70px">
      <el-form-item style="margin-left: 25px;" label="文章名称" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入文章名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="发布时间" prop="lastArticlePublishTime">
        <el-date-picker clearable v-model="queryParams.lastArticlePublishTime" type="daterange" value-format="yyyy-MM-dd"
          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px;">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="采集时间" prop="lastArticleCollectionTime">
        <el-date-picker clearable v-model="queryParams.lastArticleCollectionTime" type="daterange"
          value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
          style="width: 240px;">
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form> -->

    <el-table v-loading="loading" :data="monitorList">
      <el-table-column width="80" label="序号" align="center">
        <template v-slot="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="推荐用户昵称" prop="userName" min-width="100" show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="推荐用户名称" prop="nickName" min-width="100" show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="用户所属部门" prop="deptName" min-width="100" show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="推荐时间" align="center" prop="createTime" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>
  
<script>
import API from '@/api/ScienceApi/index.js'

export default {
  data() {
    return {
      loading: true,
      total: 0,
      monitorList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        wechatBiz: null,
        wechatName: null,
        intervalDays: null,
        publishFrequency: null,
        lastArticlePublishTime: null,
        lastArticleCollectionTime: null,
        lastArticleTitle: null,
        statisticalTime: null
      },
    };
  },
  props: {
    id: {
      type: Number,
      default: null
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询微信公众号采集监控列表 */
    getList() {
      this.loading = true;
      let queryParams = JSON.parse(JSON.stringify(this.queryParams))
      queryParams.articleId = this.id
      console.log(queryParams);
      if (queryParams.lastArticlePublishTime && queryParams.lastArticlePublishTime.length) {
        [queryParams.publishTimeStart, queryParams.publishTimeEnd] = queryParams.lastArticlePublishTime
        delete queryParams.lastArticlePublishTime
      }
      if (queryParams.lastArticleCollectionTime && queryParams.lastArticleCollectionTime.length) {
        [queryParams.collectionTimeStart, queryParams.collectionTimeEnd] = queryParams.lastArticleCollectionTime
        delete queryParams.lastArticleCollectionTime
      }
      API.recommendQueryUser(queryParams).then(response => {
        this.monitorList = response.data;
        this.total = response.total ? response.total : 0;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
  }
};
</script>
  