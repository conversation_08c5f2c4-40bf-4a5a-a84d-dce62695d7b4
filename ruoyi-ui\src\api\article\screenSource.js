import request from '@/utils/request'

// 查询大屏  信息源文章归类柱状图关系列表
export function listSourceArticle(query) {
  return request({
    url: '/screen/sourceArticle/list',
    method: 'get',
    params: query
  })
}

// 查询大屏  信息源文章归类柱状图关系详细
export function getSourceArticle(id) {
  return request({
    url: '/screen/sourceArticle/' + id,
    method: 'get'
  })
}

// 新增大屏  信息源文章归类柱状图关系
export function addSourceArticle(data) {
  return request({
    url: '/screen/sourceArticle',
    method: 'post',
    data: data
  })
}

// 修改大屏  信息源文章归类柱状图关系
export function updateSourceArticle(data) {
  return request({
    url: '/screen/sourceArticle',
    method: 'put',
    data: data
  })
}

export function updateSourceArticleById(data) {
  return request({
    url: '/screen/sourceArticle/updateById',
    method: 'post',
    data: data
  })
}

// 删除大屏  信息源文章归类柱状图关系
export function delSourceArticle(id) {
  return request({
    url: '/screen/sourceArticle/' + id,
    method: 'delete'
  })
}

export function getSourceName() {
  return request({
    url: `/large/source/getSourceNameList`,
    method: "get",
  });
}


