<template>
  <div class="default_Main" id="full_screen">
    <span class="timeStrle">{{ timeStr }}</span>
    <div class="title_Style">
      <div class="title">开源科技情报地平线数据分析系统</div>
    </div>
    <div class="mainCokpit">
      <div class="first-child">
        <div class="backround">
          <div class="moduleTitle_parent">
            <i class="icon-shengyin"></i>
            <p class="moduleTitle">企业受打压情况</p>
          </div>
          <div id="trend" class="trend_Box"></div>
        </div>
        <div class="backround">
          <div style="height: 300px;width:100%">
            <div class="moduleTitle_parent">
              <i class="icon-shengyin"></i>
              <p class="moduleTitle">各国打压企业比重</p>
            </div>
            <div id="industry" class="industry_trend" style="width:600px;height:200px"></div>
          </div>
        </div>
      </div>
      <div class="nth-child">
        <div class="topNumber">
          <div class="icon_parent">
            <div class="icon_Style">
              <img src="../../assets/images/rotate.svg" class="rotate-center" />
              <img src="../../assets/images/building.svg" class="building" />
            </div>
            <div class="icon_Style">
              <img src="../../assets/images/rotate.svg" class="rotate-center" />
              <img src="../../assets/images/building.svg" class="building" />
            </div>
            <div class="icon_Style">
              <img src="../../assets/images/rotate.svg" class="rotate-center" />
              <img src="../../assets/images/building.svg" class="building" />
            </div>
          </div>
        </div>
        <div class="info">
          <div class="info_item" style="margin-left:-20px">
            <p class="info_item_title">受打压企业</p>
            <p class="info_item_Number">1234</p>
          </div>
          <div class="info_item">
            <p class="info_item_title">卡脖子领域</p>
            <p class="info_item_Number">1234</p>
          </div>
          <div class="info_item">
            <p class="info_item_title">技术创新</p>
            <p class="info_item_Number">1234</p>
          </div>
        </div>
        <div class="cpitBack">
          <img class="top" src="../../assets/images/u2.svg" />
          <img class="bottom" src="../../assets/images/cpitBack.png" alt />
        </div>
      </div>
      <div class="last-child">
        <div class="backround">
          <div class="moduleTitle_parent">
            <i class="icon-shengyin"></i>
            <p class="moduleTitle">领域对比态势</p>
          </div>
          <div id="total" class="total_Box"></div>
        </div>
        <div class="backround">
          <div class="moduleTitle_parent">
            <i class="icon-shengyin"></i>
            <p class="moduleTitle">行业态势</p>
          </div>
          <div id="area" class="total_Box"></div>
        </div>
      </div>
    </div>
    <div class="bottom_parent">
      <div class="first_child">
        <div class="moduleTitle_parent">
          <i class="icon-shengyin"></i>
          <p class="moduleTitle">受打压企业</p>
        </div>
        <div class="tableOne">
          <div class="header">
            <div>企业名称</div>
            <div>所属领域</div>
            <div>所在行业</div>
            <div>发布时间</div>
          </div>
          <div class="table_main">
            <div class="scoll-Table">
              <div
                class="table_col"
                v-for="(item, index) in Cooperation.hotListVoList"
                :key="index"
              >
                <div class="first-child">
                  <span class="spot" :style="{ backgroundColor: colorList[index] }"></span>
                  <span class="textOverflow">{{ item.cnTitle}}</span>
                </div>
                <div class="nth-child">所属领域</div>
                <div class="nth-child3">所在行业</div>
                <div class="last-child">{{item.publishTime}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="last_child">
        <div class="moduleTitle_parent">
          <i class="icon-shengyin"></i>
          <p class="moduleTitle">重点新闻</p>
        </div>
        <div class="table">
          <div class="header">
            <div>文章标题</div>
            <div>所属行业</div>
            <div>发布时间</div>
          </div>
          <div class="table_main">
            <div class="scoll-Table">
              <div
                class="table_col"
                v-for="(item, index) in Cooperation.hotListVoList
"
                :key="index"
              >
                <div class="first-child">
                  <span class="spot" :style="{ backgroundColor: colorList[index] }"></span>
                  <span class="textOverflow" style="width:550px">{{ item.cnTitle}}</span>
                </div>
                <div class="nth-child">
                  <span class="textOverflow" style="width:550px">{{ item.cnTitle}}</span>
                </div>
                <div class="last-child">{{item.publishTime}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import img from '@/assets/images/title.png'
import * as echarts from 'echarts'
import api from '@/api/infoEscalation/index'
export default {
  data() {
    return {
      url: img,
      timeStr: null,
      timer: null,
      WeChatNum: '',
      WebNum: '',
      totalNum: '',
      toalScoll: [],
      WxScoll: [],
      WyScoll: [],
      scollList: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      colorList: [],
      myChart: null,
      myChart1: null,
      myChart2: null,
      myChart3: null,
      myChart4: null,
      Cooperation: {
        reportCount: [{}],
        hotListVoList: []
      } /* 产融数据 */
    }
  },
  mounted() {
    this.renderTrend()
  },
  watch: {
    totalNum: function (newVal, oldVal) {
      // this.totalNum = this.totalNum.split('')
    }
  },
  created() {
    for (let i = 0; i < 10; i++) {
      this.Cooperation.hotListVoList.push({ cnTitle: '测试文章展示' })
    }

    this.getCooperation()
    this.timer = setInterval(() => {
      this.timeHandler()
      this.totalNum = Number(this.totalNum) + 1
      this.WeChatNum = Number(this.WeChatNum) + 1
      this.WebNum = Number(this.WebNum) + 1
      this.toalScoll = String(this.totalNum.toLocaleString()).split('')
      this.WxScoll = String(this.WeChatNum.toLocaleString()).split('')
      this.WyScoll = String(this.WebNum.toLocaleString()).split('')
    }, 2000)
    this.toalScoll = this.totalNum.split('')
    this.WxScoll = this.WeChatNum.split('')
    this.WyScoll = this.WebNum.split('')
    this.colorCreated()
  },
  computed: {},
  methods: {
    /* 随机颜色生成 */
    colorCreated() {
      let color = '#',
        lettes = '0123456789ABCDEF'
      for (let i = 0; i < 10; i++) {
        for (let i = 0; i < 6; i++) {
          color += lettes[Math.floor(Math.random() * 16)]
        }
        this.colorList.push(color)
        color = '#'
      }
    },
    /* 处理时间 */
    timeHandler() {
      let time = new Date()
      let day = ['日', '一', '二', '三', '四', '五', '六']
      this.timeStr = time.getFullYear() + '年' + (time.getMonth() + 1) + '月' + time.getDate() + '日' + '  ' + '星期' + day[time.getDay()] + '  ' + time.getHours() + ':' + time.getMinutes() + ':' + time.getSeconds()
    },
    /* 企业受打压情况 */
    renderTrend() {
      var chartDom = document.getElementById('trend')
      this.myChart = echarts.init(chartDom)
      let xAixs = [],
        data = []
      // this.Cooperation.weekMonitoring.forEach(item => {
      //   xAixs.push(item.toDay)
      //   data.push(item.count)
      // })
      data = data.concat([542, 343, 322, 654, 364, 768, 234, 234])
      xAixs = xAixs.concat(['10/17', '10/18', '10/19', '10/20', '10/21', '20/22', '10/23', '20/24'])
      var option
      let option1 = {
        tooltip: {
          trigger: 'axis'
        },
        // legend: {
        //   icon: 'rect',
        //   show: false,
        //   itemWidth: 14,
        //   itemHeight: 5,
        //   itemGap: 13,
        //   data: [],
        //   right: '4%',
        //   textStyle: {
        //     fontSize: 12,
        //     color: '#F1F1F3'
        //   }
        // },
        grid: {
          top: '4%',
          left: '1%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLine: {
              show: false,
              lineStyle: {
                color: '#fff'
              }
            },
            data: xAixs
          },
          {
            axisPointer: {
              show: false
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#57617B'
              }
            },
            axisTick: {
              show: false
            },

            position: 'bottom',
            offset: 20
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '',
            axisTick: {
              show: false
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#fff'
              }
            },

            axisLabel: {
              margin: 30,
              fontSize: 14
            },
            splitLine: {
              lineStyle: {
                color: '#57617B',
                type: 'dashed'
              }
            }
          }
        ],
        series: [
          {
            name: '科情',
            type: 'bar',
            // smooth: true,
            // showSymbol: false,
            barWidth: '30%',
            /* 柱状图label */
            label: {
              show: true,
              position: 'insideTop',
              distance: 15,
              align: 'center',
              verticalAlign: 'middle',
              rotate: 0,
              formatter: '{c}',
              fontSize: 13
            },
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgb(52 98 191)'
                },
                {
                  offset: 1,
                  color: 'rgb(84 199 234)'
                }
              ])
              // shadowColor: 'rgb(84 199 234)',
              // shadowBlur: 10
            },

            data: data
          }
        ]
      }
      option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          top: '4%',
          left: '1%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAixs,
          boundaryGap: true,
          axisLabel: { rotate: 40 },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#fff'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false,
            lineStyle: {
              color: '#fff'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#57617B',
              type: 'dashed'
            }
          }
        },
        series: [
          {
            data: data,
            type: 'bar',
            barWidth: '40%',
            /* 柱状图label */
            label: {
              show: true,

              position: 'insideTop',
              distance: 15,
              align: 'center',
              verticalAlign: 'middle',
              formatter: '{c}',
              fontSize: 12,
              fontWeight: 'bold'
            },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 1,
                    color: 'rgba(52,98,191,1)'
                  },
                  {
                    offset: 0,
                    color: 'rgba(84,199,234,1)'
                  }
                ]),
                barBorderRadius: 2,
                shadowColor: 'rgb(84 199 234)',
                shadowBlur: 5
              }
            }
          }
        ]
      }
      option && this.myChart.setOption(option)
      this.renderTotal()
    },
    /* 企业生产总值趋势 */
    renderTotal() {
      let total = document.getElementById('total')
      this.myChart1 = echarts.init(total)
      let xAixs = [],
        data = [421, 352, 223, 664, 456, 667],
        data1 = [321, 452, 423, 564, 756, 867],
        data2 = [121, 212, 513, 204, 324, 765]
      var Line = ['半导体领域', '互联网', '机器人', '蜂花']

      data1 = [321, 452, 423, 564, 756, 867]
      let options = {
        tooltip: {
          show: true,
          trigger: 'axis'
        },
        grid: {
          top: 10,
          left: '11%',
          right: '8%',
          bottom: '12%'
        },
        legend: {
          type: 'scroll',
          data: Line,
          itemWidth: 18,
          itemHeight: 12,
          textStyle: {
            color: '#00ffff',
            fontSize: 14
          }
        },
        xAxis: {
          type: 'category',
          data: ['中国', '美国', '日本', '德国', '俄罗斯', '法国'],
          axisLabel: {
            inside: false,
            textStyle: {
              color: '#fff', // x轴颜色
              fontWeight: 'normal',
              fontSize: '14',
              lineHeight: 22
            }
          }
        },
        yAxis: {
          type: 'value',
          nameTextStyle: {
            normal: {
              color: '#fff'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#b8b9bbba',
              type: 'dashed',
              dashOffset: 4
            }
          },
          axisLabel: {
            inside: false,
            textStyle: {
              color: '#fff', // x轴颜色
              fontWeight: 'normal',
              fontSize: '14',
              lineHeight: 22
            }
          }
        },
        series: [
          {
            name: '半导体领域',
            data: data,
            type: 'line',
            label: {
              show: true,
              position: 'top',
              color: '#fff'
            },
            itemStyle: {
              color: '#12c2e9',
              borderRadius: 12
            }
          },
          {
            name: '互联网',
            data: data1,
            type: 'line',
            label: {
              show: true,
              position: 'top',
              color: '#fff'
            },
            itemStyle: {
              color: '#f7797d',
              borderRadius: 12
            }
          },
          {
            name: '机器人',
            data: data2,
            type: 'line',
            label: {
              show: true,
              position: 'top',
              color: '#fff'
            },
            itemStyle: {
              color: '#c471ed',
              borderRadius: 12
            }
          }
        ]
      }
      options && this.myChart1.setOption(options)
      this.renderArea()
    },
    /* 领域统计 */
    renderArea() {
      let total = document.getElementById('area')
      this.myChart2 = echarts.init(total)
      let obj = {
        小巨人企业: {
          华为: 1.77,
          小米: 1.44,
          联想: 1.12,
          蜂花: 1.05,
          协和: 0.81,
          其他: 0.39
        },
        龙头企业: {
          华为: 1.77,
          小米: 1.44,
          联想: 1.12,
          蜂花: 1.05,
          协和: 0.81,
          其他: 0.39
        },
        专辑特新: {
          华为: 1.77,
          小米: 1.44,
          联想: 1.12,
          蜂花: 1.05,
          协和: 0.81,
          其他: 0.39
        }
      }
      var option = {
        title: {
          text: '',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          transitionDuration: 1,
          borderColor: '#ffff',
          backgroundColor: 'rgba(255,255,255,0.6)',
          color: '#3f4041',
          decoration: 'none',
          trigger: 'item',
          formatter: function (params) {
            let html = '<div style="height:360px;width:400px;border-radius:5px;background:rgba(5, 9, 59, 1);">' + '<div style="height:110px;width:100%;background:rgba(5, 9, 59, 0.6);">' + '<div style="padding-left:18px;background:rgba(15, 89, 79, 1);color:#fff;font-size:18px;font-weight:bold;line-height:40px">' + '<span>' + params.name + '</span>' + '<span style="float:right;margin-right:18px">' + params.value + '%' + '</span>' + '</div>'

            let rightHtml = '</div>' + '<div id="tooltipBarId" style="height:200px;width:100%;border-radius:0 0 5px 0;background:rgba(5, 9, 59, 0.6);">' + '</div>' + '</div>'
            Object.keys(obj[params.name]).map(item => {
              html += '<div style="padding-left:18px;background:rgba(5, 9, 59, 0.6);color:#fff;font-size:18px;font-weight:bold;line-height:40px">' + '<span>' + item + '</span>' + '<span style="float:right;margin-right:18px">' + obj[params.name][item] + '%' + '</span>' + '</div>'
            })
            html += rightHtml
            return html
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: '65%',
            center: ['50%', '50%'],
            selectedMode: 'single',

            data: [
              {
                value: 30,
                name: '小巨人企业',
                label: {
                  position: 'inner',
                  fontSize: 10,
                  color: '#fff'
                }
              },
              {
                value: 35,
                name: '龙头企业',
                label: {
                  position: 'inner',
                  fontSize: 10,
                  color: '#fff'
                }
              },
              {
                value: 35,
                name: '专辑特新',
                label: {
                  position: 'inner',
                  fontSize: 10,
                  color: '#fff'
                }
              }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      option && this.myChart2.setOption(option)
      this.renderIndustry()
    },
    /* 年度竞争态势 */
    renderIndustry() {
      let total = document.getElementById('industry')
      this.myChart3 = echarts.init(total)

      let xAixs = ['2017', '2018', '2019', '2020', '2021', '2022', '2023', '2024']
      /* 处理数据 */
      // Object.keys(industry).forEach(item => {
      //   xAixs.push(item)
      //   industry[item].forEach(key => {
      //     name.push(key.industryName)
      //     if (!data[key.industryName]) {
      //       data[key.industryName] = []
      //     }
      //     data[key.industryName].push(key.count)
      //   })
      // })

      let options = {
        tooltip: {
          show: true,
          trigger: 'axis'
        },
        grid: {
          top: 10,
          left: '5%',
          right: '5%',
          bottom: '10%'
        },
        xAxis: {
          name: '',
          type: 'category',
          data: xAixs,
          axisLine: {
            lineStyle: {
              color: 'white'
            }
          },
          axisLabel: {
            textStyle: {
              fontFamily: 'Microsoft YaHei'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#b8b9bbba',
              type: 'dashed',
              dashOffset: 4
            }
          }
        },
        series: [
          {
            name: '半导体领域',
            data: [123, 543, 543, 453, 765, 321, 454],
            type: 'bar',
            barWidth: '8%',
            label: {
              show: false,
              position: 'top',
              color: '#fff'
            },
            itemStyle: {
              color: '#f7797d',
              borderRadius: 0
            }
          },
          {
            name: '互联网',
            data: [123, 543, 543, 453, 765, 321, 454],
            type: 'bar',
            barWidth: '8%',
            label: {
              show: false,
              position: 'top',
              color: '#fff'
            },
            itemStyle: {
              color: '#C6FFDD',
              borderRadius: 0
            }
          },
          {
            name: '机器人领域',
            data: [123, 543, 543, 453, 765, 321, 454],
            type: 'bar',
            barWidth: '8%',
            label: {
              show: false,
              position: 'top',
              color: '#fff'
            },
            itemStyle: {
              color: '#FBD786',
              borderRadius: 0
            }
          },
          {
            name: '生物医学',
            data: [123, 543, 543, 453, 765, 321, 454],
            type: 'bar',
            barWidth: '8%',
            label: {
              show: false,
              position: 'top',
              color: '#fff'
            },
            itemStyle: {
              color: '#f7797d',
              borderRadius: 0
            }
          },
          {
            name: '电池电源领域',
            data: [123, 543, 543, 453, 765, 321, 454],
            type: 'bar',
            barWidth: '8%',
            label: {
              show: false,
              position: 'top',
              color: '#fff'
            },
            itemStyle: {
              color: '#360033',
              borderRadius: 0
            }
          },
          {
            name: '工业领域',
            data: [123, 543, 543, 453, 765, 321, 454],
            type: 'bar',
            barWidth: '8%',
            label: {
              show: false,
              position: 'top',
              color: '#fff'
            },
            itemStyle: {
              color: '#0b8793',
              borderRadius: 0
            }
          }
        ]
      }
      options && this.myChart3.setOption(options)
      /* 动画轮播 */
      var count = 0
      var timeTicket = null
      var dataLength = options.series[0].data.length
      timeTicket && clearInterval(timeTicket)
      timeTicket = setInterval(() => {
        this.myChart3.dispatchAction({
          type: 'downplay',
          seriesIndex: 0
        })
        this.myChart3.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: count % dataLength
        })
        this.myChart3.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: count % dataLength
        })
        count++
      }, 2000)
      // this.renderProportion()
    },
    /* 各国打压企业比重 */
    renderProportion() {
      let getxb1 = 600, //男生人数
        getxb2 = 400, //女生人数
        getxb3 = 450,
        getxb4 = 300,
        getxb5 = 550
      var rich = {
        name: {
          color: 'red',
          fontSize: 130,
          padding: [6, 100],
          align: 'left'
        },
        percent: {
          color: '#656565',
          align: 'center',
          fontSize: 103,
          padding: [5, 10]
        },
        hr: {
          borderColor: '#C8C8C8',
          width: '100%',
          borderWidth: 0.5,
          height: 0
        }
      }

      let Proportion = document.getElementById('trend_world')
      this.myChart4 = echarts.init(Proportion)
      var option = {
        title: {
          show: false,
          left: '45%',
          bottom: '25%',
          // text: '6 : 4',
          textAlign: 'center',
          textStyle: {
            fontWeight: '600',
            fontSize: '28',
            color: '#000'
          }
        },
        tooltip: {
          show: false
        },
        // legend: {
        //     orient: 'vertical',
        //     x: 'left',
        //     data: xbzb,
        // },
        series: [
          {
            name: '性别分布',
            type: 'pie',
            radius: ['65%', '115%'],
            startAngle: 180,
            center: ['45%', '65%'],
            // roseType: 'radius',
            labelLine: {
              show: false
              // normal: {
              //     length: 20,
              //     length2: 0,
              //     lineStyle: {
              //         color: '#C8C8C8'
              //     }
              // }
            },
            label: {
              normal: {
                show: true,
                position: 'inside',
                textStyle: {
                  fontSize: '12px',
                  color: '#fff'
                  // padding: [-20, 0, 0, 0]
                }
                // formatter: ['{c}'].join('\n'),
                // formatter: ['{c}'] + ' : '['{}'],
                // formatter: function (params) {
                //   var proportion = ''
                //   for (var i = 0; i < option.series[0].data.length - 1; i++) {
                //     // console.log(option.series[0].data);
                //     if (i === 0) {
                //       proportion = proportion + option.series[0].data[i].value.toString()
                //     } else {
                //       proportion = proportion + ' : ' + option.series[0].data[i].value.toString()
                //     }
                //   }
                //   return proportion
                // }
              },
              position: 'insideTop',
              show: true
            },

            data: [
              {
                value: getxb1,
                name: '美国',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: '#4C8DFA'
                        },
                        {
                          offset: 1,
                          color: '#5CCFFF'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                value: getxb2,
                name: '日本',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: '#FFD18B'
                        },
                        {
                          offset: 1,
                          color: '#FDAD59'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                value: getxb3,
                name: '法国',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: '#FFD18B'
                        },
                        {
                          offset: 1,
                          color: '#FDAD59'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                value: getxb4,
                name: '德国',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: '#FFD18B'
                        },
                        {
                          offset: 1,
                          color: '#FDAD59'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                value: getxb5,
                name: '其他',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: '#FFD18B'
                        },
                        {
                          offset: 1,
                          color: '#FDAD59'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                value: getxb1 + getxb2 + getxb3 + getxb4 + getxb5,
                name: '',
                label: {
                  show: false
                },
                labelLine: {
                  show: false
                },
                itemStyle: {
                  normal: {
                    color: 'transparent',
                    borderWidth: 0,
                    shadowBlur: 0
                  }
                }
              }
            ]
          }
          // {
          //   type: 'pie',
          //   radius: ['55%', '130%'],
          //   startAngle: 180,
          //   hoverAnimation: false,
          //   center: ['45%', '83%'],
          //   roseType: 'radius',
          //   labelLine: {
          //     normal: {
          //       show: false
          //     }
          //   },
          //   data: [
          //     {
          //       value: getxb1,
          //       itemStyle: {
          //         normal: {
          //           color: new echarts.graphic.LinearGradient(
          //             0,
          //             0,
          //             0,
          //             1,
          //             [
          //               {
          //                 offset: 0,
          //                 color: 'rgba(76,141,250,.3)'
          //               },
          //               {
          //                 offset: 1,
          //                 color: 'rgba(92,207,255,.3)'
          //               }
          //             ],
          //             false
          //           )
          //         }
          //       }
          //     },
          //     {
          //       value: getxb2,
          //       itemStyle: {
          //         normal: {
          //           color: new echarts.graphic.LinearGradient(
          //             0,
          //             0,
          //             0,
          //             1,
          //             [
          //               {
          //                 offset: 0,
          //                 color: 'rgba(255,209,139,.3)'
          //               },
          //               {
          //                 offset: 1,
          //                 color: 'rgba(253,173,89,.3)'
          //               }
          //             ],
          //             false
          //           )
          //         }
          //       }
          //     },
          //     {
          //       value: getxb1 + getxb2,
          //       name: '',
          //       label: {
          //         show: false
          //       },
          //       labelLine: {
          //         show: false
          //       },
          //       itemStyle: {
          //         normal: {
          //           color: 'transparent',
          //           borderWidth: 0,
          //           shadowBlur: 0,
          //           borderColor: 'transparent',
          //           shadowColor: 'transparent'
          //         }
          //       }
          //     }
          //   ],
          //   z: -1
          // }
        ]
      }
      this.myChart4 && this.myChart4.setOption(option)
    },
    /* 获取产融合作数据 */
    getCooperation() {
      api.productCooperation().then(res => {
        if (res.code === 120) {
          this.Cooperation = res.data
          /* 滚动数据 */
          this.WeChatNum = res.data.platformCount.wxTotal
          this.WebNum = res.data.platformCount.wyTotal
          this.totalNum = res.data.platformCount.totalCount
          this.renderTrend()
          window.onresize = params => {
            this.myChart.resize()
            this.myChart1.resize()
            this.myChart2.resize()
            this.myChart3.resize()
          }
        }
      })
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
  }
}
</script>

<style lang="scss" scoped>
.info {
  width: 100%;
  color: #fff;
  display: flex;
  justify-content: center;
  gap: 100px;
  position: relative;
  top: -190px;
  background-color: rgba($color: #00000057, $alpha: 0.4);
  .info_item {
    width: 200px;
    height: 200px;
    text-align: center;
    margin-bottom: 10px;
    .info_item_title {
      margin-top: 80px;
      font-size: 18px;
      color: #fff;
      margin-bottom: 10px;
    }
    .info_item_Number {
      font-size: 22px;
      font-weight: 600;
      color: #fff;
      margin-bottom: 10px;
    }
  }
}
/* 斑点 */
.spot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 15px;
  margin-right: 10px;
  position: relative;
  top: 0px;
  // background-color: #5b10be;
}

.backround {
  background-color: #0808085e;
  margin-bottom: 10px;
}

/* 滚动表格 */
.table {
  width: 95%;
  margin: 0 auto;
  height: calc(400px - 57px);

  .header {
    width: 100%;
    background-color: #1162db;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    display: flex;
    color: #fff;
    z-index: 3;

    :first-child {
      width: 60%;
    }
    :nth-child(2) {
      width: 20%;
    }
    :last-child {
      width: 20%;
    }
  }

  .table_main {
    width: 100%;
    height: calc(400px - 117px);
    overflow: hidden;
    z-index: 0;

    .table_col {
      width: 100%;
      height: 40px;
      line-height: 40px;
      border-bottom: solid 1px #172652;
      background-color: #121624;
      display: flex;
      color: #ffff;
      padding-left: 10px;
      font-size: 14px;

      .first-child {
        width: 60%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }
      .nth-child {
        width: 20%;
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }
      .last-child {
        width: 20%;
      }
    }
  }
}
.tableOne {
  width: 95%;
  margin: 0 auto;
  height: calc(400px - 57px);

  .header {
    width: 100%;
    background-color: #1162db;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    display: flex;
    color: #fff;
    z-index: 3;

    :first-child {
      width: 40%;
    }
    :nth-child(2) {
      width: 20%;
    }
    :nth-child(3) {
      width: 20%;
    }
    :last-child {
      width: 20%;
    }
  }

  .table_main {
    width: 100%;
    height: calc(400px - 117px);
    overflow: hidden;
    z-index: 0;

    .table_col {
      width: 100%;
      height: 40px;
      line-height: 40px;
      border-bottom: solid 1px #172652;
      background-color: #121624;
      display: flex;
      color: #ffff;
      padding-left: 10px;
      font-size: 14px;

      .first-child {
        width: 40%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }
      .nth-child {
        width: 20%;
      }
      .nth-child3 {
        width: 20%;
      }
      .last-child {
        width: 20%;
      }
    }
  }
}
.bottom_parent {
  width: 100%;
  height: 400px;
  display: flex;
  padding: 0 15px;
  margin-top: -20px;
  gap: 20px;

  .last_child {
    width: 45%;
    height: 100%;
    background-color: #0808085e;
  }

  .first_child {
    width: 55%;
    height: 100%;
    background-color: #0808085e;

    .industry_trend {
      width: 100%;
      height: calc(400px - 57px);
    }
  }
}

/* ----------------------------------------------
 * Generated by Animista on 2023-9-12 10:42:31
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation rotate-center  旋转
 * ----------------------------------------
 */
@-webkit-keyframes rotate-center {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes rotate-center {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.rotate-center {
  -webkit-animation: rotate-center 1.5s infinite;
  animation: rotate-center 1.5s infinite;
  animation-delay: 0ms;
  animation-timing-function: linear;
  width: 150px;
  height: 150px;
  margin-left: 40px;
}

/**
 * ----------------------------------------
 * animation rotate-center  滚动
 * ----------------------------------------
 */
@keyframes scoll-Table {
  from {
    transform: translate(0, 0px);
  }

  to {
    transform: translate(0, -135px);
  }
}

.scoll-Table {
  animation: scoll-Table 10s infinite;
  transition: all 1s ease-out;
  //  animation-timing-function: linear;
  // animation-fill-mode: forwards;
  /* 在动画结束后保持最后一个关键帧的状态 */
}

/* 鼠标进入 */
.scoll-Table:hover {
  animation-play-state: paused;
}

/* 鼠标离开 */
.scoll-Table:not(:hover) {
  animation-play-state: running;
}

.default_Main {
  width: 100%;
  min-height: 100vh;
  background-image: url('../../assets/images/background.jpg');
  .icon_parent_text {
    width: 97%;
    height: 50px;
    display: flex;
    justify-content: space-between;
    text-align: center;
    .icon_Style {
      height: 50px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    span {
      margin-top: 10px;
      font-size: 38px;
      color: #fff;
      width: 110px;
      text-align: center;
      font-weight: 500;
      display: block;
    }

    p {
      width: 110px;
      color: #fff;
      text-align: center;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .icon_parent {
    width: 100%;

    height: auto;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    text-align: center;

    .icon_Style {
      span {
        margin-left: -10px;
        color: #fff;
        text-align: center;
        font-size: 65px;
        font-weight: 700;
      }

      :nth-child(2) {
        text-align: center;
        position: relative;
        right: 100px;
        bottom: 45px;
        width: 60px;
        height: 60px;
      }
    }
  }

  .moduleTitle_parent {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    padding-left: 10px;

    .moduleTitle {
      color: #ffff;
      font-size: 17px;
      font-weight: 550;
    }
  }

  .title_Style {
    width: 100%;
    height: 105px;
    background: url('../../assets/images/title.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;

    .title {
      color: aliceblue;
      text-align: center;
      font-size: 32px;
      width: 100%;
      line-height: 105px;
    }
  }

  .timeStrle {
    color: aliceblue;
    font-size: 18px;
    position: absolute;
    right: 2%;
    margin: 8px 10px 0 0;
  }

  .mainCokpit {
    display: flex;
    justify-content: space-around;
    height: 62vh;

    .first-child {
      // background-color: aquamarine;
      // border: solid 1px #003077;
      margin-top: -45px;
      padding: 15px;
      width: 27%;

      .trend_Box {
        width: 100%;
        height: 250px;
      }
    }

    .nth-child {
      // background-color: rgb(204, 0, 255);
      // border: solid 1px #003077;
      display: flex;
      // gap: 20px;
      max-height: 650px;
      padding: 10px 20px;
      flex-direction: column;
      justify-content: flex-start;
      width: 46%;

      .topNumber {
        margin-top: 30px;
        display: flex;
        gap: 15px;
        justify-content: center;

        .number {
          font-size: 45px;
          margin-top: -5px;
          display: block;
          width: 100%;
          overflow: hidden;
          // text-overflow: ellipsis;
          white-space: nowrap;
          // display: inline-block;
        }

        .total {
          width: 33%;
          height: 100px;
          background: url('../../assets/images/emissonBack.png') no-repeat;
          background-size: 100%100%;
          box-shadow: 0px 0px 11px 0px #62a8f1;
          color: #fff;
          padding-left: 10px;
        }

        .WeChat {
          width: 33%;
          height: 100px;
          background: url('../../assets/images/emissonBack.png') no-repeat;
          background-size: 100%100%;
          box-shadow: 0px 0px 11px 0px #62a8f1;
          color: #fff;
          padding-left: 10px;
        }

        .Web {
          width: 33%;
          height: 100px;
          background: url('../../assets/images/emissonBack.png') no-repeat;
          background-size: 100%100%;
          box-shadow: 0px 0px 11px 0px #62a8f1;
          color: #fff;
          padding-left: 10px;
        }
      }

      .cpitBack {
        width: 100%;
        height: 420px;

        .top {
          width: 100%;
          height: 100%;
          margin-top: -30%;
          z-index: 2;
        }

        .bottom {
          z-index: 0;
          position: absolute;
          top: 30%;
          left: 22%;
          width: 35%;
          margin: -2% 0 0 10%;
          height: 480px;
          transform: rotateX(35deg);
        }
      }
    }

    .last-child {
      // background-color: rgb(0, 255, 34);
      // border: solid 1px #003077;
      margin-top: -30px;
      width: 27%;

      .total_Box {
        width: 100%;
        height: 250px;
      }
    }
  }
}

.real-time-num {
  display: inline-block;
  width: 26px;
  height: 40px;
  font-size: 45px;
  margin-left: 3px;
  line-height: 40px;
  text-align: center;
  overflow: hidden;
}

.real-time-num > div {
  width: 26px;
  height: 40px;
  transition: all 1s ease-out;
}
.textOverflow {
  width: 350px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>