<template>
  <div>
    <div
      class="sankey-container"
      :style="{ width: width, height: containerHeight }"
    >
      <div
        ref="sankeyChart"
        class="sankey-chart"
        :style="{ height: dynamicHeight }"
      ></div>
    </div>

    <!-- 全屏桑葚图组件 -->
    <sankeyFullscreen
      :visible.sync="fullscreenVisible"
      title="美国提案影响分析"
      width="97%"
      :close-on-click-mask="false"
    />
  </div>
</template>

<script>
import * as echarts from "echarts";
import { zcfxSankey } from "@/api/bigScreen/sanhao.js";
import sankeyFullscreen from "./sankeyFullscreen.vue";

export default {
  name: "SankeyChart",
  components: {
    sankeyFullscreen,
  },
  props: {
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "100%",
    },
    dateRange: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      chart: null,
      loading: false,
      fullscreenVisible: false,
      sankeyData: {
        nodes: [],
        links: [],
      },
      // 原始数据，包含所有节点和连接
      originalData: {
        nodes: [],
        links: [],
      },
      // 记录哪些子标签节点已展开企业
      expandedSubLabels: new Set(),
      // ResizeObserver 实例
      resizeObserver: null,
      // 防抖定时器
      resizeTimer: null,
      // 基础高度
      baseHeight: 700,
      // 每个展开企业增加的高度
      heightPerExpansion: 50,
    };
  },
  computed: {
    // 动态计算桑葚图高度
    dynamicHeight() {
      // 计算当前显示的企业节点数量
      const enterpriseCount = this.sankeyData.nodes.filter(
        (node) => node.category === "企业"
      ).length;

      // 基础高度 + 企业数量 * 每个企业增加的高度
      const calculatedHeight =
        this.baseHeight + enterpriseCount * this.heightPerExpansion;
      return `${calculatedHeight}px`;
    },

    // 动态计算容器高度（用于滚动）
    containerHeight() {
      // 容器高度固定为基础高度，超出部分显示滚动条
      return `${this.baseHeight}px`;
    },
  },
  mounted() {
    this.initChart();
    this.fetchSankeyData();
  },

  watch: {
    // 监听宽度和高度变化
    width() {
      this.handleResize();
    },
    height() {
      this.handleResize();
    },
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    // 清理定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    }
    // 清理窗口大小变化监听
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    // 更新日期范围（由父组件调用）
    updateDateRange() {
      this.fetchSankeyData();
    },

    // 打开全屏（由父组件调用）
    openFullscreen() {
      this.fullscreenVisible = true;
    },

    // 获取桑葚图数据
    async fetchSankeyData() {
      try {
        this.loading = true;

        // 构建请求参数
        const params = {
          screenSn: "1",
        };

        // 如果有选择日期范围，添加日期参数
        if (this.dateRange && this.dateRange.length === 2) {
          params.startDate = this.dateRange[0];
          params.endDate = this.dateRange[1];
        }

        const response = await zcfxSankey(params);

        if (response && response.data) {
          console.log("桑葚图数据:", response.data);
          // 处理节点数据
          const processedNodes = this.processNodes(response.data.nodes || []);
          // 处理连接数据
          const processedLinks = this.processLinks(response.data.links || []);

          // 保存原始数据
          this.originalData = {
            nodes: processedNodes,
            links: processedLinks,
          };

          // 初始化时隐藏企业节点
          this.sankeyData = this.filterEnterpriseNodes(this.originalData);

          if (this.sankeyData.links.length === 0) {
            this.$message.warning("没有找到相关数据");
            this.sankeyData.nodes = [];
          }

          // 更新图表
          this.updateChart();
        }
      } catch (error) {
        console.error("获取桑葚图数据失败:", error);
      } finally {
        this.loading = false;
      }
    },

    // 处理节点数据
    processNodes(nodes) {
      const colors = ["#dd79ff", "#58d9f9", "#4992ff"];

      // category到层级的映射
      const categoryToDepth = {
        党派: 0,
        专家: 1,
        提案: 2,
        父标签: 3,
        子标签: 4,
        企业: 5,
      };

      return nodes.map((node, index) => {
        const depth =
          categoryToDepth[node.category] !== undefined
            ? categoryToDepth[node.category]
            : 0;

        return {
          id: node.id,
          name: node.name,
          category: node.category,
          depth: depth,
          itemStyle: {
            color: colors[index % colors.length],
          },
        };
      });
    },

    // 过滤企业节点，根据展开状态决定是否显示
    filterEnterpriseNodes(data) {
      // 如果没有展开任何子标签，则隐藏所有企业节点
      if (this.expandedSubLabels.size === 0) {
        const filteredNodes = data.nodes.filter(
          (node) => node.category !== "企业"
        );
        const filteredLinks = data.links.filter((link) => {
          const sourceExists = filteredNodes.find((n) => n.id === link.source);
          const targetExists = filteredNodes.find((n) => n.id === link.target);
          return sourceExists && targetExists;
        });

        return {
          nodes: filteredNodes,
          links: filteredLinks,
        };
      }

      // 如果有展开的子标签，则显示对应的企业节点
      const filteredNodes = data.nodes.filter((node) => {
        if (node.category === "企业") {
          // 查找连接到此企业的子标签节点
          const connectedToSubLabel = data.links.some((link) => {
            // 检查企业是否与已展开的子标签相连
            if (link.target === node.id) {
              // 企业是目标，检查源是否是已展开的子标签
              const sourceNode = data.nodes.find((n) => n.id === link.source);
              return (
                sourceNode &&
                sourceNode.category === "子标签" &&
                this.expandedSubLabels.has(sourceNode.id)
              );
            }
            if (link.source === node.id) {
              // 企业是源，检查目标是否是已展开的子标签
              const targetNode = data.nodes.find((n) => n.id === link.target);
              return (
                targetNode &&
                targetNode.category === "子标签" &&
                this.expandedSubLabels.has(targetNode.id)
              );
            }
            return false;
          });

          return connectedToSubLabel;
        }
        return true; // 非企业节点都显示
      });

      const filteredLinks = data.links.filter((link) => {
        const sourceExists = filteredNodes.find((n) => n.id === link.source);
        const targetExists = filteredNodes.find((n) => n.id === link.target);
        return sourceExists && targetExists;
      });

      return {
        nodes: filteredNodes,
        links: filteredLinks,
      };
    },

    // 处理连接数据
    processLinks(links) {
      return links.map((link) => {
        return {
          source: link.source,
          target: link.target,
          value: link.value || 1, // 如果没有value，默认为1
        };
      });
    },

    // 更新图表
    updateChart() {
      if (this.chart) {
        const option = this.getChartOption();
        // 使用 notMerge: false 来避免完全重新渲染，提高性能
        this.chart.setOption(option, true);
      }
    },

    // 获取图表配置
    getChartOption() {
      return {
        backgroundColor: "transparent",
        title: {
          text: "",
          textStyle: {
            color: "#ffffff",
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: "item",
          triggerOn: "mousemove",
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          borderColor: "#0ec2f4",
          borderWidth: 1,
          textStyle: {
            color: "#ffffff",
          },
          formatter: function (params) {
            if (params.dataType === "edge") {
              return `${params.data.source} → ${params.data.target}<br/>影响度: ${params.data.value}`;
            } else {
              const depthMap = {
                0: "第1级",
                1: "第2级",
                2: "第3级",
                3: "第4级",
                4: "第5级",
                5: "第6级",
              };
              const levelText = depthMap[params.data.depth] || "未知层级";
              return `${params.data.name}<br/>类别: ${
                params.data.category || "未知"
              }<br/>层级: ${levelText}`;
            }
          },
        },
        series: [
          {
            type: "sankey",
            layout: "none",
            emphasis: {
              focus: "adjacency",
            },
            data: this.sankeyData.nodes,
            links: this.sankeyData.links,
            // orient: 'vertical',
            // nodeAlign: 'justify',
            nodeGap: 10,
            nodeWidth: 40,
            layoutIterations: 0,
            left: "2%",
            right: "16%",
            top: "0.2%",
            bottom: "0.2%",
            label: {
              show: true,
              position: "right",
              color: "#ffffff",
              fontSize: 14,
              formatter: function (params) {
                return params.name.length > 12
                  ? params.name.substring(0, 12) + "..."
                  : params.name;
              },
            },
            lineStyle: {
              color: "source",
              // curveness: 0.2,
              // opacity: 0.7
            },
            // itemStyle: {
            //   borderWidth: 1,
            //   borderColor: '#0ec2f4'
            // }
          },
        ],
      };
    },

    initChart() {
      // 设置图表容器的尺寸 - 初始化时使用基础高度
      this.$refs.sankeyChart.style.width = "100%";
      this.$refs.sankeyChart.style.height = `${this.baseHeight}px`;

      this.chart = echarts.init(this.$refs.sankeyChart);

      // 初始化时设置空的图表配置
      const option = this.getChartOption();
      this.chart.setOption(option);

      // 添加节点点击事件
      this.chart.on("click", (params) => {
        if (params.dataType === "node" && params.data.category === "子标签") {
          this.toggleEnterpriseNodes(params.data.id);
        }
      });

      // 设置尺寸变化监听
      this.setupResizeListeners();
    },

    // 切换企业节点的显示/隐藏
    toggleEnterpriseNodes(subLabelId) {
      if (this.expandedSubLabels.has(subLabelId)) {
        // 如果已展开，则收起
        this.expandedSubLabels.delete(subLabelId);
      } else {
        // 如果未展开，则展开
        this.expandedSubLabels.add(subLabelId);
      }

      // 重新过滤数据
      const newSankeyData = this.filterEnterpriseNodes(this.originalData);

      // 计算新高度
      const enterpriseCount = newSankeyData.nodes.filter(
        (node) => node.category === "企业"
      ).length;
      const newHeight = `${
        this.baseHeight + enterpriseCount * this.heightPerExpansion
      }px`;

      // 同时更新数据、高度和图表，确保完全同步
      this.sankeyData = newSankeyData;
      if (this.$refs.sankeyChart) {
        this.$refs.sankeyChart.style.height = newHeight;
      }

      // 如果没有任何展开的子领域（所有企业都收起），回到顶部并恢复初始高度
      if (this.expandedSubLabels.size === 0) {
        // 先强制设置高度为基础高度
        if (this.$refs.sankeyChart) {
          this.$refs.sankeyChart.style.height = `${this.baseHeight}px`;
          // 强制浏览器重新计算布局
          this.$refs.sankeyChart.offsetHeight;
        }

        // 立即更新图表以确保高度生效
        if (this.chart) {
          this.chart.resize();
        }

        // 然后滚动到顶部
        const container = this.$refs.sankeyChart.parentElement;
        if (container) {
          // 强制触发重绘
          container.offsetHeight;
          container.scrollTop = 0;
          // 使用 scrollTo 确保滚动生效
          container.scrollTo({ top: 0, behavior: "instant" });
        }
      }

      // 立即更新图表
      this.updateChart();
      if (this.chart) {
        this.chart.resize();
      }
    },

    // 打开全屏
    openFullscreen() {
      this.fullscreenVisible = true;
    },

    // 设置尺寸变化监听
    setupResizeListeners() {
      // 创建 ResizeObserver 监听容器尺寸变化
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(() => {
          // 使用防抖处理，避免频繁触发
          clearTimeout(this.resizeTimer);
          this.resizeTimer = setTimeout(() => {
            this.handleResize();
          }, 100);
        });

        // 监听图表容器的尺寸变化
        this.resizeObserver.observe(this.$refs.sankeyChart);

        // 也监听父容器的尺寸变化
        const parentContainer = this.$refs.sankeyChart.parentElement;
        if (parentContainer) {
          this.resizeObserver.observe(parentContainer);
        }
      }

      // 监听窗口大小变化（作为备用方案）
      this.handleResize = this.handleResize.bind(this);
      window.addEventListener("resize", this.handleResize);
    },

    // 处理尺寸变化
    handleResize() {
      if (this.chart) {
        // 延迟执行 resize，确保 DOM 更新完成
        this.$nextTick(() => {
          this.chart.resize();
        });
      }
    },

    // 手动触发图表重新调整大小（供父组件调用）
    resizeChart() {
      this.handleResize();
    },

    // 更新图表高度（异步版本，保留给其他地方使用）
    updateChartHeight() {
      if (this.$refs.sankeyChart && this.chart) {
        const newHeight = this.dynamicHeight;
        const currentHeight = this.$refs.sankeyChart.style.height;

        // 只有当高度真正发生变化时才更新
        if (currentHeight !== newHeight) {
          this.$refs.sankeyChart.style.height = newHeight;

          // 使用 requestAnimationFrame 确保DOM更新后再resize
          requestAnimationFrame(() => {
            if (this.chart) {
              this.chart.resize();
            }
          });
        }
      }
    },

    // 同步更新图表高度（立即执行，无延迟）
    updateChartHeightSync() {
      if (this.$refs.sankeyChart && this.chart) {
        const newHeight = this.dynamicHeight;
        const currentHeight = this.$refs.sankeyChart.style.height;

        // 只有当高度真正发生变化时才更新
        if (currentHeight !== newHeight) {
          this.$refs.sankeyChart.style.height = newHeight;
          // 立即执行 resize，不使用异步
          this.chart.resize();
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.sankey-container {
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden;
  position: relative;
}

.sankey-chart {
  width: 100%;
  min-height: 700px; /* 设置最小高度，确保图表有足够空间渲染 */
  /* 完全移除过渡动画，实现瞬间变化 */
}

/* 自定义滚动条样式 */
.sankey-container::-webkit-scrollbar,
.sankey-fullscreen-container::-webkit-scrollbar {
  width: 8px;
}

.sankey-container::-webkit-scrollbar-track,
.sankey-fullscreen-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.sankey-container::-webkit-scrollbar-thumb,
.sankey-fullscreen-container::-webkit-scrollbar-thumb {
  background: rgba(14, 194, 244, 0.6);
  border-radius: 4px;
}

.sankey-container::-webkit-scrollbar-thumb:hover,
.sankey-fullscreen-container::-webkit-scrollbar-thumb:hover {
  background: rgba(14, 194, 244, 0.8);
}
</style>
