<template>
  <div v-loading="loading" class="artificialBar"
    style=" width: 100%; height: 100%; background: linear-gradient(to bottom, #00000027, #00000001); ">
    <div class="tag" v-for="(item, index) in keyList" :key="index">
      <div class="tag-left" :style="{ border: '1px solid ' + item.bgColor, color: item.bgColor }" @click="openList(item)">
        {{ item.name }}
      </div>
      <div class="tag-right">
        <div :style="{
          border: '1px solid ' + items.bgColor,
          color: items.bgColor,
          fontWeight: item.tab && item.tab.length && item.tab.includes(indexs + 1) ? '1000' : '0',
          fontSize: item.tab && item.tab.length && item.tab.includes(indexs + 1) ? '13px' : '12px',
          width: widthjisuan(item.children, item.tab, indexs),
        }" v-for="(items, indexs) in item.children" :key="indexs" @click="openList(items)">
          {{ items.name }}
        </div>
      </div>
    </div>

    <el-dialog :title="title" :visible.sync="articleDialogVisible" width="800px" append-to-body
      :before-close="handleClose" :close-on-click-modal="false">
      <el-table v-loading="dialogLoading" :data="list" style="width: 100%" :show-header="false" ref="table"
        class="table-all" height="510" @cell-click="openNewView">
        <el-table-column prop="title" label="标题" width="510" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-html="changeColor(scope.row.cnTitle || scope.row.title)"></span>
          </template>
        </el-table-column>
        <el-table-column prop="sourceName" label="数据源" width="140" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="publishTime" label="发布时间" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>
              {{ parseTime(scope.row.publishTime || scope.row.gatherTime, "{y}-{m}-{d}") }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        :background="false" @pagination="getList" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="drawerInfo.cnTitle || drawerInfo.title" :visible.sync="articleDialogVisible1" width="800px"
      append-to-body :before-close="handleClose1" :close-on-click-modal="false">
      <div style="line-height: 30px" v-html="drawerInfo.cnContent"></div>
      <el-empty description="当前文章暂无数据" v-if="!drawerInfo.cnContent"></el-empty>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose1">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { listAitaKeywords } from "@/api/large/aitaKeywords";
import { largeAitaDataList, largeAitaKeywordsNames, largeAitaData } from "@/api/bigScreen/index1";

export default {
  data() {
    return {
      keyList: [],
      loading: false,
      title: "",
      keywordId: null,
      articleDialogVisible: false,
      articleDialogVisible1: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      list: [],
      dialogLoading: false,
      keywords: [],
      drawerInfo: {},
    };
  },
  mounted() {
    this.init();
  },
  props: {},
  watch: {},
  components: {},
  methods: {
    init() {
      this.loading = true;
      listAitaKeywords(this.queryParams).then(response => {
        let data = response.data.map(item => {
          item.tab = item.tab ? item.tab.split(',').map(Number) : []
          return item
        })
        this.keyList = this.handleTree(data, "id", "parentId");
        this.loading = false;
      });
    },
    widthjisuan(list, tab, indexs) {
      function getMinMax(lengthWithListLength, indexs) {
        lengthWithListLength.sort((a, b) => a - b);
        const index = lengthWithListLength.findIndex(item => item >= indexs);

        const min = index > 0 ? lengthWithListLength[index - 1] : lengthWithListLength[0];
        const max = index < lengthWithListLength.length - 1 ? lengthWithListLength[index] : lengthWithListLength[lengthWithListLength.length - 1];

        return { min, max };
      }
      function getWidth(diff) {
        if (diff % 4 === 0) {
          return 'calc(25% - 10px)';
        } else if (diff % 3 === 0 && diff / 3 < 4) {
          return 'calc(33% - 10px)';
        } else if (diff % 2 === 0 && diff / 2 < 4) {
          return 'calc(50% - 5px)';
        } else if (diff == 1) {
          return '100%';
        } else {
          return 'calc(25% - 10px)';
        }
      }

      if (tab && tab.length > 0) {
        let number;
        const hasMatch = tab.includes(indexs + 1);
        if (hasMatch) {
          number = '100%';
        } else {
          let length = [0, ...tab, (list.length + 1)]
          const { min, max } = getMinMax(length, indexs + 1);
          number = getWidth(max - min - 1)
        }
        return number
      } else {
        return getWidth(list.length)
      }
    },
    async openList(item) {
      await largeAitaKeywordsNames(item.id).then((res) => {
        this.keywords = res.data;
      });
      this.title = item.name
      this.keywordId = item.id
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
      this.getList()
      this.articleDialogVisible = true;
    },
    async getList() {
      this.dialogLoading = true
      largeAitaDataList({ keywordId: this.keywordId, ...this.queryParams }).then((res) => {
        this.list = res.rows;
        this.total = res.total;
        this.dialogLoading = false
      });
    },
    // 关键字替换
    changeColor(str) {
      let Str = str;
      if (Str) {
        let keywords = this.keywords;
        keywords.map((keyitem, keyindex) => {
          if (keyitem && keyitem.length > 0) {
            // 匹配关键字正则
            let replaceReg = new RegExp(keyitem, "g");
            // 高亮替换v-html值
            let replaceString =
              '<span class="highlight"' +
              ' style="color: #ff7500;">' +
              keyitem +
              "</span>";
            Str = Str.replace(replaceReg, replaceString);
          }
        });
      }
      return Str;
    },
    async openNewView(item) {
      await largeAitaData(item.id).then((res) => {
        this.drawerInfo = res.data;
      });
      let content = this.drawerInfo.article || this.drawerInfo.content;
      if (content) {
        content = content.replace(/\n/g, "<br>");
        content = content.replace(/\${[^}]+}/g, "<br>");
        content = content.replace("|xa0", "");
        content = content.replace("opacity: 0", "");
        content = content.replace(/<img\b[^>]*>/gi, "");
        content = content.replace(/ style="[^"]*"/g, "");
      }
      this.drawerInfo.cnContent = content;
      this.articleDialogVisible1 = true;
    },
    handleClose() {
      this.articleDialogVisible = false;
    },
    handleClose1() {
      this.articleDialogVisible1 = false;
    },
  },
  beforeDestroy() { },
};
</script>

<style lang="scss">
.el-table .warning-row {
  background-color: #13436d;
}

.el-table .success-row {
  background-color: #113a65;
}

.artificialBar {
  padding: 0 33px;
  text-align: center;

  .tag {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
  }

  .tag-left {
    width: 25%;
    vertical-align: middle;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .tag-right {
    width: 74%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: -5px;

    >div {
      margin-bottom: 5px;
      height: 20px;
      overflow: hidden;
    }

    // > div:nth-child(5) {
    //   margin-top: 5px;
    // }
  }
}
</style>
<style lang="scss" scoped>
::v-deep .el-dialog {
  background: url("../../../assets/bigScreenThree/dialog.png") no-repeat;
  background-size: 100% 100% !important;
  background-size: cover;
  height: 800px;

  .el-dialog__header {
    background-color: #1d233400;
    font-size: 30px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);
    height: 100px;
    padding-left: 31px;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .el-dialog__body {
    background-color: #2a304000;
    color: #f2f2f2;
    height: calc(100% - 160px);
    overflow: auto;
  }

  .el-dialog__footer {
    background-color: #1d233400;
    padding: 0 27px 28px 0;
  }

  .el-button {
    background-color: #002766;
    color: #fff;
    border: 0px;
  }

  .el-dialog__headerbtn .el-dialog__close {
    background: url("../../../assets/bigScreenThree/close.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
    width: 31px;
    height: 31px;
    top: 16px;

    &::before {
      content: none;
    }
  }
}

::v-deep .el-table {
  background-color: #2a304000;

  tr {
    color: #f2f2f2;
    background: url("../../../assets/bigScreenTwo/弹窗列表.png") no-repeat;
    background-size: 100% 100% !important;
    height: 68px;
    padding: 0 0 0 65px;
    margin-bottom: 10px;
    display: block;
    width: 790px;
  }

  td.el-table__cell {
    border-bottom: 1px solid #1d233400;
    height: 68px;
    line-height: 68px;
    font-size: 16px;
    text-shadow: 0px 0px 9px rgba(30, 198, 255, 0.8);
  }

  &::before {
    height: 0;
  }
}

.table-all {
  ::v-deep .el-table {
    background-color: #2a304000;
  }

  ::v-deep tr {
    color: #f2f2f2;
    background: #2a304000;
    height: 41px;
    padding: 0 0 0 0px;
    margin-bottom: 10px;
    display: block;
    width: 790px;
  }

  ::v-deep td.el-table__cell {
    border-bottom: 1px solid #1d233400;
    height: 41px;
    line-height: 41px;
    font-size: 18px;
    text-shadow: 0px 0px 9px rgba(30, 198, 255, 0.8);
  }
}

::v-deep .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell,
::v-deep .el-table__empty-block {
  background-color: #2a304000;
  color: #f2f2f2;
  cursor: pointer;
}

::v-deep .pagination-container {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 0;

  .el-select .el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }

  .el-pagination__editor.el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }
}

::v-deep .el-pagination__total,
::v-deep .el-pagination__jump {
  color: #f2f2f2;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination button:disabled {
  background-color: #ffffff00;
  color: #fff;
}

::v-deep .el-pager li {
  background: #ffffff00;
  color: #fff;

  &.active {
    color: #1890ff;
  }
}
</style>