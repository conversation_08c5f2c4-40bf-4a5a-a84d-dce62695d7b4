import request from "@/utils/request";

// 查询大屏-产品数据列

export function listProduct(query) {
  return request({
    url: "/large/product/list",
    method: "get",
    params: query,
  });
}

// 查询大屏-产品数据列

export function getProduct(id) {
  return request({
    url: "/large/product/" + id,
    method: "get",
  });
}

// 新增大屏-产品数据列

export function addProduct(data) {
  return request({
    url: "/large/product/add",
    method: "post",
    data: data,
  });
}

// 修改大屏-产品数据列

export function updateProduct(data) {
  return request({
    url: "/large/product/edit",
    method: "post",
    data: data,
  });
}

// 删除大屏-产品数据列

export function delProduct(id) {
  return request({
    url: "/large/product/remove",
    method: "post",
    data: id,
  });
}
