/* 导出pdf公共方法 */
/**
 * @params Element 需要导出为pdf的DOM元素
 * @params FileName 生成后的文件名字
 **/

import html2Canvas from "html2canvas";
import JsPdf from "jspdf";
/* 导出pdf公共方法(导出有阴影) */
export function downloadPdf(Element, FileName) {
  let EleWidth = Element.offSetWidth /* 容器宽度 */,
    EleHeight = Element.offSetHeight /* 容器高度 */,
    EleOffsetTop = Element.offsetTop /* 容器距离顶部距离 */,
    EleOffsetLeft = Element.offsetLeft; /* 容器距离左侧距离 */
  var canvas = document.createElement("canvas");
  var abs = 0;

  const winClientWidth =
    document.documentElement.clientWidth ||
    document.body.clientWidth; /* 当前视口的宽度 */
  const winInnerWidth = document.innerWidth; /* 获取当前窗口的宽度 */

  if (winInnerWidth > winClientWidth) {
    // abs = (win_o - win_i)/2;    // 获得滚动条长度的一半
    abs = (winInnerWidth - winClientWidth) / 2; // 获得滚动条宽度的一半
    // console.log(a, '新abs');
  }
  canvas.width = EleWidth * 2; // 将画布宽&&高放大两倍
  canvas.height = EleHeight * 2;

  var context = canvas.getContext("2d");
  context.scale(2, 2);
  context.translate(-EleOffsetLeft - abs, -EleOffsetTop);
  // 这里默认横向没有滚动条的情况，因为offset.left(),有无滚动条的时候存在差值，因此
  // translate的时候，要把这个差值去掉

  // html2canvas(element).then( (canvas)=>{ //报错
  // html2canvas(element[0]).then( (canvas)=>{
  html2Canvas(Element, {
    dpi: 1200,
    // allowTaint: true,  //允许 canvas 污染， allowTaint参数要去掉，否则是无法通过toDataURL导出canvas数据的
    useCORS: true, //允许canvas画布内 可以跨域请求外部链接图片, 允许跨域请求。
  }).then((canvas) => {
    var contentWidth = canvas.width;
    var contentHeight = canvas.height;
    //一页pdf显示html页面生成的canvas高度;
    var pageHeight = (contentWidth / 592.28) * 841.89;
    //未生成pdf的html页面高度
    var leftHeight = contentHeight;
    //页面偏移
    var position = 0;
    //a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
    var imgWidth = 595.28;
    var imgHeight = (595.28 / contentWidth) * contentHeight;
    var pageData = canvas.toDataURL("image/jpeg", 1.0);
    var pdf = new JsPdf("", "pt", "a4");
    //有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
    //当内容未超过pdf一页显示的范围，无需分页
    if (leftHeight < pageHeight) {
      //在pdf.addImage(pageData, 'JPEG', 左，上，宽度，高度)设置在pdf中显示；
      pdf.addImage(pageData, "JPEG", 0, 0, imgWidth, imgHeight);
      // pdf.addImage(pageData, 'JPEG', 20, 40, imgWidth, imgHeight);
    } else {
      // 分页
      while (leftHeight > 0) {
        pdf.addImage(pageData, "JPEG", 0, position, imgWidth, imgHeight);
        leftHeight -= pageHeight;
        position -= 841.89;
        //避免添加空白页
        if (leftHeight > 0) {
          pdf.addPage();
        }
      }
    }
    //可动态生成
    pdf.save(FileName);
  });
}
/* 插件形式 */
export default {
  install(Vue, options) {
    Vue.prototype.getPdf = function (dom) {
      var title = '报告'; //DPF标题
      html2Canvas(dom, {
        allowTaint: true,
        taintTest: false,
        useCORS: true,
        y: 72, // 对Y轴进行裁切
        // width:1200,
        // height:5000,
        dpi: window.devicePixelRatio * 4, //将分辨率提高到特定的DPI 提高四倍
        scale: 4, //按比例增加分辨率
      }).then(function (canvas) {
        let contentWidth = canvas.width;
        let contentHeight = canvas.height;
        let pageHeight = (contentWidth / 592.28) * 841.89;
        let leftHeight = contentHeight;
        let position = 0;
        let imgWidth = 595.28;
        let imgHeight = (592.28 / contentWidth) * contentHeight;
        let pageData = canvas.toDataURL("image/jpeg", 1.0);
        let PDF = new JsPdf("", "pt", "a4");
        if (leftHeight < pageHeight) {
          PDF.addImage(pageData, "JPEG", 0, 0, imgWidth, imgHeight);
        } else {
          while (leftHeight > 0) {
            PDF.addImage(pageData, "JPEG", 0, position, imgWidth, imgHeight);
            leftHeight -= pageHeight;
            position -= 841.89;
            if (leftHeight > 0) {
              PDF.addPage();
            }
          }
        }
        PDF.save(title + ".pdf");
      });
    };
  },
};
