<template>
  <div class="bigMap">
    <div class="header">
      <div class="header-left fl" id="time"></div>
      <div class="header-center fl">
        <div class="header-title">
          开源科技情报地平线数据分析系统
        </div>
        <div class="header-img"></div>
      </div>
      <div class="header-right fl">
        <div class="textTime">
          {{ currentTime ? parseTime(currentTime, "{y}年{m}月{d}日") : '' }}
        </div>
        <div>
          {{ week }}
        </div>
        <div>
          {{ currentTime ? parseTime(currentTime, "{h}:{i}:{s}") : '' }}
        </div>
      </div>
      <div class="header-bottom fl"></div>
    </div>

    <div class="center">
      <div class="center-left fl">
        <div class="left-top">
          <!--<h1 id="ceshi">数据可视化</h1>-->
          <div class="title">基本信息</div>
          <div class="bottom-a" v-if="Cooperation.infoList">
            <div class="attributeBox">
              <div class="icon icon1"></div>
              <div class="attribute">
                <div class="attribute-name">科情总数</div>
                <div class="attribute-number">{{ Cooperation.infoList.articleCount }}</div>
              </div>
            </div>
            <div class="attributeBox">
              <div class="icon icon2"></div>
              <div class="attribute">
                <div class="attribute-name">报告数</div>
                <div class="attribute-number">{{ Cooperation.infoList.reportCount }}</div>
              </div>
            </div>
            <div class="attributeBox">
              <div class="icon icon3"></div>
              <div class="attribute">
                <div class="attribute-name">公众号</div>
                <div class="attribute-number">{{ Cooperation.infoList.wechatCount }}</div>
              </div>
            </div>
            <div class="attributeBox">
              <div class="icon icon4"></div>
              <div class="attribute">
                <div class="attribute-name">采集器</div>
                <div class="attribute-number">{{ Cooperation.infoList.gatherCount }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="left-bottom">
          <div class="title">媒体top10(柱形图)</div>
          <div class="bottom-a">
            <div class="list" v-for="(item, index) in Cooperation.topSourceList">
              <div class="ranking" :style="{ 'background': colorAllList.table[index] }">
                {{ index < 9 ? '0' + (index + 1) : index + 1 }} </div>
                  <div class="attribute">
                    <div class="attribute-top">
                      <el-tooltip class="item" effect="dark" :content="item.sourceName">
                        <div class="attribute-title">{{
                          item.sourceName }}
                        </div>
                      </el-tooltip>
                      <div class="number">
                        <div>{{ item.editingCount }}</div>
                        <div>{{ item.gatherCount }}</div>
                      </div>
                    </div>
                    <div class="progress">
                      <el-progress :percentage="Number(item.editingRate.substr(0, 2))" :text-inside="false"
                        :stroke-width="12" :color="colorAllList.table[index]"
                        :text-color="colorAllList.table[index]"></el-progress>
                    </div>
                  </div>
              </div>
            </div>
          </div>
        </div>

        <div class="center-cen fl">
          <div class="cen-top">
            <div class="bg">节点监测</div>
            <canvas class="bg" style="z-index: 1;" width="1000" height="300" id="canvas" ref="canvas"></canvas>
            <div class="bottom-a">
              <div class="attributeBox" v-for="(item, index) in Cooperation.nodeList">
                <div class="attribute">
                  <div>{{ item && item.nodeCount ? item.nodeCount : '' }}</div>
                  <div>{{ item && item.node_name ? item.node_name : '' }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="cen-bottom">
            <div class="title">科情趋势</div>
            <div class="bottom-b">
              <div id="chart1" ref="chart1" class="allnav"></div>
            </div>
          </div>
        </div>

        <div class="center-right fr">
          <div class="right-top">
            <div class="title">行业统计</div>
            <div class="bottom-b">
              <div id="chart2" ref="chart2" class="allnav"></div>
            </div>
          </div>

          <div class="right-cen">
            <div class="title">热词</div>
            <div class="bottom-b">
              <div id="chart3" ref="chart3" class="allnav"></div>
            </div>
          </div>

          <div class="right-bottom">
            <div class="title">热门文章</div>
            <div class="table">
              <!-- <div class="header">
              <div>序号</div>
              <div>文章标题</div>
            </div> -->
              <div class="table_main">
                <div class="scoll-Table">
                  <div class="table_col" v-for="(item, index) in Cooperation.hotList" :key="index">
                    <div class="first-child">
                      <div v-if="index < 10" class="tableFirstBackground"
                        :style="{ 'background-color': colorAllList.table[index] }">
                        {{ index + 1 }}
                      </div>
                      <div v-else class="tableSecondBackground" :style="{ 'background-color': randomColor() }"></div>
                    </div>
                    <el-tooltip class="item" effect="dark" :content="item.title" placement="top-start">
                      <div class="last-child">{{ item.title }}</div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>
<script >
import * as echarts from 'echarts'
require("echarts-wordcloud");
import api from '@/api/infoEscalation/index'

export default {
  data() {
    return {
      Cooperation: {
        infoList: []
      },
      currentTime: "",
      week: "",
      echartSzie: { width: 'auto', height: 'auto' }, // 图size
      colorAllList: {
        line: ['#FADB14FF', '#00FBFFFF', '#ED577AFF', '#06D6A0FF', '#1890FFFF', '#B954FBFF', '#E36414FF', '#FF99C8FF', '#65C9FAFF', '#81B29AFF', '#F18FD0FF'], // 折线图颜色库
        bar: [ // 柱状图颜色库
          new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#35E4E2' }, { offset: 1, color: '#2A65E1' }
          ]),
          new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#2378f7' }, { offset: 0.7, color: '#2378f7' }, { offset: 1, color: '#83bff6' }])
        ],
        table: ['#F56C6C', '#E5A13C', '#409EFF', '#909399', '#909399', '#909399', '#909399', '#909399', '#909399', '#909399', '#909399', '#909399', '#909399', '#909399'], // 表单颜色库
      },
    }
  },
  mounted() {
    this.getCooperation()
    this.chart1 = echarts.init(this.$refs['chart1']);
    this.chart2 = echarts.init(this.$refs['chart2']);
    this.chart3 = echarts.init(this.$refs['chart3']);
    this.funResize = this.debounce(this.resize, 200)
    window.addEventListener("resize", this.funResize)
    setInterval(() => {
      this.currentTime = new Date();
      let weekDays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"]
      this.week = weekDays[this.currentTime.getDay()]
    }, 1000);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.funResize)
  },
  methods: {
    // 获取产融合作数据
    getCooperation() {
      api.getNewLargeScreen().then(res => {
        if (res.code === 200) {
          this.Cooperation = res.data
          this.Cooperation.nodeList.length = 9
          let data1 = {
            '微信公众号': this.Cooperation.wxList,
            '网站': this.Cooperation.wyList,
          }
          this.chart1Fun(data1)
          this.chart2Fun(this.Cooperation.industryList)
          this.chart3Fun(this.Cooperation.wordsList)
        }
      })
    },
    chart1Fun(data) {
      this.chart1.resize(this.echartSzie)
      this.chart1.clear()
      this.option1 = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        xAxis: {
          axisLabel: {
            color: '#ffffff',
            fontSize: 12,
            rotate: 45,
          },
          type: 'category',
          data: data['微信公众号'].map(item => { return item.date_formatted })
        },
        yAxis: {
          axisLabel: {
            color: '#ffffff',
            fontSize: 12,
          },
          splitLine: {
            lineStyle: {
              type: 'solid',
              color: '#E6F7FF20'
            }
          },
          type: 'value'
        },
        grid: {
          left: '40px',
          right: '10px',
          bottom: '40px',
          top: '20px',
        },
        series: [
          {
            name: '微信公众号',
            type: 'line',
            stack: 'Total',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            smooth: true,
            data: data['微信公众号'].map(item => { return item.publish_count })
          },
          {
            name: '网站',
            type: 'line',
            stack: 'Total',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            smooth: true,
            data: data['网站'].map(item => { return item.publish_count })
          },
        ]
      }
      this.option1 && this.chart1.setOption(this.option1);
    },
    chart2Fun(data) {
      this.chart2.resize(this.echartSzie)
      this.chart2.clear()
      this.option2 = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        xAxis: {
          axisLabel: {
            color: '#ffffff',
            fontSize: 12,
            rotate: 30,
          },
          type: 'category',
          data: data.map(item => { return item.industry })
        },
        yAxis: {
          axisLabel: {
            color: '#ffffff',
            fontSize: 12,
          },
          splitLine: {
            lineStyle: {
              type: 'solid',
              color: '#E6F7FF20'
            }
          },
          type: 'value'
        },
        grid: {
          left: '40px',
          right: '10px',
          bottom: '35px',
          top: '20px'
        },
        series: [
          {
            data: data.map(item => { return item.industryCount }),
            type: 'bar',
            barWidth: '20px',
            showBackground: true,
            itemStyle: {
              color: this.colorAllList.bar[0]
            },
            backgroundStyle: {
              // color: this.colorAllList.bar[1]
            }
          }
        ]
      }
      this.option2 && this.chart2.setOption(this.option2);
    },
    chart3Fun(data) {
      this.chart3.resize(this.echartSzie)
      this.chart3.clear()
      this.option3 = {
        series: [
          {
            type: "wordCloud",
            shape: "circle",
            left: "center",
            top: "center",
            right: null,
            bottom: null,
            width: "100%",
            height: "100%",
            sizeRange: [12, 26],
            rotationRange: [-90, 90],
            rotationStep: 90,
            gridSize: 8,
            drawOutOfBound: false, // 超出画布部分不显示，与sizeRange相关
            textStyle: {
              normal: {
                fontFamily: "sans-serif",
                fontWeight: "normal",
              },
              emphasis: {
                shadowBlur: 5,
                shadowColor: "#333",
              },
            },
            data: data.map(item => {
              return {
                name: item.words,
                value: item.wordsNum,
                textStyle: {
                  color: this.randomColor(),
                },
              }
            })
          },
        ],
      };
      this.option3 && this.chart3.setOption(this.option3);
      this.drawLightning()
    },
    // 随机颜色
    randomColor() {
      var r = Math.floor(Math.random() * 256);
      var g = Math.floor(Math.random() * 256);
      var b = Math.floor(Math.random() * 256);
      let isDarkColor = (r * 0.299 + g * 0.587 + b * 0.114) < 128
      if (isDarkColor) {
        return this.randomColor();
      }
      return "rgb(" + r + ", " + g + ", " + b + ")"
    },
    // 防抖
    debounce(fn, delay) {
      let timer;
      return function () {
        let context = this;
        let args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(context, args);
        }, delay);
      }
    },
    // 页面重绘
    resize() {
      this.chart1.resize(this.echartSzie)
      this.chart1.clear()
      this.option1 && this.chart1.setOption(this.option1);
      this.chart2.resize(this.echartSzie)
      this.chart2.clear()
      this.option2 && this.chart2.setOption(this.option2);
      this.chart3.resize(this.echartSzie)
      this.chart3.clear()
      this.option3 && this.chart3.setOption(this.option3);
    },
    drawLightning1() {
      const canvas = document.getElementById('canvas'), ctx = canvas.getContext('2d'),
        containerWidth = 600,
        containerHeight = 300, lineLength = 5, thunderWidth = 2, thunderColor = '#1A83C7';
      var a = 10, startX, endX, startY, endY;
      const onceWidth = containerWidth / a, onceHeight = containerHeight / a;

      startX = Array(lineLength).fill(containerWidth / 2)
      endX = Array(lineLength).fill(containerWidth / 2)
      startY = Array(lineLength).fill(containerHeight / 2)
      endY = Array(lineLength).fill(containerHeight / 2)

      function draw() {
        ctx.strokeStyle = thunderColor;
        ctx.lineWidth = thunderWidth;
        ctx.beginPath();
        for (let i = 0; i < lineLength; i++) {
          startX[i] = endX[i]
          startY[i] = endY[i]
          ctx.moveTo(startX[i], startY[i]);
          endX[i] += Math.random() * (onceWidth * a) - (onceWidth * a / 2);
          endY[i] += Math.random() * (onceHeight * a) - (onceHeight * a / 2);
          ctx.lineTo(endX[i], endY[i]);
        }
        ctx.stroke();

        if (a < 10) {
          setTimeout(() => {
            requestAnimationFrame(() => draw(a += 1))
          }, 300)
        } else {
          ctx.clearRect(0, 0, canvas.width, canvas.height)
          a = 0
          startX = Array(lineLength).fill(containerWidth / 2)
          endX = Array(lineLength).fill(containerWidth / 2)
          startY = Array(lineLength).fill(containerHeight / 2)
          endY = Array(lineLength).fill(containerHeight / 2)
          draw(a)
        }
      }
      draw(a);
    },
    drawLightning() {
      const canvas = document.getElementById('canvas');
      const context = canvas.getContext('2d'), containerWidth = 1000, containerHeight = 300;
      const startPos = { x: containerWidth / 2, y: containerHeight / 2 };
      const endPos = [
        { x: 115, y: 190 },
        { x: 175, y: 100 },
        { x: 305, y: 190 },
        { x: 395, y: 100 },
        { x: 500, y: 190 },
        { x: 605, y: 100 },
        { x: 690, y: 190 },
        { x: 825, y: 100 },
        { x: 885, y: 190 },
      ];
      const arrowSize = 10; // 箭头的大小
      var animateRequestId,
        suiji =  Math.floor(Math.random() * 8), // 随机数
        nowX = startPos.x, nowY = startPos.y

      const animate = () => {
        context.clearRect(0, 0, containerWidth, containerHeight);
        animateRequestId = requestAnimationFrame(animate)
        // 绘制箭头起始节点
        context.strokeStyle = "#1A83C7";
        context.lineWidth = 4;
        context.beginPath();
        context.moveTo(115, startPos.y)
        context.lineTo(containerWidth - 115, startPos.y)
        endPos.map((row, index) => {
          context.moveTo(row.x, startPos.y);
          context.lineTo(row.x, row.y)
          context.fillStyle = '#1A83C7';

          if (index % 2 == 0) {
            context.moveTo(row.x, row.y);
            context.lineTo(row.x - arrowSize / 2, row.y - arrowSize);
            context.lineTo(row.x + arrowSize / 2, row.y - arrowSize);
            context.lineTo(row.x, row.y);
            context.fill();
          } else {
            context.moveTo(row.x, row.y);
            context.lineTo(row.x + arrowSize / 2, row.y + arrowSize);
            context.lineTo(row.x - arrowSize / 2, row.y + arrowSize);
            context.lineTo(row.x, row.y);
            context.fill();
          }
        })
        context.stroke();
        context.closePath();

        // 绘制移动的圆点
        context.beginPath();
        context.arc(nowX, nowY, 5, 0, Math.PI * 2);
        context.fillStyle = 'blue';
        context.fill();
        context.closePath();

        // 判断箭头是否到达终点
        if (endPos[suiji].x == nowX) {
          if (endPos[suiji].y == nowY) {
            nowX = startPos.x
            nowY = startPos.y
            suiji = Math.floor(Math.random() * 8)
          } else {
            if (nowY > endPos[suiji].y) {
              nowY -= 1
            } else {
              nowY += 1
            }
          }
        } else {
          if (nowX > endPos[suiji].x) {
            nowX -= 1
          } else {
            nowX += 1
          }
        }
        // requestAnimationFrame(() => animate());
      };

      animate();
      requestAnimationFrame(animate)

    }
  }
}
</script>
<style lang="scss" scoped>
.fl {
  float: left;
}

.fr {
  float: right;
}

ul,
li,
ol {
  list-style: none;
}

.allnav {
  height: 100%;
}

.bigMap {
  background: url("./images/bg01.png") no-repeat;
  background-size: cover;
  font-size: 14px;
  color: #ffffff;
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0px;
  margin: 0px;
  font-family: "微软雅黑";

  .header {
    width: 100%;
    height: 15vh;

    .header-left {
      width: 25%;
      height: 10.5vh;
      color: white;
      text-align: center;
      line-height: 10.5vh;
    }

    .header-center {
      width: 50%;
      height: 10.5vh;
      // border: 1px solid red;
      // background: url("./images/head.gif") no-repeat;
      // background-size: 100% 100%;
      position: relative;

      .header-title {
        text-align: center;
        color: #ffffff;
        font-size: 40px;
        text-shadow: 0 3px #00d8ff;
        line-height: 8.5vh;
      }
    }

    // .header-img {
    //   background: url("./images/head.gif") no-repeat center center;
    //   background-size: 100%;
    //   height: 100%;
    //   width: 100%;
    //   position: absolute;
    //   top: 5vh;
    // }

    .header-right {
      width: 25%;
      height: 10.5vh;
      line-height: 13.5vh;
      font-size: 20px;
      display: flex;
      justify-content: space-evenly;
      /*border: 1px solid gold;*/
    }

    .header-bottom {
      width: calc(100% - 4vw);
      height: 16px;
      background: url("./images/header.png") no-repeat;
      background-size: calc(100% - 2vw) 100%;
      padding: 0 20px;
      margin-left: 30px;
    }
  }

  .center {
    height: 85vh;
    padding: 10px 10px 10px 10px;

    .center-left {
      width: calc((100% - 1000px) / 2);
      padding: 10px;
      height: calc(85vh - 20px);

      .left-top {
        width: 100%;
        height: calc(33% - 20px);
        margin-bottom: 20px;
        // background: url("./images/panel.png") no-repeat;
        // background-size: 100% 100%;
        // transform-style: preserve-3d;
        border: 1px solid #217093;
        background-color: #142e5f75;
        box-shadow: inset 0px 1px 14px 6px rgb(37 104 161 / 61%);
        position: relative;
        overflow: hidden;

        .bottom-a {
          height: calc(100% - 30px);
          padding: 20px 10px 10px 10px;
          display: flex;
          flex-wrap: wrap;

          .attributeBox {
            width: 50%;
            display: flex;
            justify-content: center;
            align-items: center;

            .icon {
              width: 50px;
              height: 50px;
              background-size: 100% 100%;
              transition: transform 0.3s ease;
              /* 添加变化过渡效果 */

              &:hover {
                transform: scale(1.2);
                /* 放大图片的尺寸 */
              }
            }

            .attribute {
              width: 50%;
              margin-left: 10px;

              .attribute-name {
                color: rgba(184, 225, 244, 0.5);
                font-size: 16px;
              }

              .attribute-number {
                font-size: 24px;
                font-weight: bold;
              }
            }

            .icon1 {
              background-image: url(images/icon3.png);
            }

            .icon2 {
              background-image: url(images/icon4.png);
            }

            .icon3 {
              background-image: url(images/icon5.png);
            }

            .icon4 {
              background-image: url(images/icon6.png);
            }
          }
        }
      }

      .left-bottom {
        width: 100%;
        height: calc(67% - 0px);
        border: 1px solid #217093;
        background-color: #142e5f75;
        box-shadow: inset 0px 1px 14px 6px rgb(37 104 161 / 61%);

        .bottom-a {
          width: calc(100% - 20px);
          height: calc(100% - 50px);
          margin-left: 10px;
          margin-top: 10px;
          overflow: auto;
          overflow-x: hidden;
          display: flex;
          flex-direction: column;
          justify-content: space-around;

          .list {
            display: flex;
            height: 40px;
            width: 90%;
            margin: 0 auto 5px;
            align-items: center;

            .ranking {
              width: 30px;
              height: 30px;
              line-height: 20px;
              text-align: center;
              clip-path: polygon(0 0%, 0 100%, 50% 70%, 100% 100%, 100% 0);
            }

            .attribute {
              display: flex;
              margin-left: 5px;
              width: calc(100% - 40px);
              height: 100%;
              flex-direction: column;

              .attribute-top {
                display: flex;
                justify-content: space-between;
                height: 20px;

                .attribute-title {
                  line-height: 20px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }

                .number {
                  width: 120px;
                  display: flex;
                  justify-content: flex-end;

                  div {
                    margin-left: 25px;
                    position: relative;

                    &:nth-of-type(1) {
                      &::before {
                        content: '';
                        top: 3px;
                        left: -14px;
                        height: 14px;
                        width: 14px;
                        position: absolute;
                        background-image: url(images/icon2.png);
                        background-size: 100% 100%;
                      }
                    }

                    &:nth-of-type(2) {
                      &::before {
                        content: '';
                        top: 3px;
                        left: -14px;
                        height: 14px;
                        width: 14px;
                        position: absolute;
                        background-image: url(images/icon1.png);
                        background-size: 100% 100%;
                      }
                    }
                  }
                }
              }

              .progress {
                ::v-deep .el-progress-bar {
                  padding-right: 0px;
                }

                ::v-deep .el-progress-bar__outer,
                ::v-deep .el-progress-bar__inner {
                  border-radius: 0;
                }

                ::v-deep .el-progress-bar__innerText {
                  display: block
                }

                ::v-deep .el-progress__text {
                  position: absolute;
                  right: 10px;
                  font-size: 12px !important;
                  line-height: 16px;
                  font-weight: bold;
                }
              }
            }
          }
        }
      }
    }

    .center-cen {
      width: 1000px;
      padding: 10px;
      height: calc(85vh - 20px);

      .cen-top {
        width: 100%;
        height: calc(66.6% - 10px);
        // border: 1px solid #217093;
        // background-color: #142e5f75;
        // box-shadow: inset 0px 1px 14px 6px rgb(37 104 161 / 61%);
        margin-bottom: 30px;
        position: relative;

        .bg {
          position: absolute;
          top: calc(50% - 150px);
          left: calc(50% - 500px);
          height: 300px;
          width: 1000px;
          z-index: 5;
          line-height: 300px;
          color: #66BBDD;
          font-size: 18px;
          text-align: center;
          // background-image: url(images/bg03.png);
          // background-size: 100% 100%;
        }

        .bottom-a {
          z-index: 9;
          position: relative;
          width: calc(100% - 20px);
          height: calc(100% - 20px);
          margin-left: 10px;
          margin-top: 10px;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-around;
          align-items: center;
          align-content: space-around;

          .attributeBox {
            width: 18.3%;
            z-index: 9;

            .attribute {
              width: 120px;
              height: 120px;
              margin: 0 auto;
              background-image: url(images/bg02.png);
              background-size: 100% 100%;
              display: flex;
              flex-direction: column;
              justify-content: flex-end;
              padding-bottom: 40px;
              transition: transform 0.3s ease;
              /* 添加变化过渡效果 */

              &:hover {
                transform: scale(1.2);
                /* 放大图片的尺寸 */
              }

              div {
                width: 120px;
                height: 20px;
                line-height: 20px;
                text-align: center;
                font-size: 18px;

                &:nth-of-type(2) {
                  font-size: 16px;
                  color: #64BBDD;
                }
              }
            }

            &:nth-of-type(1) {
              margin-left: 5%;
            }

            &:nth-of-type(4) {
              margin-right: 5%;
            }
          }
        }
      }

      .cen-bottom {
        width: 100%;
        height: calc(33% - 20px);
        border: 1px solid #217093;
        background-color: #142e5f75;
        box-shadow: inset 0px 1px 14px 6px rgb(37 104 161 / 61%);

        .bottom-b {
          width: calc(100% - 20px);
          height: calc(100% - 50px);
          margin-left: 10px;
          margin-top: 10px;
        }
      }
    }

    .center-right {
      width: calc((100% - 1000px) / 2);
      padding: 10px;
      height: calc(85vh - 20px);

      .right-top {
        width: 100%;
        height: calc(33.3% - 20px);
        margin-bottom: 30px;
        border: 1px solid #217093;
        background-color: #142e5f75;
        box-shadow: inset 0px 1px 14px 6px rgb(37 104 161 / 61%);

        .bottom-b {
          width: calc(100% - 20px);
          height: calc(100% - 60px);
          margin-top: 10px;
          margin-left: 10px;

          .allnav {
            width: 100%;
            height: 100%;
          }
        }
      }

      .right-cen {
        width: 100%;
        height: calc(33.3% - 20px);
        margin-bottom: 30px;
        border: 1px solid #217093;
        background-color: #142e5f75;
        box-shadow: inset 0px 1px 14px 6px rgb(37 104 161 / 61%);

        .bottom-b {
          width: calc(100% - 20px);
          height: calc(100% - 60px);
          margin-top: 10px;
          margin-left: 10px;
        }
      }

      .right-bottom {
        width: 100%;
        height: calc(33.3% - 20px);
        border: 1px solid #217093;
        background-color: #142e5f75;
        box-shadow: inset 0px 1px 14px 6px rgb(37 104 161 / 61%);

        .table {
          width: 95%;
          margin: 0 auto;
          height: calc(100% - 40px);

          .header {
            width: 100%;
            background-color: #ffffff00;
            height: 30px;
            line-height: 30px;
            padding-left: 10px;
            display: flex;
            color: #61d2f7;
            z-index: 3;
            text-align: center;

            :first-child {
              width: 20%;
            }

            :last-child {
              width: 80%;
            }
          }

          .table_main {
            width: 100%;
            height: calc(100% - 25px);
            margin-top: 20px;
            overflow: hidden;
            z-index: 0;

            .table_col {
              width: 100%;
              height: 40px;
              line-height: 40px;
              border-bottom: solid 1px #172652;
              background-color: #12162400;
              display: flex;
              color: #ffff;
              padding-left: 10px;
              font-size: 14px;

              .first-child {
                width: 10%;

                .tableFirstBackground {
                  width: 20px;
                  height: 20px;
                  text-align: center;
                  border-radius: 4px;
                  margin: 10px 0;
                  line-height: 20px;
                }

                .tableSecondBackground {
                  width: 10px;
                  height: 10px;
                  text-align: center;
                  border-radius: 50%;
                  margin: 15px 5px;
                  line-height: 30px;
                }
              }

              .last-child {
                width: 90%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }

            .scoll-Table {
              animation: scoll-Table 9s linear infinite;
              transition: all 1s ease-out;
              //  animation-timing-function: linear;
              // animation-fill-mode: forwards;
              /* 在动画结束后保持最后一个关键帧的状态 */
            }

            /* 鼠标进入 */
            .scoll-Table:hover {
              animation-play-state: paused;
            }

            /* 鼠标离开 */
            .scoll-Table:not(:hover) {
              animation-play-state: running;
            }
          }
        }
      }
    }

    .title {
      width: 90%;
      height: 30px;
      // border: 1px solid gold;
      position: relative;
      top: 10px;
      left: 10px;
      color: white;
      font-size: 16px;
      font-weight: bold;
      padding-left: 12px;
      line-height: 30px;

      // &::before {
      //   width: 5px;
      //   height: 25px;
      //   top: 2px;
      //   position: absolute;
      //   content: "";
      //   background: #59ebe8;
      //   border-radius: 2px;
      //   left: 0;
      // }
    }
  }
}

@keyframes myfirst {
  to {
    transform: rotate(-360deg)
  }
}

@keyframes myfirst2 {
  to {
    transform: rotate(360deg)
  }
}

@keyframes scoll-Table {
  from {
    transform: translate(0, 0px);
  }

  to {
    transform: translate(0, calc(((85vh - 20px) / 3 - 90px) - 400px));
  }
}
</style>