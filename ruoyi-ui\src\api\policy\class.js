import request from '@/utils/request'

// 查询政策库主题分类列表
export function listClass(query) {
  return request({
    url: '/policy/class/list',
    method: 'get',
    params: query
  })
}

// 查询政策库主题分类详细
export function getClass(id) {
  return request({
    url: '/policy/class/' + id,
    method: 'get'
  })
}

// 新增政策库主题分类
export function addClass(data) {
  return request({
    url: '/policy/class',
    method: 'post',
    data: data
  })
}

// 修改政策库主题分类
export function updateClass(data) {
  return request({
    url: '/policy/class/edit',
    method: 'post',
    data: data
  })
}

// 删除政策库主题分类
export function delClass(data) {
  return request({
    url: '/policy/class/remove',
    method: 'post',
    data: data
  })
}
