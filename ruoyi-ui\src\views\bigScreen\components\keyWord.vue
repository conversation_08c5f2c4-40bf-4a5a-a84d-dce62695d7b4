<template>
  <div id="myChart" ref="myChart" style="width: 100%; height: 100%"></div>
</template>
 
<script>
import request from "@/utils/request";
import { demo } from "../demo";
import * as echarts from "echarts";
require("echarts-wordcloud");
const color = [
  "#FFFFFF",
  "#F4EFBD",
  "#F8C0DA",
  "#F3C59A",
  "#C697E9",
  "#A9DDC2",
  "#9BBFFF",
  "#6BB2B2",
  "#88BCE8",
];
export default {
  data() {
    return {
      chart: null,
      echartSzie: { width: "auto", height: "auto" }, // 图size
      option: null,
    };
  },
  props: {
    wordValueList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  mounted() {},
  watch: {
    wordValueList(value) {
      if (value) {
        this.chart = echarts.init(this.$refs["myChart"]);
        this.funResize = this.debounce(this.resize, 200);
        this.chartFun(value);
      }
    },
  },

  components: {},
  methods: {
    chartFun(data) {
      this.chart.resize(this.echartSzie);
      this.chart.clear();
      this.option = {
        series: [
          {
            type: "wordCloud",
            shape: "circle",
            left: "center",
            top: "center",
            right: null,
            bottom: null,
            width: "100%",
            height: "100%",
            sizeRange: [12, 26],
            rotationRange: [-90, 90],
            rotationStep: 90,
            gridSize: 8,
            drawOutOfBound: false, // 超出画布部分不显示，与sizeRange相关
            textStyle: {
              normal: {
                fontFamily: "sans-serif",
                fontWeight: "normal",
              },
              emphasis: {
                shadowBlur: 5,
                shadowColor: "#333",
              },
            },
            data: data.map((item) => {
              return {
                name: item.keyWord,
                value: item.wordFrequency,
                textStyle: {
                  color: color[Math.floor(Math.random() * 10)],
                },
              };
            }),
          },
        ],
      };
      this.option && this.chart.setOption(this.option);
    },
    // 随机颜色

    // 防抖
    debounce(fn, delay) {
      let timer;
      return function () {
        let context = this;
        let args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(context, args);
        }, delay);
      };
    },
    // 页面重绘
    resize() {
      this.chart.resize(this.echartSzie);
      this.chart.clear();
      this.option && this.chart.setOption(this.option);
    },
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.funResize);
  },
};
</script>

<style lang='scss'>
.el-table .warning-row {
  background-color: #13436d;
}
.el-table .success-row {
  background-color: #113a65;
}
</style>