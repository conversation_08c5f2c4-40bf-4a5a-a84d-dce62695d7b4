<template>
  <div class="article-notification-wrapper">
    <!-- 弹框 -->
    <transition name="slide-fade">
      <div v-if="visible" class="article-notification">
        <div class="notification-header">
          <div class="header-left">
            <i class="el-icon-bell notification-icon"></i>
            <span class="notification-title">最新文章提示</span>
          </div>
          <div class="header-right">
            <i class="el-icon-close close-btn" @click="closeNotification"></i>
          </div>
        </div>

        <div class="notification-content">
          <div class="article-list">
            <div
              v-for="(article, index) in articles"
              :key="index"
              class="article-item"
              @click="viewArticle(article)"
            >
              <div class="article-info">
                <div class="article-title">{{ article.title }}</div>
                <div class="article-meta">
                  <span class="article-source">{{ article.sourceName }}</span>
                  <span class="article-time">{{
                    formatTime(article.createTime)
                  }}</span>
                </div>
              </div>
              <div class="article-status">
                <span class="status-badge">新</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: "ArticleNotification",
  props: {
    articles: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // visible 现在通过 props 控制
    };
  },
  methods: {
    closeNotification() {
      this.$emit("close");
    },

    viewArticle(article) {
      this.$emit("view-article", article);
      this.closeNotification();
    },

    viewAllArticles() {
      this.$emit("view-all");
      this.closeNotification();
    },

    formatTime(time) {
      if (!time) return "";
      const date = new Date(time);
      const now = new Date();
      const diff = now - date;

      if (diff < 60000) {
        // 1分钟内
        return "刚刚";
      } else if (diff < 3600000) {
        // 1小时内
        return Math.floor(diff / 60000) + "分钟前";
      } else if (diff < 86400000) {
        // 1天内
        return Math.floor(diff / 3600000) + "小时前";
      } else {
        return date.toLocaleDateString();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.article-notification-wrapper {
  position: relative;
  width: 100%;
  height: 100px;
  pointer-events: none;
  z-index: 999;
  margin-top: 8px;
}

.article-notification {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(16, 216, 255, 0.4);
  box-shadow: 0px 0px 8px 0px #0056ad;
  pointer-events: auto;
  overflow: hidden;
  border-radius: 4px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: transparent;
  color: #00abf4;
  border-bottom: 1px solid rgba(16, 216, 255, 0.2);

  .header-left {
    display: flex;
    align-items: center;

    .notification-icon {
      font-size: 16px;
      margin-right: 6px;
      color: #1bdcff;
    }

    .notification-title {
      font-size: 16px;
      font-weight: 600;
      color: #00abf4;
    }
  }

  .header-right {
    .close-btn {
      font-size: 14px;
      cursor: pointer;
      padding: 2px;
      border-radius: 2px;
      transition: background-color 0.3s;
      color: #00c8ff;

      &:hover {
        background-color: rgba(16, 216, 255, 0.2);
      }
    }
  }
}

.notification-content {
  padding: 0 16px;
  height: calc(100% - 40px); // 减去头部的高度
  overflow-y: auto;
  background: transparent;

  .article-count {
    font-size: 12px;
    color: rgba(216, 240, 255, 0.6);
    margin-bottom: 8px;

    .count-number {
      color: #1bdcff;
      font-weight: 600;
    }
  }
}

.article-list {
  height: 100%;
  .article-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid rgba(16, 216, 255, 0.2);
    cursor: pointer;
    transition: background-color 0.3s;

    &:last-child {
      border-bottom: none;
    }

    // &:hover {
    //   background-color: rgba(16, 216, 255, 0.1);
    // }

    .article-info {
      flex: 1;
      margin-right: 8px;

      .article-title {
        font-size: 16px;
        color: rgba(216, 240, 255, 0.9);
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .article-meta {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: rgba(216, 240, 255, 0.6);

        .article-source {
          margin-right: 8px;
          max-width: 250px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .article-time {
          color: rgba(216, 240, 255, 0.5);
        }
      }
    }

    .article-status {
      .status-badge {
        display: inline-block;
        padding: 1px 4px;
        background: #F48200;
        color: white;
        font-size: 12px;
        border-radius: 2px;
        font-weight: 500;
      }
    }
  }
}



// 动画效果
.slide-fade-enter-active {
  transition: all 0.4s ease;
}

.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter {
  transform: translateX(100%);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

// 滚动条样式
.notification-content::-webkit-scrollbar {
  width: 4px;
}

.notification-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.notification-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.notification-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
