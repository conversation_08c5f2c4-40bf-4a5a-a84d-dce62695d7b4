/* 防抖 */
export function antiShake(time = 500, fn, father) {
  /* 获取上一次执行的时间 */
  let timeout;
  return function (...args) {
    let context = father; // 保存this指向
    clearTimeout(timeout);
    timeout = setTimeout(function () {
      fn.apply(context, args);
    }, time);
  };
}
/* 节流 */
export function throttle(time = 500, fn) {
  let old = Date.now();
  return function (...arg) {
    let newTIme = new Date();
    if (newTIme - old >= time) {
      fn.apply(null, arg);
      old = Date.now();
    }
  };
}
