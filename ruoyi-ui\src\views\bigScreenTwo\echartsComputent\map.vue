
<template>
  <div class="mapBox" v-loading="Loading" element-loading-text="地图绘制中，请耐心等待..."
    element-loading-background="rgba(0, 0, 0, 0.4)">
    <div ref="echart-map-ref" class="echarts"></div>
    <div class="fuchuang a" @click="openList({ regionName: '美国' })">
      <div class="name">美国</div>
      <div class="number">
        {{ data.filter(item => item.regionName == '美国').length ? data.filter(item => item.regionName ==
          '美国')[0].regionTotal : 0
        }}
      </div>
    </div>
    <div class="fuchuang b" @click="openList({ regionName: '英国' })">
      <div class="name">英国</div>
      <div class="number">
        {{ data.filter(item => item.regionName == '英国').length ? data.filter(item => item.regionName ==
          '英国')[0].regionTotal : 0
        }}
      </div>
    </div>
    <div class="fuchuang c" @click="openList({ regionName: '欧洲' })">
      <div class="name">欧洲</div>
      <div class="number">
        {{ data.filter(item => item.regionName == '欧洲').length ? data.filter(item => item.regionName ==
          '欧洲')[0].regionTotal : 0
        }}
      </div>
    </div>
    <div class="fuchuang d" @click="openList({ regionName: '印度' })">
      <div class="name">印度</div>
      <div class="number">
        {{ data.filter(item => item.regionName == '印度').length ? data.filter(item => item.regionName ==
          '印度')[0].regionTotal : 0
        }}
      </div>
    </div>
    <div class="fuchuang e" @click="openList({ regionName: '日本' })">
      <div class="name">日本</div>
      <div class="number">
        {{ data.filter(item => item.regionName == '日本').length ? data.filter(item => item.regionName ==
          '日本')[0].regionTotal : 0
        }}
      </div>
    </div>
    <div class="fuchuang f" @click="openList({ regionName: '韩国' })">
      <div class="name">韩国</div>
      <div class="number">
        {{ data.filter(item => item.regionName == '韩国').length ? data.filter(item => item.regionName ==
          '韩国')[0].regionTotal : 0
        }}
      </div>
    </div>
  </div>
</template>
  
<script>

import * as echarts from 'echarts';
import { MapChart } from 'echarts/charts'
import { InternationalMap } from '@/utils/InternationalMap'
import { InternationalOutline } from '@/utils/InternationalOutline'
import { nameMap } from '@/utils/countryNameComparisonTable'
import 'echarts/extension/bmap/bmap';
import { largeStatisticsAllData, largeGatherQueryGatherData } from "@/api/bigScreen/index1";

export default {
  components: {},
  dicts: [],
  data() {
    return {
      mapEchart: null, // 地图demo
      option: [],  // 地图绘制文件
      barEchartSzie: { width: 'auto', height: 'auto' }, // 地图大小
      debounceResize: null, // 重绘方法
      debounceRoam: null, // 缩放方法
      selectOptions: [],
      queryParams: { // 筛选条件
        firmId: null,
        category: '1',
      },
      Loading: false, // loading动画
      data: [],
    }
  },
  mounted() {
    this.mapEchart = echarts.init(this.$refs['echart-map-ref']);
    this.debounceResize = this.debounce(this.resize, 200)
    this.debounceRoam = this.debounce(this.roam, 200)
    echarts.use(MapChart)
    window.addEventListener("resize", this.debounceResize)
    // 世界地图
    echarts.registerMap('MapJson', InternationalMap);
    echarts.registerMap('OutlineJson', InternationalOutline);
    this.Loading = true
    this.map()
    this.init()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.debounceResize)
  },
  methods: {
    // 地图绘制
    map(timeData, mapData) {
      const imagePath = require('@/assets/bigScreenTwo/141.png');
      this.option = {
        backgroundColor: '#181F4E00',
        tooltip: {
          show: false,
          trigger: 'none'
        },
        geo: [
          {
            silent: true,
            map: 'OutlineJson',
            zoom: 1.2,
            aspectScale: 1,
            nameMap: nameMap,
            top: '5%',
            left: '180',
            roam: false,
            z: 6,
            label: {
              show: false,
            },
            tooltip: {
              show: false
            },
            itemStyle: {
              areaColor: '#00276602',
              borderColor: '#275BC180',
              borderWidth: 1.5,
            },
            emphasis: {

            },
          }
        ],
        series: [
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            data: [
              { name: '美国', value: [-101.375753, 39.783811, 100] },
              { name: '英国', value: [-2.333013, 54.674436, 100] },
              { name: '欧洲', value: [15.052458, 50.519447, 100] },
              { name: '印度', value: [78.956419, 20.587443, 100] },
              { name: '韩国', value: [127.879793, 36.035768, 100] },
              { name: '日本', value: [140.151701, 38.071103, 100] },
            ],
            symbolSize: function (val) {
              return 10;
            },
            label: {

            },
            symbol: `image://${imagePath}`,
            symbolSize: [40, 40]
          },
        ],
        options: []
      }
      this.mapEchart.setOption(this.option);
      this.Loading = false
    },
    // 防抖
    debounce(fn, delay) {
      let timer;
      return function () {
        let context = this;
        let args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(context, args);
        }, delay);
      }
    },
    // 页面重绘
    resize() {
      this.mapEchart.resize(this.barEchartSzie)
      this.mapEchart.clear()
      this.option && this.mapEchart.setOption(this.option);
    },
    openList(item) {
      this.$emit('openList', { query: item, title: item.regionName })
    },
    init() {
      largeStatisticsAllData({}).then(res => {
        this.data = res.data
      })
    }
  }
}
</script>
<style scoped lang="scss">
.mapBox {
  position: relative;
  height: 100%;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;

  .echarts {
    /* width: 100%; */
    min-width: 1912px;
    height: 100%;
  }
}

.fuchuang {
  position: absolute;
  width: 220px;
  height: 160px;
  background-size: 100% 100% !important;
  display: flex;
  justify-content: center;
  align-items: center;

  .name {
    position: absolute;
    top: 17px;
    left: 23px;
    font-weight: 800;
    font-size: 18px;
    color: #FFFFFF;
    line-height: 25px;
  }

  .number {
    text-align: center;
    font-weight: bold;
    font-size: 50px;
    line-height: 58px;
    background: linear-gradient(0deg, #05D5FF 0%, #FFFFFF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.a {
  top: 198px;
  left: 196px;
  background: url("../../../assets/bigScreenTwo/171.png") no-repeat;

  .name {
    right: 23px;
    left: auto;
  }
}

.b {
  top: 130px;
  left: 690px;
  background: url("../../../assets/bigScreenTwo/171.png") no-repeat;

  .name {
    right: 23px;
    left: auto;
  }
}

.c {
  top: 148px;
  left: 1016px;
  background: url("../../../assets/bigScreenTwo/161.png") no-repeat;
}

.d {
  top: 299px;
  left: 1323px;
  background: url("../../../assets/bigScreenTwo/161.png") no-repeat;
}

.e {
  top: 218px;
  left: 1627px;
  background: url("../../../assets/bigScreenTwo/161.png") no-repeat;
}

.f {
  top: 53px;
  left: 1336px;
  background: url("../../../assets/bigScreenTwo/151.png") no-repeat;

  .name {
    right: 23px;
    left: auto;
    top: auto;
    bottom: 17px;
  }
}
</style>