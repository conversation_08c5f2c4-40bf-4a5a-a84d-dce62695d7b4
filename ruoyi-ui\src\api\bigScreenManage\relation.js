import request from "@/utils/request";

// 查询图谱关系连线列表
export function listRelation(query) {
  return request({
    url: "/screen/relation/list",
    method: "get",
    params: query,
  });
}

// 查询图谱关系连线详细
export function getRelation(id) {
  return request({
    url: "/screen/relation/" + id,
    method: "get",
  });
}

// 新增图谱关系连线
export function addRelation(data) {
  return request({
    url: "/screen/relation",
    method: "post",
    data: data,
  });
}

// 修改图谱关系连线
export function updateRelation(data) {
  return request({
    url: "/screen/relation/edit",
    method: "post",
    data: data,
  });
}

// 删除图谱关系连线
export function delRelation(id) {
  return request({
    url: "/screen/relation/remove",
    method: "post",
    data: id,
  });
}
