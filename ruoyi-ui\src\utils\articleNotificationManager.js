import { getLatestArticles } from "@/api/notification/article";
import { getConfigKey } from "@/api/system/config";

class ArticleNotificationManager {
  constructor() {
    this.timer = null;
    this.isEnabled = false;
    this.isLoggedIn = false;
    this.callback = null;
    this.pollingInterval = 20000; // 20秒
  }

  // 初始化通知管理器
  async init(callback) {
    this.callback = callback;
    // 不在初始化时检查配置，等登录后再检查
  }

  // 检查功能是否开启（基于用户信息中的 isArticleNotification 字段）
  async checkFeatureEnabled() {
    if (!this.isLoggedIn) {
      this.isEnabled = false;
      return;
    }

    try {
      // 从 sessionStorage 获取用户信息
      const userInfo = JSON.parse(
        window.sessionStorage.getItem("userInfo") || "{}"
      );

      if (userInfo && userInfo.hasOwnProperty("isArticleNotification")) {
        // "0"是开，"1"是关
        this.isEnabled = userInfo.isArticleNotification === "0";
      } else {
        this.isEnabled = false;
      }

      // 获取轮询间隔配置
      await this.loadPollingInterval();
    } catch (error) {
      console.error("检查文章通知功能配置失败:", error);
      this.isEnabled = false;
    }
  }

  // 加载轮询间隔配置
  async loadPollingInterval() {
    try {
      const response = await getConfigKey("article.notify.timeout");

      if (response.code === 200 && response.msg) {
        const intervalSeconds = parseInt(response.msg, 10);

        if (!isNaN(intervalSeconds) && intervalSeconds > 0) {
          const newInterval = intervalSeconds * 1000; // 转换为毫秒

          if (newInterval !== this.pollingInterval) {
            this.pollingInterval = newInterval;

            // 如果当前正在轮询，重启轮询以应用新间隔
            if (this.timer) {
              this.stopPolling();
              this.startPolling();
            }
          }
        }
      }
    } catch (error) {
      console.error("获取轮询间隔配置失败:", error);
    }
  }

  // 设置登录状态
  async setLoginStatus(isLoggedIn) {
    const wasLoggedIn = this.isLoggedIn;
    this.isLoggedIn = isLoggedIn;
    if (isLoggedIn && !wasLoggedIn) {
      // 刚登录，需要检查配置
      await this.checkFeatureEnabled();

      if (this.isEnabled) {
        this.startPolling();
      }
    } else if (!isLoggedIn && wasLoggedIn) {
      // 刚退出登录，停止轮询
      this.stopPolling();
      this.isEnabled = false;
    } else if (isLoggedIn && this.isEnabled) {
      // 已登录且功能开启，确保轮询运行
      if (!this.timer) {
        this.startPolling();
      }
    } else {
      // 未登录或功能关闭，停止轮询
      this.stopPolling();
    }
  }

  // 开始轮询
  startPolling() {
    if (this.timer) {
      this.stopPolling();
    }
    // 立即执行一次
    this.checkForNewArticles();

    // 设置定时器
    this.timer = setInterval(() => {
      this.checkForNewArticles();
    }, this.pollingInterval);
  }

  // 停止轮询
  stopPolling() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }

  // 检查新文章
  async checkForNewArticles() {
    if (!this.isEnabled || !this.isLoggedIn) {
      return;
    }

    try {
      // 使用模拟API，实际项目中替换为真实API
      const response = await getLatestArticles();

      if (response.code === 200 && response.data && response.data.length > 0) {
        // 调用回调函数显示通知
        if (this.callback) {
          this.callback(response.data);
        }
      }
    } catch (error) {
      console.error("检查新文章失败:", error);
    }
  }

  // 手动检查新文章
  manualCheck() {
    this.checkForNewArticles();
  }

  // 手动重新加载轮询间隔配置
  async reloadPollingInterval() {
    if (!this.isLoggedIn) {
      return;
    }

    await this.loadPollingInterval();
  }

  // 销毁管理器
  destroy() {
    this.stopPolling();
    this.callback = null;
    this.isEnabled = false;
    this.isLoggedIn = false;
  }

  // 重新加载配置
  async reloadConfig() {
    if (!this.isLoggedIn) {
      return;
    }

    await this.checkFeatureEnabled();

    if (this.isEnabled) {
      this.startPolling();
    } else {
      this.stopPolling();
    }
  }
}

// 创建单例实例
const articleNotificationManager = new ArticleNotificationManager();

export default articleNotificationManager;
