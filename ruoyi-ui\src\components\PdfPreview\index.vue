<template>
  <div>
    <iframe class="prism-player" :src="pdfUrl" frameborder="0" :width="width" :height="`${height}+px`"></iframe>
  </div>
</template>
<script>
export default {
  name: 'Video',
  props: {
    pdfUrl: { // 视频文件url，必传，支持网络地址 https 和相对地址 require('@/assets/files/Bao.mp4')
      type: String,
      required: true,
      default: null
    },
    width: { // 视频播放器宽度
      type: String,
      default: '100%'
    },
    height: { // 视频播放器高度
      type: Number,
      default: 600
    },
  },
  data () {
    return {
    }
  },
  mounted () {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped></style>