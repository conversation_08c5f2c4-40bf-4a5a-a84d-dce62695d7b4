<template>
  <div id="techBubbleChart" style="width: 100%; height: 100%"></div>
</template>

<script>
import * as echarts from "echarts";
import { technicalData } from "@/api/bigScreen/sanhao.js";

export default {
  name: "TechBubbleChart",
  props: {
    screenSn: {
      type: String,
      default: "1",
    },
  },
  data() {
    return {
      myChart: null,
      option: {},
      // ResizeObserver 实例
      resizeObserver: null,
      // 防抖定时器
      resizeTimer: null,
      color: [
        "#E26B72FF",
        "#C1B973FF",
        "#D56149FF",
        "#4CB5F6FF",
        "#4C85F6FF",
        "#66C9D5FF",
        "#73C193FF",
        "#C1B973FF",
        "#AE66AEFF",
        "#E26B72FF",
        "#C1B973FF",
        "#D56149FF",
        "#4CB5F6FF",
        "#4C85F6FF",
        "#66C9D5FF",
        "#73C193FF",
        "#C1B973FF",
        "#AE66AEFF",
        "#E26B72FF",
        "#C1B973FF",
        "#D56149FF",
        "#4CB5F6FF",
        "#4C85F6FF",
        "#66C9D5FF",
        "#73C193FF",
        "#C1B973FF",
        "#AE66AEFF",
      ],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
      this.setupResizeListeners();
    });
  },
  watch: {
    screenSn: {
      handler() {
        this.initChart();
      },
      immediate: false,
    },
  },
  beforeDestroy() {
    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    // 清理定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    }
    // 清理窗口大小变化监听
    window.removeEventListener("resize", this.handleResize);
    // 清理图表实例
    if (this.myChart) {
      this.myChart.dispose();
    }
  },
  methods: {
    initChart() {
      console.log("techBubbleChart 获取数据，screenSn:", this.screenSn);

      // 如果图表已存在，先销毁
      if (this.myChart) {
        this.myChart.dispose();
      }

      let chartDom = document.getElementById("techBubbleChart");
      if (!chartDom) return;

      this.myChart = echarts.init(chartDom);

      technicalData({ screenSn: this.screenSn })
        .then((res) => {
          const data = res.data.map((item, index) => {
            return {
              id: item.technicalSn,
              name: item.technicalName,
              value: item.totalArticles,
              summary: item.summary,
              // 在弹窗中显示更大的节点
              symbolSize: item.nodeSize
                ? item.nodeSize * 8 + 40 // 比原来更大
                : Math.max(80, Math.min(item.totalArticles / 15, 120)), // 比原来更大
              itemStyle: {
                color: item.backgroundColor
                  ? item.backgroundColor
                  : this.color[index],
              },
              label: {
                color: item.fontColor ? item.fontColor : "#fff",
                fontSize: 16, // 更大的字体
                fontWeight: "bold",
              },
            };
          });

          this.option = {
            tooltip: {
              show: true,
              backgroundColor: "rgba(0, 0, 0, 0.8)",
              borderColor: "#00B2FF",
              borderWidth: 1,
              textStyle: {
                color: "#fff",
                fontSize: 14,
              },
              // formatter: function(params) {
              //   return `
              //     <div style="padding: 10px;">
              //       <div style="font-size: 16px; font-weight: bold; margin-bottom: 8px;">${params.data.name}</div>
              //       <div style="margin-bottom: 4px;">文章数量: ${params.data.value}</div>
              //       <div style="max-width: 300px; word-wrap: break-word;">${params.data.summary || '暂无摘要'}</div>
              //     </div>
              //   `;
              // }
            },
            grid: {
              left: "5%",
              right: "5%",
              bottom: "5%",
              top: "5%",
              containLabel: true,
            },
            series: [
              {
                type: "graph",
                layout: "force",
                force: {
                  repulsion: 200, // 增加排斥力，让节点分散更开
                  edgeLength: [100, 500], // 增加边长范围
                  gravity: 0.05, // 减少重力，让布局更松散
                },
                roam: true,
                label: {
                  show: true,
                  fontSize: 16, // 更大的标签字体
                  fontWeight: "bold",
                },
                edgeSymbol: ["circle", "arrow"],
                edgeSymbolSize: [6, 15], // 更大的边符号
                edgeLabel: {
                  fontSize: 24, // 更大的边标签字体
                },
                data: data,
                emphasis: {
                  focus: "adjacency",
                  label: {
                    fontSize: 18,
                  },
                  itemStyle: {
                    borderWidth: 3,
                    borderColor: "#00B2FF",
                  },
                },
              },
            ],
          };

          this.myChart.setOption(this.option);

          this.myChart.on("click", (params) => {
            this.$emit("openTechnologyDetails", { ...params });
          });

          // 确保图表正确渲染
          setTimeout(() => {
            this.myChart?.resize();
          }, 100);

          console.log("techBubbleChart 数据获取成功:", res.data);
        })
        .catch((error) => {
          console.error("techBubbleChart 数据获取失败:", error);
        });
    },

    // 设置尺寸变化监听
    setupResizeListeners() {
      // 创建 ResizeObserver 监听容器尺寸变化
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(() => {
          // 使用防抖处理，避免频繁触发
          clearTimeout(this.resizeTimer);
          this.resizeTimer = setTimeout(() => {
            this.handleResize();
          }, 100);
        });

        // 监听图表容器的尺寸变化
        const chartContainer = document.getElementById("techBubbleChart");
        if (chartContainer) {
          this.resizeObserver.observe(chartContainer);
        }

        // 也监听父容器的尺寸变化
        const parentContainer = chartContainer?.parentElement;
        if (parentContainer) {
          this.resizeObserver.observe(parentContainer);
        }
      }

      // 监听窗口大小变化（作为备用方案）
      this.handleResize = this.handleResize.bind(this);
      window.addEventListener("resize", this.handleResize);
    },

    // 处理尺寸变化（只做基本的 resize，不重新居中）
    handleResize() {
      if (this.myChart) {
        // 延迟执行 resize，确保 DOM 更新完成
        this.$nextTick(() => {
          this.myChart.resize();
        });
      }
    },

    // 手动触发图表重新调整大小（供父组件调用）
    resizeChart() {
      this.handleResize();
    },
  },
};
</script>

<style lang="scss" scoped>
#techBubbleChart {
  background: transparent;
}
</style>
