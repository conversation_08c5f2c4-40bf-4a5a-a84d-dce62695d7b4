import request from '@/utils/request'

// 查询信息源归类柱状图统计列表
export function listSource(query) {
  return request({
    url: '/large/source/list',
    method: 'get',
    params: query
  })
}

// 查询信息源归类柱状图统计详细
export function getSource(id) {
  return request({
    url: '/large/source/' + id,
    method: 'get'
  })
}

// 新增信息源归类柱状图统计
export function addSource(data) {
  return request({
    url: '/large/source',
    method: 'post',
    data: data
  })
}

// 修改信息源归类柱状图统计
export function updateSource(data) {
  return request({
    url: '/large/source',
    method: 'put',
    data: data
  })
}

// 删除信息源归类柱状图统计
export function delSource(id) {
  return request({
    url: '/large/source/' + id,
    method: 'delete'
  })
}
