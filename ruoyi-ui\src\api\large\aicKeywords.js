import request from '@/utils/request'

// 查询信通院大屏-人工智能关键人物 检索词库列表
export function listAicKeywords(query) {
  return request({
    url: '/large/aicKeywords/list',
    method: 'get',
    params: query
  })
}

// 检索词库列表（排除节点）
export function excludeChild(deptId) {
  return request({
    url: '/large/aicKeywords/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询信通院大屏-人工智能关键人物 检索词库详细
export function getAicKeywords(id) {
  return request({
    url: '/large/aicKeywords/' + id,
    method: 'get'
  })
}

// 新增信通院大屏-人工智能关键人物 检索词库
export function addAicKeywords(data) {
  return request({
    url: '/large/aicKeywords',
    method: 'post',
    data: data
  })
}

// 修改信通院大屏-人工智能关键人物 检索词库
export function updateAicKeywords(data) {
  return request({
    url: '/large/aicKeywords/edit',
    method: 'post',
    data: data
  })
}

// 删除信通院大屏-人工智能关键人物 检索词库
export function delAicKeywords(id) {
  let data = new FormData()
  data.append('id', id)
  return request({
    url: '/large/aicKeywords/remove',
    method: 'post',
    data: data
  })
}
