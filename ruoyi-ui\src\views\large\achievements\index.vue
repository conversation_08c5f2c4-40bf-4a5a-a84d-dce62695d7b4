<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="年份" prop="year">
        <el-input v-model="queryParams.year" placeholder="请输入年份" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="国别" prop="country">
        <el-input v-model="queryParams.country" placeholder="请输入国别" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="成果英文名" prop="englishName">
        <el-input v-model="queryParams.englishName" placeholder="请输入成果英文名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="成果中文名" prop="chineseName">
        <el-input v-model="queryParams.chineseName" placeholder="请输入成果中文名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="公司原名称" prop="originalCompanyName">
        <el-input v-model="queryParams.originalCompanyName" placeholder="请输入公司原名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="公司中文名" prop="chineseCompanyName">
        <el-input v-model="queryParams.chineseCompanyName" placeholder="请输入公司中文名" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="网址" prop="officialWebsite">
        <el-input v-model="queryParams.officialWebsite" placeholder="请输入网址" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="应用领域" prop="applicationArea">
        <el-input v-model="queryParams.applicationArea" placeholder="请输入应用领域" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['large:achievements:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['large:achievements:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['large:achievements:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['large:achievements:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="achievementsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="${comment}" align="center" prop="id" />
      <el-table-column label="年份" align="center" prop="year" />
      <el-table-column label="国别" align="center" prop="country" />
      <el-table-column label="成果英文名" align="center" prop="englishName" />
      <el-table-column label="成果中文名" align="center" prop="chineseName" />
      <el-table-column label="描述" align="center" prop="description" />
      <el-table-column label="公司原名称" align="center" prop="originalCompanyName" />
      <el-table-column label="公司中文名" align="center" prop="chineseCompanyName" />
      <el-table-column label="专家原名称" align="center" prop="originalExperts" />
      <el-table-column label="专家中文名称" align="center" prop="chineseExperts" />
      <el-table-column label="网址" align="center" prop="officialWebsite" />
      <el-table-column label="应用领域" align="center" prop="applicationArea" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['large:achievements:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['large:achievements:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改人工智能-研究成果对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="年份" prop="year">
          <el-input v-model="form.year" placeholder="请输入年份" />
        </el-form-item>
        <el-form-item label="国别" prop="country">
          <el-input v-model="form.country" placeholder="请输入国别" />
        </el-form-item>
        <el-form-item label="成果英文名" prop="englishName">
          <el-input v-model="form.englishName" placeholder="请输入成果英文名" />
        </el-form-item>
        <el-form-item label="成果中文名" prop="chineseName">
          <el-input v-model="form.chineseName" placeholder="请输入成果中文名" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="公司原名称" prop="originalCompanyName">
          <el-input v-model="form.originalCompanyName" placeholder="请输入公司原名称" />
        </el-form-item>
        <el-form-item label="公司中文名" prop="chineseCompanyName">
          <el-input v-model="form.chineseCompanyName" placeholder="请输入公司中文名" />
        </el-form-item>
        <el-form-item label="专家原名称" prop="originalExperts">
          <el-input v-model="form.originalExperts" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="专家中文名称" prop="chineseExperts">
          <el-input v-model="form.chineseExperts" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="网址" prop="officialWebsite">
          <el-input v-model="form.officialWebsite" placeholder="请输入网址" />
        </el-form-item>
        <el-form-item label="应用领域" prop="applicationArea">
          <el-input v-model="form.applicationArea" placeholder="请输入应用领域" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAchievements, getAchievements, delAchievements, addAchievements, updateAchievements } from "@/api/large/achievements";

export default {
  name: "Achievements",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 人工智能-研究成果表格数据
      achievementsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        year: null,
        country: null,
        englishName: null,
        chineseName: null,
        description: null,
        originalCompanyName: null,
        chineseCompanyName: null,
        originalExperts: null,
        chineseExperts: null,
        officialWebsite: null,
        applicationArea: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        year: [
          { required: true, message: "年份不能为空", trigger: "blur" }
        ],
        country: [
          { required: true, message: "国别不能为空", trigger: "blur" }
        ],
        englishName: [
          { required: true, message: "成果英文名不能为空", trigger: "blur" }
        ],
        chineseName: [
          { required: true, message: "成果中文名不能为空", trigger: "blur" }
        ],
        description: [
          { required: true, message: "描述不能为空", trigger: "blur" }
        ],
        originalCompanyName: [
          { required: true, message: "公司原名称不能为空", trigger: "blur" }
        ],
        chineseCompanyName: [
          { required: true, message: "公司中文名不能为空", trigger: "blur" }
        ],
        originalExperts: [
          { required: true, message: "专家原名称不能为空", trigger: "blur" }
        ],
        chineseExperts: [
          { required: true, message: "专家中文名称不能为空", trigger: "blur" }
        ],
        officialWebsite: [
          { required: true, message: "网址不能为空", trigger: "blur" }
        ],
        applicationArea: [
          { required: true, message: "应用领域不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询人工智能-研究成果列表 */
    getList() {
      this.loading = true;
      listAchievements(this.queryParams).then(response => {
        this.achievementsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        year: null,
        country: null,
        englishName: null,
        chineseName: null,
        description: null,
        originalCompanyName: null,
        chineseCompanyName: null,
        originalExperts: null,
        chineseExperts: null,
        officialWebsite: null,
        applicationArea: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加人工智能-研究成果";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getAchievements(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改人工智能-研究成果";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAchievements(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAchievements(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids;
      this.$modal.confirm('是否确认删除人工智能-研究成果编号为"' + ids + '"的数据项？').then(function () {
        return delAchievements(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('large/achievements/export', {
        ...this.queryParams
      }, `achievements_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
