!/**
 * Highcharts JS v12.3.0 (2025-06-21)
 * @module highcharts/modules/dependency-wheel
 * @requires highcharts
 * @requires highcharts/modules/sankey
 *
 * Dependency wheel module
 *
 * (c) 2010-2025 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.SVGElement):"function"==typeof define&&define.amd?define("highcharts/modules/dependency-wheel",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry,t.SVGElement)}):"object"==typeof exports?exports["highcharts/modules/dependency-wheel"]=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.SVGElement):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry,t.Highcharts.SVGElement)}("undefined"==typeof window?this:window,(t,e,a)=>(()=>{"use strict";var r,s={28:t=>{t.exports=a},512:t=>{t.exports=e},944:e=>{e.exports=t}},i={};function n(t){var e=i[t];if(void 0!==e)return e.exports;var a=i[t]={exports:{}};return s[t](a,a.exports,n),a.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var a in e)n.o(e,a)&&!n.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:e[a]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var o={};n.d(o,{default:()=>j});var h=n(944),l=n.n(h),d=n(512),c=n.n(d);let{sankey:{prototype:{pointClass:p}}}=c().seriesTypes,{pInt:u,wrap:g}=l(),f=class extends p{getDataLabelPath(t){let e=this,a=e.series.chart.renderer,r=e.shapeArgs,s=e.angle<0||e.angle>Math.PI,i=r.start||0,n=r.end||0;return e.dataLabelPath?(e.dataLabelPath=e.dataLabelPath.destroy(),delete e.dataLabelPath):g(t,"destroy",function(t){return e.dataLabelPath&&(e.dataLabelPath=e.dataLabelPath.destroy()),t.call(this)}),e.dataLabelPath=a.arc({open:!0,longArc:Math.abs(Math.abs(i)-Math.abs(n))<Math.PI?0:1}).attr({x:r.x,y:r.y,r:(r.r||0)+u(t.options?.distance||0),start:s?i:n,end:s?n:i,clockwise:+s}).add(a.defs),e.dataLabelPath}isValid(){return!0}},{defined:y,getAlignFactor:x,relativeLength:m}=l();!function(t){t.compose=function(t,a){return t.sankeyColumn=new e(t,a),t};class e{constructor(t,e){this.points=t,this.series=e}getTranslationFactor(t){let e=this.points,a=e.slice(),r=t.chart,s=t.options.minLinkWidth||0,i,n=0,o,h=(r.plotSizeY||0)-(t.options.borderWidth||0)-(e.length-1)*t.nodePadding;for(;e.length;){for(n=h/e.sankeyColumn.sum(),i=!1,o=e.length;o--;)e[o].getSum()*n<s&&(e.splice(o,1),h=Math.max(0,h-s),i=!0);if(!i)break}for(let t of(e.length=0,a))e.push(t);return n}top(t){let e=this.series,a=e.nodePadding,r=this.points.reduce((r,s)=>(r>0&&(r+=a),r+=Math.max(s.getSum()*t,e.options.minLinkWidth||0)),0);return x(e.options.nodeAlignment||"center")*((e.chart.plotSizeY||0)-r)}left(t){let e=this.series,a=e.chart,r=e.options.equalNodes,s=a.inverted?a.plotHeight:a.plotWidth,i=e.nodePadding,n=this.points.reduce((a,n)=>(a>0&&(a+=i),a+=r?s/n.series.nodes.length-i:Math.max(n.getSum()*t,e.options.minLinkWidth||0)),0);return((a.plotSizeX||0)-Math.round(n))/2}sum(){return this.points.reduce((t,e)=>t+e.getSum(),0)}offset(t,e){let a=this.points,r=this.series,s=r.nodePadding,i=0,n;if(r.is("organization")&&t.hangsFrom)return{absoluteTop:t.hangsFrom.nodeY};for(let o=0;o<a.length;o++){let h=a[o].getSum(),l=Math.max(h*e,r.options.minLinkWidth||0),d=t.options[r.chart.inverted?"offsetHorizontal":"offsetVertical"],c=t.options.offset||0;if(n=h?l+s:0,a[o]===t)return{relativeTop:i+(y(d)?m(d,l):m(c,n))};i+=n}}}t.SankeyColumnAdditions=e}(r||(r={}));let b=r;var P=n(28),M=n.n(P);let{deg2rad:L}=l(),{addEvent:k,merge:S,uniqueKey:v,defined:T,extend:C}=l();function H(t,e){e=S(!0,{enabled:!0,attributes:{dy:-5,startOffset:"50%",textAnchor:"middle"}},e);let a=this.renderer.url,r=this.text||this,s=r.textPath,{attributes:i,enabled:n}=e;if(t=t||s&&s.path,s&&s.undo(),t&&n){let e=k(r,"afterModifyTree",e=>{if(t&&n){let s=t.attr("id");s||t.attr("id",s=v());let n={x:0,y:0};T(i.dx)&&(n.dx=i.dx,delete i.dx),T(i.dy)&&(n.dy=i.dy,delete i.dy),r.attr(n),this.attr({transform:""}),this.box&&(this.box=this.box.destroy());let o=e.nodes.slice(0);e.nodes.length=0,e.nodes[0]={tagName:"textPath",attributes:C(i,{"text-anchor":i.textAnchor,href:`${a}#${s}`}),children:o}}});r.textPath={path:t,undo:e}}else r.attr({dx:0,dy:0}),delete r.textPath;return this.added&&(r.textCache="",this.renderer.buildText(r)),this}function A(t){let e=t.bBox,a=this.element?.querySelector("textPath");if(a){let t=[],{b:r,h:s}=this.renderer.fontMetrics(this.element),i=s-r,n=RegExp('(<tspan>|<tspan(?!\\sclass="highcharts-br")[^>]*>|<\\/tspan>)',"g"),o=a.innerHTML.replace(n,"").split(/<tspan class="highcharts-br"[^>]*>/),h=o.length,l=(t,e)=>{let{x:s,y:n}=e,o=(a.getRotationOfChar(t)-90)*L,h=Math.cos(o),l=Math.sin(o);return[[s-i*h,n-i*l],[s+r*h,n+r*l]]};for(let e=0,r=0;r<h;r++){let s=o[r].length;for(let i=0;i<s;i+=5)try{let s=e+i+r,[n,o]=l(s,a.getStartPositionOfChar(s));0===i?(t.push(o),t.push(n)):(0===r&&t.unshift(o),r===h-1&&t.push(n))}catch(t){break}e+=s-1;try{let s=e+r,i=a.getEndPositionOfChar(s),[n,o]=l(s,i);t.unshift(o),t.unshift(n)}catch(t){break}}t.length&&t.push(t[0].slice()),e.polygon=t}return e}function w(t){let e=t.labelOptions,a=t.point,r=e[a.formatPrefix+"TextPath"]||e.textPath;r&&!e.useHTML&&(this.setTextPath(a.getDataLabelPath?.(this)||a.graphic,r),a.dataLabelPath&&!r.enabled&&(a.dataLabelPath=a.dataLabelPath.destroy()))}let{animObject:N}=l(),{deg2rad:O}=l(),{pie:F,sankey:R}=c().seriesTypes,{extend:W,merge:Y,relativeLength:_}=l();({compose:function(t){k(t,"afterGetBBox",A),k(t,"beforeAddingDataLabel",w);let e=t.prototype;e.setTextPath||(e.setTextPath=H)}}).compose(M());class B extends R{animate(t){if(!t){let t=N(this.options.animation).duration/2/this.nodes.length,e=0;for(let a of this.nodes){let r=a.graphic;r&&(r.attr({opacity:0}),setTimeout(()=>{a.graphic&&a.graphic.animate({opacity:1},{duration:t})},t*e++))}for(let t of this.points){let e=t.graphic;!t.isNode&&e&&e.attr({opacity:0}).animate({opacity:1},this.options.animation)}}}createNode(t){let e=super.createNode(t);return e.getSum=()=>e.linksFrom.concat(e.linksTo).reduce((t,e)=>t+e.weight,0),e.offset=t=>{let a=t=>t.fromNode===e?t.toNode:t.fromNode,r=0,s=e.linksFrom.concat(e.linksTo),i;s.sort((t,e)=>a(t).index-a(e).index);for(let t=0;t<s.length;t++)if(a(s[t]).index>e.index){s=s.slice(0,t).reverse().concat(s.slice(t).reverse()),i=!0;break}i||s.reverse();for(let e=0;e<s.length;e++){if(s[e]===t)return r;r+=s[e].weight}},e}createNodeColumns(){let t=[b.compose([],this)];for(let e of this.nodes)e.column=0,t[0].push(e);return t}getNodePadding(){return this.options.nodePadding/Math.PI}translate(){let t=this.options,e=2*Math.PI/(this.chart.plotHeight+this.getNodePadding()),a=this.getCenter(),r=(t.startAngle-90)*O,s=t.borderRadius,i="object"==typeof s?s.radius:s;for(let s of(super.translate(),this.nodeColumns[0]))if(s.sum){let n=s.shapeArgs,o=a[0],h=a[1],l=a[2]/2,d=l-_(("auto"===t.nodeWidth?20:t.nodeWidth)||0,l),c=r+e*(n.y||0),p=r+e*((n.y||0)+(n.height||0));for(let a of(s.angle=c+(p-c)/2,s.shapeType="arc",s.shapeArgs={x:o,y:h,r:l,innerR:d,start:c,end:p,borderRadius:i},s.dlBox={x:o+Math.cos((c+p)/2)*(l+d)/2,y:h+Math.sin((c+p)/2)*(l+d)/2,width:1,height:1},s.linksFrom))if(a.linkBase){let s,i,n=a.linkBase.map((n,l)=>{let c=e*n,p=Math.cos(r+c)*(d+1),u=Math.sin(r+c)*(d+1);return s=t.curveFactor||0,(i=Math.abs(a.linkBase[3-l]*e-c))>Math.PI&&(i=2*Math.PI-i),(i*=d)<d&&(s*=i/d),{x:o+p,y:h+u,cpX:o+(1-s)*p,cpY:h+(1-s)*u}});a.shapeArgs={d:[["M",n[0].x,n[0].y],["A",d,d,0,0,1,n[1].x,n[1].y],["C",n[1].cpX,n[1].cpY,n[2].cpX,n[2].cpY,n[2].x,n[2].y],["A",d,d,0,0,1,n[3].x,n[3].y],["C",n[3].cpX,n[3].cpY,n[0].cpX,n[0].cpY,n[0].x,n[0].y]]}}}}}B.defaultOptions=Y(R.defaultOptions,{center:[null,null],curveFactor:.6,startAngle:0,dataLabels:{textPath:{enabled:!1,attributes:{dy:5}}}}),W(B.prototype,{orderNodes:!1,getCenter:F.prototype.getCenter}),B.prototype.pointClass=f,c().registerSeriesType("dependencywheel",B);let j=l();return o.default})());