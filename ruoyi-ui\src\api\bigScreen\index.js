import request from "@/utils/request";

// 报告
export function groupReport(year) {
  return request({
    url: `/screen/report/group/${year}`,
    method: "get",
    params: {
      pageNum: 1,
      pageSize: 1000,
    },
  });
}

// 舆情
export function groupOpinions(year) {
  return request({
    url: `/screen/opinions/group/${year}`,
    method: "get",
    params: {
      pageNum: 1,
      pageSize: 1000,
    },
  });
}

//新闻动态
export function listWarehous() {
  return request({
    url: `/screen/warehous/list`,
    method: "get",
    params: {
      pageNum: 1,
      pageSize: 1000,
    },
  });
}

//关键词
export function listKeyword() {
  return request({
    url: `/screen/keyword/list`,
    method: "get",
    params: {
      pageNum: 1,
      pageSize: 1000,
    },
  });
}

//关键词
export function tempDataTemplate() {
  return request({
    url: `/screen/template/tempData`,
    method: "get",
    params: {
      pageNum: 1,
      pageSize: 1000,
    },
  });
}

//总数

export function totalTemplate() {
  return request({
    url: `/screen/template/total`,
    method: "get",
    params: {
      pageNum: 1,
      pageSize: 1000,
    },
  });
}

//图片轮播
export function screenMonitor() {
  return request({
    url: `/system/config/configKey/screen.monitor.carousel`,
    method: "get",
    params: {
      pageNum: 1,
      pageSize: 1000,
    },
  });
}

// tab2
//关键词
export function listWord() {
  return request({
    url: `/screen/word/list`,
    method: "get",
    params: {
      pageNum: 1,
      pageSize: 1000,
    },
  });
}
//实体
export function groupInfo() {
  return request({
    url: `/screen/info/group`,
    method: "get",
    params: {
      pageNum: 1,
      pageSize: 1000,
    },
  });
}
//实体详情
export function infoScreen(id) {
  return request({
    url: `/screen/info/${id}`,
    method: "get",
    params: {
      pageNum: 1,
      pageSize: 1000,
    },
  });
}
//地图点位
export function listMap() {
  return request({
    url: `/screen/map/list`,
    method: "get",
    params: {
      pageNum: 1,
      pageSize: 1000,
    },
  });
}

//知识图谱
export function atlasNode() {
  return request({
    url: `/screen/node/atlas`,
    method: "get",
    params: {
      pageNum: 1,
      pageSize: 1000,
    },
  });
}


//第一页大屏
export function largeScreenDataMonitoring(data) {
  return request({
    url: `/article/largeScreen/data/monitoring`,
    method: "get",
    params: data,
  });
}
