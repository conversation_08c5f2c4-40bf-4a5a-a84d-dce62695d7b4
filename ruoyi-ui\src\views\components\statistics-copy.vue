<template>
  <div class="father" :style="{height: height +'px'}">
    <div class="topInfo">
      <div class="itemStyle">
        <p class="round">
          <i class="el-icon-s-order"></i>
        </p>
        <div>
          <p>信息总量</p>
          <p>123</p>
        </div>
      </div>
      <div class="itemStyle">
        <p class="round" style="background-color: #2da8fa;">
          正
        </p>
        <div>
          <p>正面信息</p>
          <p>123</p>
        </div>
      </div>
      <div class="itemStyle">
        <p class="round" style="background-color: #ffc70f;">
          中
        </p>
        <div>
          <p>中性信息</p>
          <p>123</p>
        </div>
      </div>
      <div class="itemStyle">
        <p class="round" style="background-color: #f03b23;">
          负
        </p>
        <div>
          <p>负面信息</p>
          <p>123</p>
        </div>
      </div>
    </div>
    <div class="lineParent">
      <div class="title_Info">
        数据汇总
      </div>
      <div class="lineStyle" id="lineBox">

      </div>
    </div>
    <div class="chartsBox">
      <div class="lineParent">
        <div class="title_Info">
          平台类型占比
        </div>
        <div class="cakeBox" id="cake">

        </div>
      </div>
      <div class="lineParent">
        <div class="title_Info">
          情绪趋势
        </div>
        <div class="cakeBox" id="FoldLine">

        </div>
      </div>
    </div>
    <div class="chartsBox">
      <div class="lineParent">
        <div class="title_Info">
          情感属性
        </div>
        <div class="cakeBox" id="rose">

        </div>
      </div>
      <div class="lineParent">
        <div class="title_Info">
          热门词汇
        </div>
        <div class="cakeBox" id="text">

        </div>
      </div>
    </div>
    <div class="lineParent">
      <div class="title_Info">
        数据汇总
      </div>
      <div class="lineStyle" id="colBox">

      </div>
    </div>
    <div class="chartsBox">
      <div class="lineParent">
        <div class="title_Info">
          热门文章占比
        </div>
        <div class="cakeBox">
          <div class="tableStyle">
            <div class="titleTOp">
              <p>序号</p>
              <p>标题</p>
              <p>平台类型</p>
              <p>发布源</p>
            </div>
            <div class="mainTable" v-for="(item, key) in articleList" :key="key">
              <p>{{ key + 1 }}</p>
              <p>{{ item.title }}</p>
              <p>{{ item.type }}</p>
              <p>{{ item.source }}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="lineParent">
        <div class="title_Info">
          属地占比
        </div>
        <div class="cakeBox" id="Annular">

        </div>
      </div>

    </div>
    <div class="nationwide">
      <div class="title_Info" >
        发布地
      </div>
      <div class="nationwideCharts"  id="nationwide">

      </div>
     
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { renderLineCharts, renderCol, renderAnnular, renderNationwide } from '@/utils/renderLine.js'
import { renderTextDom } from '@/utils/textStacking.js'
export default {
  props: {
    height: {
      required: false,
      type: Number,
      default:800
    }
  },
  data () {
    return {
      articleList: [{
        title: '成都大运会博物馆开馆',
        type: '微信公众号',
        source: '新华社'
      }, {
        title: '成都大运会博物馆开馆',
        type: '微信公众号',
        source: '新华社'
      }, {
        title: '成都大运会博物馆开馆',
        type: '微信公众号',
        source: '新华社'
      }, {
        title: '成都大运会博物馆开馆',
        type: '微信公众号',
        source: '新华社'
      }, {
        title: '成都大运会博物馆开馆',
        type: '微信公众号',
        source: '新华社'
      }, {
        title: '成都大运会博物馆开馆',
        type: '微信公众号',
        source: '新华社'
      }, {
        title: '成都大运会博物馆开馆',
        type: '微信公众号',
        source: '新华社'
      }, {
        title: '成都大运会博物馆开馆',
        type: '微信公众号',
        source: '新华社'
      }],
    }
  },
  mounted () {
    this.renderFoldLine()
    this.renderCake()

  },
  methods: {
    /* 平台折线 */
    renderFoldLine () {
      let obj = {
        WeChat: [358, 290, 543, 123, 231, 341],
        Web: [432,257, 366, 368, 432, 213, 312],
      }
      let charts = new renderLineCharts('lineBox',obj)
      charts.render()
      this.renderFoldLineOne()
    },
    /* 区域折线图 */
    renderFoldLineOne () {
      let obj = {
        neutal: [423, 265, 543, 123, 231, 341],
        front: [321, 432, 542, 123, 432, 213, 312],
        negative: [234, 245, 234, 543, 263, 654, 354]
      }
      let charts = new renderLineCharts('FoldLine', obj)
      charts.render()
      this.renderRose()
    },
    renderCake () {
    
       let data = {
        data: [
          { value: 1048, name: "WeChat" },
          { value: 735, name: "Web" },
        ],
        title: true,
        legend: false,
        dom: 'cake'
      }
      new renderAnnular(data)
    
    },
    renderRose () {
      var chartDom = document.getElementById('rose');
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        legend: {
          top: 'bottom',
          show: false
        },
        toolbox: {
          show: false,
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: '72%',
            center: ['50%', '50%'],
            data: [
              { value: 335, name: '正面' },
              { value: 310, name: '中性' },
              { value: 250, name: '负面' },

            ].sort(function (a, b) {
              return a.value - b.value;
            }),
            roseType: 'radius',
            label: {
              color: 'rgba(255, 255, 255, 0.3)'
            },

            labelLine: {
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.3)'
              },
              smooth: 0.2,
              length: 10,
              length2: 20
            },
            itemStyle: {
              color: '#c23531',
              shadowBlur: 200,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
              normal: {
                label: {
                  show: true,
                  position: 'outside',
                  color: 'black',
                  formatter: function (params) {
                    var percent = 0;
                    var total = 0;
                    // for (var i = 0; i < trafficWay.length; i++)
                    // {
                    //   total += trafficWay[i].value;
                    // }
                    total = 1048 + 735
                    percent = ((params.value / total) * 100).toFixed(0);
                    if (params.name !== '')
                    {
                      return params.name + '\n' + '\n' + percent + '%';
                    } else
                    {
                      return '';
                    }
                  },
                },

                labelLine: {
                  length: 30,
                  length2: 20,
                  show: true,
                  color: '#00ffff'
                }
              },

            },
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: function (idx) {
              return Math.random() * 200;
            }
          }
        ]
      };

      option && myChart.setOption(option);
      this.renderText()
    },
    /* 热词图 */
    renderText () {
      renderTextDom('text')
      this.renderColEvent()
    },
    /* 柱状图 */
    renderColEvent () {
      let charts = new renderCol('colBox')
      charts.render()
      this.renderAnnu()
    },
    /* 属地占比-环形图 */
    renderAnnu () {
      let data = {
        data: [{
          name: '黑龙江',
          value: 100,
        }, {
          name: '北京',
          value: 400,
        }, {
          name: '天津',
          value: 200,
        }, {
          name: '贵州',
          value: 120,
        }, {
          name: '新疆',
          value: 140,
        }, {
          name: '四川',
          value: 180,
        }, {
          name: '深圳',
          value: 380,
          }],
        title: true,
        legend: false,
        dom:'Annular'
      }
      new renderAnnular(data)
       this.nationwideRender()
    },
    /* 全国地图 */
    nationwideRender () {
      new renderNationwide('nationwide')
    }
  },
}
</script>

<style lang="scss" scoped>
.father {
  height: 800px;
  overflow: scroll;
}

.topInfo {
  margin-top: 8px;
  box-shadow: 2px 2px 4px 0px #dddddd;
  background-color: rgb(255, 255, 255);
  height: 80px;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .itemStyle {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;

    div {
      margin-left: 20px;
      font-weight: 500;

      p {
        height: 16px;
      }
    }
  }

  .round {
    background-color: rgb(61, 43, 226);
    font-size: 40px;
    text-align: center;
    line-height: 60px;
    color: #ffff;
    width: 60px;
    height: 60px;
    border-radius: 35px;
    margin: 10px 0;

  }
}

.lineParent {
  margin-top: 8px;
  width: 100%;
  padding: 10px 0 0 10px;
  height: 440px;
  box-shadow: 0px 1px 10px 2px #dddddd;
}

.nationwide {
  width: 100%;
  height: 730px;
  box-shadow: 0px 1px 10px 2px #dddddd;
  margin-top: 8px;
  padding: 10px 0 0 10px;
}
.nationwideCharts {
  width: 100%;
  height: 700px;
  // box-shadow: 0px 1px 10px 2px #dddddd;
  // margin-top: 8px;
  // padding: 10px 0 0 10px;
}

.lineStyle {

  width: 100%;
  height: 400px;
}

.chartsBox {
  margin-top: 10px;
  display: flex;
  gap: 10px;

  .cakeBox {
    width: 100%;
    height: 400px;
    background-color: rgb(255, 255, 255);

    // box-shadow: 2px 0px 9px 0px #bebebe;
    .tableStyle {
      width: 98%;
      margin: 0 auto;
      padding-top: 10px;
      list-style: none;
      padding: none;
      display: flex;
      flex-direction: column;


      .titleTOp {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 40px;
        background-color: #4682b4;

        :first-child {
          flex: 1;
        }

        :nth-child(2) {
          flex: 4;
        }

        p {
          flex: 2;
          font-size: 14px;
          text-align: center;
          line-height: 40px;
          height: 40px;
          width: 100%;
          color: #ffffff;
          border: solid 1px rgb(219, 218, 218)
        }
      }

      .mainTable {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 35px;

        :first-child {
          flex: 1;
        }

        :nth-child(2) {
          flex: 4;
        }

        p {
          flex: 2;
          font-size: 14px;
          text-align: center;
          line-height: 35px;
          height: 35px;
          width: 100%;
          color: #252525;
          border: solid 1px rgb(219, 218, 218)
        }

      }
    }

  }
}
</style>