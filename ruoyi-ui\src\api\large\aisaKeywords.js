import request from '@/utils/request'

// 查询信通院大屏 人工智能态势感知 检索词库列表
export function listAisaKeywords(query) {
  return request({
    url: '/large/aisaKeywords/list',
    method: 'get',
    params: query
  })
}
// 检索词库列表（排除节点）
export function excludeChild(deptId) {
  return request({
    url: '/large/aisaKeywords/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询信通院大屏 人工智能态势感知 检索词库详细
export function getAisaKeywords(id) {
  return request({
    url: '/large/aisaKeywords/' + id,
    method: 'get'
  })
}

// 新增信通院大屏 人工智能态势感知 检索词库
export function addAisaKeywords(data) {
  return request({
    url: '/large/aisaKeywords',
    method: 'post',
    data: data
  })
}

// 修改信通院大屏 人工智能态势感知 检索词库
export function updateAisaKeywords(data) {
  return request({
    url: '/large/aisaKeywords/edit',
    method: 'post',
    data: data
  })
}

// 删除信通院大屏 人工智能态势感知 检索词库
export function delAisaKeywords(id) {
  let data = new FormData()
  data.append('id', id)
  return request({
    url: '/large/aisaKeywords/remove',
    method: 'post',
    data: data
  })
}
