import request from "@/utils/request";

// 查询领域列表
export function listField(query) {
  return request({
    url: "/article/field/list",
    method: "get",
    params: query,
  });
}

// 查询领域详细
export function getField(id) {
  return request({
    url: "/article/field/" + id,
    method: "get",
  });
}

// 新增领域
export function addField(data) {
  return request({
    url: "/article/field",
    method: "post",
    data: data,
  });
}

// 修改领域
export function updateField(data) {
  return request({
    url: "/article/field/edit",
    method: "post",
    data: data,
  });
}

// 删除领域
export function delField(id) {
  return request({
    url: "/article/field/remove",
    method: "post",
    data: id,
  });
}
