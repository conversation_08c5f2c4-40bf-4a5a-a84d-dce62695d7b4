import request from '@/utils/request'

// 查询API授权列表
export function listAuthorization(query) {
  return request({
    url: '/article/authorization/list',
    method: 'get',
    params: query
  })
}

// 查询API授权详细
export function getAuthorization(id) {
  return request({
    url: '/article/authorization/' + id,
    method: 'get'
  })
}

// 新增API授权
export function addAuthorization(data) {
  return request({
    url: '/article/authorization',
    method: 'post',
    data: data
  })
}

// 修改API授权
export function updateAuthorization(data) {
  return request({
    url: '/article/authorization/edit',
    method: 'post',
    data: data
  })
}

// 删除API授权
export function delAuthorization(id) {
  return request({
    url: '/article/authorization/remove',
    method: 'post',
    data: id
  })
}

// 获取数据源字典
export function getSnList() {
  return request({
    url: '/article/source/listAll',
    method: 'get'
  })
}

// 查询api数据源
export function getAuthorizationSn(id) {
  return request({
    url: '/article/authorization/assign/detail/' + id,
    method: 'get'
  })
}

// 修改api数据源
export function changeAuthorizationSn(data) {
  return request({
    url: '/article/authorization/assign/source',
    method: 'post',
    data: data
  })
}
