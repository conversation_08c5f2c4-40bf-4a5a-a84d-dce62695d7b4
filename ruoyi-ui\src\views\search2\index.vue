<template>
  <div
    class="app-container"
    v-loading="loading"
    element-loading-text="检索中..."
  >
    <div class="search-init" v-if="isInit">
      <div class="search-init-img">
        <!-- <img src="@/assets/images/xiaoxinsousuo.png" />2 -->
      </div>
      <el-input
        placeholder="请输入内容"
        v-model="searchInput"
        clearable
        class="search-input"
      >
        <el-select
          v-model="searchInputSelect"
          slot="prepend"
          placeholder="请选择"
          class="search-input-select"
          size="medium"
        >
          <el-option
            v-for="item in searchInputSelectList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-button
          slot="append"
          icon="el-icon-search"
          @click="fetchSearchResults"
          >检索</el-button
        >
      </el-input>
    </div>
    <div class="search-result" v-else>
      <div class="search-result-header">
        <div class="search-result-header-img">
          <img src="@/assets/images/xiaoxinsousuo.png" />
        </div>
        <div class="search-result-header-input">
          <el-input
            placeholder="请输入内容"
            v-model="searchInput"
            clearable
            class="search-input"
          >
            <el-select
              v-model="searchInputSelect"
              slot="prepend"
              placeholder="请选择"
              class="search-input-select"
              size="medium"
            >
              <el-option
                v-for="item in searchInputSelectList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="fetchSearchResults"
              @keyup.enter.native="fetchSearchResults"
              >检索</el-button
            >
          </el-input>
        </div>
      </div>
      <div class="search-container">
        <div class="search-left">
          <div class="search-item">
            <div class="search-item-title">相近结果筛选</div>
            <div class="checkedList">
              <el-checkbox-group
                v-model="isCheckedList"
                @change="handleCheckChange"
              >
                <el-row>
                  <el-col :span="12" v-for="item in checkedList" :key="item">
                    <el-checkbox :label="item">{{ item }}</el-checkbox>
                  </el-col>
                </el-row>
              </el-checkbox-group>
            </div>
          </div>
          <div class="search-item">
            <div class="search-item-title">基本信息</div>
            <div class="search-item2-content" v-if="currentPerson">
              <div class="search-item2-content-item">
                <div class="search-item2-content-item-title">姓名：</div>
                <div class="search-item2-content-item-value">
                  {{ currentPerson.name }}
                </div>
              </div>
              <div class="search-item2-content-item">
                <div class="search-item2-content-item-title">联系方式：</div>
                <div class="search-item2-content-item-value">
                  {{ currentPerson.phone }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="search-right">
          <div class="search-item">
            <div class="search-item-title">人物历年所属机构</div>
            <div class="search-item3-content">
              <el-button
                v-for="(item, index) in allCompanies"
                :key="'company-' + index"
                >{{ item }}</el-button
              >
            </div>
          </div>
          <div class="search-item">
            <div class="search-item-title">知识图谱</div>
            <div class="knowledge-graph-legend">
              <div class="legend-item">
                <div class="legend-color" style="background-color: #1890ff;"></div>
                <span>中心人物</span>
              </div>
              <div class="legend-item">
                <div class="legend-color" style="background-color: #5AD8A6;"></div>
                <span>所属机构</span>
              </div>
              <div class="legend-item">
                <div class="legend-color" style="background-color: #FF9845;"></div>
                <span>相关观点</span>
              </div>
              <div class="legend-item">
                <div class="legend-color" style="background-color: #F04864;"></div>
                <span>被提及事由</span>
              </div>
            </div>
            <div class="knowledge-graph-container">
              <div id="knowledge-graph" ref="knowledgeGraph"></div>
            </div>
          </div>
          <div class="search-item">
            <div class="search-item-title">人物相关观点</div>
            <div class="search-item4-content">
              <div
                class="search-item4-content-item"
                v-for="(item, index) in allViews"
                :key="'view-' + index"
              >
                <div class="search-item4-content-item-title">
                  {{ item.view }}
                </div>
                <div
                  class="search-item4-content-item-value"
                  @click="openArticle(item)"
                >
                  引自《{{ item.articleName }}》
                </div>
              </div>
            </div>
          </div>
          <div class="search-item">
            <div class="search-item-title">人物被提及事由</div>
            <div class="search-item4-content">
              <div
                class="search-item4-content-item"
                v-for="(item, index) in allStaffs"
                :key="'staff-' + index"
              >
                <div class="search-item4-content-item-title">
                  {{ item.staff }}
                </div>
                <div
                  class="search-item4-content-item-value"
                  @click="openArticle(item)"
                >
                  引自《{{ item.articleName }}》
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script>
import API from "@/api/ScienceApi/index.js";
import G6 from "@antv/g6";
import mockData from "./mockData.json";

export default {
  data() {
    return {
      checkedList: [], // 搜索结果的人名列表
      isCheckedList: [], // 当前选中的人名列表
      searchResults: [], // 搜索结果数据
      searchInput: "", // 搜索输入内容
      loading: false, // 加载状态
      isInit: true, // 是否初始化
      searchInputSelect: "人物", // 搜索输入选择
      searchInputSelectList: [
        { label: "人物检索", value: "人物" },
        { label: "机构检索", value: "机构" },
        { label: "智能检索", value: "智能检索" },
      ],
      knowledgeGraph: null, // G6图实例
      // 新增：用于控制每个分类显示的子节点数量
      visibleCounts: {
        company: 6,
        view: 6,
        staff: 6
      },
      // 新增：模拟数据
      mockData: null,
      // 新增：拖拽状态
      isDragging: false,
      // 新增：定时器引用
      dragTimer: null,
      zoomTimer: null,
    };
  },
  computed: {
    currentPerson() {
      // 获取第一个被勾选的人物的详细信息
      if (this.isCheckedList.length > 0) {
        // 按照页面显示顺序，查找第一个被勾选的人物
        for (const name of this.checkedList) {
          if (this.isCheckedList.includes(name)) {
            return this.searchResults.find((item) => item.name === name);
          }
        }
      }
      return null;
    },
    // 计算所有选中人物的所属机构
    allCompanies() {
      // 优先使用模拟数据
      if (this.mockData && this.mockData.searchResults) {
        const selectedPersons = this.mockData.searchResults.filter((person) =>
          this.isCheckedList.includes(person.name)
        );
        const companies = [];
        selectedPersons.forEach((person) => {
          if (person.company && person.company.length) {
            companies.push(...person.company);
          }
        });
        return [...new Set(companies)];
      }

      // 原有逻辑作为备用
      if (!this.searchResults || !this.isCheckedList.length) return [];

      const selectedPersons = this.searchResults.filter((person) =>
        this.isCheckedList.includes(person.name)
      );

      const companies = [];
      selectedPersons.forEach((person) => {
        if (person.company && person.company.length) {
          companies.push(...person.company);
        }
      });

      return [...new Set(companies)];
    },
    // 计算所有选中人物的观点
    allViews() {
      // 优先使用模拟数据
      if (this.mockData && this.mockData.searchResults) {
        const selectedPersons = this.mockData.searchResults.filter((person) =>
          this.isCheckedList.includes(person.name)
        );
        const views = [];
        selectedPersons.forEach((person) => {
          if (person.views && person.views.length) {
            views.push(...person.views);
          }
        });
        return views;
      }

      // 原有逻辑作为备用
      if (!this.searchResults || !this.isCheckedList.length) return [];

      const selectedPersons = this.searchResults.filter((person) =>
        this.isCheckedList.includes(person.name)
      );

      const views = [];
      selectedPersons.forEach((person) => {
        if (person.views && person.views.length) {
          views.push(...person.views);
        }
      });

      return views;
    },
    // 计算所有选中人物的被提及事由
    allStaffs() {
      // 优先使用模拟数据
      if (this.mockData && this.mockData.searchResults) {
        const selectedPersons = this.mockData.searchResults.filter((person) =>
          this.isCheckedList.includes(person.name)
        );
        const staffs = [];
        selectedPersons.forEach((person) => {
          if (person.staffs && person.staffs.length) {
            staffs.push(...person.staffs);
          }
        });
        return staffs;
      }

      // 原有逻辑作为备用
      if (!this.searchResults || !this.isCheckedList.length) return [];

      const selectedPersons = this.searchResults.filter((person) =>
        this.isCheckedList.includes(person.name)
      );

      const staffs = [];
      selectedPersons.forEach((person) => {
        if (person.staffs && person.staffs.length) {
          staffs.push(...person.staffs);
        }
      });

      return staffs;
    },
    // 计算知识图谱数据
    knowledgeGraphData() {
      // 检查是否有选中项
      if (!this.isCheckedList.length) {
        console.log("没有选中项");
        return { nodes: [], edges: [] };
      }

      // 优先使用模拟数据，如果没有则使用原始数据
      const hasData = this.mockData && this.mockData.searchResults ? true : (this.searchResults && this.searchResults.length > 0);
      if (!hasData) {
        console.log("没有数据源");
        return { nodes: [], edges: [] };
      }

      console.log("开始生成知识图谱数据:", {
        isCheckedList: this.isCheckedList,
        allCompanies: this.allCompanies,
        allViews: this.allViews,
        allStaffs: this.allStaffs,
        visibleCounts: this.visibleCounts
      });

      const nodes = [];
      const edges = [];

      // 中心节点 - 选中的人物名称
      const centerNodeId = 'center';
      const centerNodeLabel = this.isCheckedList.join('，');
      // 限制中心节点文字长度
      const displayLabel = centerNodeLabel.length > 6 ? centerNodeLabel.substring(0, 6) + '...' : centerNodeLabel;
      nodes.push({
        id: centerNodeId,
        label: displayLabel,
        originalLabel: centerNodeLabel, // 保存完整文字用于悬停显示
        type: 'center',
        x: 300, // 减小坐标，让整体更紧凑
        y: 300,
        size: [50, 20], // 缩小中心节点
        style: {
          fill: '#1890ff',
          stroke: '#1890ff',
          lineWidth: 2,
          radius: 5,
        },
        labelCfg: {
          style: {
            fill: '#fff',
            fontSize: 11,
            fontWeight: 'bold',
          },
        },
      });

      // 机构父节点
      const allCompanies = this.allCompanies;
      if (allCompanies.length > 0) {
        const companyParentId = 'company_parent';
        nodes.push({
          id: companyParentId,
          label: '机构',
          type: 'category',
          x: 300 + 200 * Math.cos(Math.PI * 5 / 6), // 左下方 150度角，增加距离
          y: 300 + 200 * Math.sin(Math.PI * 5 / 6),
          size: [35, 18], // 进一步缩小分类节点
          style: {
            fill: '#5AD8A6',
            stroke: '#5AD8A6',
            lineWidth: 2,
            radius: 4,
          },
          labelCfg: {
            style: {
              fill: '#fff',
              fontSize: 10,
              fontWeight: 'bold',
            },
          },
        });

        edges.push({
          source: centerNodeId,
          target: companyParentId,
          style: {
            stroke: '#5AD8A6',
            lineWidth: 2,
          },
        });

        // 处理机构子节点，支持"更多..."功能
        const visibleCompanies = allCompanies.slice(0, this.visibleCounts.company);
        const hasMoreCompanies = allCompanies.length > this.visibleCounts.company;

        // 如果需要显示"更多..."，则为其预留位置
        const totalDisplayItems = hasMoreCompanies ? visibleCompanies.length + 1 : visibleCompanies.length;

        // 机构子节点 - 花朵状分布
        visibleCompanies.forEach((company, index) => {
          const nodeId = `company_${index}`;
          const angle = (index / totalDisplayItems) * 2 * Math.PI;
          const radius = 80; // 增加分类到子节点的距离
          const displayLabel = company.length > 6 ? company.substring(0, 6) + '...' : company;

          nodes.push({
            id: nodeId,
            label: displayLabel,
            originalLabel: company,
            type: 'company',
            x: (300 + 200 * Math.cos(Math.PI * 5 / 6)) + radius * Math.cos(angle),
            y: (300 + 200 * Math.sin(Math.PI * 5 / 6)) + radius * Math.sin(angle),
            size: [45, 16], // 统一子节点大小
            style: {
              fill: '#A8E6CF',
              stroke: '#5AD8A6',
              lineWidth: 1,
              radius: 3,
            },
            labelCfg: {
              style: {
                fill: '#000',
                fontSize: 9,
              },
            },
          });

          edges.push({
            source: companyParentId,
            target: nodeId,
            style: {
              stroke: '#5AD8A6',
              lineWidth: 1,
            },
          });
        });

        // 添加"更多..."节点
        if (hasMoreCompanies) {
          const moreNodeId = 'company_more';
          const moreIndex = visibleCompanies.length;
          const angle = (moreIndex / totalDisplayItems) * 2 * Math.PI;
          const radius = 80;

          nodes.push({
            id: moreNodeId,
            label: '更多...',
            type: 'more',
            category: 'company',
            x: (300 + 200 * Math.cos(Math.PI * 5 / 6)) + radius * Math.cos(angle),
            y: (300 + 200 * Math.sin(Math.PI * 5 / 6)) + radius * Math.sin(angle),
            size: [45, 16],
            style: {
              fill: '#E8F5E8',
              stroke: '#5AD8A6',
              lineWidth: 1,
              radius: 3,
              lineDash: [3, 3], // 虚线边框
            },
            labelCfg: {
              style: {
                fill: '#5AD8A6',
                fontSize: 9,
                fontWeight: 'bold',
              },
            },
          });

          edges.push({
            source: companyParentId,
            target: moreNodeId,
            style: {
              stroke: '#5AD8A6',
              lineWidth: 1,
              lineDash: [3, 3], // 虚线连接
            },
          });
        }

      }

      // 观点父节点
      const allViews = this.allViews;
      if (allViews.length > 0) {
        const viewParentId = 'view_parent';
        nodes.push({
          id: viewParentId,
          label: '观点',
          type: 'category',
          x: 300 + 200 * Math.cos(Math.PI / 6), // 右下方 30度角，增加距离
          y: 300 + 200 * Math.sin(Math.PI / 6),
          size: [35, 18], // 统一分类节点大小
          style: {
            fill: '#FF9845',
            stroke: '#FF9845',
            lineWidth: 2,
            radius: 4,
          },
          labelCfg: {
            style: {
              fill: '#fff',
              fontSize: 10,
              fontWeight: 'bold',
            },
          },
        });

        edges.push({
          source: centerNodeId,
          target: viewParentId,
          style: {
            stroke: '#FF9845',
            lineWidth: 2,
          },
        });

        // 处理观点子节点，支持"更多..."功能
        const visibleViews = allViews.slice(0, this.visibleCounts.view);
        const hasMoreViews = allViews.length > this.visibleCounts.view;

        const totalDisplayItems = hasMoreViews ? visibleViews.length + 1 : visibleViews.length;

        // 观点子节点 - 花朵状分布
        visibleViews.forEach((view, index) => {
          const nodeId = `view_${index}`;
          const angle = (index / totalDisplayItems) * 2 * Math.PI;
          const radius = 80; // 增加分类到子节点的距离
          const displayLabel = view.view.length > 6 ? view.view.substring(0, 6) + '...' : view.view;

          nodes.push({
            id: nodeId,
            label: displayLabel,
            originalLabel: view.view,
            type: 'view',
            x: (300 + 200 * Math.cos(Math.PI / 6)) + radius * Math.cos(angle),
            y: (300 + 200 * Math.sin(Math.PI / 6)) + radius * Math.sin(angle),
            size: [45, 16], // 统一子节点大小
            style: {
              fill: '#FFD4A3',
              stroke: '#FF9845',
              lineWidth: 1,
              radius: 3,
            },
            labelCfg: {
              style: {
                fill: '#000',
                fontSize: 9,
              },
            },
          });

          edges.push({
            source: viewParentId,
            target: nodeId,
            style: {
              stroke: '#FF9845',
              lineWidth: 1,
            },
          });
        });

        // 添加"更多..."节点
        if (hasMoreViews) {
          const moreNodeId = 'view_more';
          const moreIndex = visibleViews.length;
          const angle = (moreIndex / totalDisplayItems) * 2 * Math.PI;
          const radius = 80;

          nodes.push({
            id: moreNodeId,
            label: '更多...',
            type: 'more',
            category: 'view',
            x: (300 + 200 * Math.cos(Math.PI / 6)) + radius * Math.cos(angle),
            y: (300 + 200 * Math.sin(Math.PI / 6)) + radius * Math.sin(angle),
            size: [45, 16],
            style: {
              fill: '#FFF2E8',
              stroke: '#FF9845',
              lineWidth: 1,
              radius: 3,
              lineDash: [3, 3],
            },
            labelCfg: {
              style: {
                fill: '#FF9845',
                fontSize: 9,
                fontWeight: 'bold',
              },
            },
          });

          edges.push({
            source: viewParentId,
            target: moreNodeId,
            style: {
              stroke: '#FF9845',
              lineWidth: 1,
              lineDash: [3, 3],
            },
          });
        }
      }

      // 事由父节点
      const allStaffs = this.allStaffs;
      if (allStaffs.length > 0) {
        const staffParentId = 'staff_parent';
        nodes.push({
          id: staffParentId,
          label: '事由',
          type: 'category',
          x: 300 + 200 * Math.cos(Math.PI * 3 / 2), // 正上方 270度角（向上），增加距离
          y: 300 + 200 * Math.sin(Math.PI * 3 / 2),
          size: [35, 18], // 统一分类节点大小
          style: {
            fill: '#F04864',
            stroke: '#F04864',
            lineWidth: 2,
            radius: 4,
          },
          labelCfg: {
            style: {
              fill: '#fff',
              fontSize: 10,
              fontWeight: 'bold',
            },
          },
        });

        edges.push({
          source: centerNodeId,
          target: staffParentId,
          style: {
            stroke: '#F04864',
            lineWidth: 2,
          },
        });

        // 处理事由子节点，支持"更多..."功能
        const visibleStaffs = allStaffs.slice(0, this.visibleCounts.staff);
        const hasMoreStaffs = allStaffs.length > this.visibleCounts.staff;

        const totalDisplayItems = hasMoreStaffs ? visibleStaffs.length + 1 : visibleStaffs.length;

        // 事由子节点 - 花朵状分布
        visibleStaffs.forEach((staff, index) => {
          const nodeId = `staff_${index}`;
          const angle = (index / totalDisplayItems) * 2 * Math.PI;
          const radius = 80; // 增加分类到子节点的距离
          const displayLabel = staff.staff.length > 6 ? staff.staff.substring(0, 6) + '...' : staff.staff;

          nodes.push({
            id: nodeId,
            label: displayLabel,
            originalLabel: staff.staff,
            type: 'staff',
            x: (300 + 200 * Math.cos(Math.PI * 3 / 2)) + radius * Math.cos(angle),
            y: (300 + 200 * Math.sin(Math.PI * 3 / 2)) + radius * Math.sin(angle),
            size: [45, 16], // 统一子节点大小
            style: {
              fill: '#FFB3C1',
              stroke: '#F04864',
              lineWidth: 1,
              radius: 3,
            },
            labelCfg: {
              style: {
                fill: '#000',
                fontSize: 9,
              },
            },
          });

          edges.push({
            source: staffParentId,
            target: nodeId,
            style: {
              stroke: '#F04864',
              lineWidth: 1,
            },
          });
        });

        // 添加"更多..."节点
        if (hasMoreStaffs) {
          const moreNodeId = 'staff_more';
          const moreIndex = visibleStaffs.length;
          const angle = (moreIndex / totalDisplayItems) * 2 * Math.PI;
          const radius = 80;

          nodes.push({
            id: moreNodeId,
            label: '更多...',
            type: 'more',
            category: 'staff',
            x: (300 + 200 * Math.cos(Math.PI * 3 / 2)) + radius * Math.cos(angle),
            y: (300 + 200 * Math.sin(Math.PI * 3 / 2)) + radius * Math.sin(angle),
            size: [45, 16],
            style: {
              fill: '#FFE8ED',
              stroke: '#F04864',
              lineWidth: 1,
              radius: 3,
              lineDash: [3, 3],
            },
            labelCfg: {
              style: {
                fill: '#F04864',
                fontSize: 9,
                fontWeight: 'bold',
              },
            },
          });

          edges.push({
            source: staffParentId,
            target: moreNodeId,
            style: {
              stroke: '#F04864',
              lineWidth: 1,
              lineDash: [3, 3],
            },
          });
        }
      }

      // 添加跨分类的关系连线
      if (this.mockData && this.mockData.relationships && this.mockData.relationships.cross_category_relationships) {
        this.mockData.relationships.cross_category_relationships.forEach((rel, index) => {
          let sourceNodeId = null;
          let targetNodeId = null;

          // 查找源节点ID
          if (rel.source_category === 'company') {
            const sourceIndex = allCompanies.slice(0, this.visibleCounts.company).indexOf(rel.source);
            if (sourceIndex !== -1) sourceNodeId = `company_${sourceIndex}`;
          } else if (rel.source_category === 'view') {
            const sourceIndex = allViews.slice(0, this.visibleCounts.view).findIndex(v => v.view === rel.source);
            if (sourceIndex !== -1) sourceNodeId = `view_${sourceIndex}`;
          } else if (rel.source_category === 'staff') {
            const sourceIndex = allStaffs.slice(0, this.visibleCounts.staff).findIndex(s => s.staff === rel.source);
            if (sourceIndex !== -1) sourceNodeId = `staff_${sourceIndex}`;
          }

          // 查找目标节点ID
          if (rel.target_category === 'company') {
            const targetIndex = allCompanies.slice(0, this.visibleCounts.company).indexOf(rel.target);
            if (targetIndex !== -1) targetNodeId = `company_${targetIndex}`;
          } else if (rel.target_category === 'view') {
            const targetIndex = allViews.slice(0, this.visibleCounts.view).findIndex(v => v.view === rel.target);
            if (targetIndex !== -1) targetNodeId = `view_${targetIndex}`;
          } else if (rel.target_category === 'staff') {
            const targetIndex = allStaffs.slice(0, this.visibleCounts.staff).findIndex(s => s.staff === rel.target);
            if (targetIndex !== -1) targetNodeId = `staff_${targetIndex}`;
          }

          // 如果找到了源节点和目标节点，添加连线
          if (sourceNodeId && targetNodeId) {
            edges.push({
              id: `cross_rel_${index}`,
              source: sourceNodeId,
              target: targetNodeId,
              style: {
                stroke: '#999',
                lineWidth: Math.max(1, rel.strength * 2),
                lineDash: [4, 4],
                opacity: 0.5,
              },
              label: rel.relation,
              labelCfg: {
                style: {
                  fill: '#666',
                  fontSize: 8,
                  background: {
                    fill: '#fff',
                    padding: [1, 3],
                    radius: 2,
                    stroke: '#ddd',
                    lineWidth: 1,
                  },
                },
              },
            });
          }
        });
      }

      return { nodes, edges };
    },

  },
  created() {
    // 加载模拟数据
    this.mockData = mockData;

    // 在组件创建时获取路由参数并调用搜索接口
    this.searchInput = this.$route.query.searchInput || "";
    if (this.searchInput) {
      this.fetchSearchResults();
    } else {
      // 如果没有搜索参数，使用模拟数据进行演示
      this.loadMockData();
    }

    // 监听路由变化，当路由参数改变时重新搜索
    this.$watch(
      () => this.$route.query.searchInput,
      (newVal) => {
        if (newVal !== this.searchInput) {
          this.searchInput = newVal || "";
          if (newVal) {
            this.fetchSearchResults();
          } else {
            this.loadMockData();
          }
        }
      }
    );
  },
  methods: {
    // 加载模拟数据
    loadMockData() {
      if (this.mockData && this.mockData.searchResults) {
        this.searchResults = this.mockData.searchResults;
        this.checkedList = this.searchResults.map((item) => item.name);
        this.isCheckedList = this.checkedList.length > 0 ? [this.checkedList[0]] : [];
        this.isInit = false;

        // 初始化知识图谱
        this.$nextTick(() => {
          this.initKnowledgeGraph();
        });
      }
    },

    // 处理复选框变化
    handleCheckChange() {
      // 重置显示数量
      this.visibleCounts = {
        company: 6,
        view: 6,
        staff: 6
      };

      // 更新知识图谱
      this.$nextTick(() => {
        this.updateKnowledgeGraph();
      });
    },

    // 处理"更多..."节点点击
    handleMoreClick(category) {
      // 增加对应分类的显示数量
      this.visibleCounts[category] += 6;

      // 更新知识图谱
      this.$nextTick(() => {
        this.updateKnowledgeGraph();
      });
    },
    // 获取搜索结果
    fetchSearchResults() {
      // 显示加载中
      this.loading = true;
      // 构建请求参数
      const params = {
        text_query: this.searchInput,
        find_type: this.searchInputSelect,
        return_type: "json",
      };

      API.searchGraphData(params)
        .then((response) => {
          // 判断响应是否成功
          if (response.result) {
            this.handleSearchResults(response.result);
          } else {
            this.$message.error("获取搜索结果失败：返回数据格式不正确");
          }
        })
        .catch((error) => {
          console.error("API请求错误:", error);
          this.$message.error(
            "获取搜索结果失败：" + (error.message || "未知错误")
          );


        })
        .finally(() => {
          this.isInit = false;
          this.loading = false;
        });
    },

    // 处理搜索结果数据
    handleSearchResults(data) {
      try {
        let parsedData = data;

        // 处理API返回的字符串格式(带有\n的JSON字符串)
        if (typeof data === "string") {
          if (data === "[]") {
            parsedData = [];
            return;
          }
          parsedData = JSON.parse(JSON.parse(data));
          console.log("解析后的数据:", parsedData);
        }

        // 保存原始数据
        this.searchResults = Array.isArray(parsedData) ? parsedData : [];

        if (this.searchResults.length === 0) {
          this.$message.warning("没有找到相关人物信息");
          return;
        }

        // 处理数据，确保每个对象的结构一致
        this.searchResults = this.searchResults.map((person) => {
          // 处理company字段 - API返回字符串数组
          if (person.company && Array.isArray(person.company)) {
            // 如果是字符串数组，直接使用
            person.company = person.company
              .map(item => typeof item === 'string' ? item : (item.company || ''))
              .filter(Boolean); // 过滤掉null或空值
          } else {
            person.company = [];
          }

          // 处理views字段，确保有必要的字段
          if (person.views && Array.isArray(person.views)) {
            person.views = person.views.filter(
              (item) => item && item.view && item.articleName
            );
          } else {
            person.views = [];
          }

          // 处理staffs字段，确保有必要的字段
          if (person.staffs && Array.isArray(person.staffs)) {
            person.staffs = person.staffs.filter(
              (item) => item && item.staff && item.articleName
            );
          } else {
            person.staffs = [];
          }

          return person;
        });

        console.log("处理后的搜索结果:", this.searchResults);

        // 提取所有人名到复选框列表
        this.checkedList = this.searchResults.map((item) => item.name);

        // 默认只勾选第一个，而不是全部勾选
        this.isCheckedList =
          this.checkedList.length > 0 ? [this.checkedList[0]] : [];



        // 初始化知识图谱 - 确保在DOM更新后执行
        console.log("准备初始化知识图谱，当前数据:", {
          searchResults: this.searchResults,
          isCheckedList: this.isCheckedList,
          isInit: this.isInit
        });

        // 使用setTimeout确保DOM完全更新后再初始化
        setTimeout(() => {
          this.initKnowledgeGraph();
        }, 100);
      } catch (error) {
        console.error("处理搜索结果数据错误:", error);
        this.$message.error("处理搜索结果失败：" + error.message);
      }
    },

    // 打开文章
    openArticle(item) {
      if (item && item.articleSn) {
        window.open(`/expressDetails?articleSn=${item.articleSn}`, "_blank");
      }
    },
    // 初始化知识图谱
    initKnowledgeGraph() {
      console.log("开始初始化知识图谱");
      console.log("当前状态:", {
        isInit: this.isInit,
        hasRef: !!this.$refs.knowledgeGraph,
        searchResultsLength: this.searchResults.length,
        isCheckedListLength: this.isCheckedList.length
      });

      if (!this.$refs.knowledgeGraph) {
        console.log("知识图谱容器不存在，可能是因为页面还在初始化状态");
        // 如果容器不存在，可能是因为页面还在初始化，延迟重试
        if (!this.isInit && this.searchResults.length > 0) {
          setTimeout(() => {
            this.initKnowledgeGraph();
          }, 500);
        }
        return;
      }

      // 如果已存在图实例，先销毁
      if (this.knowledgeGraph) {
        this.knowledgeGraph.destroy();
      }

      // 初始化图实例
      this.initKnowledgeGraphInstance();

      // 渲染数据
      this.updateKnowledgeGraph();

      // 添加窗口大小变化监听器
      this.resizeHandler = () => {
        if (this.knowledgeGraph && this.$refs.knowledgeGraph) {
          const container = this.$refs.knowledgeGraph;
          const containerWidth = container.offsetWidth || 800;
          const containerHeight = container.offsetHeight || 600;
          this.knowledgeGraph.changeSize(containerWidth, containerHeight);
          this.knowledgeGraph.fitView();
        }
      };
      window.addEventListener('resize', this.resizeHandler);
    },
    // 更新知识图谱数据
    updateKnowledgeGraph() {
      const data = this.knowledgeGraphData;
      console.log("知识图谱数据:", data);

      if (data.nodes.length === 0) {
        console.log("没有节点数据");
        return;
      }

      try {
        // 如果图实例存在，只清除数据，不销毁实例
        if (this.knowledgeGraph) {
          this.knowledgeGraph.clear();

          // 设置数据并渲染
          this.knowledgeGraph.data(data);
          this.knowledgeGraph.render();

          // 立即计算合适的缩放比例和位置，确保居中和占满
          if (this.knowledgeGraph && !this.knowledgeGraph.destroyed) {
            // 获取图的边界
            const bbox = this.knowledgeGraph.getGroup().getCanvasBBox();
            const containerWidth = this.knowledgeGraph.getWidth();
            const containerHeight = this.knowledgeGraph.getHeight();

            // 计算缩放比例，让图谱占据容器的85%，确保能看到完整图形
            const padding = 40; // 留出边距
            const scaleX = (containerWidth - padding * 2) / bbox.width;
            const scaleY = (containerHeight - padding * 2) / bbox.height;
            const scale = Math.min(scaleX, scaleY, 1.2); // 限制最大缩放，避免过大

            // 计算居中位置
            const centerX = containerWidth / 2;
            const centerY = containerHeight / 2;
            const bboxCenterX = bbox.x + bbox.width / 2;
            const bboxCenterY = bbox.y + bbox.height / 2;

            // 先重置变换
            this.knowledgeGraph.zoomTo(1, { x: 0, y: 0 }, false);
            this.knowledgeGraph.translate(0, 0, false);

            // 再应用新的变换
            this.knowledgeGraph.zoomTo(scale, { x: centerX, y: centerY }, false);
            this.knowledgeGraph.translate(
              centerX - bboxCenterX * scale,
              centerY - bboxCenterY * scale,
              false // 禁用动画
            );
          }
        } else {
          // 如果没有图实例，重新创建
          this.initKnowledgeGraphInstance();

          if (this.knowledgeGraph) {
            this.knowledgeGraph.data(data);
            this.knowledgeGraph.render();

            // 立即适配，确保居中和占满
            if (this.knowledgeGraph && !this.knowledgeGraph.destroyed) {
              // 同样的适配逻辑
              const bbox = this.knowledgeGraph.getGroup().getCanvasBBox();
              const containerWidth = this.knowledgeGraph.getWidth();
              const containerHeight = this.knowledgeGraph.getHeight();

              const padding = 40;
              const scaleX = (containerWidth - padding * 2) / bbox.width;
              const scaleY = (containerHeight - padding * 2) / bbox.height;
              const scale = Math.min(scaleX, scaleY, 1.2);

              const centerX = containerWidth / 2;
              const centerY = containerHeight / 2;
              const bboxCenterX = bbox.x + bbox.width / 2;
              const bboxCenterY = bbox.y + bbox.height / 2;

              // 先重置变换
              this.knowledgeGraph.zoomTo(1, { x: 0, y: 0 }, false);
              this.knowledgeGraph.translate(0, 0, false);

              // 再应用新的变换
              this.knowledgeGraph.zoomTo(scale, { x: centerX, y: centerY }, false);
              this.knowledgeGraph.translate(
                centerX - bboxCenterX * scale,
                centerY - bboxCenterY * scale,
                false // 禁用动画
              );
            }
          }
        }

        console.log("知识图谱渲染完成");
      } catch (error) {
        console.error("知识图谱渲染错误:", error);
      }
    },

    // 单独的图实例初始化方法
    initKnowledgeGraphInstance() {
      if (!this.$refs.knowledgeGraph) {
        return;
      }

      const container = this.$refs.knowledgeGraph;
      const containerWidth = container.offsetWidth || 800;
      const containerHeight = container.offsetHeight || 600;

      // 创建G6图实例
      this.knowledgeGraph = new G6.Graph({
        container: this.$refs.knowledgeGraph,
        width: containerWidth,
        height: containerHeight,
        renderer: 'svg', // 改用SVG渲染器，通常拖影问题更少
        pixelRatio: 1, // 固定像素比，避免高DPI问题
        modes: {
          default: [
            {
              type: 'drag-canvas',
              enableOptimize: false, // 禁用优化，避免拖影
              shouldUpdate: () => true,
            },
            {
              type: 'zoom-canvas',
              enableOptimize: false, // 禁用优化，避免拖影
              shouldUpdate: () => true,
              sensitivity: 1,
              minZoom: 0.3,
              maxZoom: 3,
            }
          ],
        },
        defaultNode: {
          size: [50, 18],
          type: 'rect',
          style: {
            lineWidth: 1,
            stroke: '#5B8FF9',
            fill: '#C6E5FF',
            radius: 3,
          },
          labelCfg: {
            style: {
              fill: '#000',
              fontSize: 9,
              fontWeight: 'normal',
            },
          },
        },
        defaultEdge: {
          style: {
            stroke: '#e2e2e2',
            lineWidth: 1,
          },
        },
        nodeStateStyles: {
          hover: {
            lineWidth: 2,
          },
        },
        edgeStateStyles: {
          hover: {
            stroke: '#1890ff',
            lineWidth: 2,
          },
        },
        // 防拖影和防抖动配置
        enabledStack: false,
        animate: false, // 禁用所有动画
        animateCfg: {
          duration: 0, // 动画时长为0
        },
        fitView: false,
        groupByTypes: false,
        autoPaint: true,
        localRefresh: false, // 禁用局部刷新
        optimizeZoom: false, // 禁用缩放优化
        // 禁用默认的适配动画
        defaultViewport: {
          zoom: 1,
          x: 0,
          y: 0,
        },
      });

      this.addGraphEventListeners();
    },

    // 单独的事件监听器添加方法
    addGraphEventListeners() {
      if (!this.knowledgeGraph) return;

      try {
        // 节点悬停事件
        this.knowledgeGraph.on('node:mouseenter', (e) => {
          const nodeItem = e.item;
          if (nodeItem && this.knowledgeGraph) {
            this.knowledgeGraph.setItemState(nodeItem, 'hover', true);
            const model = nodeItem.getModel();
            if (model.originalLabel && model.originalLabel !== model.label) {
              this.showTooltip(e, model.originalLabel);
            }
          }
        });

        this.knowledgeGraph.on('node:mouseleave', (e) => {
          const nodeItem = e.item;
          if (nodeItem && this.knowledgeGraph) {
            this.knowledgeGraph.setItemState(nodeItem, 'hover', false);
            this.hideTooltip();
          }
        });

        // 节点点击事件
        this.knowledgeGraph.on('node:click', (e) => {
          const nodeItem = e.item;
          if (nodeItem && this.knowledgeGraph) {
            const model = nodeItem.getModel();
            if (model.type === 'more' && model.category) {
              this.handleMoreClick(model.category);
            }
          }
        });

        // 边悬停事件
        this.knowledgeGraph.on('edge:mouseenter', (e) => {
          const edgeItem = e.item;
          if (edgeItem && this.knowledgeGraph) {
            this.knowledgeGraph.setItemState(edgeItem, 'hover', true);
          }
        });

        this.knowledgeGraph.on('edge:mouseleave', (e) => {
          const edgeItem = e.item;
          if (edgeItem && this.knowledgeGraph) {
            this.knowledgeGraph.setItemState(edgeItem, 'hover', false);
          }
        });

        // SVG渲染器通常不需要复杂的拖影处理
        // 只保留基本的交互状态管理
        this.knowledgeGraph.on('canvas:dragstart', () => {
          this.isDragging = true;
        });

        this.knowledgeGraph.on('canvas:dragend', () => {
          this.isDragging = false;
        });

      } catch (error) {
        console.error("添加事件监听器错误:", error);
      }
    },

    // 显示提示框
    showTooltip(e, text) {
      // 移除已存在的提示框
      this.hideTooltip();

      // 创建提示框
      const tooltip = document.createElement('div');
      tooltip.id = 'knowledge-graph-tooltip';
      tooltip.style.cssText = `
        position: absolute;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        max-width: 200px;
        word-wrap: break-word;
        z-index: 1000;
        pointer-events: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      `;
      tooltip.textContent = text;

      // 添加到页面
      document.body.appendChild(tooltip);

      // 设置位置
      const rect = this.$refs.knowledgeGraph.getBoundingClientRect();
      tooltip.style.left = (e.canvasX + rect.left + 10) + 'px';
      tooltip.style.top = (e.canvasY + rect.top - 30) + 'px';
    },

    // 隐藏提示框
    hideTooltip() {
      const tooltip = document.getElementById('knowledge-graph-tooltip');
      if (tooltip) {
        tooltip.remove();
      }
    },

  },
  mounted() {
    // 如果页面已经有搜索结果，初始化知识图谱
    if (!this.isInit && this.searchResults.length > 0 && this.isCheckedList.length > 0) {
      setTimeout(() => {
        this.initKnowledgeGraph();
      }, 200);
    }
  },
  beforeDestroy() {
    // 清理定时器
    if (this.dragTimer) {
      clearTimeout(this.dragTimer);
      this.dragTimer = null;
    }
    if (this.zoomTimer) {
      clearTimeout(this.zoomTimer);
      this.zoomTimer = null;
    }

    // 移除窗口大小变化监听器
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler);
    }

    // 销毁G6图实例
    if (this.knowledgeGraph) {
      this.knowledgeGraph.destroy();
      this.knowledgeGraph = null;
    }
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  height: calc(100vh - 56px);
  overflow-y: auto;
  position: relative;
}
.search-init {
  width: 50%;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;

  .search-init-img {
    width: 60%;
    margin: 0 auto;
    margin-bottom: 10px;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}

::v-deep .search-input {
  height: 60px;

  .search-input-select {
    width: 150px;
  }

  & > .el-input__inner {
    height: 60px;
    line-height: 60px;
    border: 2px solid #304156;
    border-left: none;
    border-right: none;
    font-size: 18px;
    color: #000;
  }

  .el-input-group__prepend {
    border: 2px solid #304156;
    font-size: 18px;
    color: #000;
    border-radius: 10px 0 0 10px;

    .el-select__caret {
      color: #000;
    }
  }

  .el-input-group__append {
    border: 2px solid #304156;
    font-size: 18px;
    background-color: #304156;
    color: #fff;
    border-radius: 0 10px 10px 0;
    padding: 0 30px;
  }
}

.search-result {
  padding: 0 10%;

  .search-result-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .search-result-header-img {
      width: 220px;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    .search-result-header-input {
      flex: 1;
      padding-left: 10px;
    }
  }

  .search-container {
    display: flex;
    .search-left {
      width: 30%;
    }
    .search-right {
      flex: 1;
      padding-left: 30px;
    }

    .search-item {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px 20px;
      margin-bottom: 20px;

      .search-item2-content {
        .search-item2-content-item {
          margin-bottom: 10px;
          .search-item2-content-item-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 4px;
          }
          .search-item2-content-item-value {
            font-size: 14px;
            color: #606266;
          }
        }
      }

      .search-item3-content {
        display: flex;
        flex-wrap: wrap;
        .el-button {
          margin-bottom: 10px;
          margin-left: 0px;
          margin-right: 10px;
        }
      }

      .search-item4-content {
        display: flex;
        flex-direction: column;
        max-height: 268px;
        overflow-y: auto;
        .search-item4-content-item {
          margin-bottom: 20px;
          .search-item4-content-item-title {
            margin-bottom: 4px;
            color: #303133;
            font-size: 14px;
          }
          .search-item4-content-item-value {
            color: #636ae8;
            cursor: pointer;
            font-size: 14px;
          }
        }
      }
    }

    .search-item-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 15px;
    }
  }
}

::v-deep .el-checkbox__label {
  vertical-align: text-top;
  white-space: normal;
  word-break: break-all;
  width: calc(100% - 10px);
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #636ae8;
  border-color: #636ae8;
}

::v-deep .el-checkbox__inner:hover {
  border-color: #636ae8;
}

::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #636ae8;
}

.knowledge-graph-legend {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
  gap: 20px;

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #606266;

    .legend-color {
      width: 16px;
      height: 16px;
      border-radius: 3px;
      border: 1px solid #ddd;
    }
  }
}

.knowledge-graph-container {
  width: 100%;
  height: 800px; // 增加高度以适应新的布局
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fafafa;

  #knowledge-graph {
    width: 100%;
    height: 100%;

    // SVG特殊样式，防止拖影
    svg {
      width: 100%;
      height: 100%;
      display: block;
      // 强制重绘，防止拖影
      will-change: transform;
      transform: translateZ(0);
      // 确保SVG正确渲染
      shape-rendering: geometricPrecision;
      text-rendering: geometricPrecision;
    }

    // Canvas备用样式
    canvas {
      width: 100%;
      height: 100%;
      display: block;
    }
  }
}

// 知识图谱样式
.knowledge-graph-legend {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
  gap: 20px;

  .legend-item {
    display: flex;
    align-items: center;
    font-size: 12px;

    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;
      margin-right: 6px;
    }
  }
}

.knowledge-graph-container {
  display: flex;
  justify-content: center;
  width: 100%;
  height: 600px; // 设置固定高度

  #knowledge-graph {
    width: 100%;
    height: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #fafafa;
  }
}
</style>
