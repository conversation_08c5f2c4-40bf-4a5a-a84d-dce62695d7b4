<!-- 简报服务 -->
<template>
  <div
    v-loading="$store.state.app.loading"
    class="brieFing"
    element-loading-text="数据加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <div class="topSeach" v-show="!preview">
      <div class="formStyle">
        <div class="formItem">
          <div>
            <div style="margin-right: 10px">简报名称</div>
            <el-input
              v-model="BriefingName"
              placeholder="请输入简报名称"
              clearable
              style="width: 240px"
            />
          </div>
          <div>
            <div style="margin-right: 10px">创建时间</div>
            <el-select placeholder v-model="timeRange">
              <el-option value="all" label="全部">全部</el-option>
              <el-option value="time" label="时间选择器">时间选择器</el-option>
            </el-select>
          </div>
          <div>
            <el-date-picker
              value-format="yyyy-MM-dd HH:mm:ss"
              unlink-panels
              v-if="timeRange == 'time'"
              v-model="TImeInterval"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </div>
          <div>
            <el-button type="primary" @click="getList" :loading="seachLoading"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" @click="resetFields"
              >重置</el-button
            >
          </div>
        </div>
        <div class="iconTop">
          <el-tooltip
            class="item"
            effect="dark"
            content="图片视图"
            placement="bottom"
          >
            <p
              class="el-icon-menu"
              :style="{ color: switchView == '图片视图' ? '#1890ff' : '' }"
              @click="switchView = '图片视图'"
            ></p>
          </el-tooltip>
          <el-tooltip
            class="item"
            effect="dark"
            content="列表视图"
            placement="bottom"
          >
            <p
              class="el-icon-s-operation"
              :style="{ color: switchView == '列表视图' ? '#1890ff' : '' }"
              @click="switchView = '列表视图'"
            ></p>
          </el-tooltip>
        </div>
      </div>
    </div>
    <mainBriefingVue
      ref="mainBriefing"
      :preview="preview"
      :switchView="switchView"
      :tableData="tableData"
      :total="total"
      :pageCurrent="pageCurrent"
      :pageSize="pageSize"
      @deleteBrie="deleteBrie"
      :timeData="timeData"
      @editTime="editTime"
      @handleCurrentChange="handleCurrentChange"
      @handleSizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import mainBriefingVue from "@/views/components/mainBriefing.vue";
import API from "@/api/ScienceApi/briefing.js";
import { listRole, allocatedUserList } from "@/api/system/role";
export default {
  components: {
    mainBriefingVue,
  },
  data() {
    return {
      preview: false,
      BriefingName: "",
      timeRange: "",
      switchView: "图片视图",
      visble: false,
      TImeInterval: [],
      total: 0,
      pageSize: 50,
      pageCurrent: 1,
      tableData: [],
      distinguish: "生成简报",
      frequency: "" /* 出发频率 */,
      timeData: [] /* 定时任务列表 */,
      loadingOpen: false,
      editData: {},
      seachLoading: false,
      editorUsers: [], // 编辑角色用户列表
      chiefEditorUsers: [], // 主编角色用户列表
    };
  },
  watch: {
    switchView: function (newVal, oldVal) {
      this.pageCurrent = 1;
      this.pageSize = 50;
      this.resetFields();
      this.getList();
    },
  },
  created() {
    this.getList();
    this.getRolesAndUsers();
  },
  methods: {
    resetFields() {
      this.BriefingName = "";
      this.timeRange = "";
      this.frequency = "";
      this.TImeInterval = "";
    },
    /* 简报列表 */
    async getList() {
      this.loadingOpen = true;
      this.seachLoading = true;
      if (this.seachLoading == false) return;
      let params = {
        pageSize: this.pageSize,
        pageNum: this.pageCurrent,
        title: this.BriefingName,
        startTime: this.TImeInterval[0],
        endTime: this.TImeInterval[1],
        productType: this.$route.query.productType,
        reportStatus: "2",
      };
      await API.queryReportList(params)
        .then((response) => {
          if (response.code == 200) {
            this.tableData = response.rows.sort(
              (a, b) =>
                new Date(b.createTime).getTime() -
                new Date(a.createTime).getTime()
            );
            this.tableData = this.tableData.map((item) => {
              return {
                ...item,
                inputShow: false,
              };
            });
            this.total = response.total;
          } else {
            this.$message({
              message: "简报数据获取失败,请反馈给管理员",
              type: "error",
            });
          }
        })
        .catch((err) => {
          this.$message({
            message: "简报数据获取失败,请反馈给管理员",
            type: "error",
          });
        });

      this.loadingOpen = false;
      setTimeout(() => {
        this.seachLoading = false;
      }, 1000);
    },
    /* 页码变化 */
    handleCurrentChange(current) {
      this.pageCurrent = current;
      this.getList();
      document.querySelector(".mainBriefing_imgView") &&
        document.querySelector(".mainBriefing_imgView").scrollTo(0, 0);
      document.querySelector(".mainBriefing") &&
        document.querySelector(".mainBriefing").scrollTo(0, 0);
      window.scrollTo(0, 0);

      // 处理列表视图表格滚动
      this.$nextTick(() => {
        const mainBriefingRef = this.$refs.mainBriefing;
        if (mainBriefingRef && mainBriefingRef.$refs.tableRef) {
          mainBriefingRef.$refs.tableRef.bodyWrapper.scrollTop = 0;
        }
      });
    },
    /* 数量变化 */
    handleSizeChange(size) {
      this.pageSize = size;
      this.getList();
      document.querySelector(".mainBriefing_imgView") &&
        document.querySelector(".mainBriefing_imgView").scrollTo(0, 0);
      document.querySelector(".mainBriefing") &&
        document.querySelector(".mainBriefing").scrollTo(0, 0);
      window.scrollTo(0, 0);

      // 处理列表视图表格滚动
      this.$nextTick(() => {
        const mainBriefingRef = this.$refs.mainBriefing;
        if (mainBriefingRef && mainBriefingRef.$refs.tableRef) {
          mainBriefingRef.$refs.tableRef.bodyWrapper.scrollTop = 0;
        }
      });
    },
    /* 删除简报 */
    deleteBrie(id) {
      this.$confirm("您确定要删除这条数据吗", "删除提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let res;
          if (this.switchView !== "定时任务") {
            res = await API.removeBriefing([id]);
          } else {
            res = await API.removeTime([id]);
          }
          if (res.code == 200) {
            this.$message({ message: "删除成功", type: "success" });
            this.getList();
          } else {
            this.$message({ message: "删除失败，请联系管理员", type: "error" });
          }
        })
        .catch(() => {
          /* 错误捕捉 */
        });
    },
    /* 修改定时任务 */
    editTime(item) {
      this.visble = true;
      this.editData = item;
      this.distinguish = "查看定时任务";
    },
    /* 获取角色和用户列表 */
    async getRolesAndUsers() {
      try {
        // 获取所有角色
        const roleResponse = await listRole({});
        const roles = roleResponse.rows || [];

        // 查找编辑和主编角色
        const editorRole = roles.find((role) => role.roleName === "编辑");
        const chiefEditorRole = roles.find((role) => role.roleName === "主编");

        if (editorRole) {
          // 获取编辑角色的用户列表
          const editorUsersResponse = await allocatedUserList({
            roleId: editorRole.roleId,
            pageNum: 1,
            pageSize: 1000,
          });
          this.editorUsers = editorUsersResponse.rows || [];
        }

        if (chiefEditorRole) {
          // 获取主编角色的用户列表
          const chiefEditorUsersResponse = await allocatedUserList({
            roleId: chiefEditorRole.roleId,
            pageNum: 1,
            pageSize: 1000,
          });
          this.chiefEditorUsers = chiefEditorUsersResponse.rows || [];
        }
      } catch (error) {
        console.error("获取角色和用户列表失败:", error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.brieFing {
  height: calc(100vh - 94px);
}

.topSeach {
  width: 100%;
  line-height: 40px;
  box-shadow: 1px 1px 16px 6px #efefef;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
}

.formStyle {
  padding: 20px;
  display: flex;
  justify-content: space-between;

  .formItem {
    display: flex;
    align-items: center;
    gap: 10px;

    & > div {
      display: flex;
      align-items: center;
    }
  }
}

.iconTop {
  margin-right: 20px;
  display: flex;
  align-items: center;

  p {
    font-size: 28px;
    margin: 0 5px;
    color: rgb(90, 89, 89);
  }
}
</style>
