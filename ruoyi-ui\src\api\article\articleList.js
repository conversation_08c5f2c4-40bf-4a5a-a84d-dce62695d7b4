import request from '@/utils/request'

// 查询文章列列表
export function listList(query) {
  return request({
    url: '/myarticle/articleList/list',
    method: 'get',
    params: query
  })
}

export function getlistByUser(query) {
  return request({
    url: '/myarticle/articleList/getlistByUser',
    method: 'get',
    params: query
  })
}

// 查询文章列详细
export function getList(id) {
  return request({
    url: '/myarticle/articleList/' + id,
    method: 'get'
  })
}

// 新增文章列
export function addList(data) {
  return request({
    url: '/myarticle/articleList',
    method: 'post',
    data: data
  })
}

// 修改文章列
export function updateList(data) {
  return request({
    url: '/myarticle/articleList',
    method: 'put',
    data: data
  })
}

// 删除文章列
export function delList(id) {
  return request({
    url: '/myarticle/articleList' + id,
    method: 'delete'
  })
}

//领取任务
export function getTask() {
  return request({
    url: '/myarticle/articleList/getTask',
    method: 'get'
  })
}

