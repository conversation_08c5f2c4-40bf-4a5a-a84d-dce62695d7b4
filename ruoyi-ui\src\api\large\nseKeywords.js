import request from '@/utils/request'

// 查询信通院大屏 企业列 检索词库列表
export function listNseKeywords(query) {
  return request({
    url: '/large/nseKeywords/list',
    method: 'get',
    params: query
  })
}

// 检索词库列表（排除节点）
export function excludeChild(deptId) {
  return request({
    url: '/large/nseKeywords/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询信通院大屏 企业列 检索词库详细
export function getNseKeywords(id) {
  return request({
    url: '/large/nseKeywords/' + id,
    method: 'get'
  })
}

// 新增信通院大屏 企业列 检索词库
export function addNseKeywords(data) {
  return request({
    url: '/large/nseKeywords',
    method: 'post',
    data: data
  })
}

// 修改信通院大屏 企业列 检索词库
export function updateNseKeywords(data) {
  return request({
    url: '/large/nseKeywords/edit',
    method: 'post',
    data: data
  })
}

// 删除信通院大屏 企业列 检索词库
export function delNseKeywords(id) {
  let data = new FormData()
  data.append('id', id)
  return request({
    url: '/large/nseKeywords/remove',
    method: 'post',
    data: data
  })
}
