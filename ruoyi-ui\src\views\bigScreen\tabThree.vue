<template>
  <div style="height: 100%; display: flex" class="three">
    <!-- <div class="three-bg">
      <img src="@/assets/bigScreen/bgThree.png" alt="" />
    </div> -->
    <!-- 左侧 -->
    <el-col style="width: 650px; height: 100%">
      <el-row class="left">
        <!-- 上半部分 -->
        <!-- ecahrt 柱状图 -->
        <el-row class="left-top">
          <el-row style="margin-bottom: 10px">
            <img
              src="@/assets/bigScreen/section-title-info.png"
              alt=""
              style="width: 100%;margin-top: 26px;"
            />
            <span class="title-linear">研究报告</span>
          </el-row>
          <barEchartVue :valueObj="barList" />
        </el-row>
        <!-- ecahrt 折线图 -->
        <el-row class="left-center">
          <el-row style="margin-bottom: 10px">
            <img
              src="@/assets/bigScreen/section-title-info.png"
              alt=""
              style="width: 100%;margin-top: 26px;"
            />
            <span class="title-linear">舆情参考</span>
          </el-row>
          <div
            style="
              width: 100%;
              height: 345px;
              background: linear-gradient(to bottom, #1c0bda60, #1b5bff60);
            "
          >
            <linEchartVue :valueObj="lineList" />
          </div>
        </el-row>
      </el-row>
    </el-col>
    <!-- 中间 -->
    <el-col class="center">
      <div style="width: 100%;margin-top: 26px;">
        <chartNumVue :number="number" />
      </div>

      <div class="card-all">
        <div v-for="(item, index) in allCard" class="card">
          <div class="card-single">
            <div>{{ item.value }}</div>
            <div>{{ item.title }}</div>
          </div>
        </div>
      </div>

      <!-- 轮播 -->
      <div class="banner">
        <div class="swiper-container" id="homeBanner">
          <div class="swiper-wrapper">
            <div
              class="swiper-slide"
              v-for="(item, index) of imgs"
              :key="index"
              @click="jumpFun(item.jumpurl)"
            >
              <!-- require('@/assets/bigScreen/wenxian.png') -->
              <!-- {{ baseUrl + item.cover }} -->
              <img
                :src="item.url + item.cover"
                style="
                  object-fit: cover;
                  width: 212px;
                  height: 278px;
                  min-height: 100%;
                  margin: 0 auto;
                "
              />
              <div class="swiper-info">{{ item.name }}</div>
            </div>
          </div>
          <!-- 如果需要分页器 -->
          <div class="swiper-pagination"></div>
        </div>
      </div>

      <div class="center-bottom">
        <!-- <img :src="require('@/assets/bigScreen/ligh.png')" alt="" /> -->
      </div>
      <div class="center-bottom">
        <!-- <img :src="require('@/assets/bigScreen/ligh.png')" alt="" /> -->
      </div>
    </el-col>
    <!-- 右边 -->

    <el-col style="width: 650px; height: 100%">
      <el-row class="right">
        <!-- 上半部分 -->
        <el-row class="right-top">
          <el-row style="margin-bottom: 10px">
            <img
              src="@/assets/bigScreen/section-title-info.png"
              alt=""
              style="width: 100%;margin-top: 26px;"
            />
            <span class="title-linear">入库报告</span>
          </el-row>
          <div class="table">
            <div class="table_main">
              <div :class="{ 'scoll-Table': hotList.length > 10 }">
                <div
                  class="table_col"
                  v-for="(item, index) in hotList"
                  :key="index"
                >
                  <div>
                    <div
                      style="
                        width: 10px;
                        height: 10px;
                        background: #1bdcff;
                        margin: auto;
                      "
                    ></div>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="item.reportName"
                      placement="top-start"
                    >
                      <div class="last-child" @click="jumpFun(item.reportUrl)">
                        {{ item.reportName }}
                      </div>
                    </el-tooltip>
                    <div>{{ item.reportDate }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-row>
        <!-- 下半部分 -->
        <!-- 热词 -->
        <el-row class="right-bottom">
          <el-row style="margin-bottom: 10px">
            <img
              src="@/assets/bigScreen/section-title-info.png"
              alt=""
              style="width: 100%;margin-top: 26px;"
            />
            <span class="title-linear">关键词</span>
          </el-row>
          <div
            style="
              width: 100%;
              height: 260px;
              background: linear-gradient(to bottom, #1c0bda90, #060d5b90);
              padding: 12px 20px;
            "
          >
            <keyWordVue :wordValueList="wordValueList" />
          </div>
        </el-row>
      </el-row>
    </el-col>
  </div>
</template>
<script >
import barEchartVue from "./components/barEchart.vue";
import linEchartVue from "./components/linEchart.vue";
import chartNumVue from "./components/chartNum.vue";
import pieEchartVue from "./components/pieEchart.vue";
import keyWordVue from "./components/keyWord.vue";
import {
  groupReport,
  groupOpinions,
  listWarehous,
  listKeyword,
  tempDataTemplate,
  totalTemplate,
  screenMonitor,
} from "@/api/bigScreen/index.js";
export default {
  data() {
    return {
      hotList: [], //滚动
      barList: {
        xData: [],
        yData: [],
      },
      lineList: {
        xData: [],
        yData: [],
      },
      allCard: [],
      imgs: [
        // { url: require("@/assets/bigScreen/wenxian.png") },
        // { url: require("@/assets/bigScreen/xiaoxi.png") },
        // { url: require("@/assets/bigScreen/baogao.png") },
      ],
      wordValueList: [],
      number: 0,
      // baseUrl: process.env.VUE_APP_BASE_API,
    };
  },
  components: {
    barEchartVue,
    linEchartVue,
    chartNumVue,
    pieEchartVue,
    keyWordVue,
  },
  created() {
    //
    this.init();
  },
  mounted() {
    //轮播
  },
  beforeDestroy() {},
  methods: {
    init() {
      //左一
      groupReport("2024").then((res) => {
        let color = ["#089EDF", "#1BDCFF", "#F2FDFF"];
        this.barList = {
          xData: res.data.chartData.map((item) => {
            return new Date(item.time).getMonth() + 1 + "月";
          }),
          yData: [],
        };
        let yData = [];
        for (let key in res.data.dictMap) {
          const data = [];

          res.data.chartData.map((item) => {
            data.push(item[key] ? item[key] : null);
          });
          yData.push({
            name: res.data.dictMap[key],
            type: "bar",
            barWidth: 20,
            stack: "Ad",
            emphasis: {
              focus: "series",
            },
            data: data,
            itemStyle: {
              color: color[key - 1],
            },
          });
        }
        this.barList.yData = yData;
      });
      //左二
      groupOpinions("2024").then((res) => {
        let color = ["#039583", "#F76767", "#9E00FF", "#1BDCFF"];
        this.lineList = {
          xData: res.data.chartData.map((item) => {
            return new Date(item.time).getMonth() + 1 + "月";
          }),
          yData: [],
        };
        let yData = [];
        for (let key in res.data.dictMap) {
          const data = [];

          res.data.chartData.map((item) => {
            data.push(item[key] ?? null);
          });
          yData.push({
            name: res.data.dictMap[key],
            type: "line",
            stack: "Total",
            data: data,
            //短线处理
            connectNulls: true,
            smooth: true,
            itemStyle: {
              color: color[key - 1],
            },
            symbolSize: 8,
            lineStyle: {
              width: 2, // 设置线宽为5
            },
          });
        }
        this.lineList.yData = yData;
      });
      //右一
      listWarehous().then((res) => {
        this.hotList = res.rows;
      });
      //右二
      listKeyword().then((res) => {
        this.wordValueList = res.rows;
      });
      //3 个 tab
      tempDataTemplate().then((res) => {
        this.allCard = res.data;
      });
      //总数
      totalTemplate().then((res) => {
        this.number = res.data;
      });
      //轮播图
      screenMonitor().then((res) => {
        this.imgs = JSON.parse(res.msg);
        this.swiperFun();
      });
    },
    swiperFun() {
      this.$nextTick(() => {
        new Swiper("#homeBanner", {
          loop: true,
          slidesPerView: 3,
          spaceBetween: 0,
          centeredSlides: true,
          autoplay: 3000,
          autoplayDisableOnInteraction: false,
          pagination: ".swiper-pagination",
          paginationClickable: true,
        });
      });
    },
    jumpFun(url) {
      window.open(url, "_blank");
    },
  },
};
</script>
<style lang="scss" scoped>
.three-bg {
  opacity: 0.4;
  position: absolute;
  top: -100px;
  left: 0px;
  width: 100%;
  height: 100%;
  z-index: 999999;
  pointer-events: none;
}
.three {
  position: relative;
}
.left {
  margin: 20px 10px 0 50px;
  height: 100%;
  display: flex;
  flex-direction: column;

  > div {
    margin-bottom: 10px;
  }
  z-index: 99999;
}
.center {
  height: 100%;
  position: relative;
  flex: 1;
  .card-all {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    .card {
      height: 68px;
      width: 157px;

      background: url("../../assets/bigScreen/Item.png") no-repeat 0px 0px !important;
      background-size: 100% 100% !important;

      margin: 0 4px;
      color: #fff;
      .card-single {
        background: linear-gradient(to bottom, #0756f9, #068dfc, #05c4ff);
        margin: 2px 1px;
        height: 64px;
        border-radius: 3px;
        > div:first-child {
          padding-top: 10px;
          font-size: 24px;
          text-align: center;
          font-family: "League-Gothic-Regular";
        }
        > div:last-child {
          // margin-top: 5px;
          font-size: 12px;
          font-weight: bold;
          text-align: center;
        }
      }
    }
  }
  .center-bottom {
    width: 794px;
    height: 198px;
    background: url("../../assets/bigScreen/ligh.png") no-repeat 0px 0px !important;
    background-size: 100% 100% !important;
    transform: translate(-50%, -50%);
    position: absolute;
    bottom: 10px;
    left: 50%;
    z-index: 9;
    img {
      width: 800px;
    }
  }
  .banner {
    margin: 0 auto;
    margin-top: 90px;
    width: 510px;
  }
}
.right {
  margin: 20px 50px 0 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  z-index: 99999;
  .title {
    font-size: 22px;
    font-weight: bold;
    color: #a8d3e4;
    // height: 100px;
  }
  .right-top {
    .table {
      background: linear-gradient(to bottom, #1c0bda60, #1b5bff60);
      padding: 10px 15px;
      width: 100%;
      margin: 0 auto;
      height: 480px;

      .table_main {
        width: 100%;
        height: calc(100% - 25px);
        // margin-top: 20px;
        overflow: hidden;
        z-index: 0;

        .table_col {
          width: 100%;
          height: 44px;
          line-height: 44px;
          border-bottom: 1px dotted #1bdcff60;

          display: flex;
          color: #ffff;
          padding-left: 10px;
          font-size: 14px;

          .last-child {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-left: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
          }
          > div {
            width: 100%;
            display: flex;
            justify-content: space-between;
            > div:last-child {
              color: #3bb0d5;
              font-size: 16px;
              width: 170px;
              text-align: right;
            }
          }
        }
        @keyframes scoll-Table {
          from {
            transform: translate(0, 0px);
          }

          to {
            transform: translate(0, calc(((85vh - 20px) / 3 - 90px) - 400px));
          }
        }

        .scoll-Table {
          animation: scoll-Table 7s linear infinite;
          transition: all 1s ease-out;
          //  animation-timing-function: linear;
          // animation-fill-mode: forwards;
          /* 在动画结束后保持最后一个关键帧的状态 */
        }

        /* 鼠标进入 */
        .scoll-Table:hover {
          animation-play-state: paused;
        }

        /* 鼠标离开 */
        .scoll-Table:not(:hover) {
          animation-play-state: running;
        }
      }
    }
  }
  .right-bottom {
    margin-top: 10px;
  }
}
.title-linear {
  position: absolute;
  top: 30px;
  left: 100px;
  font-size: 24px;
  font-weight: bold;
  color: linear-gradient(to right, #fff, #fee4c4, #fec989);
  background: linear-gradient(to right, #fff, #fee4c4, #fec989);
  /* 使用渐变背景时，通常需要设置这些值以确保文字可读 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
</style>

<style lang="scss">
.banner {
  height: 500px;
  #homeBanner {
    width: 500px;
    height: 320px;
    margin: 0 auto;
    z-index: 1;

    .swiper-container {
      position: relative;
      width: 100%;
    }
    .swiper-slide {
      text-align: center;
      font-size: 18px;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: 300ms;
      transform: scale(0.8);
      z-index: 88;
      height: 278px;
    }
    .swiper-slide-active,
    .swiper-slide-duplicate-active {
      transform: scale(1);
      z-index: 99999;
    }
    .swiper-info {
      position: absolute;
      font-size: 40px;
      width: 100px;
      letter-spacing: 10px;
      line-height: 1.1;
      font-weight: bold;
      margin-top: -15px;
      margin-left: 10px;
      color: linear-gradient(to bottom, #fff, #8dedff, #1bdcff);
      background: linear-gradient(to bottom, #fff, #8dedff, #1bdcff);
      /* 使用渐变背景时，通常需要设置这些值以确保文字可读 */
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-family: "Alien-Encounters";
    }
  }

  .swiper-pagination {
    left: 0px;
    bottom: 3px;
    .swiper-pagination-bullet {
      cursor: pointer;
      width: 16px;
      height: 16px;
      margin: auto 15px;
      display: inline-block;
      border-radius: 7px;
      background: rgba($color: #fff, $alpha: 1);
      opacity: 1 !important;
    }

    .swiper-pagination-bullet-active {
      background: #1bdcff;
    }
  }
}
</style>
