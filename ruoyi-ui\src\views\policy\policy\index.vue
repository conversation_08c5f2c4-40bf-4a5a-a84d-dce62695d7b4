<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="政策唯一标识" prop="sn">
        <el-input v-model="queryParams.sn" placeholder="请输入政策唯一标识" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="索 引 号" prop="indexNumber">
        <el-input v-model="queryParams.indexNumber" placeholder="请输入索 引 号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="成文日期" prop="writtenTime">
        <el-date-picker clearable v-model="queryParams.writtenTime" type="date" value-format="yyyy-MM-dd"
          placeholder="请选择成文日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="文号，如“国函〔2024〕125号”。" prop="documentNumber">
        <el-input v-model="queryParams.documentNumber" placeholder="请输入文号，如“国函〔2024〕125号”。" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="文件层级，市人大" prop="fileLevel">
        <el-input v-model="queryParams.fileLevel" placeholder="请输入文件层级，市人大" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="发布机构分类" prop="policyUnitClass">
        <el-input v-model="queryParams.policyUnitClass" placeholder="请输入发布机构分类" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="主题分类" prop="policyTopicClass">
        <el-input v-model="queryParams.policyTopicClass" placeholder="请输入主题分类" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="政策网站采集来源网站地址" prop="sourceBaseUrl">
        <el-input v-model="queryParams.sourceBaseUrl" placeholder="请输入政策网站采集来源网站地址" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="政策网站采集，来源唯一标识" prop="sourceSn">
        <el-input v-model="queryParams.sourceSn" placeholder="请输入政策网站采集，来源唯一标识" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="关键字，用于搜索，如“中新天津生态城”、“绿色发展示范区”、“实施方案”等，多个关键字可用逗号分隔。" prop="keywords">
        <el-input v-model="queryParams.keywords" placeholder="请输入关键字，用于搜索，如“中新天津生态城”、“绿色发展示范区”、“实施方案”等，多个关键字可用逗号分隔。"
          clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="地区编码" prop="publishCode">
        <el-input v-model="queryParams.publishCode" placeholder="请输入地区编码" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="发布地区" prop="publishArea">
        <el-input v-model="queryParams.publishArea" placeholder="请输入发布地区" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="发布日期，如“2024-08-03”。" prop="publishTime">
        <el-date-picker clearable v-model="queryParams.publishTime" type="date" value-format="yyyy-MM-dd"
          placeholder="请选择发布日期，如“2024-08-03”。">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="单位ID" prop="deptId">
        <el-input v-model="queryParams.deptId" placeholder="请输入单位ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="初始发布时间 " prop="firstPublishTime">
        <el-date-picker clearable v-model="queryParams.firstPublishTime" type="date" value-format="yyyy-MM-dd"
          placeholder="请选择初始发布时间 ">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="首次采集时间 " prop="firstCreateTime">
        <el-date-picker clearable v-model="queryParams.firstCreateTime" type="date" value-format="yyyy-MM-dd"
          placeholder="请选择首次采集时间 ">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="初始原站点发布时间" prop="firstWebstePublishTime">
        <el-input v-model="queryParams.firstWebstePublishTime" placeholder="请输入初始原站点发布时间" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="最新原站点发布时间" prop="webstePublishTime">
        <el-input v-model="queryParams.webstePublishTime" placeholder="请输入最新原站点发布时间" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="删除人" prop="deleteBy">
        <el-input v-model="queryParams.deleteBy" placeholder="请输入删除人" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="删除时间" prop="deleteTime">
        <el-date-picker clearable v-model="queryParams.deleteTime" type="date" value-format="yyyy-MM-dd"
          placeholder="请选择删除时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['policy:policy:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['policy:policy:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['policy:policy:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['policy:policy:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="policyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="政策唯一标识" align="center" prop="sn" />
      <el-table-column label="政策标题" align="center" prop="title" />
      <el-table-column label="索 引 号" align="center" prop="indexNumber" />
      <el-table-column label="成文日期" align="center" prop="writtenTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.writtenTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文号，如“国函〔2024〕125号”。" align="center" prop="documentNumber" />
      <el-table-column label="政策原文链接，如“https://www.gov.cn/zhengce/zhengceku/202408/content_6966985.htm”。" align="center"
        prop="originalUrl" />
      <el-table-column label="文件类型，地方性法规" align="center" prop="fileType" />
      <el-table-column label="文件层级，市人大" align="center" prop="fileLevel" />
      <el-table-column label="发布机构分类" align="center" prop="policyUnitClass" />
      <el-table-column label="主题分类" align="center" prop="policyTopicClass" />
      <el-table-column label="公文种类" align="center" prop="policyType" />
      <el-table-column label="政策状态，如“已发布”、“实施中”、“已废止”等。" align="center" prop="policyStatus" />
      <el-table-column label="政策网站采集来源网站地址" align="center" prop="sourceBaseUrl" />
      <el-table-column label="政策网站采集来源名称，如：中华人民共和国中央人民政府" align="center" prop="sourceName" />
      <el-table-column label="政策网站采集，来源唯一标识" align="center" prop="sourceSn" />
      <el-table-column label="政策数据来源" align="center" prop="sourceData" />
      <el-table-column label="关键字，用于搜索，如“中新天津生态城”、“绿色发展示范区”、“实施方案”等，多个关键字可用逗号分隔。" align="center" prop="keywords" />
      <el-table-column label="政策概要或主要内容摘要。" align="center" prop="summary" />
      <el-table-column label="封面图片" align="center" prop="cover" />
      <el-table-column label="地区编码" align="center" prop="publishCode" />
      <el-table-column label="发布地区" align="center" prop="publishArea" />
      <el-table-column label="发文机关，如“国务院”。" align="center" prop="publishUnit" />
      <el-table-column label="发布日期，如“2024-08-03”。" align="center" prop="publishTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="政策详细内容，如果需要在数据库中存储完整政策内容，则可使用此字段" align="center" prop="content" />
      <el-table-column label="状态(0正常 1停用)" align="center" prop="status" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="单位ID" align="center" prop="deptId" />
      <el-table-column label="初始发布时间 " align="center" prop="firstPublishTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.firstPublishTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="首次采集时间 " align="center" prop="firstCreateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.firstCreateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="初始原站点发布时间" align="center" prop="firstWebstePublishTime" />
      <el-table-column label="最新原站点发布时间" align="center" prop="webstePublishTime" />
      <el-table-column label="是否有变更(0.无；1.有)" align="center" prop="isCrawlerContent" />
      <el-table-column label="删除人" align="center" prop="deleteBy" />
      <el-table-column label="删除时间" align="center" prop="deleteTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.deleteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['policy:policy:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['policy:policy:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改政策库对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="政策唯一标识" prop="sn">
          <el-input v-model="form.sn" placeholder="请输入政策唯一标识" />
        </el-form-item>
        <el-form-item label="政策标题" prop="title">
          <el-input v-model="form.title" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="索 引 号" prop="indexNumber">
          <el-input v-model="form.indexNumber" placeholder="请输入索 引 号" />
        </el-form-item>
        <el-form-item label="成文日期" prop="writtenTime">
          <el-date-picker clearable v-model="form.writtenTime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择成文日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="文号，如“国函〔2024〕125号”。" prop="documentNumber">
          <el-input v-model="form.documentNumber" placeholder="请输入文号，如“国函〔2024〕125号”。" />
        </el-form-item>
        <el-form-item label="政策原文链接，如“https://www.gov.cn/zhengce/zhengceku/202408/content_6966985.htm”。"
          prop="originalUrl">
          <el-input v-model="form.originalUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="文件层级，市人大" prop="fileLevel">
          <el-input v-model="form.fileLevel" placeholder="请输入文件层级，市人大" />
        </el-form-item>
        <el-form-item label="发布机构分类" prop="policyUnitClass">
          <el-input v-model="form.policyUnitClass" placeholder="请输入发布机构分类" />
        </el-form-item>
        <el-form-item label="主题分类" prop="policyTopicClass">
          <el-input v-model="form.policyTopicClass" placeholder="请输入主题分类" />
        </el-form-item>
        <el-form-item label="政策网站采集来源网站地址" prop="sourceBaseUrl">
          <el-input v-model="form.sourceBaseUrl" placeholder="请输入政策网站采集来源网站地址" />
        </el-form-item>
        <el-form-item label="政策网站采集来源名称，如：中华人民共和国中央人民政府" prop="sourceName">
          <el-input v-model="form.sourceName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="政策网站采集，来源唯一标识" prop="sourceSn">
          <el-input v-model="form.sourceSn" placeholder="请输入政策网站采集，来源唯一标识" />
        </el-form-item>
        <el-form-item label="政策数据来源" prop="sourceData">
          <el-input v-model="form.sourceData" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="关键字，用于搜索，如“中新天津生态城”、“绿色发展示范区”、“实施方案”等，多个关键字可用逗号分隔。" prop="keywords">
          <el-input v-model="form.keywords" placeholder="请输入关键字，用于搜索，如“中新天津生态城”、“绿色发展示范区”、“实施方案”等，多个关键字可用逗号分隔。" />
        </el-form-item>
        <el-form-item label="政策概要或主要内容摘要。" prop="summary">
          <el-input v-model="form.summary" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="封面图片" prop="cover">
          <el-input v-model="form.cover" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="地区编码" prop="publishCode">
          <el-input v-model="form.publishCode" placeholder="请输入地区编码" />
        </el-form-item>
        <el-form-item label="发布地区" prop="publishArea">
          <el-input v-model="form.publishArea" placeholder="请输入发布地区" />
        </el-form-item>
        <el-form-item label="发文机关，如“国务院”。" prop="publishUnit">
          <el-input v-model="form.publishUnit" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="发布日期，如“2024-08-03”。" prop="publishTime">
          <el-date-picker clearable v-model="form.publishTime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择发布日期，如“2024-08-03”。">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="政策详细内容，如果需要在数据库中存储完整政策内容，则可使用此字段">
          <editor v-model="form.content" :min-height="192" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="单位ID" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入单位ID" />
        </el-form-item>
        <el-form-item label="初始发布时间 " prop="firstPublishTime">
          <el-date-picker clearable v-model="form.firstPublishTime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择初始发布时间 ">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="首次采集时间 " prop="firstCreateTime">
          <el-date-picker clearable v-model="form.firstCreateTime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择首次采集时间 ">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="初始原站点发布时间" prop="firstWebstePublishTime">
          <el-input v-model="form.firstWebstePublishTime" placeholder="请输入初始原站点发布时间" />
        </el-form-item>
        <el-form-item label="最新原站点发布时间" prop="webstePublishTime">
          <el-input v-model="form.webstePublishTime" placeholder="请输入最新原站点发布时间" />
        </el-form-item>
        <el-form-item label="是否有变更(0.无；1.有)">
          <editor v-model="form.isCrawlerContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="删除人" prop="deleteBy">
          <el-input v-model="form.deleteBy" placeholder="请输入删除人" />
        </el-form-item>
        <el-form-item label="删除时间" prop="deleteTime">
          <el-date-picker clearable v-model="form.deleteTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择删除时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPolicy, getPolicy, delPolicy, addPolicy, updatePolicy } from "@/api/policy/policy";

export default {
  name: "Policy",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 政策库表格数据
      policyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        sn: null,
        title: null,
        indexNumber: null,
        writtenTime: null,
        documentNumber: null,
        originalUrl: null,
        fileType: null,
        fileLevel: null,
        policyUnitClass: null,
        policyTopicClass: null,
        policyType: null,
        policyStatus: null,
        sourceBaseUrl: null,
        sourceName: null,
        sourceSn: null,
        sourceData: null,
        keywords: null,
        summary: null,
        cover: null,
        publishCode: null,
        publishArea: null,
        publishUnit: null,
        publishTime: null,
        content: null,
        status: null,
        userId: null,
        deptId: null,
        firstPublishTime: null,
        firstCreateTime: null,
        firstWebstePublishTime: null,
        webstePublishTime: null,
        isCrawlerContent: null,
        deleteBy: null,
        deleteTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "政策标题不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询政策库列表 */
    getList() {
      this.loading = true;
      listPolicy(this.queryParams).then(response => {
        this.policyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        sn: null,
        title: null,
        indexNumber: null,
        writtenTime: null,
        documentNumber: null,
        originalUrl: null,
        fileType: null,
        fileLevel: null,
        policyUnitClass: null,
        policyTopicClass: null,
        policyType: null,
        policyStatus: null,
        sourceBaseUrl: null,
        sourceName: null,
        sourceSn: null,
        sourceData: null,
        keywords: null,
        summary: null,
        cover: null,
        publishCode: null,
        publishArea: null,
        publishUnit: null,
        publishTime: null,
        content: null,
        status: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null,
        firstPublishTime: null,
        firstCreateTime: null,
        firstWebstePublishTime: null,
        webstePublishTime: null,
        isCrawlerContent: null,
        delFlag: null,
        deleteBy: null,
        deleteTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加政策库";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getPolicy(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改政策库";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePolicy(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPolicy(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids;
      this.$modal.confirm('是否确认删除政策库编号为"' + ids + '"的数据项？').then(function () {
        return delPolicy(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('policy/policy/export', {
        ...this.queryParams
      }, `policy_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
