<template>
  <div>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="70px">
      <el-form-item style="margin-left: 25px;" label="文章名称" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入文章名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="发布时间" prop="lastArticlePublishTime">
        <el-date-picker clearable v-model="queryParams.lastArticlePublishTime" type="daterange" value-format="yyyy-MM-dd"
          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px;">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="采集时间" prop="lastArticleCollectionTime">
        <el-date-picker clearable v-model="queryParams.lastArticleCollectionTime" type="daterange"
          value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
          style="width: 240px;">
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="monitorList">
      <el-table-column width="80" label="序号" align="center" prop="id" />
      <el-table-column label="文章标题" prop="title" min-width="300" show-overflow-tooltip>
        <template slot-scope="scope">
          <span style="color: #40A9FF;;cursor: pointer;" @click="openNewView(scope.row)">{{ scope.row.title }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文章发布时间" align="center" prop="publishTime" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文章采集时间" align="center" prop="createTime" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { articleListWechatSourceName } from "@/api/monitor/monitor";

export default {
  name: "Monitor",
  data() {
    return {
      loading: true,
      total: 0,
      monitorList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        wechatBiz: null,
        wechatName: null,
        intervalDays: null,
        publishFrequency: null,
        lastArticlePublishTime: null,
        lastArticleCollectionTime: null,
        lastArticleTitle: null,
        statisticalTime: null
      },
    };
  },
  props: {
    id: {
      type: String,
      default: null
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询微信公众号采集监控列表 */
    getList() {
      this.loading = true;
      let queryParams = JSON.parse(JSON.stringify(this.queryParams))
      queryParams.wechatBiz = this.id
      if (queryParams.lastArticlePublishTime && queryParams.lastArticlePublishTime.length) {
        [queryParams.publishTimeStart, queryParams.publishTimeEnd] = queryParams.lastArticlePublishTime
        delete queryParams.lastArticlePublishTime
      }
      if (queryParams.lastArticleCollectionTime && queryParams.lastArticleCollectionTime.length) {
        [queryParams.collectionTimeStart, queryParams.collectionTimeEnd] = queryParams.lastArticleCollectionTime
        delete queryParams.lastArticleCollectionTime
      }
      articleListWechatSourceName(queryParams).then(response => {
        this.monitorList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /* 跳转新页面 */
    openNewView(item) {
      window.open(`/expressDetails?id=${item.id}&docId=${item.id}`, '_blank')
    },
  }
};
</script>
