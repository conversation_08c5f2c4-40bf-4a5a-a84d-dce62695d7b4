import request from "@/utils/request";

// 查询大屏 科技安全监控 热词列表
export function listWord(query) {
  return request({
    url: "/screen/word/list",
    method: "get",
    params: query,
  });
}

// 查询大屏 科技安全监控 热词详细
export function getWord(id) {
  return request({
    url: "/screen/word/" + id,
    method: "get",
  });
}

// 新增大屏 科技安全监控 热词
export function addWord(data) {
  return request({
    url: "/screen/word",
    method: "post",
    data: data,
  });
}

// 修改大屏 科技安全监控 热词
export function updateWord(data) {
  return request({
    url: "/screen/word/edit",
    method: "post",
    data: data,
  });
}

// 删除大屏 科技安全监控 热词
export function delWord(id) {
  return request({
    url: "/screen/word/remove",
    method: "post",
    data: id,
  });
}
