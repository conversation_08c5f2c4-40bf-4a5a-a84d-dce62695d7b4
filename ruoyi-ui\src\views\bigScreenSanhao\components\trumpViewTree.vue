<template>
  <div class="trump-view-container">
    <!-- 连线层 - 使用绝对定位的div -->
    <div class="connection-lines" v-if="currentCharacterData">
      <!-- 左侧连线 -->
      <!-- 人物到左侧分支点 -->
      <div class="line horizontal" :style="{ left: 'calc(50% - 100px)', top: '146px', width: '100px' }"></div>

      <!-- 左侧分支点垂直线 -->
      <div class="line vertical" :style="{ left: 'calc(50% - 101px)', top: '46px', height: getVerticalLineHeight('left') }"></div>

      <!-- 左侧分支到各观点 -->
      <div
        v-for="(item, index) in leftSideData"
        :key="'left-' + index"
        class="line horizontal"
        :style="{ left: 'calc(50% - 260px)', top: (46 + index * 100) + 'px', width: '160px' }"
      ></div>

      <!-- 右侧连线 -->
      <!-- 人物到右侧分支点 -->
      <div class="line horizontal" :style="{ left: 'calc(50% + 0px)', top: '146px', width: '100px' }"></div>

      <!-- 右侧分支点垂直线 -->
      <div class="line vertical" :style="{ left: 'calc(50% + 99px)', top: '46px', height: getVerticalLineHeight('right') }"></div>

      <!-- 右侧分支到各观点 -->
      <div
        v-for="(item, index) in rightSideData"
        :key="'right-' + index"
        class="line horizontal"
        :style="{ left: 'calc(50% + 100px)', top: (46 + index * 100) + 'px', width: '160px' }"
      ></div>
    </div>

    <!-- 节点层 -->
    <div class="nodes-container" v-if="currentCharacterData">
      <!-- 中心节点 人物 -->
      <div class="center-node" :style="{ left: 'calc(50% - 60px)', top: '45px' }">
        <img :src="currentCharacterData.avatar" class="character-avatar" alt="" />
        <img src="../../../assets/bigScreenSanhao/articleBase.png" class="articleBase" alt="" />
        <span>{{ currentCharacterData.name }}</span>
      </div>

      <!-- 左侧观点 -->
      <div
        v-for="(item, index) in leftSideData"
        :key="'left-node-' + index"
        class="node view-node"
        :class="{ active: nodeType === item.type }"
        :style="{ left: 'calc(50% - 390px)', top: (10 + index * 100) + 'px' }"
        @click="handleNodeClick(item.type)"
      >
        <el-tooltip placement="left">
          <template #content>
            <div style="width: 300px;">
              {{ item.desc }}
            </div>
          </template>
          <span ref="textSpan" class="tooltip-text">{{ item.title }}</span>
        </el-tooltip>
      </div>

      <!-- 右侧观点 -->
      <div
        v-for="(item, index) in rightSideData"
        :key="'right-node-' + index"
        class="node view-node"
        :class="{ active: nodeType === item.type }"
        :style="{ left: 'calc(50% + 130px)', top: (10 + index * 100) + 'px' }"
        @click="handleNodeClick(item.type)"
      >
        <el-tooltip placement="right">
          <template #content>
            <div style="width: 300px;">
              {{ item.desc }}
            </div>
          </template>
          <span ref="textSpan" class="tooltip-text">{{ item.title }}</span>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script>
import { treeData1 } from '../data/renwu.js';

export default {
  name: "TrumpViewTree",
  props: {
    move: {
      type: Boolean,
      default: false,
    },
    // 新增props，用于指定当前显示的人物，默认为特朗普
    currentCharacter: {
      type: String,
      default: 'trump'
    }
  },
  data() {
    return {
      nodeType: null,
      scrollTimer: null,
    };
  },
  computed: {
    // 获取当前人物的数据
    currentCharacterData() {
      return treeData1[this.currentCharacter] || null;
    },
    // 获取所有观点数据
    allViewData() {
      return this.currentCharacterData ? this.currentCharacterData.data : [];
    },
    // 左侧观点数据（前3个）
    leftSideData() {
      return this.allViewData.slice(0, 3);
    },
    // 右侧观点数据（后3个）
    rightSideData() {
      return this.allViewData.slice(3, 6);
    },
    // 所有观点类型
    allTypes() {
      return this.allViewData.map(item => item.type);
    }
  },
  methods: {
    handleNodeClick(type) {
      this.nodeType = type;
      this.$emit("handleNodeClick", type);
    },
    startScroll() {
      this.clearScrollTimer();
      this.scrollTimer = setInterval(() => {
        if (this.allTypes.length === 0) return;

        let currentIndex = this.allTypes.indexOf(this.nodeType);
        currentIndex = (currentIndex + 1) % this.allTypes.length;
        this.handleNodeClick(this.allTypes[currentIndex]);
      }, 8000);
    },
    clearScrollTimer() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null;
      }
    },
    // 计算垂直连线高度
    getVerticalLineHeight(side) {
      const sideData = side === 'left' ? this.leftSideData : this.rightSideData;
      if (sideData.length === 0) return '0px';
      // 垂直线应该从第一个节点连接到最后一个节点
      // 第一个节点的top是46px，每个节点间隔100px
      // 所以高度应该是 (节点数量 - 1) * 100px
      return ((sideData.length - 1) * 100) + 'px';
    }
  },
  created() {
    this.$nextTick(() => {
      setTimeout(() => {
        if (this.allTypes.length > 0) {
          this.handleNodeClick(this.allTypes[0]);
          this.startScroll();
        }
      }, 500);
    })
  },
  beforeDestroy() {
    this.clearScrollTimer();
  },
  watch: {
    move: {
      handler(newType) {
        if (!newType) {
          this.startScroll()
        } else {
          this.clearScrollTimer()
        }
      },
      deep: true,
    },
    // 监听人物切换
    currentCharacter: {
      handler() {
        // 人物切换时重新初始化
        this.clearScrollTimer();
        this.$nextTick(() => {
          if (this.allTypes.length > 0) {
            this.handleNodeClick(this.allTypes[0]);
            if (!this.move) {
              this.startScroll();
            }
          }
        });
      },
      immediate: true
    }
  },
};
</script>

<style lang="scss" scoped>
.trump-view-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: transparent;
  overflow: hidden;
}

.connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.line {
  position: absolute;
  background-color: transparent;
}

.line.horizontal {
  height: 2px;
  border-top: 2px dashed #fff;
}

.line.vertical {
  width: 2px;
  border-left: 2px dashed #fff;
}

.nodes-container {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 10;
}

.node {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
}

.center-node {
  position: absolute;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #081f46;

  &:hover {
    transform: scale(1.05);
  }

  .character-avatar {
    width: 90px;
    height: 91px;
    object-fit: contain;
  }

  .articleBase {
    margin-top: -20px;
  }

  span {
    position: absolute;
    top: 95px;
    width: 200px;
    text-align: center;
  }
}

.view-node {
  width: 260px;
  height: 72px;
  background: radial-gradient(ellipse 100% 70% at 50% 50%,
      #153264 0%,
      #0086ff 100%);
  border-radius: 20px 20px 20px 20px;
  border: 2px solid #00bfff;
  font-size: 16px;
  font-weight: normal;
}

.active {
  box-shadow: 0 0 10px 8px rgba(0, 134, 255, 0.6)
}

.tooltip-text {
  padding: 0px 20px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}
</style>