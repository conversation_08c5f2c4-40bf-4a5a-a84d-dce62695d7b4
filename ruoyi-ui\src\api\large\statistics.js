import request from '@/utils/request'

// 查询大屏-区域分析文章数量列表
export function listStatistics(query) {
  return request({
    url: '/large/statistics/list',
    method: 'get',
    params: query
  })
}

// 查询大屏-区域分析文章数量详细
export function getStatistics(id) {
  return request({
    url: '/large/statistics/' + id,
    method: 'get'
  })
}

// 新增大屏-区域分析文章数量
export function addStatistics(data) {
  return request({
    url: '/large/statistics',
    method: 'post',
    data: data
  })
}

// 修改大屏-区域分析文章数量
export function updateStatistics(data) {
  return request({
    url: '/large/statistics',
    method: 'put',
    data: data
  })
}

// 删除大屏-区域分析文章数量
export function delStatistics(id) {
  return request({
    url: '/large/statistics/' + id,
    method: 'delete'
  })
}
