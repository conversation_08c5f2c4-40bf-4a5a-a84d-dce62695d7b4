import request from '@/utils/request'

// 查询政策库列表
export function listPolicy(query) {
  return request({
    url: '/policy/policy/list',
    method: 'get',
    params: query
  })
}

// 查询政策库详细
export function getPolicy(id) {
  return request({
    url: '/policy/policy/' + id,
    method: 'get'
  })
}

// 新增政策库
export function addPolicy(data) {
  return request({
    url: '/policy/policy',
    method: 'post',
    data: data
  })
}

// 修改政策库
export function updatePolicy(data) {
  return request({
    url: '/policy/policy/edit',
    method: 'post',
    data: data
  })
}

// 删除政策库
export function delPolicy(data) {
  return request({
    url: '/policy/policy/remove',
    method: 'post',
    data: data
  })
}
