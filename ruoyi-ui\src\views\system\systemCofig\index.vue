<template>
  <div>
    <div class="seach">
      <el-row :gutter="15">
        <el-col :span="1.5" class="params">参数名称:</el-col>
        <el-col :span="5">
          <el-input v-model="paramsName" style="width: 90%;" size="mini" placeholder="请输入参数名称"></el-input>
        </el-col>
        <el-col :span="5">
          <el-button type="primary" size="mini" icon="el-icon-search" @click="getList()">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh-right" @click="reset()">重置</el-button>
        </el-col>
      </el-row>
    </div>
    <div class="table_Main">
      <el-table :data="tableData" size="mini" style="width: 100%" :header-cell-style="{ textAlign: 'center' }"
        :cell-style="{ textAlign: 'center' }" height="calc(100vh - 150px)">
        <el-table-column prop="configId" label="参数主键" width="180">
        </el-table-column>
        <el-table-column prop="configName" label="参数名称" width="180">
        </el-table-column>
        <el-table-column prop="configKey" label="参数键名">
        </el-table-column>
        <el-table-column prop="configValue" label="参数键值" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column prop="configType" label="系统内置">
          <template slot-scope="scoped">
            <el-tag :type="scoped.row.configType == 'N' ? 'danger' : 'primary'">
              {{ scoped.row.configType == 'N' ? '否' : '是' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间">
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-link type="primary" icon="el-icon-edit" @click="editParams(scope.row)">修改</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog title="修改参数" :visible.sync="dialogFormVisible" width="35%" :close-on-click-modal="false">
      <div style="width: 100%;height: 35px; line-height: 35px;text-align: center;">
        <el-radio v-model="switchForm" :label="1">腾讯云</el-radio>
        <el-radio v-model="switchForm" :label="2">阿里云</el-radio>
      </div>
      <el-form :model="form" :rules="rules">
        <el-form-item label="参数名称" :label-width="formLabelWidth" prop="name">
          <el-input v-model="form.name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="参数键名" :label-width="formLabelWidth">
          <el-input v-model="form.key" disabled autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="系统内置" :label-width="formLabelWidth" prop="redio">
          <el-radio v-model="form.redio" disabled label="Y">是</el-radio>
          <el-radio v-model="form.redio" disabled label="N">否</el-radio>
        </el-form-item>
        <template v-if="switchForm == 1">
          <el-form-item label="appId" :label-width="formLabelWidth" prop="appId">
            <el-input v-model="form.appId" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="secretId" :label-width="formLabelWidth" prop="secretId">
            <el-input v-model="form.secretId" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="secretKey" :label-width="formLabelWidth" prop="secretKey">
            <el-input v-model="form.secretKey" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="smsSdkAppId" :label-width="formLabelWidth" prop="smsSdkAppId">
            <el-input v-model="form.smsSdkAppId" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="smsSign" :label-width="formLabelWidth" prop="smsSign">
            <el-input v-model="form.smsSign" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="templateId" :label-width="formLabelWidth" prop="templateId">
            <el-input v-model="form.templateId" autocomplete="off"></el-input>
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item label="access_key_id" :label-width="formLabelWidth" prop="access_key_id">
            <el-input v-model="form.access_key_id" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="access_key_secret" :label-width="formLabelWidth" prop="access_key_secret">
            <el-input v-model="form.access_key_secret" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="signName" :label-width="formLabelWidth" prop="signName">
            <el-input v-model="form.signName" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="templateCode" :label-width="formLabelWidth" prop="templateCode">
            <el-input v-model="form.templateCode" autocomplete="off"></el-input>
          </el-form-item>
        </template>
        <el-form-item label="备注" :label-width="formLabelWidth">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" maxlength="500" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="平台切换" :label-width="formLabelWidth">
          <el-switch v-model="form.switchValue" active-text="阿里云" inactive-text="腾讯云">
          </el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { SystemConfig, updateConfig } from '@/api/system/config.js'
export default {
  dicts: ['sys_yes_no'],
  data () {
    return {
      tableData: [],
      paramsName: '',
      pageSize: 10,
      pageCurrent: 1,
      dialogFormVisible: false,
      formLabelWidth: '150px',
      form: {
        name: '',
        key: '',
        redio: 'N',
        /* 腾讯云 */
        appId: '',
        secretId: '',
        smsSdkAppId: '',
        secretKey: '',
        smsSign: '',
        templateId: '',
        remark: '',
        /* 阿里云 */
        access_key_id: '',
        access_key_secret: '',
        signName: '',
        templateCode: '',
        switchValue: 0,
      },
      rules: {
        name: [{ required: true, message: '请填写参数名称', trigger: 'blur' }],
        appId: [{ required: true, message: '请填写appId', trigger: 'blur' }],
        secretId: [{ required: true, message: '请填写secretId', trigger: 'blur' }],
        smsSdkAppId: [{ required: true, message: '请填写smsSdkAppId', trigger: 'blur' }],
        smsSign: [{ required: true, message: '请填写smsSign', trigger: 'blur' }],
        templateId: [{ required: true, message: '请填写templateId', trigger: 'blur' }],
        remark: [{ required: true, message: '备注', trigger: 'blur' }],
        access_key_id: [{ required: true, message: '请填写access_key_id', trigger: 'blur' }],
        access_key_secret: [{ required: true, message: '请填写access_key_secret', trigger: 'blur' }],
        signName: [{ required: true, message: '请填写signName', trigger: 'blur' }],
        templateCode: [{ required: true, message: '请填写templateCode', trigger: 'blur' }],
      },
      switchForm: 1,
      total: 0,
    }
  },
  created () {
    this.getList()
  },
  methods: {
    async getList () {
      let res = await SystemConfig({ pageSize: this.pageSize, pageNum: this.pageCurrent })
      this.tableData = res.rows
      this.total = res.total
    },
    editParams (scope) {
      let value = JSON.parse(scope.configValue)[0]
      console.log(value);
      this.dialogFormVisible = true
      this.form = {
        configId: scope.configId,
        name: scope.configName,
        key: scope.configKey,
        redio: scope.configType,
        /* 腾讯云 */
        appId: value.tencent.appId,
        secretId: value.tencent.secretId,
        smsSdkAppId: value.tencent.smsSdkAppId,
        secretKey: value.tencent.secretKey,
        smsSign: value.tencent.smsSign,
        templateId: value.tencent.templateId,
        remark: scope.remark,
        /* 阿里云 */
        access_key_id: value.aliyun.accessKeyId,
        access_key_secret: value.aliyun.accessKeySecret,
        signName: value.aliyun.signName,
        templateCode: value.aliyun.templateCode,
        switchValue: Boolean(value.type),
      }
    },
    async onSubmit () {
      this.dialogFormVisible = false
      let json = [{
        tencent: {
          appId: this.form.appId,
          secretId: this.form.secretId,
          secretKey: this.form.secretKey,
          smsSdkAppId: this.form.smsSdkAppId,
          smsSign: this.form.smsSign,
          templateId: this.form.templateId
        },
        aliyun: {
          accessKeyId: this.form.access_key_id,
          accessKeySecret: this.form.access_key_secret,
          signName: this.form.signName,
          templateCode: this.form.templateCode
        },
        type: Number(this.form.switchValue)
      }]
      let params = {
        remark: this.form.remark,
        configId: this.form.configId,
        configName: this.form.name,
        configKey: this.form.key,
        configValue: JSON.stringify(json),
        configType: this.form.redio
      }
      let res = await updateConfig(params)
      if (res.code == 200)
      {
        this.$message({ message: '修改成功', type: 'success' })
        this.getList()
      } else
      {
        this.$message({ message: '修改失败,请联系管理员', type: 'error' })
      }
    },
    reset(){
      this.paramsName = '',
      this.getList()
    }
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__header {
  background-color: #0094f7;
  padding: 5px 10px;
  line-height: 30px;

}

::v-deep .el-dialog__body {
  padding: 10px 20px 30px 20px;
}

::v-deep .el-dialog__title {
  color: #fff;
}

.seach {
  width: 100%;
  height: 60px;
  line-height: 60px;
  padding-left: 20px;

  .params {
    font-size: 15px;
    font-weight: 550;
  }
}

.table_Main {
  width: 98%;
  margin: 0 auto;
}
</style>