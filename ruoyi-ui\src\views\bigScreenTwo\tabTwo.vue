<template>
  <div style="height: 100%; display: flex" class="two">
    <div class="left">
      <TitleComponent :title="'人工智能技术架构'" />
      <div style="height: 266px; width: 100%; margin-bottom: 26px">
        <artificialBar />
      </div>
      <TitleComponent :title="'专家观点'" />
      <div style="height: 602px; width: 100%; background: rgba(0, 0, 0, 0.15)">
        <expertOpinions @expertFun="expertFun" />
      </div>
    </div>
    <div class="center">
      <TitleComponent :title="'风险态势'" :width="'1914px'" :type="'2'" />
      <div style="height: 612px; width: 100%; margin-bottom: 15px" @click="openList">
        <linEchart :valueObj="lineList" />
      </div>
      <!-- <TitleComponent :title="'关键企业'" :width="'1914px'" :type="'2'" /> -->
      <div :class="['title', 'title2']" :style="{ width: '1914px', height: '45px' }">
        <span @click="guanjianType = 1" :class="{ 'titleColor': guanjianType == 1 }"> 关键企业 </span>
        /
        <span @click="guanjianType = 2" :class="{ 'titleColor': guanjianType == 2 }"> 关键人 </span>
      </div>
      <div style="height: 266px; width: 100%; background: rgba(0, 0, 0, 0.15)">
        <div class="img-all" v-if="guanjianType == 1">
          <div class="img-info" v-for="(item, index) in allImgList" :key="index" @click="guanjianqiye(item)">
            <div class="img">
              <img :src="baseUrl + item.cover" alt="" />
            </div>
          </div>
        </div>
        <div class="img-all" v-else>
          <div class="img-info" v-for="(item, index) in allImgList1" :key="index" @click="guanjianren(item)">
            <div class="img1">
              <div>{{ item.name }}</div>
              <img :src="baseUrl + item.cover" alt="" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <TitleComponent :title="'前沿应用'" />
      <div style="height: 260px;width: 100%;margin-bottom: 26px;background: rgba(0, 0, 0, 0.15);">
        <pieGlEchart @openList="openList" />
      </div>
      <TitleComponent :title="'研究成果'" />
      <div style="height: 260px;width: 100%;margin-bottom: 26px;background: rgba(0, 0, 0, 0.15);">
        <sankeyEchart />
      </div>
      <TitleComponent :title="'前沿研究'" />
      <div style="height: 260px; width: 100%; background: rgba(0, 0, 0, 0.15)">
        <keyWordVue :wordValueList="wordValueList" />
      </div>
    </div>

    <el-dialog :title="title" :visible.sync="articleDialogVisible" width="800px" append-to-body
      :before-close="handleClose" :close-on-click-modal="false">
      <div
        style="line-height: 30px;display: -webkit-box;-webkit-line-clamp: 3;-webkit-box-orient: vertical;overflow: hidden;min-height: 90px;text-indent:2em;">
        {{ cnContent ? cnContent : '暂无介绍' }}
      </div>
      <div style="line-height: 20px;margin: 5px 0;font-size: 18px;">相关内容</div>
      <el-table v-loading="guanjianqiyeLoading" :data="list" style="width: 100%" :show-header="false" ref="table"
        class="table-all" height="400" @cell-click="openNewView">
        <el-table-column prop="title" label="标题" width="510" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-html="changeColor(scope.row.cnTitle || scope.row.title)"></span>
          </template>
        </el-table-column>
        <el-table-column prop="sourceName" label="数据源" width="140" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="publishTime" label="发布时间" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>
              {{ parseTime(scope.row.publishTime || scope.row.gatherTime, "{y}-{m}-{d}") }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        :background="false" @pagination="guanjianqiyeList" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 抽屉 -->
    <drawerComponent :open.sync="open" />

    <el-dialog :title="drawerInfo.cnTitle || drawerInfo.title" :visible.sync="articleDialogVisible1" width="800px"
      append-to-body :before-close="handleClose1" :close-on-click-modal="false">
      <div style="line-height: 30px" v-html="drawerInfo.cnContent"></div>
      <el-empty description="当前文章暂无数据" v-if="!drawerInfo.cnContent"></el-empty>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose1">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import drawerComponent from "./components/drawerComponent.vue";
import TitleComponent from "./components/titleVue.vue";
import artificialBar from "./components/artificialBar.vue";
import expertOpinions from "./components/expertOpinions.vue";
import linEchart from "./components/linEchart.vue";
import pieGlEchart from "./components/pieGlEchart.vue";
import sankeyEchart from "./components/sankeyEchart.vue";
import keyWordVue from "./components/keyWord.vue";
import { demo } from "./demo";
import { listEnterprise } from "@/api/large/enterprise";
import { listCharacter } from "@/api/large/character";
import { largeEnterpriseDataList, largeEnterpriseKeywordsNames, largeEnterpriseData, aiStatisticsAllData, largeAicKeywordsNames, largeCharactersDataList, largeCharactersData } from "@/api/bigScreen/index1";

export default {
  data() {
    return {
      lineList: {
        xData: [],
        yData: [],
      },
      wordValueList: [],
      allImgList: [],
      allImgList1: [],
      guanjianType: 1,
      baseUrl: process.env.VUE_APP_BASE_API,
      articleDialogVisible: false,
      title: "",
      cnContent: '',
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      list: [],
      guanjianqiyeLoading: false,
      keywords: [],
      drawerInfo: {},
      articleDialogVisible1: false,
    };
  },
  components: {
    TitleComponent,
    artificialBar,
    expertOpinions,
    linEchart,
    pieGlEchart,
    sankeyEchart,
    keyWordVue,
    drawerComponent,
  },
  mounted() {
    this.init();
    this.getGuanjianqiyeList()
  },
  beforeDestroy() { },
  methods: {
    init() {
      aiStatisticsAllData().then(res => {
        const data = res.data
        const months = Object.keys(data).sort().map(date => date);
        const uniqueAiTrendNames = [...new Set(Object.values(data).flat().map(item => item.aiTrendName))];

        let yData = []
        let color = ["#5B8FF9", "#5AD8A6", "#5D7092", "#F6BD16", "#E8684A", "#6DC8EC", "#9270CA", "#FF9D4D", "#5B8FF9", "#5AD8A6", "#5D7092", "#F6BD16", "#E8684A", "#6DC8EC", "#9270CA", "#FF9D4D",];
        uniqueAiTrendNames.map((rows, key) => {
          yData.push({
            name: rows,
            type: "line",
            data: months.map(month => {
              const item = data[month].find(item => item.aiTrendName === rows);
              if (item) {
                return item.aiTrendTotal;
              } else {
                return 0;
              }
            }),
            connectNulls: true,
            smooth: true,
            itemStyle: {
              color: color[key - 1],
            },
            symbolSize: 8,
            lineStyle: {
              width: 2, // 设置线宽为5
            },
          });
        })
        let lineList = {
          xData: months,
          yData: yData,
        }
        this.$set(this, 'lineList', lineList)
      })
      this.wordValueList = demo;
    },
    getGuanjianqiyeList() {
      listEnterprise({ pageNum: 1, pageSize: 10, type: 1 }).then(response => {
        this.allImgList = response.rows;
      });
      listCharacter({ pageNum: 1, pageSize: 10, type: 1 }).then(response => {
        this.allImgList1 = response.rows
      });
    },
    handleClose() {
      this.articleDialogVisible = false;
    },
    expertFun(item) {
      this.articleDialogVisible = true;
    },
    openList() {
      // this.open = true;
    },
    async guanjianqiye(item) {
      await largeEnterpriseKeywordsNames(item.name).then((res) => {
        this.keywords = res.data;
        this.keywords.push(item.name)
      });
      this.cnContent = item.summary
      this.title = item.name
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
      this.guanjianqiyeList()
      this.articleDialogVisible = true;
    },
    async guanjianqiyeList() {
      this.guanjianqiyeLoading = true
      largeEnterpriseDataList({ enterpriseName: this.title, ...this.queryParams, type: 1 }).then((res) => {
        this.list = res.rows;
        this.total = res.total;
        this.guanjianqiyeLoading = false
      });
    },
    // 关键字替换
    changeColor(str) {
      let Str = str;
      if (Str) {
        let keywords = this.keywords;
        keywords.map((keyitem, keyindex) => {
          if (keyitem && keyitem.length > 0) {
            // 匹配关键字正则
            let replaceReg = new RegExp(keyitem, "g");
            // 高亮替换v-html值
            let replaceString =
              '<span class="highlight"' +
              ' style="color: #ff7500;">' +
              keyitem +
              "</span>";
            Str = Str.replace(replaceReg, replaceString);
          }
        });
      }
      return Str;
    },
    async openNewView(item) {
      if (this.guanjianType == 1) {
        await largeEnterpriseData(item.id).then((res) => {
          this.drawerInfo = res.data;
        });
      } else {
        await largeCharactersData(item.id).then((res) => {
          this.drawerInfo = res.data;
        });
      }
      let content = this.drawerInfo.article || this.drawerInfo.content;
      if (content) {
        content = content.replace(/\n/g, "<br>");
        content = content.replace(/\${[^}]+}/g, "<br>");
        content = content.replace("|xa0", "");
        content = content.replace("opacity: 0", "");
        content = content.replace(/<img\b[^>]*>/gi, "");
        content = content.replace(/ style="[^"]*"/g, "");
      }
      this.drawerInfo.cnContent = content;
      this.articleDialogVisible1 = true;
    },
    handleClose1() {
      this.articleDialogVisible1 = false;
    },
    async guanjianren(item) {
      await largeAicKeywordsNames(item.name).then((res) => {
        this.keywords = res.data;
        this.keywords.push(item.name)
      });
      this.cnContent = item.summary
      this.title = item.name
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
      this.guanjianrenList()
      this.articleDialogVisible = true;
    },
    async guanjianrenList() {
      this.guanjianqiyeLoading = true
      largeCharactersDataList({ enterpriseName: this.title, ...this.queryParams, type: 1 }).then((res) => {
        this.list = res.rows;
        this.total = res.total;
        this.guanjianqiyeLoading = false
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.two {
  height: 100%;
  width: 100%;

  .left {
    width: 872px;
  }

  .center {
    margin-left: 24px;
    width: 1914px;
  }

  .right {
    margin-left: 106px;
    width: 872px;
  }
}

.img-all {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  padding: 0 10px 5px;

  .img-info {
    position: relative;
    display: inline-block;
    cursor: pointer;

    .img {
      width: 160px;
      height: 160px;
      background: #fff;
      vertical-align: middle;
      line-height: 160px;

      img {
        width: 140px;
        height: 140px;
        margin: 10px;
      }
    }

    .img1 {
      width: 160px;
      height: 160px;
      background: #fff;
      vertical-align: middle;
      position: relative;

      div {
        position: absolute;
        bottom: 0;
        height: 30px;
        font-size: 18px;
        line-height: 30px;
        width: 100%;
        background-color: #31313187;
        color: #fff;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      img {
        width: 160px;
        height: 160px;
        // margin: 10px;
        display: inline-block;
      }
    }
  }
}

::v-deep .el-dialog {
  background: url("../../assets/bigScreenTwo/dialogBackground.png") no-repeat;
  background-size: 100% 100% !important;
  background-size: cover;
  height: 800px;

  .el-dialog__header {
    background-color: #1d233400;
    font-size: 30px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);
    height: 100px;
    padding-left: 31px;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .el-dialog__body {
    background-color: #2a304000;
    color: #f2f2f2;
    height: calc(100% - 160px);
    overflow: auto;
  }

  .el-dialog__footer {
    background-color: #1d233400;
    padding: 0 27px 28px 0;
  }

  .el-button {
    background-color: #002766;
    color: #fff;
    border: 0px;
  }

  .el-dialog__headerbtn .el-dialog__close {
    background: url("../../assets/bigScreenTwo/关闭小.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
    width: 31px;
    height: 31px;
    top: 16px;

    &::before {
      content: none;
    }
  }
}

::v-deep .el-drawer__open {
  .el-drawer {
    background: url("../../assets/bigScreenTwo/drawerBackground.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
  }

  .el-dialog__close {
    background: url("../../assets/bigScreenTwo/关闭小.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
    width: 31px;
    height: 31px;
    top: 18px;
    right: 10px;

    &::before {
      content: none;
    }
  }

  .el-drawer__header {
    background-color: #1d233400;
    font-size: 30px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);
    height: 100px;
  }
}

::v-deep .el-table {
  background-color: #2a304000;

  tr {
    color: #f2f2f2;
    background: url("../../assets/bigScreenTwo/弹窗列表.png") no-repeat;
    background-size: 100% 100% !important;
    height: 68px;
    padding: 0 0 0 65px;
    margin-bottom: 10px;
    display: block;
    width: 790px;
  }

  td.el-table__cell {
    border-bottom: 1px solid #1d233400;
    height: 68px;
    line-height: 68px;
    font-size: 16px;
    text-shadow: 0px 0px 9px rgba(30, 198, 255, 0.8);
  }

  &::before {
    height: 0;
  }
}

.table-all {
  ::v-deep .el-table {
    background-color: #2a304000;
  }

  ::v-deep tr {
    color: #f2f2f2;
    background: #2a304000;
    height: 30px;
    padding: 0 0 0 0px;
    margin-bottom: 10px;
    display: block;
    width: 790px;
  }

  ::v-deep td.el-table__cell {
    border-bottom: 1px solid #1d233400;
    height: 30px;
    line-height: 30px;
    font-size: 16px;
    text-shadow: 0px 0px 9px rgba(30, 198, 255, 0.8);
  }
}

::v-deep .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell,
::v-deep .el-table__empty-block {
  background-color: #2a304000;
  color: #f2f2f2;
  cursor: pointer;
}

::v-deep .pagination-container {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 0;

  .el-select .el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }

  .el-pagination__editor.el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }
}

::v-deep .el-pagination__total,
::v-deep .el-pagination__jump {
  color: #f2f2f2;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination button:disabled {
  background-color: #ffffff00;
  color: #fff;
}

::v-deep .el-pager li {
  background: #ffffff00;
  color: #fff;

  &.active {
    color: #1890ff;
  }
}
</style>
<style lang="scss">
::-webkit-scrollbar {
  display: none;
}

/* 保持滚动效果 */
.scrollable {
  overflow-y: scroll;
}

/* 隐藏滚动条 */
.scrollable {
  scrollbar-width: none;
  /* Firefox */
}

/* 隐藏滚动条 */
.scrollable {
  -ms-scrollbar-face-color: transparent;
  /* IE and Edge */
  -ms-scrollbar-3dlight-color: transparent;
  /* IE and Edge */
}
</style>
<style lang="scss" scoped>
.title {
  background: url("../../assets/bigScreenTwo/title.png") -5px 0px no-repeat;
  background-size: 100% 100% !important;
  background-size: cover;
  font-family: "pingFangMedium";
  font-size: 20px;
  color: #ffffff;
  line-height: 45px;
  letter-spacing: 2px;
  text-align: left;
  font-style: normal;
  padding-left: 65px;
}

.title2 {
  background: url("../../assets/bigScreenTwo/title-long.png") -5px 0px no-repeat;
}

.titleColor {
  color: #EE9900;
}
</style>