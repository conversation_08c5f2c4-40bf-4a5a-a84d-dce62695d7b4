<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="相关节点" prop="originNodeName">
        <el-input
          v-model="queryParams.originNodeName"
          placeholder="请输入相关节点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="指向节点" prop="targetNodeName">
        <el-input
          v-model="queryParams.targetNodeName"
          placeholder="请输入指向节点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['screen:relation:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['screen:relation:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['screen:relation:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['screen:relation:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="relationList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index" />
      <el-table-column label="相关节点" align="center" prop="originNodeName" />
      <el-table-column label="指向节点" align="center" prop="targetNodeName" />
      <el-table-column label="关系线颜色" align="center" prop="lineColor" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:relation:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['screen:relation:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改图谱关系连线对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="相关节点" prop="originNodeId">
          <el-select
            v-model="form.originNodeId"
            placeholder="相关节点"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in nodeList"
              :key="dict.id"
              :label="dict.nodeName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="指向节点" prop="targetNodeId">
          <el-select
            v-model="form.targetNodeId"
            placeholder="指向节点"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in nodeList"
              :key="dict.id"
              :label="dict.nodeName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关系线颜色" prop="lineColor">
          <el-color-picker v-model="form.lineColor"></el-color-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listRelation,
  getRelation,
  delRelation,
  addRelation,
  updateRelation,
} from "@/api/bigScreenManage/relation";
import { listNode } from "@/api/bigScreenManage/node";

export default {
  name: "Relation",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 图谱关系连线表格数据
      relationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        originNodeName: null,
        targetNodeName: null,
        lineColor: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      nodeList: [],
    };
  },
  created() {
    this.getList();
    this.getNodeList();
  },
  methods: {
    getNodeList() {
      listNode({ pageNum: 1, pageSize: 10000 }).then((response) => {
        this.nodeList = response.rows;
      });
    },
    /** 查询图谱关系连线列表 */
    getList() {
      this.loading = true;
      listRelation(this.queryParams).then((response) => {
        this.relationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        originNodeId: null,
        targetNodeId: null,
        lineColor: null,
        createTime: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加图谱关系连线";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getRelation(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改图谱关系连线";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.targetNodeId === this.form.originNodeId) {
            this.$message.warning("相关节点和指向节点不能相同");
            return;
          }
          if (this.form.id != null) {
            updateRelation(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRelation(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids;
      this.$modal
        .confirm('是否确认删除图谱关系连线编号为"' + ids + '"的数据项？')
        .then(function () {
          return delRelation(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "screen/relation/export",
        {
          ...this.queryParams,
        },
        `relation_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
