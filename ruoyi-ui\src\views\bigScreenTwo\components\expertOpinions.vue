<template>
  <div class="table">
    <div class="table_main">
      <div :class="{ 'scoll-Table': hotList.length > 6 }">
        <div
          class="table_col"
          v-for="(item, index) in hotList"
          :key="index"
          @click="expertFun(item)"
        >
          <el-tooltip
            class="item"
            effect="dark"
            :content="item.reportName"
            placement="top-start"
          >
            <div class="last-child" @click="jumpFun(item.reportUrl)">
              {{ item.reportName }}
            </div>
          </el-tooltip>
          <div class="item-bottom">
            <div style="width: 150px">{{ item.reportDate }}</div>
            <div style="flex: 1; text-align: center">{{ item.name }}</div>
            <div style="width: 150px; text-align: right">
              {{ item.nickName }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      hotList: [
        {
          reportName: "2030年中国产业端AI市场规模约为9.4万亿国民币",
          reportDate: "2024-07-06 ",
          name: "世通海外投资咨询",
          nickName: "彭文生",
        },
        {
          reportName: "国际计算机学会2024中国图灵大会在长沙举行",
          reportDate: "2024-07-06",
          name: "今日头条",
          nickName: "周海兵，段献忠",
        },
        {
          reportName: "快手大模型首次集体亮相，用AI重塑内容与商业生态",
          reportDate: "2024-07-06",
          name: "潮起网",
          nickName: "盖坤",
        },
        {
          reportName: "新书上架丨数字原生银行",
          reportDate: "2024-07-06",
          name: "雪球",
          nickName: "刘兴赛",
        },
        {
          reportName: "用好“AI+安全” 促进数字化转型与可持续发展",
          reportDate: "2024-07-06",
          name: "搜狐号",
          nickName: "齐向东",
        },
        {
          reportName: "实时监护，创新应用 丨“实时远程心电监护临床创新应用模式”",
          reportDate: "2024-07-06",
          name: "微信",
          nickName: "董旭辉，杜斐斐",
        },
        {
          reportName: "奔向问题，问题实际上创造了机会",
          reportDate: "2024-07-06",
          name: "微信",
          nickName: "苏姿丰",
        },
        {
          reportName: "“新生万物”，把当代中国风带到巴黎",
          reportDate: "2024-07-06",
          name: "搜狐号",
          nickName: "杨澜",
        },
        {
          reportName: "人工智能驱动数字金融高质量发展",
          reportDate: "2024-07-06",
          name: "中国经济新闻网",
          nickName: "张柱宏",
        },
        {
          reportName: "算力即服务， RISC-V+AI按下「加速键」",
          reportDate: "2024-07-06",
          name: "科技猎",
          nickName: "李岩",
        },
      ], //滚动
    };
  },
  mounted() {},

  props: {},
  watch: {},

  components: {},
  methods: {
    /**
     *
     */
    expertFun(item) {
      this.$emit("expertFun", item);
    },
  },
  beforeDestroy() {
    /* 页面组件销毁的时候，别忘了移除绑定的监听resize事件，否则的话，多渲染几次
          容易导致内存泄漏和额外CPU或GPU占用哦*/
  },
};
</script>

<style lang="scss">
.table {
  padding: 10px 33px;
  width: 100%;
  margin: 0 auto;
  height: 620px;

  .table_main {
    width: 100%;
    height: calc(100% - 25px);
    overflow: hidden;
    z-index: 0;

    .table_col {
      width: 100%;
      height: 80px;
      background: url("../../../assets/bigScreenTwo/border.png") no-repeat 0px
        0px !important;
      background-size: 100% 100% !important;
      padding: 0 32px;
      font-size: 14px;
      margin-bottom: 14px;
      .last-child {
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-top: 16px;
        font-size: 16px;
        font-weight: bold;
        font-weight: 500;
        color: #e6f7ff;
        font-style: normal;
      }
      .item-bottom {
        margin-top: 8px;
        display: flex;
        font-family: "pingFangMedium";
        font-weight: 300;
        font-size: 14px;
        color: #e6f7ff;
        font-style: normal;
        justify-content: space-between;
      }
    }
    @keyframes scoll-Table {
      from {
        transform: translate(0, 0px);
      }

      to {
        transform: translate(0, calc(((85vh - 20px) / 3 - 90px) - 400px));
      }
    }

    .scoll-Table {
      animation: scoll-Table 7s linear infinite;
      transition: all 1s ease-out;
      //  animation-timing-function: linear;
      // animation-fill-mode: forwards;
      /* 在动画结束后保持最后一个关键帧的状态 */
    }

    /* 鼠标进入 */
    .scoll-Table:hover {
      animation-play-state: paused;
    }

    /* 鼠标离开 */
    .scoll-Table:not(:hover) {
      animation-play-state: running;
    }
  }
}
</style>
