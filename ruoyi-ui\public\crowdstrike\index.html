﻿<!DOCTYPE html>
<html>
  <head>
    <title>crowdstrike</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/crowdstrike/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/crowdstrike/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u0" class="ax_default box_2">
        <div id="u0_div" class=""></div>
        <div id="u0_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1" class="ax_default box_2">
        <div id="u1_div" class=""></div>
        <div id="u1_text" class="text ">
          <p><span>事件发展趋势</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2" class="ax_default box_2">
        <div id="u2_div" class=""></div>
        <div id="u2_text" class="text ">
          <p><span>影响范围</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3" class="ax_default box_2">
        <div id="u3_div" class=""></div>
        <div id="u3_text" class="text ">
          <p style="font-size:14px;"><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;font-weight:700;color:#00FFFF;">即时影响</span></p><p style="font-size:12px;"><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;color:#FFFFFF;">事事件发生当天，全球范围内的Windows操作系统计算机出现大规模蓝屏死机，导致即时的业务中断和运营混乱。航空、银行、医疗等行业立即受到影响，航班取消、银行服务中断、医院手术推迟。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4" class="ax_default box_2">
        <div id="u4_div" class=""></div>
        <div id="u4_text" class="text ">
          <p style="font-size:14px;"><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;font-weight:700;color:#00FFFF;">短期影响</span></p><p style="font-size:12px;"><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;color:#FFFFFF;">随着事件的发酵，更多企业和组织意识到问题的严重性，开始采取措施进行系统恢复。这一阶段，全球经济损失开始显现，保险赔付需求上升，相关企业和行业的股价可能出现波动。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5" class="ax_default box_2">
        <div id="u5_div" class=""></div>
        <div id="u5_text" class="text ">
          <p style="font-size:14px;text-align:left;"><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;font-weight:700;color:#00FFFF;">中期影响</span></p><p style="font-size:12px;text-align:justify;text-justify:inter-word;"><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;color:#FFFFFF;">在接下来的几周内，受影响的企业努力恢复正常运营，但完全恢复可能需要更长时间。此期间，经济损失评估更加明确，保险赔付开始实施，企业可能面临法律诉讼和财务赔偿。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6" class="ax_default box_2">
        <div id="u6_div" class=""></div>
        <div id="u6_text" class="text ">
          <p style="font-size:14px;text-align:left;"><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;font-weight:700;color:#00FFFF;">长期影响</span></p><p style="font-size:12px;text-align:justify;text-justify:inter-word;"><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;color:#FFFFFF;">事件的长期影响包括对网络安全意识的提升、政策和法规的修订、企业网络安全策略的重新评估和加强。此外，可能导致对...</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u7" class="ax_default label">
        <div id="u7_div" class=""></div>
        <div id="u7_text" class="text ">
          <p><span>经济影响</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u8" class="ax_default label">
        <div id="u8_div" class=""></div>
        <div id="u8_text" class="text ">
          <p><span>关联事件</span></p>
        </div>
      </div>

      <!-- Unnamed (表格) -->
      <div id="u9" class="ax_default">

        <!-- Unnamed (表格单元) -->
        <div id="u10" class="ax_default table_cell">
          <img id="u10_img" class="img " src="images/crowdstrike/u10.png"/>
          <div id="u10_text" class="text ">
            <p><span>7月10日</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u11" class="ax_default table_cell">
          <img id="u11_img" class="img " src="images/crowdstrike/u11.png"/>
          <div id="u11_text" class="text ">
            <p><span style="color:#409EFF;">埃隆·马斯克（Elon Musk）</span><span style="color:#FFFFFF;">在社交平台上表示，将直接在特斯拉所有系统中全部删掉</span><span style="color:#F59A23;">CrowdStrike</span><span style="color:#FFFFFF;">...</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u12" class="ax_default table_cell">
          <img id="u12_img" class="img " src="images/crowdstrike/u10.png"/>
          <div id="u12_text" class="text ">
            <p><span>7月9日</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u13" class="ax_default table_cell">
          <img id="u13_img" class="img " src="images/crowdstrike/u11.png"/>
          <div id="u13_text" class="text ">
            <p><span style="color:#409EFF;">肖新光</span><span style="color:#FFFFFF;">：</span><span style="color:#F59A23;">CrowdStrike</span><span style="color:#FFFFFF;">在技术结构设计、运行理念和技术能力上的先进性是根本内因</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u14" class="ax_default table_cell">
          <img id="u14_img" class="img " src="images/crowdstrike/u14.png"/>
          <div id="u14_text" class="text ">
            <p><span>6月20日</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u15" class="ax_default table_cell">
          <img id="u15_img" class="img " src="images/crowdstrike/u15.png"/>
          <div id="u15_text" class="text ">
            <p><span style="color:#409EFF;">美国商务部工业和安全局(BIS)</span><span style="color:#FFFFFF;">宣布全面禁止</span><span style="color:#409EFF;">卡巴斯基</span><span style="color:#FFFFFF;">实验室及其所有附属公司、子公司和母公司...</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u16" class="ax_default label">
        <div id="u16_div" class=""></div>
        <div id="u16_text" class="text ">
          <p><span>行业影响</span></p>
        </div>
      </div>

      <!-- Unnamed (表格) -->
      <div id="u17" class="ax_default">

        <!-- Unnamed (表格单元) -->
        <div id="u18" class="ax_default">
          <img id="u18_img" class="img " src="images/crowdstrike/u18.png"/>
          <div id="u18_text" class="text ">
            <p><span>航空运输</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u19" class="ax_default">
          <img id="u19_img" class="img " src="images/crowdstrike/u19.png"/>
          <div id="u19_text" class="text ">
            <p><span style="color:#409EFF;">美国</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">澳大利亚</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">英国</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">荷兰</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">印度</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">捷克</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">匈牙利</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">西班牙</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">中国香港</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">瑞士</span><span style="color:#FFFFFF;">等部分航空公司出现航班延误或机场服务中断。</span><span style="color:#409EFF;">美国达美航空</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">美国航空</span><span style="color:#FFFFFF;">和</span><span style="color:#409EFF;">忠实航空</span><span style="color:#FFFFFF;">宣布停飞所有航班。</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u20" class="ax_default">
          <img id="u20_img" class="img " src="images/crowdstrike/u18.png"/>
          <div id="u20_text" class="text ">
            <p><span>媒体通信</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u21" class="ax_default">
          <img id="u21_img" class="img " src="images/crowdstrike/u19.png"/>
          <div id="u21_text" class="text ">
            <p><span style="color:#409EFF;">以色列邮政</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">法国电视频道TF1</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">TFX</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">LCI和Canal+Group网络</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">爱尔兰国家广播公司RTÉ</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">加拿大广播公司</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">沃丰达集团</span><span style="color:#FFFFFF;">、电话和互联网服务提供商</span><span style="color:#409EFF;">Bouygues Telecom</span><span style="color:#FFFFFF;">等。</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u22" class="ax_default">
          <img id="u22_img" class="img " src="images/crowdstrike/u22.png"/>
          <div id="u22_text" class="text ">
            <p><span>交通运输</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u23" class="ax_default">
          <img id="u23_img" class="img " src="images/crowdstrike/u23.png"/>
          <div id="u23_text" class="text ">
            <p><span style="color:#FFFFFF;">澳大利亚货运列车运营商</span><span style="color:#409EFF;">Aurizon</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">西日本旅客铁道公司</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">马来西亚</span><span style="color:#FFFFFF;">铁路运营商</span><span style="color:#409EFF;">KTMB</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">英国铁路公司</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">澳大利亚猎人线</span><span style="color:#FFFFFF;">和</span><span style="color:#409EFF;">南部高地线</span><span style="color:#FFFFFF;">的区域列车等。</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u24" class="ax_default">
          <img id="u24_img" class="img " src="images/crowdstrike/u18.png"/>
          <div id="u24_text" class="text ">
            <p><span>银行与金融服务</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u25" class="ax_default">
          <img id="u25_img" class="img " src="images/crowdstrike/u19.png"/>
          <div id="u25_text" class="text ">
            <p><span style="color:#409EFF;">加拿大皇家银行</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">加拿大道明银行</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">印度储备银行</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">印度国家银行</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">新加坡星展银行</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">巴西布拉德斯科银行</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">西太平洋银行</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">澳新银行</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">联邦银行</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">本迪戈银行</span><span style="color:#FFFFFF;">等。</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u26" class="ax_default">
          <img id="u26_img" class="img " src="images/crowdstrike/u22.png"/>
          <div id="u26_text" class="text ">
            <p><span>零售</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u27" class="ax_default">
          <img id="u27_img" class="img " src="images/crowdstrike/u23.png"/>
          <div id="u27_text" class="text ">
            <p><span style="color:#409EFF;">德国</span><span style="color:#FFFFFF;">连锁超市</span><span style="color:#409EFF;">Tegut</span><span style="color:#FFFFFF;">、部分</span><span style="color:#409EFF;">麦当劳</span><span style="color:#FFFFFF;">和</span><span style="color:#409EFF;">星巴克</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">迪克体育用品公司</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">英国</span><span style="color:#FFFFFF;">杂货连锁店</span><span style="color:#409EFF;">Waitrose</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">新西兰</span><span style="color:#FFFFFF;">的</span><span style="color:#409EFF;">Foodstuffs</span><span style="color:#FFFFFF;">和</span><span style="color:#409EFF;">Woolworths</span><span style="color:#FFFFFF;">超市等。</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u28" class="ax_default">
          <img id="u28_img" class="img " src="images/crowdstrike/u28.png"/>
          <div id="u28_text" class="text ">
            <p><span>医疗服务</span></p>
          </div>
        </div>

        <!-- Unnamed (表格单元) -->
        <div id="u29" class="ax_default">
          <img id="u29_img" class="img " src="images/crowdstrike/u29.png"/>
          <div id="u29_text" class="text ">
            <p><span style="color:#409EFF;">纪念斯隆凯特琳癌症中心</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">英国国家医疗服务体系</span><span style="color:#FFFFFF;">、</span><span style="color:#409EFF;">德国吕贝克</span><span style="color:#FFFFFF;">和</span><span style="color:#409EFF;">基尔</span><span style="color:#FFFFFF;">的两家医院、北美部分医院等。</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u30" class="ax_default box_2">
        <img id="u30_img" class="img " src="images/crowdstrike/u30.svg"/>
        <div id="u30_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u31" class="ax_default box_2">
        <img id="u31_img" class="img " src="images/crowdstrike/u30.svg"/>
        <div id="u31_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u32" class="ax_default box_2">
        <img id="u32_img" class="img " src="images/crowdstrike/u32.svg"/>
        <div id="u32_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u33" class="ax_default box_2">
        <img id="u33_img" class="img " src="images/crowdstrike/u30.svg"/>
        <div id="u33_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u34" class="ax_default _三级标题">
        <div id="u34_div" class=""></div>
        <div id="u34_text" class="text ">
          <p><span>7月19日</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u35" class="ax_default" data-left="68" data-top="80" data-width="7" data-height="82">

        <!-- Unnamed (椭圆) -->
        <div id="u36" class="ax_default ellipse">
          <img id="u36_img" class="img " src="images/crowdstrike/u36.svg"/>
          <div id="u36_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (垂直线) -->
        <div id="u37" class="ax_default line1">
          <img id="u37_img" class="img " src="images/crowdstrike/u37.svg"/>
          <div id="u37_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u38" class="ax_default" data-left="422" data-top="92" data-width="7" data-height="70">

        <!-- Unnamed (椭圆) -->
        <div id="u39" class="ax_default ellipse">
          <img id="u39_img" class="img " src="images/crowdstrike/u36.svg"/>
          <div id="u39_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (垂直线) -->
        <div id="u40" class="ax_default line1">
          <img id="u40_img" class="img " src="images/crowdstrike/u40.svg"/>
          <div id="u40_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u41" class="ax_default" data-left="242" data-top="220" data-width="7" data-height="82">

        <!-- Unnamed (椭圆) -->
        <div id="u42" class="ax_default ellipse">
          <img id="u42_img" class="img " src="images/crowdstrike/u36.svg"/>
          <div id="u42_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (垂直线) -->
        <div id="u43" class="ax_default line1">
          <img id="u43_img" class="img " src="images/crowdstrike/u37.svg"/>
          <div id="u43_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u44" class="ax_default" data-left="581" data-top="220" data-width="7" data-height="82">

        <!-- Unnamed (椭圆) -->
        <div id="u45" class="ax_default ellipse">
          <img id="u45_img" class="img " src="images/crowdstrike/u36.svg"/>
          <div id="u45_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (垂直线) -->
        <div id="u46" class="ax_default line1">
          <img id="u46_img" class="img " src="images/crowdstrike/u37.svg"/>
          <div id="u46_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u47" class="ax_default _三级标题">
        <div id="u47_div" class=""></div>
        <div id="u47_text" class="text ">
          <p><span>更长时间</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u48" class="ax_default _三级标题">
        <div id="u48_div" class=""></div>
        <div id="u48_text" class="text ">
          <p><span>7月20日~数周后</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u49" class="ax_default _三级标题">
        <div id="u49_div" class=""></div>
        <div id="u49_text" class="text ">
          <p><span>7月19日~7月20日</span></p>
        </div>
      </div>

      <!-- 柱状图 (组合) -->
      <div id="u50" class="ax_default" data-label="柱状图" data-left="28" data-top="579" data-width="750" data-height="113">

        <!-- acp-config (中继器) -->
        <div id="u51" class="ax_default" data-label="acp-config">
          <script id="u51_script" type="axure-repeater-template" data-label="acp-config">
          </script>
          <div id="u51-1" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-2" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-3" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-4" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-5" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-6" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-7" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-8" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-9" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-10" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-11" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-12" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-13" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-14" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-15" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-16" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-17" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-18" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-19" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-20" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-21" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-22" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-23" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-24" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-25" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-26" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-27" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-28" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-29" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-30" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-31" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-32" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-33" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-34" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u51-35" class="preeval" style="width: 0px; height: 0px;">
          </div>
        </div>

        <!-- acp-g2pColumn-chart (SVG) -->
        <div id="u52" class="ax_default image1" data-label="acp-g2pColumn-chart">
          <img id="u52_img" class="img " src="images/crowdstrike/acp-g2pcolumn-chart_u52.svg"/>
          <div id="u52_text" class="text " style="display:none; visibility: hidden">
            <p style="line-height:normal;"></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u53" class="ax_default label">
        <div id="u53_div" class=""></div>
        <div id="u53_text" class="text ">
          <p><span>事件主体经济走势</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u54" class="ax_default label">
        <div id="u54_div" class=""></div>
        <div id="u54_text" class="text ">
          <p><span>关联行业经济影响</span></p>
        </div>
      </div>

      <!-- 股票图 (组合) -->
      <div id="u55" class="ax_default" data-label="股票图" data-left="29" data-top="432" data-width="750" data-height="113">

        <!-- acp-config (中继器) -->
        <div id="u56" class="ax_default" data-label="acp-config">
          <script id="u56_script" type="axure-repeater-template" data-label="acp-config">
          </script>
          <div id="u56-1" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-2" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-3" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-4" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-5" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-6" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-7" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-8" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-9" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-10" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-11" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-12" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-13" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-14" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-15" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-16" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-17" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-18" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-19" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-20" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-21" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-22" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-23" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-24" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-25" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-26" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-27" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-28" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-29" class="preeval" style="width: 0px; height: 0px;">
          </div>
          <div id="u56-30" class="preeval" style="width: 0px; height: 0px;">
          </div>
        </div>

        <!-- acp-g2pStock-chart (SVG) -->
        <div id="u57" class="ax_default image1" data-label="acp-g2pStock-chart">
          <img id="u57_img" class="img " src="images/crowdstrike/acp-g2pstock-chart_u57.svg"/>
          <div id="u57_text" class="text " style="display:none; visibility: hidden">
            <p style="line-height:normal;"></p>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
