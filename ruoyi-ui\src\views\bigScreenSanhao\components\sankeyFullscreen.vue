<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div
      v-show="show"
      class="custom-dialog"
      :class="{ 'sankey-fullscreen': isFullscreen }"
      :style="
        isFullscreen
          ? {}
          : { width: typeof width === 'number' ? width + 'px' : width }
      "
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div class="date-picker-container">
          <el-date-picker
            v-model="fullscreenDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="onDateRangeChange"
            size="small"
            :picker-options="pickerOptions"
          />
        </div>
        <div style="display: flex; align-items: center">
          <div
            @click="handleScreen"
            :title="isFullscreen ? '退出全屏' : '全屏'"
            style="
              margin-right: 20px;
              cursor: pointer;
              color: #ffffff;
              font-size: 20px;
            "
          >
            <i
              :class="isFullscreen ? 'el-icon-rank' : 'el-icon-full-screen'"
              style="width: 20px; height: 20px"
            ></i>
          </div>
          <div class="custom-dialog-close" @click="closeDialog"></div>
        </div>
      </div>
      <div class="custom-dialog-body">
        <!-- 筛选条件区域 -->
        <!-- <div class="filter-container">
          <div class="filter-row">
            <div class="filter-item">
              <label>日期区间：</label>
              <el-date-picker
                v-model="fullscreenDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                size="small"
                class="filter-date-picker"
                :picker-options="pickerOptions"
              />
            </div>
            <div class="filter-item">
              <label>议员：</label>
              <el-input
                v-model="sankeyFilters.proposalsExperts"
                placeholder="请输入议员名称"
                size="small"
                clearable
                class="filter-input"
              />
            </div>
            <div class="filter-item">
              <label>提案：</label>
              <el-input
                v-model="sankeyFilters.proposalsTitle"
                placeholder="请输入提案名称"
                size="small"
                clearable
                class="filter-input"
              />
            </div>
            <div class="filter-item">
              <label>领域：</label>
              <el-input
                v-model="sankeyFilters.parentName"
                placeholder="请输入领域"
                size="small"
                clearable
                class="filter-input"
              />
            </div>
            <div class="filter-item">
              <label>子领域：</label>
              <el-input
                v-model="sankeyFilters.labelName"
                placeholder="请输入子领域"
                size="small"
                clearable
                class="filter-input"
              />
            </div>
            <div class="filter-item">
              <label>企业：</label>
              <el-input
                v-model="sankeyFilters.enterpriseName"
                placeholder="请输入企业名称"
                size="small"
                clearable
                class="filter-input"
              />
            </div>
            <div class="filter-item">
              <el-button
                type="primary"
                size="small"
                @click="searchSankeyData"
                class="search-btn"
                icon="el-icon-search"
              >
                搜索
              </el-button>
            </div>
            <div class="filter-item">
              <el-button
                size="small"
                @click="resetSankeyFilters"
                class="reset-btn"
              >
                重置
              </el-button>
            </div>
          </div>
        </div> -->
        <div class="bg-box">
          <div class="bg-box-content">
            <div class="sankey-container" :style="{ height: containerHeight }">
              <div
                ref="sankeyChart"
                class="sankey-chart"
                :style="{ height: dynamicHeight }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 节点关系弹窗 -->
    <NodeRelationDialog
      :visible.sync="nodeRelationVisible"
      :nodeData="selectedNodeData"
      title="节点完整关系路径"
    />
  </div>
</template>

<script>
import * as echarts from "echarts";
import { zcfxSankey } from "@/api/bigScreen/sanhao.js";
import NodeRelationDialog from "./NodeRelationDialog.vue";

export default {
  name: "SankeyFullscreen",
  components: {
    NodeRelationDialog,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "美国提案影响分析",
    },
    closeOnClickMask: {
      type: Boolean,
      default: false,
    },
    width: {
      type: [String, Number],
      default: "95%",
    },
  },
  data() {
    return {
      show: false,
      chart: null,
      loading: false,
      sankeyData: {
        nodes: [],
        links: [],
      },
      // 原始数据，包含所有节点和连接
      originalData: {
        nodes: [],
        links: [],
      },
      // 记录哪些子标签节点已展开企业
      expandedSubLabels: new Set(),
      // 节点关系弹窗相关数据
      nodeRelationVisible: false,
      selectedNodeData: null,
      // 全屏组件独立的日期区间
      fullscreenDateRange: [],
      // 全屏状态
      isFullscreen: false,
      // ResizeObserver 实例
      resizeObserver: null,
      // 防抖定时器
      resizeTimer: null,
      // 桑基图筛选条件
      sankeyFilters: {
        proposalsExperts: "", // 议员
        proposalsTitle: "", // 提案
        parentName: "", // 领域
        labelName: "", // 子领域
        enterpriseName: "", // 企业
      },

      // 日期选择器快捷选项
      pickerOptions: {
        shortcuts: [
          {
            text: "近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      // 基础高度
      baseHeight: 800,
      // 每个展开企业增加的高度
      heightPerExpansion: 50,
    };
  },
  computed: {
    // 动态计算桑葚图高度
    dynamicHeight() {
      // 计算当前显示的企业节点数量
      const enterpriseCount = this.sankeyData.nodes.filter(
        (node) => node.category === "企业"
      ).length;

      // 基础高度 + 企业数量 * 每个企业增加的高度
      const calculatedHeight =
        this.baseHeight + enterpriseCount * this.heightPerExpansion;
      return `${calculatedHeight}px`;
    },

    // 动态计算容器高度（用于滚动）
    containerHeight() {
      // 容器高度固定为基础高度，超出部分显示滚动条
      return `${this.baseHeight}px`;
    },
  },
  mounted() {
    // 添加ESC键监听
    document.addEventListener("keydown", this.handleKeydown);
    // 添加窗口大小变化监听
    window.addEventListener("resize", this.handleWindowResize);
  },

  beforeDestroy() {
    this.destroyChart();
    // 移除ESC键监听
    document.removeEventListener("keydown", this.handleKeydown);
    // 移除窗口大小变化监听
    window.removeEventListener("resize", this.handleWindowResize);
  },

  watch: {
    visible(newVal) {
      if (newVal) {
        this.show = true;
        this.$nextTick(() => {
          this.initDefaultDateRange();
          // this.resetSankeyFilters();
          // 重置展开状态，确保所有子领域都是收起的
          this.expandedSubLabels.clear();
          // 重置全屏状态
          this.isFullscreen = false;
          this.initChart();
          this.fetchSankeyData();
        });
      } else {
        this.show = false;
        this.destroyChart();
      }
    },
  },
  methods: {
    // 初始化默认日期范围（最近三个月）
    initDefaultDateRange() {
      const today = new Date();
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(today.getMonth() - 3);

      this.fullscreenDateRange = [
        this.formatDate(threeMonthsAgo),
        this.formatDate(today),
      ];
    },

    // 格式化日期为 yyyy-MM-dd 格式
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    // 关闭弹窗的方法
    closeDialog() {
      this.$emit("update:visible", false);
    },

    // 处理遮罩层点击事件
    handleMaskClick() {
      if (this.closeOnClickMask) {
        this.closeDialog();
      }
    },

    // 日期区间变化处理（移除自动查询）
    onDateRangeChange() {
      // 不再自动查询，需要点击搜索按钮
      if (this.fullscreenDateRange && this.fullscreenDateRange.length === 2) {
        this.fetchSankeyData();
      }
    },

    // 搜索桑基图数据
    searchSankeyData() {
      this.fetchSankeyData();
    },

    // 重置桑基图筛选条件
    resetSankeyFilters() {
      this.sankeyFilters = {
        proposalsExperts: "",
        proposalsTitle: "",
        parentName: "",
        labelName: "",
        enterpriseName: "",
      };
      // 重新初始化默认日期范围
      this.initDefaultDateRange();
      // 自动执行搜索
      this.searchSankeyData();
    },

    // 获取桑葚图数据
    async fetchSankeyData() {
      try {
        this.loading = true;

        // 构建请求参数
        const params = {
          screenSn: "1",
        };

        // 如果有选择日期范围，添加日期参数
        if (this.fullscreenDateRange && this.fullscreenDateRange.length === 2) {
          params.startDate = this.fullscreenDateRange[0];
          params.endDate = this.fullscreenDateRange[1];
        }

        // 添加筛选条件参数
        // if (this.sankeyFilters) {
        //   if (this.sankeyFilters.proposalsExperts) {
        //     params.proposalsExperts = this.sankeyFilters.proposalsExperts;
        //   }
        //   if (this.sankeyFilters.proposalsTitle) {
        //     params.proposalsTitle = this.sankeyFilters.proposalsTitle;
        //   }
        //   if (this.sankeyFilters.parentName) {
        //     params.parentName = this.sankeyFilters.parentName;
        //   }
        //   if (this.sankeyFilters.labelName) {
        //     params.labelName = this.sankeyFilters.labelName;
        //   }
        //   if (this.sankeyFilters.enterpriseName) {
        //     params.enterpriseName = this.sankeyFilters.enterpriseName;
        //   }
        // }

        const response = await zcfxSankey(params);

        if (response && response.data) {
          // 处理节点数据
          const processedNodes = this.processNodes(response.data.nodes || []);
          // 处理连接数据
          const processedLinks = this.processLinks(response.data.links || []);

          // 保存原始数据
          this.originalData = {
            nodes: processedNodes,
            links: processedLinks,
          };

          // 初始化时隐藏企业节点
          this.sankeyData = this.filterEnterpriseNodes(this.originalData);

          if (this.sankeyData.links.length === 0) {
            this.$message.warning("没有找到相关数据");
            this.sankeyData.nodes = [];
          }

          // 更新图表
          this.updateChart();
        }
      } catch (error) {
        console.error("获取桑葚图数据失败:", error);
      } finally {
        this.loading = false;
      }
    },

    // 处理节点数据
    processNodes(nodes) {
      const colors = ["#dd79ff", "#58d9f9", "#4992ff"];

      // category到层级的映射
      const categoryToDepth = {
        党派: 0,
        专家: 1,
        提案: 2,
        父标签: 3,
        子标签: 4,
        企业: 5,
      };

      return nodes.map((node, index) => {
        const depth =
          categoryToDepth[node.category] !== undefined
            ? categoryToDepth[node.category]
            : 0;

        return {
          id: node.id,
          name: node.name,
          category: node.category,
          depth: depth,
          itemStyle: {
            color: colors[index % colors.length],
          },
        };
      });
    },

    // 处理连接数据
    processLinks(links) {
      return links.map((link) => {
        return {
          source: link.source,
          target: link.target,
          value: link.value || 1, // 如果没有value，默认为1
        };
      });
    },

    // 过滤企业节点，根据展开状态决定是否显示
    filterEnterpriseNodes(data) {
      // 如果没有展开任何子标签，则隐藏所有企业节点
      if (this.expandedSubLabels.size === 0) {
        const filteredNodes = data.nodes.filter(
          (node) => node.category !== "企业"
        );
        const filteredLinks = data.links.filter((link) => {
          const sourceExists = filteredNodes.find((n) => n.id === link.source);
          const targetExists = filteredNodes.find((n) => n.id === link.target);
          return sourceExists && targetExists;
        });

        return {
          nodes: filteredNodes,
          links: filteredLinks,
        };
      }

      // 如果有展开的子标签，则显示对应的企业节点
      const filteredNodes = data.nodes.filter((node) => {
        if (node.category === "企业") {
          // 查找连接到此企业的子标签节点
          const connectedToSubLabel = data.links.some((link) => {
            // 检查企业是否与已展开的子标签相连
            if (link.target === node.id) {
              // 企业是目标，检查源是否是已展开的子标签
              const sourceNode = data.nodes.find((n) => n.id === link.source);
              return (
                sourceNode &&
                sourceNode.category === "子标签" &&
                this.expandedSubLabels.has(sourceNode.id)
              );
            }
            if (link.source === node.id) {
              // 企业是源，检查目标是否是已展开的子标签
              const targetNode = data.nodes.find((n) => n.id === link.target);
              return (
                targetNode &&
                targetNode.category === "子标签" &&
                this.expandedSubLabels.has(targetNode.id)
              );
            }
            return false;
          });

          return connectedToSubLabel;
        }
        return true; // 非企业节点都显示
      });

      const filteredLinks = data.links.filter((link) => {
        const sourceExists = filteredNodes.find((n) => n.id === link.source);
        const targetExists = filteredNodes.find((n) => n.id === link.target);
        return sourceExists && targetExists;
      });

      return {
        nodes: filteredNodes,
        links: filteredLinks,
      };
    },

    // 更新图表
    updateChart() {
      if (this.chart) {
        const option = this.getChartOption();
        // 使用 notMerge: false 来避免完全重新渲染，提高性能
        this.chart.setOption(option, true);
      }
    },

    // 获取图表配置
    getChartOption() {
      return {
        backgroundColor: "transparent",
        title: {
          text: "",
          textStyle: {
            color: "#ffffff",
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: "item",
          triggerOn: "mousemove",
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          borderColor: "#0ec2f4",
          borderWidth: 1,
          textStyle: {
            color: "#ffffff",
          },
          formatter: function (params) {
            if (params.dataType === "edge") {
              return `${params.data.source} → ${params.data.target}<br/>影响度: ${params.data.value}`;
            } else {
              const depthMap = {
                0: "第1级",
                1: "第2级",
                2: "第3级",
                3: "第4级",
                4: "第5级",
                5: "第6级",
              };
              const levelText = depthMap[params.data.depth] || "未知层级";
              return `${params.data.name}<br/>类别: ${
                params.data.category || "未知"
              }<br/>层级: ${levelText}`;
            }
          },
        },
        series: [
          {
            type: "sankey",
            layout: "none",
            emphasis: {
              focus: "adjacency",
            },
            data: this.sankeyData.nodes,
            links: this.sankeyData.links,
            // orient: 'vertical',
            // nodeAlign: "justify",
            nodeGap: 10,
            nodeWidth: 40,
            layoutIterations: 0,
            left: "2%",
            right: "16%",
            top: "0.2%",
            bottom: "0.2%",
            label: {
              show: true,
              position: "right",
              color: "#ffffff",
              fontSize: 16,
              formatter: function (params) {
                return params.name.length > 16
                  ? params.name.substring(0, 16) + "..."
                  : params.name;
              },
            },
            lineStyle: {
              color: "source",
            },
          },
        ],
      };
    },

    // 初始化图表
    initChart() {
      if (this.$refs.sankeyChart) {
        // 设置图表容器的尺寸 - 初始化时使用基础高度
        this.$refs.sankeyChart.style.width = "100%";
        this.$refs.sankeyChart.style.height = `${this.baseHeight}px`;

        this.chart = echarts.init(this.$refs.sankeyChart);

        // 添加节点点击事件
        this.chart.on("click", (params) => {
          if (params.dataType === "node" && params.data.category === "子标签") {
            this.toggleEnterpriseNodes(params.data.id);
          }
        });

        // 添加节点双击事件
        this.chart.on("dblclick", (params) => {
          if (params.dataType === "node") {
            this.handleNodeDoubleClick(params.data);
          }
        });

        // 设置尺寸变化监听
        this.setupResizeListeners();
      }
    },

    // 切换企业节点的显示/隐藏
    toggleEnterpriseNodes(subLabelId) {
      if (this.expandedSubLabels.has(subLabelId)) {
        // 如果已展开，则收起
        this.expandedSubLabels.delete(subLabelId);
      } else {
        // 如果未展开，则展开
        this.expandedSubLabels.add(subLabelId);
      }

      // 重新过滤数据
      const newSankeyData = this.filterEnterpriseNodes(this.originalData);

      // 计算新高度
      const enterpriseCount = newSankeyData.nodes.filter(
        (node) => node.category === "企业"
      ).length;
      const newHeight = `${
        this.baseHeight + enterpriseCount * this.heightPerExpansion
      }px`;

      // 同时更新数据、高度和图表，确保完全同步
      this.sankeyData = newSankeyData;
      if (this.$refs.sankeyChart) {
        this.$refs.sankeyChart.style.height = newHeight;
      }

      // 如果没有任何展开的子领域（所有企业都收起），回到顶部并恢复初始高度
      if (this.expandedSubLabels.size === 0) {
        // 先强制设置高度为基础高度
        if (this.$refs.sankeyChart) {
          this.$refs.sankeyChart.style.height = `${this.baseHeight}px`;
          // 强制浏览器重新计算布局
          this.$refs.sankeyChart.offsetHeight;
        }

        // 立即更新图表以确保高度生效
        if (this.chart) {
          this.chart.resize();
        }

        // 然后滚动到顶部
        const container = this.$refs.sankeyChart.parentElement;
        if (container) {
          // 强制触发重绘
          container.offsetHeight;
          container.scrollTop = 0;
          // 使用 scrollTo 确保滚动生效
          container.scrollTo({ top: 0, behavior: "instant" });
        }
      }

      // 立即更新图表
      this.updateChart();
      if (this.chart) {
        this.chart.resize();
      }
    },

    // 处理节点双击事件
    handleNodeDoubleClick(nodeData) {
      // 从原始数据中提取与该节点相关的所有关系
      const relatedData = this.extractNodeRelations(nodeData);

      this.selectedNodeData = {
        node: nodeData,
        relations: relatedData,
      };

      this.nodeRelationVisible = true;
    },

    // 提取节点关系数据（包含所有上下游路径）
    extractNodeRelations(targetNode) {
      const relatedNodes = new Set();
      const visitedUpstream = new Set();
      const visitedDownstream = new Set();

      // 添加目标节点
      relatedNodes.add(targetNode.id);

      // 递归查找所有上游节点（指向目标节点的路径）
      const findUpstreamNodes = (nodeId) => {
        if (visitedUpstream.has(nodeId)) return;
        visitedUpstream.add(nodeId);

        this.originalData.links.forEach((link) => {
          if (link.target === nodeId && !relatedNodes.has(link.source)) {
            relatedNodes.add(link.source);
            // 递归查找上游的上游
            findUpstreamNodes(link.source);
          }
        });
      };

      // 递归查找所有下游节点（从目标节点出发的路径）
      const findDownstreamNodes = (nodeId) => {
        if (visitedDownstream.has(nodeId)) return;
        visitedDownstream.add(nodeId);

        this.originalData.links.forEach((link) => {
          if (link.source === nodeId && !relatedNodes.has(link.target)) {
            relatedNodes.add(link.target);
            // 递归查找下游的下游
            findDownstreamNodes(link.target);
          }
        });
      };

      // 查找所有相关节点
      findUpstreamNodes(targetNode.id);
      findDownstreamNodes(targetNode.id);

      // 收集所有相关节点之间的连线
      const relatedLinks = [];
      this.originalData.links.forEach((link) => {
        // 如果连线的源节点和目标节点都在相关节点集合中，则添加这条连线
        if (relatedNodes.has(link.source) && relatedNodes.has(link.target)) {
          relatedLinks.push(link);
        }
      });

      // 获取相关节点的详细信息
      const nodes = this.originalData.nodes.filter((node) =>
        relatedNodes.has(node.id)
      );

      console.log("目标节点:", targetNode.name);
      console.log("相关节点数量:", relatedNodes.size);
      console.log("相关连线数量:", relatedLinks.length);
      console.log("相关节点:", Array.from(relatedNodes));

      return {
        nodes: nodes,
        links: relatedLinks,
      };
    },

    // 销毁图表
    destroyChart() {
      if (this.chart) {
        // 清理 ResizeObserver
        if (this.resizeObserver) {
          this.resizeObserver.disconnect();
        }
        // 清理定时器
        if (this.resizeTimer) {
          clearTimeout(this.resizeTimer);
        }
        // 清理窗口大小变化监听
        window.removeEventListener("resize", this.handleResize);
        this.chart.dispose();
        this.chart = null;
      }
    },

    // 设置尺寸变化监听
    setupResizeListeners() {
      // 创建 ResizeObserver 监听容器尺寸变化
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(() => {
          // 使用防抖处理，避免频繁触发
          clearTimeout(this.resizeTimer);
          this.resizeTimer = setTimeout(() => {
            this.handleResize();
          }, 100);
        });

        // 监听图表容器的尺寸变化
        this.resizeObserver.observe(this.$refs.sankeyChart);

        // 也监听父容器的尺寸变化
        const parentContainer = this.$refs.sankeyChart.parentElement;
        if (parentContainer) {
          this.resizeObserver.observe(parentContainer);
        }
      }

      // 监听窗口大小变化（作为备用方案）
      this.handleResize = this.handleResize.bind(this);
      window.addEventListener("resize", this.handleResize);
    },

    // 处理尺寸变化
    handleResize() {
      if (this.chart) {
        // 延迟执行 resize，确保 DOM 更新完成
        this.$nextTick(() => {
          this.chart.resize();
        });
      }
    },

    // 手动触发图表重新调整大小（供父组件调用）
    resizeChart() {
      this.handleResize();
    },

    // 全屏切换
    handleScreen() {
      this.isFullscreen = !this.isFullscreen;

      // 延迟调整图表大小，确保DOM更新完成
      this.$nextTick(() => {
        setTimeout(() => {
          // 强制重新计算容器尺寸
          if (this.$refs.sankeyChart) {
            this.$refs.sankeyChart.style.height = this.isFullscreen
              ? "100%"
              : "800px";
          }
          this.handleResize();
        }, 300); // 等待CSS动画完成
      });
    },

    // 处理键盘事件
    handleKeydown(event) {
      // 按ESC键退出全屏
      if (event.key === "Escape" && this.isFullscreen && this.visible) {
        this.isFullscreen = false;
      }
    },

    // 处理窗口大小变化
    handleWindowResize() {
      if (this.isFullscreen) {
        // 重新调整图表大小
        this.handleResize();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  // 确保在所有分辨率下都能正确覆盖
  min-width: 100%;
  min-height: 100%;
  overflow: hidden;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("../../../assets/bigScreenSanhao/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;
    // transition: all 0.3s ease;

    &.sankey-fullscreen {
      width: calc(100vw - 40px) !important;
      height: calc(100vh - 40px) !important;
      max-width: none !important;
      max-height: none !important;
      margin: 0 !important;
      // 确保在所有分辨率下都能正确显示
      min-width: calc(100% - 40px) !important;
      min-height: calc(100% - 40px) !important;

      .custom-dialog-body {
        height: calc(100% - 80px); // 减去边距和header高度
        max-height: calc(100% - 80px);
        overflow-y: auto;
        overflow-x: hidden;

        .bg-box-content {
          height: 100%;

          .sankey-container {
            height: 100%; /* 减去header、padding和bg-box的padding */

            .sankey-chart {
              min-height: 100%;
              height: 100%;
            }
          }
        }
      }
    }

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("../../../assets/bigScreenSanhao/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("../../../assets/bigScreenSanhao/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px;
        padding: 16px;
        height: 100%;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }
      }
    }
  }
}

/* 桑葚图容器样式 */
.sankey-container {
  width: 100%;
  height: 800px;
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden;
  position: relative;
}

.sankey-chart {
  width: 100%;
  min-height: 800px; /* 设置最小高度，确保图表有足够空间渲染 */
  /* 完全移除过渡动画，实现瞬间变化 */
}

/* 自定义滚动条样式 */
.sankey-container::-webkit-scrollbar {
  width: 8px;
}

.sankey-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.sankey-container::-webkit-scrollbar-thumb {
  background: rgba(14, 194, 244, 0.6);
  border-radius: 4px;
}

.sankey-container::-webkit-scrollbar-thumb:hover {
  background: rgba(14, 194, 244, 0.8);
}

/* 筛选条件样式 */
.filter-container {
  margin: 10px 0;
  padding: 15px;
  background: #1b283b;
  border-radius: 6px;
  border: 1px solid rgba(14, 194, 244, 0.3);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
  // min-width: 200px;
  margin-bottom: 0;
}

.filter-item label {
  color: #ffffff;
  font-size: 14px;
  white-space: nowrap;
  min-width: 50px;
}

.filter-input {
  width: 180px;
}

.filter-select {
  width: 180px;
}

.filter-date-picker {
  width: 220px;
}

.filter-actions {
  justify-content: center;
  margin-top: 5px;

  .filter-item {
    margin-bottom: 0;
    min-width: 100px;
  }
}

.search-btn {
  background: rgba(14, 194, 244, 0.8);
  border-color: rgba(14, 194, 244, 0.8);
  color: #ffffff;
  // margin-right: 10px;
}

.search-btn:hover {
  background: rgba(14, 194, 244, 1);
  border-color: rgba(14, 194, 244, 1);
}

.reset-btn {
  background: rgba(102, 102, 102, 0.8);
  border-color: rgba(102, 102, 102, 0.8);
  color: #ffffff;
}

.reset-btn:hover {
  background: rgba(102, 102, 102, 1);
  border-color: rgba(102, 102, 102, 1);
}

/* 自定义筛选输入框样式 */
::v-deep .filter-container .el-input__inner {
  background: rgba(14, 194, 244, 0.1);
  border: 1px solid rgba(14, 194, 244, 0.5);
  color: #ffffff;
  border-radius: 4px;
}

::v-deep .filter-container .el-input__inner::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

::v-deep .filter-container .el-input__inner:hover {
  border-color: rgba(14, 194, 244, 0.8);
}

::v-deep .filter-container .el-input__inner:focus {
  border-color: rgba(14, 194, 244, 1);
}

/* 自定义筛选选择框样式 */
::v-deep .filter-container .el-select .el-input__inner {
  background: rgba(14, 194, 244, 0.1);
  border: 1px solid rgba(14, 194, 244, 0.5);
  color: #ffffff;
}

::v-deep .filter-container .el-select .el-input__inner:hover {
  border-color: rgba(14, 194, 244, 0.8);
}

::v-deep .filter-container .el-select .el-input__inner:focus {
  border-color: rgba(14, 194, 244, 1);
}

/* 自定义筛选日期选择器样式 */
::v-deep .filter-container .filter-date-picker {
  background: rgba(14, 194, 244, 0.1);
  border: 1px solid rgba(14, 194, 244, 0.5);
  border-radius: 4px;
}

::v-deep .filter-container .filter-date-picker .el-range-input {
  background: transparent;
  color: #ffffff;
}

::v-deep .filter-container .filter-date-picker .el-range-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

::v-deep .filter-container .filter-date-picker .el-range-separator {
  color: #ffffff;
}

::v-deep .filter-container .filter-date-picker:hover {
  border-color: rgba(14, 194, 244, 0.8);
}

/* 日期选择器容器 */
.date-picker-container {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  margin: 0 20px;
}

/* 自定义日期选择器样式 */
::v-deep .date-picker-container .el-date-editor {
  background: rgba(14, 194, 244, 0.1);
  border: 1px solid rgba(14, 194, 244, 0.5);
  border-radius: 4px;
}

::v-deep .date-picker-container .el-date-editor .el-range-input {
  background: transparent;
  color: #ffffff;
}

::v-deep .date-picker-container .el-date-editor .el-range-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

::v-deep .date-picker-container .el-date-editor .el-range-separator {
  color: #ffffff;
}

::v-deep .date-picker-container .el-date-editor .el-range-input {
  background-color: transparent;
  color: #ffffff;
}

::v-deep .date-picker-container .el-date-editor:hover {
  border-color: rgba(14, 194, 244, 0.8);
}
</style>
