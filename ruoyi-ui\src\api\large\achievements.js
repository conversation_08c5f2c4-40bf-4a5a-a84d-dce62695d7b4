import request from '@/utils/request'

// 查询人工智能-研究成果列表
export function listAchievements(query) {
  return request({
    url: '/large/achievements/list',
    method: 'get',
    params: query
  })
}

// 查询人工智能-研究成果详细
export function getAchievements(id) {
  return request({
    url: '/large/achievements/' + id,
    method: 'get'
  })
}

// 新增人工智能-研究成果
export function addAchievements(data) {
  return request({
    url: '/large/achievements',
    method: 'post',
    data: data
  })
}

// 修改人工智能-研究成果
export function updateAchievements(data) {
  return request({
    url: '/large/achievements/edit',
    method: 'post',
    data: data
  })
}

// 删除人工智能-研究成果
export function delAchievements(data) {
  return request({
    url: '/large/achievements/remove',
    method: 'post',
    data: data
  })
}
