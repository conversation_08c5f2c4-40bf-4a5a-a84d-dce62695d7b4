<!-- 科情专题 -->
<template>
  <div style="overflow: hidden">
    <LeftLink :type="'specialSubject'" :isActive="isActive" :ActiveData="ActiveData" :drawer="drawer" :KeList="KeList"
      @activeLink="ActiveSwitch" @AddKeEvent="AddKeEvent" @deleteEvetn="deleteLink" />
    <div class="rightMain" style="height: calc(100vh - 58px);overflow-y: auto;">
      <topSeach :ActiveData="ActiveData" :SeachData="SeachData" :buttonDisabled="buttonDisabled" @EmitInfo="SwitchInfo"
        @SeachEvent="(resetPage) => SpecialEs(resetPage)" @seniorSerch="seniorSerchFlag = !seniorSerchFlag" :seniorSerchFlag="seniorSerchFlag"
        :areaList="areaList" :countryList="countryList" />
      <MainArticle v-loading="buttonDisabled" :ArticleList="ArticleList" :flag="'Special'" v-if="isInfo == '信息模式'"
        @Refresh="refreshData" @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange"
        :total="total" :currentPage="currentPage" :pageSize="pageSize" :SeachData="SeachData" :keywords="keywords" ref="mainArticle" />
      <statistics v-if="isInfo == '统计模式'" />
    </div>
    <KeDrawer :visble="drawer" :title="DrawerTitle" :editData="editData" @beforCloe="drawer = false"
      @submitKe="submitData" :areaList="areaList" :countryList="countryList" />
  </div>
</template>

<script>
import LeftLink from '@/views/components/leftLink.vue'
import KeDrawer from '@/views/components/KeDrawer.vue'
import topSeach from '@/views/components/topSeach.vue'
import MainArticle from '@/views/components/MainArticle.vue'
import statistics from '@/views/components/statistics.vue'
import api from '@/api/ScienceApi/index.js'
export default {
  components: {
    LeftLink,
    KeDrawer,
    topSeach,
    MainArticle,
    statistics
  },
  data() {
    return {
      DrawerTitle: '新建专题',
      isActive: 0 /* 当前选中的index */,
      ActiveData: {} /* 当前选中的data对像 */,
      drawer: false /* 新增弹窗 */,
      KeList: [],
      isInfo: '信息模式',
      seniorSerchFlag: false,
      editData: {},
      SeachData: {
        metaMode: '' /* 匹配模式 */,
        keywords: '' /* 关键词 */,
        sortMode: false /* 排序模式 */,
        releaseArea: '' /* 发布地区 */,
        timeRange: '4' /* 时间范围 */,
        country: '' /* 国家或地区 */,
        customDay: '' /* 自定义天 */,
        radio: '' /* 平台类型 */,
        area: [] /* 领域 */,
        industry: [] /* 行业 */
      },
      ArticleList: [],
      currentPage: 1,
      pageSize: 50,
      total: 0,
      areaList: [],
      countryList: [],
      buttonDisabled: false,
      keywords: '',
      isEmpty: false
    }
  },
  created() {
    this.getList()
    // window.addEventListener('wheel', event => {
    //   if (event.deltaY > 0) {
    //     this.seniorSerchFlag = false
    //   } else if (event.deltaY < 0) {
    //   }
    // })
  },
  watch: {
    isActive(newVal, oldVal) {
      if (newVal == null) {
        this.isActive = oldVal
      }
    },
    'SeachData.timeRange': {
      handler(newVal, oldVal) {
        this.SeachData.customDay = ''
      },
      deep: true
    },
    KeList: function (newVal, oldVal) {
      if (newVal.length == 0) {
        this.isEmpty = true
      }
    }
  },
  methods: {
    handleSizeChange(num) {
      this.pageSize = num
      this.scrollToTop()
      this.SpecialEs()
    },
    handleCurrentChange(current) {
      this.currentPage = current
      this.scrollToTop()
      this.SpecialEs()
    },
    ActiveSwitch(bool) {
      if (bool == null) {
        bool = this.isActive
      }
      this.isActive = bool
      this.ActiveData = this.KeList[bool]
      setTimeout(() => {
        this.currentPage = 1
        this.SpecialEs(true)
        this.scrollToTop()
      }, 100)
    },
    SwitchInfo(data) {
      this.isInfo = data
    },
    /* 提交 */
    async submitData(model) {
      this.drawer = false
      let res
      let params = {
        areaType: model.region,
        exclusions: model.exclude,
        keywords: model.mainBody,
        title: model.KeName,
        type: String(model.platformType),
        publishArea: String(model.countryOrCrea)
      }
      if (this.DrawerTitle == '新建专题') {
        res = await api.addSpecial(params)
        if (res.code == 200) {
          this.$message({ message: '专题新增成功', type: 'success' })
          this.getList()
        } else {
          this.$message({ message: '专题新增失败', type: 'error' })
        }
        return
      }
      params.id = model.id
      res = await api.EditSpecialEs(params)
      if (res.code == 200) {
        this.$message({ message: '专题修改成功', type: 'success' })
        this.getList()
      } else {
        this.$message({ message: '专题修改失败，请联系管理员', type: 'error' })
      }
    },
    /* 专题列表 */
    async getList(sign) {
      try {
        const data = await api.SpecialList({ pageNum: 1, pageSize: 50 })
        if (data.code == 200) {
          this.KeList = data.rows
          /* 专题列表为空 */
          if (data.rows.length == 0) {
            this.isEmpty = true
            this.$message({ message: '请先添加专题', type: 'warning' })
            return
          }
          
          if (sign) {
            this.ActiveData = data.rows[0]
          } else {
            this.ActiveData = this.KeList[this.isActive]
          }
          
          this.isEmpty = false
          // 在获取专题列表成功后，先获取地区列表，再搜索
          this.getArea().then(() => {
            // 只在这里调用一次SpecialEs
            this.SpecialEs(true)
          })
        } else {
          this.$message({ message: '专题列表获取失败', type: 'error' })
          this.isEmpty = true
        }
      } catch (err) {
        this.$message({ message: '专题列表获取失败', type: 'error' })
        this.isEmpty = true
      }
    },
    /* 专题数据检索 */
    async SpecialEs(resetPage) {
      // 如果已经在执行中，则不重复调用
      if (this.buttonDisabled) {
        console.log('正在搜索中，请稍候...')
        return
      }
      
      this.buttonDisabled = true
      
      // 如果需要重置页码，则将currentPage设为1
      if (resetPage === true) {
        this.currentPage = 1
      }
      
      this.scrollToTop()
      
      let keywords = () => {
        if (this.SeachData.keyword && this.KeList[this.isActive].keywords) {
          return this.SeachData.keyword + ',' + this.KeList[this.isActive].keywords
        } else if (this.SeachData.keyword) {
          return this.SeachData.keyword
        } else if (this.KeList[this.isActive].keywords) {
          return this.KeList[this.isActive].keywords
        }
      }
      let industry = this.SeachData.industry.map(item => String(item)),
        area = this.SeachData.area.map(item => String(item))
      let params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        id: this.KeList[this.isActive].id,
        dateType: this.SeachData.timeRange != 6 ? this.SeachData.timeRange : '',
        startTime: this.SeachData.customDay[0],
        endTime: this.SeachData.customDay[1],
        publishType: this.SeachData.releaseArea,
        publishArea: String(this.SeachData.country),
        isSort: this.SeachData.sortMode,
        sourceType: this.SeachData.radio,
        matchObject: '',
        matchType: this.SeachData.metaMode,
        keywords: keywords(),
        industryList: industry,
        fieldList: area,
        isTechnology: this.SeachData.isTechnology
      }
      this.keywords = keywords()
      try {
        const res = await api.SpecialEs(params)
        if (res.code == 200) {
          this.ArticleList = res.data.list
          this.total = res.data.total
        } else {
          this.$message({ message: '文章获取失败，请联系管理员', type: 'error' })
        }
      } catch (err) {
        this.$message({ message: '文章获取失败，请联系管理员', type: 'error' })
      } finally {
        setTimeout(() => {
          this.buttonDisabled = false
        }, 1000)
      }
    },
    /* 删除 */
    async deleteLink(dataId) {
      this.$confirm('您确定要删除这条数据吗', '删除专题', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let res = await api.deleteSpecialEs([dataId])
          if (res.code == 200) {
            this.$message({ message: '删除成功', type: 'success' })
            this.isActive = 0
            this.getList()
          } else {
            this.$message({ message: '删除失败', type: 'error' })
          }
        })
        .catch(() => { })
    },
    async getArea() {
      return await api.getAreaList().then(Response => {
        if (Response.code == 200) {
          this.areaList = Response.data[0]
          this.countryList = Response.data[1]
          // 不在这里调用SpecialEs
        } else {
          this.$message({ message: '地区数据获取失败', type: 'error' })
        }
      }).catch(err => {
        this.$message({ message: '地区数据获取失败', type: 'error' })
      })
    },
    AddKeEvent(flag, item) {
      this.editData = JSON.parse(JSON.stringify(item))
      this.drawer = flag
      if (item) {
        this.DrawerTitle = '修改专题'
      }
    },
    /* 滚动到顶部 */
    scrollToTop() {
      this.$nextTick(() => {
        // 获取右侧内容区域
        const rightMain = this.$el.querySelector('.rightMain')
        if (rightMain) {
          rightMain.scrollTop = 0
        }
        
        // 获取文章列表滚动区域
        if (this.$el.querySelector('.scollBox')) {
          this.$el.querySelector('.scollBox').scrollTop = 0
        }
        
        // 使用 MainArticle 组件中的 mainScorll 方法
        const mainArticleRef = this.$refs.mainArticle
        if (mainArticleRef && typeof mainArticleRef.mainScorll === 'function') {
          mainArticleRef.mainScorll()
        }
      })
    },
    // 添加刷新数据的方法，使用防抖控制
    refreshData() {
      if (!this.buttonDisabled) {
        this.SpecialEs(false)
      } else {
        console.log('请等待当前搜索完成')
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
