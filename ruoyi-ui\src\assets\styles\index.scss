@import "./variables.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";
@import "./btn.scss";

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
  user-select: none;
  /* 禁止用户选择文本 */
  // filter: grayscale(100%); 网站一键变灰
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg,
      rgba(32, 182, 249, 1) 0%,
      rgba(32, 182, 249, 1) 0%,
      rgba(33, 120, 241, 1) 100%,
      rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
    border-bottom: solid 1px rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

.emptyFather {
  position: relative;

  .empty {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    width: 200px;
    height: 200px;
    margin: auto;
    text-align: center;

    p {
      font-size: 24px;
      color: #9b9b9b;
      // vertical-align: top;
      margin-top: -10px;
    }
  }
}

.formSytle {
  width: 90%;
  margin: 0 auto;
}

.title_Info {
  height: 30px;
  line-height: 30px;
  padding-left: 10px;
  font-size: 16px;
  font-weight: 600;
  color: #337ab7;
  border-left: solid 3px #337ab7;
}

.rightMain {
  margin-left: 10.5%;
  // height: 90vh;
}

i {
  font-style: normal;
}

.el-drawer__header {
  padding: 0 0 0 10px;
  background-color: #0090ff;
  color: #fff;
  line-height: 45px;
}

.leftLink {
  width: 10%;
  float: left;
  height: calc(100vh - 58px);
  background-color: rgb(248, 248, 248);
  box-shadow: -1px 20px 20px #dad9d9;

  .treeMain {
    width: 95%;
    height: auto;
    margin: 20px auto;
  }

  .keListConteneurs {
    width: 95%;
    height: 100%;
    text-align: center;
    margin-top: 20px;
    margin: 20px auto;

    p {
      color: rgb(43, 43, 43);
      font-size: 14px;
      height: 30px;
      line-height: 30px;

      span {
        text-align: left;
        // padding-left: 5px;
        overflow: hidden;
        white-space: nowrap;
        /* 防止文本换行 */
        text-overflow: ellipsis;
        width: 100%;
      }
    }

    .textStyle {
      // display: flex;
      // justify-content: space-between;
      // align-items: center;
      padding-right: 5px;
    }

    .active {
      width: 90%;
      margin: 15px 0 15px 5%;
      // height: 35px;
      // line-height: 35px;
      // background-color: #ff9318;
      border-radius: 2px;
      // color: #fafafa;
    }
  }
}

/* 滚动条 */

// 滚动条宽度
div::-webkit-scrollbar {
  width: 6px;
}

// 滚动条轨道
div::-webkit-scrollbar-track {
  background: rgb(255, 255, 255);
  border-radius: 2px;
}

// 小滑块
div::-webkit-scrollbar-thumb {
  background: #d3d3d349;
  border-radius: 10px;
}

div::-webkit-scrollbar-thumb:hover {
  background: #e9e9e9;
}

/* world文档预览 */
::v-deep.docx-wrapper {
  background-color: #fff !important;
  padding: 0;
}

.el-drawer__header {
  z-index: 0;
  margin-bottom: 10px;
}

.mediaStyle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}


.splitpanes.default-theme {
  .splitpanes__splitter {
    background-color: #d5d9d5 !important;
  }

  .splitpanes__splitter:before,
  .splitpanes__splitter:after {
    background-color: #000000 !important;
  }

  .splitpanes__pane {
    background-color: #fff !important;
  }
}