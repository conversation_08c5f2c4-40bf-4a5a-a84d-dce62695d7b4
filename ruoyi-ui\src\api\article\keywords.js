import request from '@/utils/request'

// 查询检索词库列表
export function listKeywords(query) {
  return request({
    url: '/article/keywords/list',
    method: 'get',
    params: query
  })
}

// 查询检索词库详细
export function getKeywords(id) {
  return request({
    url: '/article/keywords/' + id,
    method: 'get'
  })
}

// 新增检索词库
export function addKeywords(data) {
  return request({
    url: '/article/keywords',
    method: 'post',
    data: data
  })
}

// 修改检索词库
export function updateKeywords(data) {
  return request({
    url: '/article/keywords/edit',
    method: 'post',
    data: data
  })
}

// 删除检索词库
export function delKeywords(id) {
  return request({
    url: '/article/keywords/remove',
    method: 'post',
    data: id
  })
}
