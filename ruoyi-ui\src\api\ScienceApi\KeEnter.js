import request from "@/utils/request";
const uploadImage = (params) => {
  /* 上传封面 */
  return request({
    url: "/article/articleList/cover",
    method: "post",
    data: params,
  });
};
/* 移除文件 */
const removeImages = (params) => {
  return request({
    url: "/article/articleList/deleteFile",
    method: "get",
    params,
  });
};
/* 科情录入 */
const articleList = (params) => {
  return request({
    url: "/article/articleList",
    method: "post",
    data: params,
  });
};
/* 科情与专题列表 */
const monitoringAndSpecial = params => {
  return request({
    url: "/article/articleList/monitoringAndSpecial",
    method: 'get'
  });
}
/* 科情与专题列表 */
const checkSn = params => {
  return request({
    url: "/article/articleList/check/sn",
    method: 'get',
    params: params,
  });
}
// 上传文件
const uploadFile = (params) => {
  return request({
    url: "/article/articleList/upload/file",
    method: "post",
    data: params,
  });
};
// 删除文件
const removeFile = (params) => {
  return request({
    url: "/article/articleList/deleteFile",
    method: "get",
    params: params,
  });
};

export default {
  uploadImage,
  removeImages,
  articleList,
  monitoringAndSpecial,
  checkSn,
  uploadFile,
  removeFile,
};
