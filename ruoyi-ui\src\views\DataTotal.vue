<!-- 大屏汇总 -->
<template>
  <div>
    <div class="btn_fixed">
      <el-button
        :type="activeName == 'first'?'primary':''"
        icon="el-icon-data-line"
        :class="{button_primary:activeName == 'first',button_noActive:activeName != 'first'}"
        @click="activeName = 'first'"
      >产融合作</el-button>
      <el-button
        @click="activeName = 'second'"
        icon="el-icon-data-line"
        :class="{button_primary:activeName == 'second',button_noActive:activeName != 'second'}"
        :type="activeName == 'second'?'primary':''"
      >技术安全风险</el-button>
      <el-button
        @click="activeName = 'third'"
        icon="el-icon-data-line"
        :class="{button_primary:activeName == 'third',button_noActive:activeName != 'third'}"
        :type="activeName == 'third'?'primary':''"
      >数字经济发展</el-button>
    </div>
    <div v-if="activeName == 'first'">
      <cockpit></cockpit>
    </div>
    <div v-if="activeName == 'second'">
      <securityRisks></securityRisks>
    </div>
    <!-- <div v-if="activeName == 'third'" >
      
    </div>-->
  </div>
</template>

<script>
import cockpit from '@/views/cockpit/index'
import securityRisks from '@/views/securityRisks/index'
export default {
  components: { cockpit, securityRisks },
  data() {
    return {
      activeName: 'first'
    }
  },
  watch: {
    activeName(newVal, oldVal) {
      if (newVal == 'third') {
        this.activeName = oldVal
        this.$message.warning('暂未开放')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.btn_fixed {
  position: fixed;
  top: 5px;
  left: 20px;
}
.button_primary {
  background-image: url('../assets/images/switchBack.svg');
  // background-color: rgba($color: #ce0000, $alpha: 0.5);
  border: none;
}
.button_noActive {
  background-image: url('../assets/images/noActive.svg');
  background-color: rgba($color: #000000, $alpha: 0);
  border: none;
  color: #fff;
}
</style>