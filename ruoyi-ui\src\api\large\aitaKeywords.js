import request from '@/utils/request'

// 查询信通院大屏  人工智能技术架构  检索词库列表
export function listAitaKeywords(query) {
  return request({
    url: '/large/aitaKeywords/list',
    method: 'get',
    params: query
  })
}

// 检索词库列表（排除节点）
export function excludeChild(deptId) {
  return request({
    url: '/large/aitaKeywords/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询信通院大屏  人工智能技术架构  检索词库详细
export function getAitaKeywords(id) {
  return request({
    url: '/large/aitaKeywords/' + id,
    method: 'get'
  })
}

// 新增信通院大屏  人工智能技术架构  检索词库
export function addAitaKeywords(data) {
  return request({
    url: '/large/aitaKeywords',
    method: 'post',
    data: data
  })
}

// 修改信通院大屏  人工智能技术架构  检索词库
export function updateAitaKeywords(data) {
  return request({
    url: '/large/aitaKeywords/edit',
    method: 'post',
    data: data
  })
}

// 删除信通院大屏  人工智能技术架构  检索词库
export function delAitaKeywords(id) {
  let data = new FormData()
  data.append('id', id)
  return request({
    url: '/large/aitaKeywords/remove',
    method: 'post',
    data: data
  })
}
