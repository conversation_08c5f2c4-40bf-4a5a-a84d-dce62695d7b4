<template>
  <div class="one">
    <div class="left">
      <TitleComponent :title="'热点推荐'" />
      <myList style="height: 659px" @openNewView="openNewView"> </myList>
      <TitleComponent :title="'产品介绍'" />
      <product style="height: 229px"> </product>
    </div>
    <div class="center">
      <div class="top">
        <div class="top-content bg1">
          <div class="top-content-number">
            {{ padWithZeros(gatherTotal, 6) }}
          </div>
          <div class="top-content-name">有效采集量</div>
        </div>
        <div class="top-content bg2">
          <div class="top-content-number">
            {{ padWithZeros(gatherDayNumber, 6) }}
          </div>
          <div class="top-content-name">当日采集数量</div>
        </div>
      </div>
      <mymap @openList="openList" style="width: 855px; height: 725px"></mymap>
    </div>
    <div class="right">
      <TitleComponent :title="'所属归类'" />
      <barEchart
        @openList="openList"
        style="height: 970px; width: 520px; margin-bottom: 20px"
      >
      </barEchart>
      <TitleComponent :title="'采集趋势'" v-if='false'/>
      <linEchart style="height: 439px; width: 520px" v-if='false'> </linEchart>
    </div>
    <el-dialog
      :title="drawerInfo.cnTitle || drawerInfo.title"
      :visible.sync="articleDialogVisible"
      width="800px"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div style="line-height: 30px;font-size: 16px;" v-html="drawerInfo.cnContent"></div>
      <el-empty
        description="当前文章暂无数据"
        v-if="!drawerInfo.cnContent"
      ></el-empty>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>
    <el-drawer
      :title="title"
      :visible.sync="drawer"
      direction="rtl"
      :before-close="handleClose1"
      append-to-body
      size="800px"
    >
      <el-table
        :data="list"
        style="width: 100%"
        :show-header="false"
        ref="table"
        @cell-click="openNewView"
      >
        <el-table-column
          prop="title"
          label="标题"
          width="510"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span
              v-html="changeColor(scope.row.cnTitle || scope.row.title)"
            ></span>
          </template>
        </el-table-column>
        <el-table-column
          prop="sourceName"
          label="数据源"
          width="100"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="publishTime"
          label="发布时间"
          width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{
              parseTime(
                scope.row.publishTime || scope.row.gatherTime,
                "{y}-{m}-{d}"
              )
            }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :background="false"
        @pagination="getList"
      />
    </el-drawer>
  </div>
</template>
<script>
import TitleComponent from "./components/titleVue.vue";
import mymap from "./echartsComputent/map.vue";
import myList from "./echartsComputent/list.vue";
import product from "./echartsComputent/product.vue";
import linEchart from "./echartsComputent/linEchart.vue";
import barEchart from "./echartsComputent/barEchart.vue";
import {
  largeSourceDataList,
  largeGatherQueryGatherData,
  largeRegionDataList,
  largeHotQueryById,
  largeRegionDataQueryById,
  largeSourceDataQueryById,
  largeSourceGetKeywords,
} from "@/api/bigScreen/index1";

export default {
  data() {
    return {
      drawerInfo: {},
      articleDialogVisible: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      drawer: false,
      title: "",
      list: [],
      query: {},
      gatherTotal: 0,
      gatherDayNumber: 0,
      keywords: "",
    };
  },
  components: {
    TitleComponent,
    mymap,
    myList,
    product,
    linEchart,
    barEchart,
  },
  mounted() {
    this.init();
  },
  beforeDestroy() {},
  methods: {
    init() {
      largeGatherQueryGatherData({}).then((res) => {
        this.gatherTotal = res.data.gatherTotal;
        this.gatherDayNumber = res.data.gatherDayNumber;
      });
    },
    padWithZeros(num, targetLength) {
      const numStr = num.toString();
      const padding = "0".repeat(targetLength - numStr.length);
      return `${padding}${numStr}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    async openNewView(item) {
      if (this.query.regionName) {
        await largeRegionDataQueryById(item.id).then((res) => {
          this.drawerInfo = res.data;
        });
      } else if (this.query.sourceId) {
        await largeSourceDataQueryById(item.id).then((res) => {
          this.drawerInfo = res.data;
        });
      } else {
        await largeHotQueryById(item.id).then((res) => {
          this.drawerInfo = res.data;
        });
      }
      let content = this.drawerInfo.article || this.drawerInfo.content;
      if (content) {
        content = content.replace(/\n/g, "<br>");
        content = content.replace(/\${[^}]+}/g, "<br>");
        content = content.replace("|xa0", "");
        content = content.replace("opacity: 0", "");
        content = content.replace(/<img\b[^>]*>/gi, "");
        content = content.replace(/ style="[^"]*"/g, "");
      }
      this.drawerInfo.cnContent = content;
      this.articleDialogVisible = true;
    },
    handleClose() {
      this.drawerInfo = {};
      this.articleDialogVisible = false;
    },
    handleClose1() {
      this.title = "";
      this.list = [];
      this.query = {};
      this.keywords = "";
      this.drawer = false;
    },
    openList({ query, title }) {
      this.query = query;
      this.title = title;
      this.keywords = [title];
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.getList();
    },
    async getList() {
      if (this.query.regionName) {
        await largeRegionDataList(this.query, this.queryParams).then((res) => {
          this.list = res.rows;
          this.total = res.total;
        });
      } else {
        await largeSourceGetKeywords(this.query.sourceName).then((res) => {
          this.keywords = res.data;
        });
        let query = {
          sourceId: this.query.sourceId,
        };
        await largeSourceDataList(query, this.queryParams).then((res) => {
          this.list = res.rows;
          this.total = res.total;
        });
      }
      this.drawer = true;
    },
    // 关键字替换
    changeColor(str) {
      let Str = str;
      if (Str) {
        let keywords = this.keywords;
        keywords.map((keyitem, keyindex) => {
          if (keyitem && keyitem.length > 0) {
            // 匹配关键字正则
            let replaceReg = new RegExp(keyitem, "g");
            // 高亮替换v-html值
            let replaceString =
              '<span class="highlight"' +
              ' style="color: #ff7500;">' +
              keyitem +
              "</span>";
            Str = Str.replace(replaceReg, replaceString);
          }
        });
      }
      return Str;
    },
  },
};
</script>
<style lang="scss" scoped>
.one {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-around;

  .left {
    width: 520px;
  }

  .center {
    width: 855px;

    .top {
      height: 252px;
      display: flex;
      align-items: flex-end;
      justify-content: center;
      padding-bottom: 20px;

      .top-content {
        position: relative;
        width: 324px;
        height: 112px;
        text-align: center;
        margin: 0 30px;

        .top-content-number {
          width: 170px;
          position: absolute;
          left: 126px;
          top: 16px;
          font-size: 40px;
          line-height: 47px;
          font-weight: bold;
          background: linear-gradient(180deg, #ffffff 0%, #05d5ff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .top-content-name {
          width: 170px;
          position: absolute;
          left: 126px;
          top: 68px;
          font-size: 20px;
          line-height: 28px;
          letter-spacing: 1px;
          font-weight: bold;
          background: linear-gradient(180deg, #ffffff 0%, #05d5ff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .bg1 {
        background: url("../../assets/bigScreenTwo/121.png") no-repeat;
        background-size: 100% 100% !important;
        background-size: cover;
      }

      .bg2 {
        background: url("../../assets/bigScreenTwo/131.png") no-repeat;
        background-size: 100% 100% !important;
        background-size: cover;
      }
    }
  }

  .right {
    width: 520px;
  }
}

::v-deep .el-dialog {
  background: url("../../assets/bigScreenTwo/dialogBackground.png") no-repeat;
  background-size: 100% 100% !important;
  background-size: cover;
  height: 800px;

  .el-dialog__header {
    background-color: #1d233400;
    font-size: 30px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);
    height: 100px;
  }

  .el-dialog__body {
    background-color: #2a304000;
    color: #f2f2f2;
    height: calc(100% - 160px);
    overflow: auto;
  }

  .el-dialog__footer {
    background-color: #1d233400;
    padding: 18px 20px;
  }

  .el-button {
    background-color: #002766;
    color: #fff;
    border: 0px;
  }

  .el-dialog__headerbtn .el-dialog__close {
    background: url("../../assets/bigScreenTwo/关闭小.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
    width: 31px;
    height: 31px;
    top: 16px;

    &::before {
      content: none;
    }
  }
}

::v-deep .el-drawer__open {
  .el-drawer {
    background: url("../../assets/bigScreenTwo/drawerBackground.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
  }

  .el-dialog__close {
    background: url("../../assets/bigScreenTwo/关闭小.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
    width: 31px;
    height: 31px;
    top: 18px;
    right: 10px;

    &::before {
      content: none;
    }
  }

  .el-drawer__header {
    background-color: #1d233400;
    font-size: 30px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);
    height: 100px;
  }
}

::v-deep .el-table {
  background-color: #2a304000;

  tr {
    color: #f2f2f2;
    background: url("../../assets/bigScreenTwo/弹窗列表.png") no-repeat;
    background-size: 100% 100% !important;
    height: 68px;
    padding: 0 0 0 65px;
    margin-bottom: 10px;
    display: block;
    width: 790px;
  }

  td.el-table__cell {
    border-bottom: 1px solid #1d233400;
    height: 68px;
    line-height: 68px;
    font-size: 16px;
    text-shadow: 0px 0px 9px rgba(30, 198, 255, 0.8);
  }

  &::before {
    height: 0;
  }
}

::v-deep
  .el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell,
::v-deep .el-table__empty-block {
  background-color: #2a304000;
  color: #f2f2f2;
  cursor: pointer;
}

::v-deep .pagination-container {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 0;

  .el-select .el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }

  .el-pagination__editor.el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }
}

::v-deep .el-pagination__total,
::v-deep .el-pagination__jump {
  color: #f2f2f2;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination button:disabled {
  background-color: #ffffff00;
  color: #fff;
}

::v-deep .el-pager li {
  background: #ffffff00;
  color: #fff;

  &.active {
    color: #1890ff;
  }
}
</style>
