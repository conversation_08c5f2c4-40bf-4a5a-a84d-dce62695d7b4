<template>
  <div class="chartNum">
    <div class="articleTitle"> 文章总数</div>
    <div class="articleNumber">
      <div
        v-for="(item, index) in orderNum"
        :key="index"
        :class="[{ number: item !== ',' }]"
      >
        <span>{{ item }}</span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    // 显示的数字
    number: {
      type: Number | String,
      require: true,
    }, // 显示的长度
    length: {
      type: Number,
    },
  },
  data() {
    return {
      initValue: 0, //初始的值
      orderNum: [], // 默认总数
      timer: null,
    };
  },
  mounted() {},
  watch: {
    number: {
      handler(val) {
        this.init(val);
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    init(val) {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
      this.initValue = Number(val) - Number(String(val).slice(0, -2));
      this.orderNum = this.addCommas(this.initValue);
      this.setIntervalFun();
    },
    setIntervalFun() {
      let that = this;
      this.timer = setInterval(() => {
        if (that.initValue >= that.number) {
          that.initValue =
            Number(that.number) - Number(String(that.number).slice(0, -2));
          this.orderNum = that.addCommas(that.initValue);
        } else {
          that.initValue =
            that.initValue + Math.floor(Math.random() * (10 - 1 + 1)) + 1;
          that.orderNum = that.addCommas(that.initValue);
        }
      }, 2000);
    },
    addCommas(num) {
      var numToFixed = Number(num);
      var parts = numToFixed.toString().split(".");
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      return parts.join(".");
    },
  },
};
</script>
<style lang="scss" scoped>
.chartNum {
  .articleTitle {
    font-size: 20px;
    font-weight: bold;
    color: #1bdcff;
    text-align: center;
    margin-top: 60px;
  }
  .articleNumber {
    text-align: center;
    margin-top: 35px;
    display: flex;
    justify-content: center;
    > div {
      color: #1bdcff;
      font-weight: bold;
      font-size: 32px;
      margin: 0 17px;
      font-family: "Akzidenz-Grotesk-Bq-Bold-Webfont";
    }
    .number {
      font-family: "Akzidenz-Grotesk-Bq-Bold-Webfont";
      width: 44px;
      height: 44px;
      line-height: 44px;
      background: linear-gradient(to bottom, #0756f9, #0772fb, #05c4ff);
      span {
        color: linear-gradient(to bottom, #fff, #1bdcff);
        background: linear-gradient(to bottom, #fff, #1bdcff);
        /* 使用渐变背景时，通常需要设置这些值以确保文字可读 */
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }
  }
}
</style>