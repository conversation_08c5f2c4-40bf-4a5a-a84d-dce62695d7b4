<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div
      v-show="show"
      class="custom-dialog"
      :class="{ 'technology-details-fullscreen': isFullscreen }"
      :style="isFullscreen ? {} : { width: width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div style="display: flex; align-items: center">
          <div
            @click="handleScreen"
            :title="isFullscreen ? '退出全屏' : '全屏'"
            style="
              margin-right: 20px;
              cursor: pointer;
              color: #ffffff;
              font-size: 20px;
            "
          >
            <i
              :class="isFullscreen ? 'el-icon-rank' : 'el-icon-full-screen'"
              style="width: 20px;height: 20px;"
            ></i>
          </div>
          <div class="custom-dialog-close" @click="closeDialog"></div>
        </div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="bg-box-title">技术简介</div>
          <div class="bg-box-content flex-box">
            <div
              class="bg-box-summary"
              v-html="formatTextarea(item.summary)"
            ></div>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">内容趋势</div>
          <div class="bg-box-content">
            <div ref="chartKeyDetails" style="width: 100%; height: 300px"></div>
          </div>
        </div>
        <div class="bg-box" v-loading="loading">
          <div class="bg-box-title">相关内容</div>
          <div class="bg-box-content">
            <ul class="article-list" v-if="articles.length > 0">
              <li
                v-for="article in articles"
                :key="article.id"
                class="article-item"
              >
                <div class="article-title" @click="openDetail(article)">
                  {{ article.title }}
                </div>
                <div class="article-publishTime">{{ article.publishTime }}</div>
              </li>
            </ul>
            <div v-else>
              <div class="no-data">暂无数据</div>
            </div>
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import {
  technicalTrendData,
  technicalArticleList,
} from "@/api/bigScreen/sanhao.js";

export default {
  name: "TechnologyDetails",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    closeOnClickMask: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 1200,
    },
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      myChart: null,
      option: {},
      articles: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      show: false,
      loading: true,
      // 全屏状态
      isFullscreen: false,
      // ResizeObserver 实例
      resizeObserver: null,
      // 防抖定时器
      resizeTimer: null,
    };
  },

  mounted() {
    // 添加ESC键监听
    document.addEventListener("keydown", this.handleKeydown);
    // 添加窗口大小变化监听
    window.addEventListener("resize", this.handleWindowResize);
  },

  beforeDestroy() {
    // 销毁图表实例
    if (this.myChart) {
      this.myChart.dispose();
      this.myChart = null;
    }
    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    // 清理定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    }
    // 移除ESC键监听
    document.removeEventListener("keydown", this.handleKeydown);
    // 移除窗口大小变化监听
    window.removeEventListener("resize", this.handleWindowResize);
  },

  watch: {
    visible: {
      handler(newVisible) {
        if (newVisible) {
          // 重置全屏状态
          this.isFullscreen = false;
          this.$nextTick(() => {
            if (this.$refs.chartKeyDetails) {
              this.myChart = echarts.init(this.$refs.chartKeyDetails);
              this.queryParams = {
                pageNum: 1,
                pageSize: 10,
              };
              this.show = false;
              this.getList();
              this.initChart();
              this.setupResizeListeners();
            }
          });
        }
      },
    },
  },
  methods: {
    initChart() {
      technicalTrendData({ technicalSn: this.item.id }).then((res) => {
        const valueObj = {
          xData: [],
          yData: [],
        };
        res.data.map((item, index) => {
          valueObj.xData.push(item.publishTime);
          valueObj.yData.push(item.publishCount);
        });
        this.option = {
          tooltip: {
            show: true,
            trigger: "axis",
            showContent: true,
            triggerOn: "mousemove",
            // axisPointer: {
            //   type: "shadow",
            // },
          },
          grid: {
            left: "0%",
            right: "0%",
            bottom: "0%",
            top: "5%",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            data: valueObj.xData,
            axisLabel: {
              fontSize: "14px",
              color: "#fff",
              formatter: function (value) {
                if (value.length > 8) {
                  return `${value.slice(0, 8)}...`;
                }
                return value;
              },
            },
          },
          yAxis: {
            type: "value",
            splitLine: {
              show: true,
              lineStyle: {
                color: "#ffffff70",
                type: "dotted",
              },
            },
            axisLabel: {
              interval: 0,
              fontSize: "14px",
              color: "#fff",
            },
          },
          series: [
            {
              data: valueObj.yData,
              type: "line",
              smooth: true,
            },
          ],
        };

        setTimeout(() => {
          this.myChart?.resize();
        }, 1);
        this.myChart.setOption(this.option);
        this.show = true;
      });
    },

    openDetail(item) {
      this.$emit("openArticleDetail", item);
    },

    getList() {
      this.loading = true;
      technicalArticleList({
        ...this.queryParams,
        technicalSn: this.item.id,
      }).then((res) => {
        this.articles = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },

    // 关闭弹窗的方法
    closeDialog() {
      this.$emit("update:visible", false);
    },

    // 处理遮罩层点击事件
    handleMaskClick() {
      if (this.closeOnClickMask) {
        this.closeDialog();
      }
    },
    formatTextarea(content) {
      content = content.replace(/\n/g, "<br>");
      content = content.replace(/\\n/g, "<br>");
      content = content.replace(/\\\n/g, "<br>");
      content = content.replace(/\${[^}]+}/g, "<br>");
      content = content.replace("|xa0", "");
      content = content.replace("opacity: 0", "");
      return content;
    },

    // 设置尺寸变化监听
    setupResizeListeners() {
      // 创建 ResizeObserver 监听容器尺寸变化
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(() => {
          // 使用防抖处理，避免频繁触发
          clearTimeout(this.resizeTimer);
          this.resizeTimer = setTimeout(() => {
            this.handleResize();
          }, 100);
        });

        // 监听图表容器的尺寸变化
        if (this.$refs.chartKeyDetails) {
          this.resizeObserver.observe(this.$refs.chartKeyDetails);
        }
      }
    },

    // 处理尺寸变化
    handleResize() {
      if (this.myChart) {
        this.$nextTick(() => {
          this.myChart.resize();
        });
      }
    },

    // 全屏切换
    handleScreen() {
      this.isFullscreen = !this.isFullscreen;

      // 延迟调整图表大小，确保DOM更新完成
      this.$nextTick(() => {
        setTimeout(() => {
          this.handleResize();
        }, 300); // 等待CSS动画完成
      });
    },

    // 处理键盘事件
    handleKeydown(event) {
      // 按ESC键退出全屏
      if (event.key === "Escape" && this.isFullscreen && this.visible) {
        this.isFullscreen = false;
      }
    },

    // 处理窗口大小变化
    handleWindowResize() {
      if (this.isFullscreen) {
        // 重新调整图表大小
        this.handleResize();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  // 确保在所有分辨率下都能正确覆盖
  min-width: 100%;
  min-height: 100%;
  overflow: hidden;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("../../../assets/bigScreenSanhao/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;
    transition: all 0.3s ease;

    &.technology-details-fullscreen {
      width: calc(100vw - 40px) !important;
      height: calc(100vh - 40px) !important;
      max-width: none !important;
      max-height: none !important;
      margin: 0 !important;
      // 确保在所有分辨率下都能正确显示
      min-width: calc(100% - 40px) !important;
      min-height: calc(100% - 40px) !important;

      .custom-dialog-body {
        height: calc(100% - 80px); // 减去header高度和padding
        max-height: calc(100% - 80px);
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("../../../assets/bigScreenSanhao/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("../../../assets/bigScreenSanhao/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      height: 800px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .bg-box-content {
          font-size: 16px;

          .bg-box-img {
            width: 120px;
            height: 120px;
            background: #fff;
            vertical-align: middle;
            position: relative;

            img {
              width: 120px;
              height: 120px;
              display: inline-block;
            }
          }

          .bg-box-summary {
            margin-left: 20px;
          }
        }

        .flex-box {
          display: flex;
        }
      }

      .article-list {
        list-style: none;
        padding: 0;
        margin: 0;

        .article-item {
          display: flex;
          height: 40px;
          line-height: 40px;
          margin-bottom: 10px;
          font-size: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .article-title {
            cursor: pointer;
            width: calc(100% - 100px);
            font-size: 18px;
            // font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .article-publishTime {
            width: 100px;
            color: rgba(255, 255, 255, 0.5);
          }
        }
      }
    }
  }
}

.no-data {
  text-align: center;
  color: #fff;
  font-size: 16px;
  height: 100px;
  line-height: 80px;
}

::v-deep .pagination-container {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 20px 0 0;
  padding-bottom: 0px !important;

  .el-select__wrapper,
  .el-input__wrapper {
    .el-select__placeholder {
      color: #fff;
    }

    background: #2a304000;
    border-color: #ffffff;
  }

  .el-input__inner {
    color: #fff;
  }
}

::v-deep .el-pagination__total,
::v-deep .el-pagination__jump {
  color: #f2f2f2;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination button:disabled {
  background-color: #ffffff00 !important;
  color: #fff !important;
}

::v-deep .el-pager li {
  background: #ffffff00 !important;
  color: #fff !important;

  &.active {
    color: #1890ff !important;
  }
}
</style>
