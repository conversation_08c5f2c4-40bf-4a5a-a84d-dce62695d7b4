<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div
      v-show="show"
      class="custom-dialog"
      :class="{ 'hot-technology-fullscreen': isFullscreen }"
      :style="isFullscreen ? {} : { width: width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div style="display: flex; align-items: center">
          <div
            @click="handleScreen"
            :title="isFullscreen ? '退出全屏' : '全屏'"
            style="
              margin-right: 20px;
              cursor: pointer;
              color: #ffffff;
              font-size: 20px;
            "
          >
            <i
              :class="isFullscreen ? 'el-icon-rank' : 'el-icon-full-screen'"
              style="width: 20px; height: 20px"
            ></i>
          </div>
          <div class="custom-dialog-close" @click="closeDialog"></div>
        </div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="bg-box-title1">
            报告标题:
            <span> {{ content.reportName }}</span>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title1">
            发布机构:
            <span> {{ content.publishUnit }}</span>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title1">
            发布时间:
            <span>{{ content.publishTime }}</span>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">国内技术报告</div>
          <div class="bg-box-content">
            <div v-html="content.content"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { technicalReportDetail } from "@/api/bigScreen/sanhao.js";

export default {
  name: "HotTechnology",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "自定义弹窗",
    },
    closeOnClickMask: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 800,
    },
    id: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      content: {},
      show: false,
      // 全屏状态
      isFullscreen: false,
    };
  },

  mounted() {
    // 添加ESC键监听
    document.addEventListener("keydown", this.handleKeydown);
    // 添加窗口大小变化监听
    window.addEventListener("resize", this.handleWindowResize);
  },

  beforeDestroy() {
    // 移除ESC键监听
    document.removeEventListener("keydown", this.handleKeydown);
    // 移除窗口大小变化监听
    window.removeEventListener("resize", this.handleWindowResize);
  },

  watch: {
    visible: {
      handler(newValue) {
        if (newValue) {
          // 重置全屏状态
          this.isFullscreen = false;
          this.show = false;
          technicalReportDetail({ reportSn: this.id }).then((res) => {
            this.content = res.data;
            this.show = true;
          });
        }
      },
    },
  },
  methods: {
    // 关闭弹窗的方法
    closeDialog() {
      this.$emit("update:visible", false);
    },

    // 处理遮罩层点击事件
    handleMaskClick() {
      if (this.closeOnClickMask) {
        this.closeDialog();
      }
    },

    // 全屏切换
    handleScreen() {
      this.isFullscreen = !this.isFullscreen;

      // 延迟调整图表大小，确保DOM更新完成
      this.$nextTick(() => {
        setTimeout(() => {
          // 如果有图表组件，调整其大小
          // 这里可以根据实际的图表组件引用进行调整
        }, 300); // 等待CSS动画完成
      });
    },

    // 处理键盘事件
    handleKeydown(event) {
      // 按ESC键退出全屏
      if (event.key === "Escape" && this.isFullscreen && this.visible) {
        this.isFullscreen = false;
      }
    },

    // 处理窗口大小变化
    handleWindowResize() {
      if (this.isFullscreen) {
        // 重新调整图表大小
        // 这里可以根据实际的图表组件引用进行调整
      }
    },
  },
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  // 确保在所有分辨率下都能正确覆盖
  min-width: 100%;
  min-height: 100%;
  overflow: hidden;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("../../../assets/bigScreenSanhao/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;
    transition: all 0.3s ease;

    &.hot-technology-fullscreen {
      width: calc(100vw - 40px) !important;
      height: calc(100vh - 40px) !important;
      max-width: none !important;
      max-height: none !important;
      margin: 0 !important;
      // 确保在所有分辨率下都能正确显示
      min-width: calc(100% - 40px) !important;
      min-height: calc(100% - 40px) !important;

      .custom-dialog-body {
        height: calc(100% - 80px); // 减去header高度和padding
        max-height: calc(100% - 80px);
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("../../../assets/bigScreenSanhao/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("../../../assets/bigScreenSanhao/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      height: 800px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;
        font-size: 16px;

        .bg-box-title {
          font-weight: bolder;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .bg-box-content {
          color: #ffffff;
          white-space: pre-wrap;
        }

        .bg-box-title1 {
          font-weight: bold;
          font-size: 16px;
          color: #ffffff;
          height: 40px;
          line-height: 40px;
          margin-bottom: -5px;

          span {
            color: #a1a6ae;
            font-size: 14px;
            margin-left: 10px;
          }
        }

        .content-flex {
          display: flex;
          justify-content: center;
        }
      }
    }
  }
}
</style>
