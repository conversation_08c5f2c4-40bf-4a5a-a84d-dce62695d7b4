<template>
  <div class="dashboard-editor-container">
    <heard />
    <splitpanes class="split-panes default-theme">
      <pane class="leftLink" ref="leftLink" min-size="15" max-size="30" size="20">
        <div class="muludaohang">目录导航</div>
        <!-- <div class="head-container">
          <el-input v-model="treeInput" placeholder="请输入部门名称" clearable size="small" prefix-icon="el-icon-search"
            style="margin-bottom: 20px" />
        </div> -->
        <div class="shujulaiyuan">数据来源(<span>{{ deptOptionsLength }}</span>条)</div>
        <div class="treeBox">
          <el-tree :data="deptOptions" :props="defaultProps" :expand-on-click-node="false"
            :filter-node-method="filterNode" ref="tree" node-key="id" default-expand-all highlight-current
            @node-click="handleNodeClick" />
        </div>
      </pane>
      <pane min-size="50" max-size="85" size="80">
        <div class="rightBox">
          <el-form :model="queryParams" ref="queryForm" label-width="90px" @submit.native.prevent>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="排序" prop="slotType">
                  <el-radio-group v-model="queryParams.slotType">
                    <el-radio-button label="1">按相关度</el-radio-button>
                    <el-radio-button label="2">按时间</el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="搜索位置" prop="test">
                  <el-radio-group v-model="queryParams.test">
                    <el-radio-button label="1">全文</el-radio-button>
                    <el-radio-button label="2">标题</el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="日期筛选" prop="dateType">
                  <el-select size="mini" v-model="queryParams.dateType" placeholder="请选择" style="width: 100px;">
                    <el-option v-for="item in dateTypeOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                  <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="queryParams.customDay"
                    v-if="queryParams.dateType == 99" style="display: inline-block; width: 320px; margin-left: 10px"
                    size="mini" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    unlink-panels clearable></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="发文机构" prop="test1">
                  <el-radio-group v-model="queryParams.test1">
                    <el-radio-button v-for="item in issuingAgencyOptions" :key="item.label" :label="item.label">
                      {{ item.label }}
                    </el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="主题分类" prop="test2">
                  <el-radio-group v-model="queryParams.test2">
                    <el-radio-button v-for="item in subjectClassificationOptions" :key="item.label" :label="item.label">
                      {{ item.label }}
                    </el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="文件类型" prop="test3">
                  <el-radio-group v-model="queryParams.test3">
                    <el-radio-button v-for="item in fileYypeOptions" :key="item.label" :label="item.label">
                      {{ item.label }}
                    </el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="已选条件">
                  <div class="conditionBox">
                    <div class="conditionItem" v-for="(item, index) in selectedCondition" :key="index"
                      v-if="queryParams[item.key]">
                      {{ `${item.name}: ${queryParams[item.key]}` }}
                      <i class="el-icon-close" @click="removeCondition(item.key)"></i>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <div class="listBox">
            <div v-for="(item, index) in list" class="listItem">
              <div class="title_Article" @click="openNewView(item)" v-html="changeColor(item.cnTitle || item.title)">
              </div>
              <div class="content_Article" v-html="changeColor(item.cnContent || item.content)"></div>
              <div class="bottom_Article">
                <div class="time">
                  发布时间:{{ item.publishTime }}
                </div>
              </div>
            </div>
          </div>

          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </pane>
    </splitpanes>


  </div>
</template>
  
<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import heard from './heard.vue'

export default {
  components: { Treeselect, Splitpanes, Pane, heard },
  watch: {
    // 根据名称筛选部门树
    treeInput(val) {
      this.$refs.tree.filter(val)
    },
  },
  data() {
    return {
      treeInput: null,
      deptOptions: undefined,
      deptOptionsLength: 0,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        slotType: '1',
        test: '1',
        dateType: '',
      },
      list: [],
      total: 0,
      dateTypeOptions: [
        { value: '', label: '不限' },
        { value: '1', label: '一周内' },
        { value: '2', label: '一月内' },
        { value: '3', label: '一年内' },
        { value: '99', label: '自定义' },
      ],
      issuingAgencyOptions: [
        { value: '1', label: '中央' },
        { value: '2', label: '地方' },
        { value: '3', label: '中央和地方' },
      ],
      subjectClassificationOptions: [
        { value: '1', label: '中央' },
        { value: '2', label: '地方' },
        { value: '3', label: '中央和地方' },
      ],
      fileYypeOptions: [
        { value: '1', label: '中央' },
        { value: '2', label: '地方' },
        { value: '3', label: '中央和地方' },
      ],
      selectedCondition: [
        { name: '发文机构', key: 'test1' },
        { name: '主题分类', key: 'test2' },
        { name: '文件类型', key: 'test3' },
      ]
    }
  },
  created() {
    this.getTree()
    this.getList()
  },
  methods: {
    getList() {

    },
    getTree() {
      // deptTreeSelect().then(response => {
      //   this.deptOptions = this.handleTree(response.data, "id", "parentId")
      // })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id
      this.handleQuery()
    },
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.queryParams.deptId = undefined
      this.$refs.tree.setCurrentKey(null)
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    removeCondition(key) {
      this.$set(this.queryParams, key, '')
    }
  }
}
</script>
  
<style lang="scss" scoped>
.split-panes {
  min-height: calc(100vh - 85px);

  .treeBox {
    width: 100%;
    height: calc(100vh - 185px);
    overflow-y: auto;
  }

  .muludaohang {
    padding: 10px 20px;
    text-align: center;
    color: #fff;
    background-color: rgba(236, 128, 141, 0.996078431372549);
    height: 50px;
    font-size: 18px;
    font-weight: 700;
  }

  .shujulaiyuan {
    padding: 10px 20px;
    text-align: center;
    font-size: 18px;
    font-weight: 700;
    background-color: rgb(242, 242, 242);
    height: 50px;

    span {
      color: rgba(236, 128, 141, 0.996078431372549);
    }
  }
}

.rightBox {
  padding: 10px;
  height: calc(100vh - 85px);

  .conditionBox {
    display: flex;

    .conditionItem {
      margin-right: 10px;
      padding: 5px 10px;
      border: 1px solid #ccc;
      border-radius: 5px;
      height: 30px;
      line-height: 20px;
    }
  }
}

::v-deep .el-form-item {
  margin-bottom: 10px;
}
</style>
  