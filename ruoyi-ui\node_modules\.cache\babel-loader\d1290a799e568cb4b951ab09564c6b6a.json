{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\InfoEscalation\\Wechat.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\InfoEscalation\\Wechat.vue", "mtime": 1754299716358}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "_work", "_keywords", "_articleHistory", "_splitpanes", "_vuex", "_index2", "_ai", "_marked", "_config", "components", "Splitpanes", "Pane", "TreeTable", "dicts", "data", "loading", "tableLoading", "queryParams", "id", "pageNum", "pageSize", "dateType", "tags", "tagsSubset", "keywords", "isTechnology", "sortMode", "emotion", "<PERSON><PERSON><PERSON>", "total", "treeDataTransfer", "filterText", "checkList", "ArticleList", "checked", "ids", "multiple", "dialogVisible", "reportOptions", "reportId", "tagsList", "tagsList1", "checkAll", "isIndeterminate", "showHistory", "historyList", "historyTimeout", "dialogVisible1", "historyLoading", "queryParams1", "total1", "historyList1", "showSummary", "treeCurrentPage", "treePageSize", "treeTotal", "initializationCompleted", "searchDebounceTimer", "queryDebounceTimer", "<PERSON><PERSON><PERSON><PERSON>", "isRightFilter", "isLeftReset", "selectedClassify", "savedCheckboxData", "aiDialogVisible", "chatMessages", "isThinking", "userAvatar", "streamingMessage", "markdownOptions", "gfm", "breaks", "headerIds", "mangle", "headerPrefix", "pedantic", "sanitize", "smartLists", "smartypants", "xhtml", "isRequesting", "isAborted", "currentReader", "aiPlatform", "articleAiPrompt", "nodeCheckList", "chartDialogVisible", "chartHtml", "chartLoading", "currentChartIframe", "difyApikey", "article", "chart", "chartPrompt", "globalLoading", "watch", "handler", "newVal", "oldVal", "handleRightFilterChange", "_this", "listKeywords", "parentId", "then", "res", "handleCheckAllTagsSubset", "catch", "error", "console", "$message", "JSON", "stringify", "scrollToTopImmediately", "queryArticleList", "val", "_this2", "api", "getNewBuilt", "sourceType", "code", "message", "type", "closeReport", "computed", "_objectSpread2", "default", "mapGetters", "created", "_this3", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getConfigKey", "msg", "$store", "getters", "avatar", "Promise", "all", "getArticleHistory", "filter", "item", "initializeData", "roles", "includes", "t0", "stop", "mounted", "methods", "getSafeSummary", "cnSummary", "summary", "processedCnSummary", "replace", "trim", "processedSummary", "_this4", "_callee2", "_callee2$", "_context2", "queryTreeData", "$nextTick", "queryTreeAndList", "_this5", "_callee3", "savedData", "_callee3$", "_context3", "_toConsumableArray2", "length", "restoreFromSavedCheckboxData", "setTimeout", "_this6", "matchedItems", "for<PERSON>ach", "savedItem", "foundItem", "find", "treeItem", "sourceSn", "push", "$refs", "treeTable", "restoreSelectionSilently", "queryTreeDataWithRestoreFromSaved", "_this7", "_callee4", "_callee4$", "_context4", "handlePagination", "handleHistoryPagination", "getArticleHistory1", "dialogContent", "document", "querySelector", "scrollTop", "_this8", "_callee5", "params", "dataList", "mapData", "_callee5$", "_context5", "platformType", "m", "label", "join", "filterwords", "thinkTankClassification", "is<PERSON>ummary", "isSwdt01", "isSwdt02", "isContentTranslated", "isTranslated", "monitoringMedium", "sent", "rows", "map", "index", "concat", "Date", "now", "Math", "random", "toString", "substring", "cnName", "count", "articleCount", "orderNum", "country", "countryOf<PERSON><PERSON>in", "url", "finish", "flag", "_this9", "_callee7", "_callee7$", "_context7", "abrupt", "clearTimeout", "_callee6", "articleList", "_callee6$", "_context6", "isSort", "weChatName", "String", "addArticleHistory", "keyword", "esRetrieval", "list", "cnTitle", "changeColor", "title", "deduplicateArticles", "max", "ceil", "handleSelectionChange", "selectedData", "operationType", "currentPageIds", "filteredCheckList", "filteredSavedData", "combinedCheckList", "combinedSavedData", "deduplicateBySourceSn", "dataArray", "seen", "Set", "has", "add", "handleReset", "treeClear", "handleTreeCurrentChange", "page", "handleTreePageSizeChange", "size", "name", "handleFilterSearch", "handleClassifyChange", "classifyValue", "handleCheckedChange", "value", "checkedCount", "handleSearch", "handleRefresh", "keywordsChange", "handleTableSelectionChange", "selection", "handleCheckAllChange", "toggleAllSelection", "clearSelection", "openReport", "reportSubmit", "_this10", "_callee8", "keyWordList", "_callee8$", "_context8", "listId", "AddReport", "batchDelete", "_this11", "$confirm", "API", "batchRemove", "response", "openTaizhang", "_this12", "addWork", "publishHot", "_this13", "publishEverydayHot", "removeHistory", "_this14", "_callee9", "_callee9$", "_context9", "delArticleHistory", "focus", "showHistoryList", "hideHistoryList", "_this15", "_this16", "listArticleHistory", "clearHistory", "_this17", "_callee10", "_callee10$", "_context10", "cleanArticleHistory", "moreHistory", "_this18", "openNewView", "window", "open", "docId", "getTechnologyLabel", "mapping", "has<PERSON><PERSON>ual<PERSON><PERSON>nt", "text", "contentWithoutTags", "test", "rightMain", "table", "bodyWrapper", "$el", "str", "regex", "originalStr", "hasTagsSubset", "hasKeywords", "result", "split", "keyitem", "trimmedKeyword", "escapedKeyword", "replaceString", "htmlTagRegex", "parts", "lastIndex", "match", "exec", "content", "part", "RegExp", "resultEvent", "_this19", "warning", "zhuangtai", "row", "snapshotUrl", "$msgbox", "showCancelButton", "confirmButtonText", "beforeClose", "_", "__", "done", "downLoadExportKe", "location", "origin", "openUrl", "difyAiChat", "_this20", "_callee11", "_articlesResponse$dat", "selectedArticles", "titles", "articlesResponse", "articlesContent", "aiMessage", "prompt", "reader", "decoder", "buffer", "<PERSON><PERSON><PERSON><PERSON>", "isInThinkTag", "decodeUnicode", "updateContent", "_yield$reader$read", "lastData", "decodedAnswer", "chunk", "newlineIndex", "line", "jsonData", "answer", "_callee11$", "_context11", "cancel", "log", "resolve", "getListByIds", "Error", "_selectedArticles$ind", "_selectedArticles$ind2", "role", "difyAiQa", "ok", "body", "<PERSON><PERSON><PERSON><PERSON>", "TextDecoder", "fromCharCode", "parseInt", "newContent", "renderedContent", "marked", "scrollHeight", "read", "parse", "e", "warn", "decode", "indexOf", "slice", "startsWith", "t1", "t2", "ollamaAiChat", "_this21", "_callee13", "_articlesResponse$dat2", "_aiMessage", "lastUpdateTime", "isThinkContent", "temp<PERSON><PERSON><PERSON>", "processStream", "_callee13$", "_context13", "_selectedArticles$ind3", "_selectedArticles$ind4", "ollamaAiQa", "currentTime", "_ref2", "_callee12", "_yield$reader$read2", "lines", "_iterator", "_step", "_response", "thinkStartIndex", "thinkEndIndex", "_callee12$", "_context12", "_createForOfIteratorHelper2", "s", "n", "f", "apply", "arguments", "deepseekAiChat", "_this22", "_callee14", "_aiMessage2", "_lastUpdateTime", "_yield$reader$read3", "_iterator2", "_step2", "_jsonData$choices", "_callee14$", "_context14", "_selectedArticles$ind5", "_selectedArticles$ind6", "deepseekAiQa", "choices", "delta", "t3", "t4", "closeAiDialog", "articleAiChat", "chartAiChat", "difyChartAiChat", "deepseekChartAiChat", "_this23", "_callee15", "articleResult", "aiResult", "aiData", "content2", "parsedData", "finalHtml", "_callee15$", "_context15", "chartContent", "innerHTML", "AreaInfo", "json", "html", "onload", "onerror", "iframe", "createElement", "style", "width", "height", "border", "display", "overflow", "append<PERSON><PERSON><PERSON>", "contentWindow", "Chart", "chartScript", "src", "head", "executeIframeScripts", "doc", "write", "close", "close<PERSON>hart<PERSON><PERSON><PERSON>", "_this24", "_callee16", "_callee16$", "_context16", "instances", "Object", "values", "instance", "destroy", "_this25", "articles", "titleMap", "Map", "cleanTitle", "get", "set", "originalTitle", "_ref3"], "sources": ["src/views/InfoEscalation/Wechat.vue"], "sourcesContent": ["<template>\r\n  <div v-loading=\"globalLoading\" element-loading-text=\"数据加载中\">\r\n    <splitpanes class=\"default-theme\">\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"26\"\r\n      >\r\n        <TreeTable\r\n          ref=\"treeTable\"\r\n          :data=\"treeDataTransfer\"\r\n          :total=\"treeTotal\"\r\n          :current-page=\"treeCurrentPage\"\r\n          :page-size=\"treePageSize\"\r\n          :loading=\"loading\"\r\n          :selected-sources=\"savedCheckboxData\"\r\n          row-key=\"id\"\r\n          @selection-change=\"handleSelectionChange\"\r\n          @reset=\"handleReset\"\r\n          @size-change=\"handleTreePageSizeChange\"\r\n          @current-change=\"handleTreeCurrentChange\"\r\n          @filter-search=\"handleFilterSearch\"\r\n          @classify-change=\"handleClassifyChange\"\r\n        />\r\n      </pane>\r\n      <pane min-size=\"50\" max-size=\"80\" size=\"74\">\r\n        <div\r\n          class=\"rightMain\"\r\n          style=\"margin-left: 0; overflow-y: auto\"\r\n          ref=\"rightMain\"\r\n          v-loading=\"tableLoading\"\r\n          element-loading-text=\"数据加载中\"\r\n        >\r\n          <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"Form\"\r\n            label-width=\"90px\"\r\n            @submit.native.prevent\r\n          >\r\n            <el-form-item label=\"发布日期:\" prop=\"dateType\">\r\n              <el-radio-group v-model=\"queryParams.dateType\" size=\"small\">\r\n                <el-radio-button :label=\"1\">今天</el-radio-button>\r\n                <el-radio-button :label=\"2\">近2天</el-radio-button>\r\n                <el-radio-button :label=\"4\">近7天</el-radio-button>\r\n                <el-radio-button :label=\"5\">近30天</el-radio-button>\r\n                <el-radio-button :label=\"10\">全部</el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <div style=\"display: flex\">\r\n              <el-form-item\r\n                label=\"小信优选:\"\r\n                prop=\"isTechnology\"\r\n                style=\"margin-right: 20px\"\r\n              >\r\n                <el-radio-group v-model=\"queryParams.isTechnology\" size=\"small\">\r\n                  <el-radio-button\r\n                    v-for=\"dict in dict.type.is_technology\"\r\n                    :label=\"dict.value\"\r\n                    :key=\"'is_technology' + dict.value\"\r\n                    >{{ dict.label }}</el-radio-button\r\n                  >\r\n                  <el-radio-button :label=\"null\" :key=\"'is_technologyAll'\"\r\n                    >全部</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n              <el-form-item label=\"小信精选:\" prop=\"emotion\">\r\n                <el-radio-group v-model=\"queryParams.emotion\" size=\"small\">\r\n                  <el-radio-button :label=\"'1'\" :key=\"'is_emotion1'\"\r\n                    >选中</el-radio-button\r\n                  >\r\n                  <el-radio-button :label=\"'0'\" :key=\"'is_emotion0'\"\r\n                    >全部</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n            </div>\r\n            <el-form-item label=\"检索词库:\" prop=\"tags\">\r\n              <el-radio-group v-model=\"queryParams.tags\" size=\"small\">\r\n                <el-radio :label=\"''\">全部</el-radio>\r\n                <el-radio\r\n                  v-for=\"item in tagsList1\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.id\"\r\n                  >{{ item.name }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item\r\n              style=\"width: 100%; overflow: auto\"\r\n              label=\"\"\r\n              prop=\"tagsSubset\"\r\n              v-if=\"queryParams.tags != ''\"\r\n            >\r\n              <el-checkbox\r\n                style=\"float: left; margin-right: 30px\"\r\n                :indeterminate=\"isIndeterminate\"\r\n                v-model=\"checkAll\"\r\n                @change=\"handleCheckAllTagsSubset\"\r\n                >全选</el-checkbox\r\n              >\r\n              <el-checkbox-group v-model=\"queryParams.tagsSubset\">\r\n                <el-checkbox\r\n                  v-for=\"item in tagsList\"\r\n                  :key=\"item.name\"\r\n                  :label=\"item.name\"\r\n                ></el-checkbox>\r\n              </el-checkbox-group>\r\n            </el-form-item>\r\n            <el-form-item class=\"keyword\" label=\"关键词:\" prop=\"keywords\">\r\n              <el-input\r\n                ref=\"keywordRef\"\r\n                placeholder=\"请输入关键词,使用逗号分割(英文)\"\r\n                style=\"width: 430px\"\r\n                v-model=\"queryParams.keywords\"\r\n                @focus=\"showHistoryList()\"\r\n                @blur=\"hideHistoryList()\"\r\n                @keyup.enter.native=\"handleSearch()\"\r\n              >\r\n              </el-input>\r\n              <div class=\"history\" v-show=\"showHistory\">\r\n                <div\r\n                  class=\"historyItem\"\r\n                  v-for=\"(history, index) in historyList\"\r\n                  :key=\"index\"\r\n                  v-loading=\"historyLoading\"\r\n                >\r\n                  <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n                    {{ history.keyword }}\r\n                  </div>\r\n                  <el-button\r\n                    type=\"text\"\r\n                    @click=\"removeHistory(history, 1)\"\r\n                    style=\"color: #999; font-size: 12px\"\r\n                    >删除</el-button\r\n                  >\r\n                </div>\r\n                <div class=\"historyItem\">\r\n                  <el-button type=\"text\" @click=\"moreHistory()\">更多</el-button>\r\n                  <el-button\r\n                    type=\"text\"\r\n                    @click=\"clearHistory()\"\r\n                    style=\"color: #999; font-size: 12px\"\r\n                    >清空</el-button\r\n                  >\r\n                </div>\r\n              </div>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                style=\"margin-left: 10px; height: 36px\"\r\n                @click=\"handleSearch()\"\r\n                >搜索</el-button\r\n              >\r\n            </el-form-item>\r\n            <div class=\"keyword-tip\">\r\n              *支持按照多个关键词检索，“与”使用“,”分隔，“或”使用“|”分割\r\n            </div>\r\n          </el-form>\r\n          <div class=\"TopBtnGroup\">\r\n            <div class=\"TopBtnGroup_left\">\r\n              <el-checkbox v-model=\"checked\" @change=\"handleCheckAllChange\"\r\n                >全选</el-checkbox\r\n              >\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  title=\"批量删除文章\"\r\n                  class=\"icon-shanchu\"\r\n                  @click=\"batchDelete\"\r\n                ></i>\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"icon-shuaxin-copy\"\r\n                  title=\"刷新\"\r\n                  @click=\"handleRefresh\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p class=\"toolTitle\">\r\n              <i class=\"icon--_tianjiadaoku\" title=\"添加到报告\" @click=\"openReport\" v-hasPermi=\"['result:report:add']\"></i>\r\n            </p> -->\r\n              <!-- <p class=\"toolTitle\">\r\n              <i title=\"批量生成快照\" class=\"icon-pingmukuaizhao\" style=\"color:green\"\r\n                v-hasPermi=\"['article:collection:snapshot']\" @click=\"resultEvent()\"></i>\r\n            </p> -->\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-document\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"添加到工作台账\"\r\n                  @click=\"openTaizhang\"\r\n                  v-hasPermi=\"['article:work:add']\"\r\n                ></i>\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-document-add\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"发布到每日最新热点\"\r\n                  @click=\"publishHot\"\r\n                ></i>\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-chat-dot-round\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"Deepseek深度解读\"\r\n                  @click=\"articleAiChat\"\r\n                ></i>\r\n                <span class=\"deepseek-text\" @click=\"articleAiChat\"\r\n                  >Deepseek深度解读</span\r\n                >\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-pie-chart\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"生成Deepseek图表看板\"\r\n                  @click=\"chartAiChat\"\r\n                ></i>\r\n                <span class=\"deepseek-text\" @click=\"chartAiChat\"\r\n                  >生成Deepseek图表看板</span\r\n                >\r\n              </p>\r\n            </div>\r\n            <div>\r\n              <el-checkbox\r\n                v-model=\"showSummary\"\r\n                @change=\"(e) => (showSummary = e)\"\r\n                style=\"margin-right: 10px\"\r\n                >是否显示摘要</el-checkbox\r\n              >\r\n              <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n              <el-select v-model=\"queryParams.sortMode\" size=\"mini\">\r\n                <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n                <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n                <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n                <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n                <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n          <!-- <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleUpdate0\"\r\n                v-hasPermi=\"['wechat:user:remove']\"\r\n                >设置为科技无关</el-button\r\n              >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleUpdate1\"\r\n                v-hasPermi=\"['wechat:user:remove']\"\r\n                >设置为科技有关</el-button\r\n              >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleUpdate2\"\r\n                v-hasPermi=\"['wechat:user:remove']\"\r\n                >设置为其他</el-button\r\n              >\r\n            </el-col>\r\n          </el-row> -->\r\n          <el-table\r\n            :data=\"ArticleList\"\r\n            style=\"width: 100%; user-select: text\"\r\n            :show-header=\"false\"\r\n            ref=\"table\"\r\n            :height=\"\r\n              'calc(100vh - ' +\r\n              (374 + (queryParams.tags != '' ? 51 : 0)) +\r\n              'px)'\r\n            \"\r\n            @selection-change=\"handleTableSelectionChange\"\r\n          >\r\n            <el-table-column type=\"selection\" width=\"35\" align=\"center\" />\r\n            <el-table-column width=\"50\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span style=\"color: #080808; font-size: 15px\">\r\n                  {{\r\n                    (queryParams.pageNum - 1) * queryParams.pageSize +\r\n                    scope.$index +\r\n                    1\r\n                  }}\r\n                </span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"编号\"\r\n              align=\"center\"\r\n              key=\"id\"\r\n              prop=\"id\"\r\n              width=\"100\"\r\n              v-if=\"false\"\r\n            />\r\n            <el-table-column prop=\"title\" label=\"日期\" min-width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <span class=\"article_title\" @click=\"openNewView(scope.row)\">\r\n                  <span\r\n                    style=\"color: #080808\"\r\n                    v-html=\"scope.row.title || scope.row.cnTitle\"\r\n                  ></span>\r\n                  <span style=\"color: #5589f5\">\r\n                    {{ \"(\" + scope.row.publishTime + \")\" }}\r\n                  </span>\r\n                  <span>&nbsp;||&nbsp;</span>\r\n                  <span style=\"color: #5589f5\">\r\n                    {{ scope.row.sourceName }}\r\n                  </span>\r\n                  <span>&nbsp;||&nbsp;</span>\r\n                  <span style=\"color: #5589f5\">\r\n                    大模型筛选:{{ getTechnologyLabel(scope.row.isTechnology) }}\r\n                  </span>\r\n                </span>\r\n                <div\r\n                  class=\"ArticlMain\"\r\n                  style=\"\r\n                    display: -webkit-box;\r\n                    -webkit-box-orient: vertical;\r\n                    -webkit-line-clamp: 2;\r\n                    overflow: hidden;\r\n                    text-overflow: ellipsis;\r\n                    word-break: break-all;\r\n                  \"\r\n                  v-if=\"\r\n                    showSummary &&\r\n                    hasActualContent(scope.row.cnSummary || scope.row.summary)\r\n                  \"\r\n                >\r\n                  <span style=\"color: #9b9b9b\">摘要：</span>\r\n                  <span\r\n                    style=\"color: #4b4b4b\"\r\n                    v-html=\"changeColor(getSafeSummary(scope.row))\"\r\n                    @click=\"openNewView(scope.row)\"\r\n                  ></span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <!-- <el-table-column prop=\"publishTime\" label=\"发布时间\" width=\"180\">\r\n            </el-table-column> -->\r\n            <!-- <el-table-column label=\"操作\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <i class=\"icon--_tianjiadaoku\" title=\"添加到报告\" @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"></i>\r\n              </template>\r\n            </el-table-column> -->\r\n          </el-table>\r\n          <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"handlePagination\"\r\n            :autoScroll=\"true\"\r\n          />\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n    <el-dialog\r\n      title=\"添加到报告\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"500px\"\r\n      :before-close=\"closeReport\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-row style=\"line-height: 50px\">\r\n        <el-col :span=\"18\">\r\n          <el-select\r\n            v-model=\"reportId\"\r\n            placeholder=\"请选择报告\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, key) in reportOptions\"\r\n              :key=\"key\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-col>\r\n      </el-row>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closeReport\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"reportSubmit\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"关键词历史\"\r\n      :visible.sync=\"dialogVisible1\"\r\n      width=\"570px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"history\" v-loading=\"historyLoading\">\r\n        <div\r\n          class=\"historyItem\"\r\n          v-for=\"(history, index) in historyList1\"\r\n          :key=\"index\"\r\n        >\r\n          <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n            {{ history.keyword }}\r\n          </div>\r\n          <el-button type=\"text\" @click=\"removeHistory(history, 2)\"\r\n            >删除</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n      <pagination\r\n        v-show=\"total1 > 0\"\r\n        :total=\"total1\"\r\n        :page.sync=\"queryParams1.pageNum\"\r\n        :limit.sync=\"queryParams1.pageSize\"\r\n        :background=\"false\"\r\n        @pagination=\"handleHistoryPagination\"\r\n        :layout=\"'total, prev, pager, next'\"\r\n        :autoScroll=\"true\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek深度解读\"\r\n      :visible.sync=\"aiDialogVisible\"\r\n      width=\"1000px\"\r\n      :before-close=\"closeAiDialog\"\r\n      custom-class=\"ai-dialog\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"ai-chat-container\">\r\n        <div class=\"chat-messages\" ref=\"chatMessages\">\r\n          <div\r\n            class=\"message\"\r\n            v-for=\"(message, index) in chatMessages\"\r\n            :key=\"index\"\r\n            :class=\"[\r\n              'message',\r\n              message.role === 'user' ? 'user-message' : 'ai-message',\r\n            ]\"\r\n          >\r\n            <div class=\"avatar\">\r\n              <img\r\n                style=\"width: 30px; height: 30px\"\r\n                v-if=\"message.role === 'user'\"\r\n                :src=\"userAvatar || require('@/assets/images/home/<USER>')\"\r\n                alt=\"用户头像\"\r\n              />\r\n              <img v-else src=\"@/assets/images/logo2.png\" alt=\"AI头像\" />\r\n            </div>\r\n            <div class=\"message-wrapper\">\r\n              <div\r\n                v-if=\"\r\n                  message.role === 'assistant' && isThinking && !message.content\r\n                \"\r\n                class=\"thinking-animation\"\r\n              >\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"message-content\"\r\n                v-html=\"\r\n                  message.role === 'assistant'\r\n                    ? message.content\r\n                    : message.content\r\n                \"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <template slot=\"footer\">\r\n        <el-button @click=\"closeAiDialog\">取 消</el-button>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek图表看板\"\r\n      :visible.sync=\"chartDialogVisible\"\r\n      width=\"1200px\"\r\n      :before-close=\"closeChartDialog\"\r\n      custom-class=\"chart-dialog\"\r\n      destroy-on-close\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div\r\n        v-if=\"chartDialogVisible\"\r\n        class=\"chart-container\"\r\n        v-loading=\"chartLoading\"\r\n        element-loading-text=\"正在生成图表看板...\"\r\n        element-loading-spinner=\"el-icon-loading\"\r\n        element-loading-background=\"rgba(255, 255, 255, 0.8)\"\r\n      >\r\n        <div class=\"chart-content\" ref=\"chartContent\"></div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closeChartDialog\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/ScienceApi/index.js\";\r\nimport {\r\n  listWork,\r\n  getWork,\r\n  delWork,\r\n  addWork,\r\n  updateWork,\r\n} from \"@/api/article/work\";\r\nimport { listKeywords } from \"@/api/article/keywords\";\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport {\r\n  listArticleHistory,\r\n  delArticleHistory,\r\n  addArticleHistory,\r\n  cleanArticleHistory,\r\n  getListByIds,\r\n} from \"@/api/article/articleHistory\";\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\nimport { mapGetters } from \"vuex\";\r\nimport TreeTable from \"@/components/TreeTable/index.vue\";\r\nimport { deepseekAiQa, difyAiQa, ollamaAiQa } from \"@/api/infoEscalation/ai\";\r\nimport { marked } from \"marked\";\r\nimport { getConfigKey } from \"@/api/system/config\";\r\n\r\nexport default {\r\n  components: { Splitpanes, Pane, TreeTable },\r\n  dicts: [\"is_technology\"],\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      tableLoading: false, // 表格loading状态\r\n      queryParams: {\r\n        id: 100,\r\n        pageNum: 1,\r\n        pageSize: 50,\r\n        dateType: 4,\r\n        tags: \"\",\r\n        tagsSubset: [],\r\n        keywords: \"\",\r\n        isTechnology: \"1\",\r\n        sortMode: \"4\",\r\n        emotion: \"0\",\r\n        hasCache: \"0\",\r\n      },\r\n      total: 0,\r\n      treeDataTransfer: [], // 原始树形数据\r\n      filterText: \"\", // 左侧树搜索栏\r\n      checkList: [], // 左侧勾选数据\r\n      ArticleList: [], // 列表数据\r\n      checked: false, // 全选\r\n      ids: [], // 选中的数据\r\n      // 非多个禁用\r\n      multiple: true,\r\n      dialogVisible: false, // 添加到报告弹框\r\n      reportOptions: [], // 报告列表\r\n      reportId: \"\", // 已选择的报告\r\n      tagsList: [], // 检索词库二级列表\r\n      tagsList1: [], // 检索词库一级列表\r\n      checkAll: false, // 检索词库全选\r\n      isIndeterminate: true, // 检索词库选了值\r\n      showHistory: false,\r\n      historyList: [],\r\n      historyTimeout: null,\r\n      dialogVisible1: false,\r\n      historyLoading: false,\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      historyList1: [],\r\n      showSummary: true,\r\n      /* 树形分页数据 */\r\n      treeCurrentPage: 1,\r\n      treePageSize: 100,\r\n      treeTotal: 0,\r\n      /* 初始化完成标记 */\r\n      initializationCompleted: false,\r\n      /* 搜索防抖 */\r\n      searchDebounceTimer: null,\r\n      /* 查询防抖 */\r\n      queryDebounceTimer: null,\r\n      /* 防止重复查询 */\r\n      isQuerying: false,\r\n      /* 标记右侧筛选条件是否发生变化 */\r\n      isRightFilter: false,\r\n      /* 标记左侧树是否重置 */\r\n      isLeftReset: false,\r\n      /* 选中的数据源分类 */\r\n      selectedClassify: null,\r\n      /* 保存的勾选数据（永久保存，只有特定操作才更新） */\r\n      savedCheckboxData: [],\r\n      // ai相关\r\n      aiDialogVisible: false,\r\n      chatMessages: [],\r\n      isThinking: false,\r\n      userAvatar: \"\", // 用户头像\r\n      streamingMessage: \"\", // 添加用于存储正在流式输出的消息\r\n      markdownOptions: {\r\n        gfm: true,\r\n        breaks: true,\r\n        headerIds: true,\r\n        mangle: false,\r\n        headerPrefix: \"\",\r\n        pedantic: false,\r\n        sanitize: false,\r\n        smartLists: true,\r\n        smartypants: true,\r\n        xhtml: true,\r\n      },\r\n      isRequesting: false, // 标记是否正在请求中\r\n      isAborted: false, // 标记是否已中断\r\n      currentReader: null, // 当前的 reader\r\n      aiPlatform: \"\",\r\n      articleAiPrompt: \"\",\r\n      nodeCheckList: [],\r\n      chartDialogVisible: false,\r\n      chartHtml: \"\",\r\n      chartLoading: true,\r\n      currentChartIframe: null, // 添加变量跟踪当前iframe\r\n      difyApikey: {\r\n        article: \"\",\r\n        chart: \"\",\r\n      },\r\n      chartPrompt: \"\",\r\n      globalLoading: false,\r\n    };\r\n  },\r\n  watch: {\r\n    // 监听筛选条件变化\r\n    \"queryParams.dateType\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.isTechnology\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.emotion\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.tags\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n\r\n        // this.queryParams.tagsSubset = [];\r\n        this.checkAll = true;\r\n        this.isIndeterminate = false;\r\n\r\n        if (newVal != \"\") {\r\n          // 不在这里设置tableLoading，让后续的queryArticleList来处理\r\n          listKeywords({ parentId: newVal, pageNum: 1, pageSize: 10 })\r\n            .then((res) => {\r\n              this.tagsList = res.data;\r\n              this.handleCheckAllTagsSubset(true);\r\n              // this.handleRightFilterChange();\r\n            })\r\n            .catch((error) => {\r\n              console.error(\"获取检索词库失败:\", error);\r\n              this.$message.error(\"获取检索词库失败\");\r\n            });\r\n        } else {\r\n          this.handleRightFilterChange();\r\n        }\r\n      },\r\n    },\r\n    \"queryParams.tagsSubset\": {\r\n      handler(newVal, oldVal) {\r\n        if (\r\n          !this.initializationCompleted ||\r\n          JSON.stringify(newVal) === JSON.stringify(oldVal)\r\n        )\r\n          return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.sortMode\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.scrollToTopImmediately();\r\n        this.queryArticleList();\r\n      },\r\n    },\r\n    dialogVisible(val) {\r\n      if (val) {\r\n        api.getNewBuilt({ sourceType: \"1\" }).then((data) => {\r\n          if (data.code == 200) {\r\n            this.reportOptions = data.data;\r\n          } else {\r\n            this.$message({ message: \"报告列表获取失败了\", type: \"error\" });\r\n            this.closeReport();\r\n          }\r\n        });\r\n      }\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"roles\"]),\r\n  },\r\n  async created() {\r\n    getConfigKey(\"sys.ai.platform\").then((res) => {\r\n      if (res.code == 200) {\r\n        this.aiPlatform = res.msg;\r\n      }\r\n    });\r\n    getConfigKey(\"wechat.ai.articlePrompt\").then((res) => {\r\n      if (res.code == 200) {\r\n        this.articleAiPrompt = res.msg;\r\n      }\r\n    });\r\n    getConfigKey(\"wechat.ai.chartPrompt\").then((res) => {\r\n      if (res.code == 200) {\r\n        this.chartPrompt = res.msg;\r\n      }\r\n    });\r\n    // 获取用户头像\r\n    this.userAvatar = this.$store.getters.avatar;\r\n    try {\r\n      // 先加载基础数据\r\n      Promise.all([\r\n        this.getArticleHistory(),\r\n        listKeywords({ parentId: 0, pageNum: 1, pageSize: 10 }).then((res) => {\r\n          this.tagsList1 = res.data.filter((item) => item.parentId == 0);\r\n        }),\r\n      ]);\r\n\r\n      // 加载树数据和内容数据\r\n      await this.initializeData();\r\n\r\n      if (this.roles.includes(\"information\")) {\r\n        this.showSummary = false;\r\n      }\r\n\r\n      // 标记初始化完成，这样watch监听器才会开始工作\r\n      this.initializationCompleted = true;\r\n    } catch (error) {\r\n      console.error(\"组件初始化失败:\", error);\r\n      this.$message.error(\"初始化失败，请刷新页面重试\");\r\n    }\r\n  },\r\n\r\n  mounted() {},\r\n  methods: {\r\n    // 安全处理摘要内容，避免null调用replace报错\r\n    getSafeSummary(item) {\r\n      const cnSummary = item.cnSummary || \"\";\r\n      const summary = item.summary || \"\";\r\n\r\n      const processedCnSummary = cnSummary\r\n        ? cnSummary\r\n            .replace(/p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6|ul|li|a/g, \"span\")\r\n            .replace(/<img[^>]*>/g, \"\") // 移除img标签\r\n            .replace(/\\\\n|\\\\\\\\n|\\\\\\\\\\\\n|\\n/g, \" \") // 移除各种换行符\r\n            .replace(/\\s+/g, \" \") // 合并多个空格为一个\r\n            .trim()\r\n        : \"\";\r\n\r\n      const processedSummary = summary\r\n        ? summary\r\n            .replace(/p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6|ul|li|a/g, \"span\")\r\n            .replace(/<img[^>]*>/g, \"\") // 移除img标签\r\n            .replace(/\\\\n|\\\\\\\\n|\\\\\\\\\\\\n|\\n/g, \" \") // 移除各种换行符\r\n            .replace(/\\s+/g, \" \") // 合并多个空格为一个\r\n            .trim()\r\n        : \"\";\r\n\r\n      return processedCnSummary || processedSummary;\r\n    },\r\n    // 初始化数据\r\n    async initializeData() {\r\n      try {\r\n        // this.globalLoading = true;\r\n        // 加载文章列表（内部已经处理了 tableLoading）\r\n        this.queryArticleList();\r\n        // 先加载树数据\r\n        await this.queryTreeData();\r\n        // 等待树组件完全渲染\r\n        await this.$nextTick();\r\n\r\n        // 默认全选第一页数据源\r\n        // if (this.treeDataTransfer && this.treeDataTransfer.length > 0) {\r\n        //   // 全选第一页的所有数据源\r\n        //   const firstPageData = [...this.treeDataTransfer];\r\n        //   this.checkList = firstPageData;\r\n        //   this.savedCheckboxData = firstPageData;\r\n\r\n        //   // 通知 TreeTable 组件设置选中状态\r\n        //   this.$nextTick(() => {\r\n        //     if (this.$refs.treeTable) {\r\n        //       this.$refs.treeTable.restoreSelectionSilently(firstPageData);\r\n        //     }\r\n        //   });\r\n\r\n        //   // 延迟一下再查询文章列表，确保选中状态已设置\r\n        //   setTimeout(() => {\r\n        //     this.queryArticleList();\r\n        //   }, 100);\r\n        // } else {\r\n        //   // 如果没有数据源，直接查询文章列表\r\n        //   this.queryArticleList();\r\n        // }\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"初始化失败，请刷新页面重试\");\r\n        // this.globalLoading = false;\r\n      }\r\n    },\r\n\r\n    // 处理右侧筛选条件变化\r\n    handleRightFilterChange() {\r\n      this.isRightFilter = true; // 标记右侧筛选条件发生变化\r\n\r\n      // 不再保存当前选中状态，使用永久保存的勾选数据\r\n      // 永久保存的勾选数据会在查询后自动恢复\r\n\r\n      // 重置分页到第一页\r\n      this.queryParams.pageNum = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"0\";\r\n\r\n      // 滚动到顶部\r\n      this.scrollToTopImmediately();\r\n\r\n      // 同时查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 同时查询树和列表\r\n    async queryTreeAndList() {\r\n      try {\r\n        // 保存当前的永久勾选数据，避免在查询过程中丢失\r\n        const savedData = [...this.savedCheckboxData];\r\n\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (savedData && savedData.length > 0) {\r\n          this.checkList = [...savedData];\r\n        } else {\r\n          // 如果没有永久保存的勾选数据，清空选中状态\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 同时查询树数据和右侧列表（保持性能优势）\r\n        await Promise.all([\r\n          this.queryTreeData(),\r\n          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading\r\n        ]);\r\n\r\n        // 确保永久保存的勾选数据不会丢失\r\n        this.savedCheckboxData = savedData;\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n\r\n        // 查询完成后重置右侧筛选标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      } catch (error) {\r\n        console.error(\"同时查询树和列表失败:\", error);\r\n        this.$message.error(\"查询失败，请重试\");\r\n        // 即使出错也要重置标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      }\r\n    },\r\n\r\n    // 恢复选中数据源的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 从永久保存的勾选数据恢复选中状态（仅处理界面选中状态）\r\n    restoreFromSavedCheckboxData() {\r\n      if (!this.savedCheckboxData || this.savedCheckboxData.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // 在当前树数据中查找匹配的项\r\n      const matchedItems = [];\r\n      this.savedCheckboxData.forEach((savedItem) => {\r\n        const foundItem = this.treeDataTransfer.find(\r\n          (treeItem) => treeItem.sourceSn === savedItem.sourceSn\r\n        );\r\n        if (foundItem) {\r\n          matchedItems.push(foundItem);\r\n        }\r\n      });\r\n\r\n      if (matchedItems.length > 0) {\r\n        // 更新选中列表（此时 checkList 已经在查询前恢复过了）\r\n        this.checkList = matchedItems;\r\n        // 通知 TreeTable 组件恢复界面选中状态（不触发事件）\r\n        this.$nextTick(() => {\r\n          if (this.$refs.treeTable) {\r\n            this.$refs.treeTable.restoreSelectionSilently(matchedItems);\r\n          }\r\n        });\r\n      } else {\r\n        // 如果没有匹配项，清空选中状态\r\n        this.checkList = [];\r\n      }\r\n    },\r\n\r\n    // 保存当前选中状态的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 查询树数据并从永久保存的勾选数据恢复选中状态（用于关键字过滤）\r\n    async queryTreeDataWithRestoreFromSaved() {\r\n      try {\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.checkList = [...this.savedCheckboxData];\r\n        } else {\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 查询树数据\r\n        await this.queryTreeData();\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n      } catch (error) {\r\n        console.error(\r\n          \"查询树数据并从永久保存的勾选数据恢复选中状态失败:\",\r\n          error\r\n        );\r\n      }\r\n    },\r\n\r\n    // 分页处理\r\n    handlePagination() {\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 历史记录分页处理\r\n    handleHistoryPagination() {\r\n      this.getArticleHistory1();\r\n      this.$nextTick(() => {\r\n        const dialogContent = document.querySelector(\".el-dialog__body\");\r\n        if (dialogContent) {\r\n          dialogContent.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 查询树数据\r\n    async queryTreeData() {\r\n      this.loading = true;\r\n      try {\r\n        const params = {\r\n          platformType: 0,\r\n          id: this.queryParams.id,\r\n          pageNum: this.treeCurrentPage,\r\n          pageSize: this.treePageSize,\r\n          m: 1,\r\n          dateType:\r\n            this.queryParams.dateType != 6 ? this.queryParams.dateType : \"\",\r\n          tags: this.queryParams.tags,\r\n          tagsSubset: this.queryParams.tagsSubset,\r\n          keywords: this.queryParams.keywords,\r\n          isTechnology: this.queryParams.isTechnology,\r\n          emotion: this.queryParams.emotion,\r\n          label: this.queryParams.tagsSubset.join(\",\"),\r\n          // 添加关键字过滤参数\r\n          filterwords: this.filterText || \"\",\r\n          // 添加数据源分类参数\r\n          thinkTankClassification: this.selectedClassify,\r\n          hasCache: this.queryParams.hasCache,\r\n          // 小信精选附加参数\r\n          isSummary: this.queryParams.emotion,\r\n          isSwdt01: this.queryParams.emotion,\r\n          isSwdt02: this.queryParams.emotion,\r\n          isContentTranslated: this.queryParams.emotion,\r\n          isTranslated: this.queryParams.emotion,\r\n        };\r\n\r\n        if (!this.queryParams.tags) {\r\n          params.tagsSubset = [];\r\n          params.label = \"\";\r\n        }\r\n\r\n        const res = await api.monitoringMedium(params);\r\n\r\n        if (res.code === 200) {\r\n          const dataList = res.rows || [];\r\n          const total = res.total || 0;\r\n\r\n          const mapData = (data) =>\r\n            data.map((item, index) => ({\r\n              id: `${\r\n                item.sourceSn || \"unknown\"\r\n              }_${index}_${Date.now()}_${Math.random()\r\n                .toString(36)\r\n                .substring(2, 11)}`, // 确保绝对唯一性\r\n              label: item.cnName,\r\n              count: item.articleCount || 0,\r\n              orderNum: item.orderNum,\r\n              country: item.countryOfOrigin || null,\r\n              sourceSn: item.sourceSn,\r\n              url: item.url || null,\r\n            }));\r\n\r\n          this.treeDataTransfer = mapData(dataList);\r\n          this.treeTotal = total;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"查询树数据失败:\", error);\r\n        this.$message.error(\"获取数据源失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 查询文章列表（带防抖）\r\n    async queryArticleList(flag) {\r\n      // 防止重复查询\r\n      if (this.isQuerying) {\r\n        return;\r\n      }\r\n\r\n      if (!flag || flag === \"isKeywordsSearch\") {\r\n        this.tableLoading = true;\r\n      }\r\n\r\n      // 清除之前的防抖定时器\r\n      if (this.queryDebounceTimer) {\r\n        clearTimeout(this.queryDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，300ms后执行查询\r\n      this.queryDebounceTimer = setTimeout(async () => {\r\n        try {\r\n          if (flag === \"sourceItemChanged\") {\r\n            this.globalLoading = true;\r\n          }\r\n\r\n          this.isQuerying = true;\r\n\r\n          const params = {\r\n            m: 1,\r\n            pageNum: this.queryParams.pageNum,\r\n            pageSize: this.queryParams.pageSize,\r\n            id: this.queryParams.id,\r\n            isSort: this.queryParams.sortMode,\r\n            dateType:\r\n              this.queryParams.dateType != 6 ? this.queryParams.dateType : \"\",\r\n            tags: this.queryParams.tags,\r\n            tagsSubset: this.queryParams.tagsSubset,\r\n            keywords: this.queryParams.keywords,\r\n            isTechnology: this.queryParams.isTechnology,\r\n            emotion: this.queryParams.emotion,\r\n            label: this.queryParams.tagsSubset.join(\",\"),\r\n            platformType: 0,\r\n            // 小信精选附加参数\r\n            isSummary: this.queryParams.emotion,\r\n            isSwdt01: this.queryParams.emotion,\r\n            isSwdt02: this.queryParams.emotion,\r\n            isContentTranslated: this.queryParams.emotion,\r\n            isTranslated: this.queryParams.emotion,\r\n          };\r\n\r\n          if (!this.queryParams.tags) {\r\n            params.tagsSubset = [];\r\n            params.label = \"\";\r\n          }\r\n\r\n          // 使用永久保存的勾选数据构建查询参数\r\n          if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n            const data = this.savedCheckboxData.map((item) => item.label);\r\n            const sourceSn = this.savedCheckboxData.map(\r\n              (item) => item.sourceSn\r\n            );\r\n\r\n            params.weChatName = String(data);\r\n            params.sourceSn = String(sourceSn);\r\n          }\r\n\r\n          // 记录关键词历史\r\n          if (\r\n            params.keywords &&\r\n            params.keywords.trim() !== \"\" &&\r\n            flag === \"isKeywordsSearch\"\r\n          ) {\r\n            addArticleHistory({ keyword: params.keywords, type: 1 }).then(\r\n              () => {\r\n                this.getArticleHistory();\r\n              }\r\n            );\r\n          }\r\n\r\n          const res = await api.esRetrieval(params);\r\n\r\n          if (res.code == 200) {\r\n            let articleList = res.data.list\r\n              ? res.data.list.map((item) => {\r\n                  item.cnTitle = item.cnTitle\r\n                    ? this.changeColor(item.cnTitle)\r\n                    : null;\r\n                  item.title = this.changeColor(item.title);\r\n                  return item;\r\n                })\r\n              : [];\r\n\r\n            // 去重逻辑：只有在没有关键词搜索时才进行去重\r\n            if (\r\n              !this.queryParams.keywords ||\r\n              this.queryParams.keywords.trim() === \"\"\r\n            ) {\r\n              articleList = this.deduplicateArticles(articleList);\r\n            }\r\n\r\n            this.ArticleList = articleList;\r\n            this.total = res.data.total || 0;\r\n\r\n            // 如果有永久保存的勾选数据，恢复选中状态（静默恢复，不触发右侧查询）\r\n            if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n              this.restoreFromSavedCheckboxData();\r\n            }\r\n\r\n            // 处理分页为空的情况\r\n            if (\r\n              this.ArticleList.length == 0 &&\r\n              this.queryParams.pageSize * (this.queryParams.pageNum - 1) >=\r\n                this.total &&\r\n              this.total != 0\r\n            ) {\r\n              this.queryParams.pageNum = Math.max(\r\n                1,\r\n                Math.ceil(this.total / this.queryParams.pageSize)\r\n              );\r\n              // 重新查询\r\n              await this.queryArticleList();\r\n              return; // 重新查询时不要关闭loading\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || \"获取数据失败\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"查询文章列表失败:\", error);\r\n          this.$message.error(\"查询失败，请重试\");\r\n        } finally {\r\n          this.isQuerying = false;\r\n          this.globalLoading = false;\r\n          this.tableLoading = false; // 查询完成后关闭loading\r\n        }\r\n      }, 1000);\r\n    },\r\n\r\n    // TreeTable 组件事件处理方法\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selectedData, operationType) {\r\n      if (operationType === \"row-click\" || operationType === \"clear-all\") {\r\n        // 点击行（单选）或取消所有选中：直接替换，不需要追加去重\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      } else if (\r\n        operationType === \"checkbox-change\" ||\r\n        operationType === \"select-all\"\r\n      ) {\r\n        // 点击勾选框（多选）或全选：需要正确处理选中和取消选中\r\n        // 先从保存的数据中移除当前页面的所有数据\r\n        const currentPageIds = this.treeDataTransfer.map(\r\n          (item) => item.sourceSn\r\n        );\r\n        const filteredCheckList = this.checkList.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n        const filteredSavedData = this.savedCheckboxData.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n\r\n        // 然后添加当前页面新选中的数据\r\n        const combinedCheckList = [...filteredCheckList, ...selectedData];\r\n        const combinedSavedData = [...filteredSavedData, ...selectedData];\r\n\r\n        // 对合并后的数据进行去重处理\r\n        this.checkList = this.deduplicateBySourceSn(combinedCheckList);\r\n        this.savedCheckboxData = this.deduplicateBySourceSn(combinedSavedData);\r\n      } else {\r\n        // 默认情况：直接替换（兼容性处理）\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      }\r\n\r\n      // 重置页码并查询内容\r\n      this.queryParams.pageNum = 1;\r\n      this.scrollToTopImmediately();\r\n      if (!this.isRightFilter) {\r\n        this.queryArticleList(\"sourceItemChanged\");\r\n      }\r\n    },\r\n\r\n    // 根据sourceSn去重的辅助方法\r\n    deduplicateBySourceSn(dataArray) {\r\n      const seen = new Set();\r\n      return dataArray.filter((item) => {\r\n        if (seen.has(item.sourceSn)) {\r\n          return false;\r\n        }\r\n        seen.add(item.sourceSn);\r\n        return true;\r\n      });\r\n    },\r\n\r\n    // 处理重置\r\n    handleReset() {\r\n      // 先清空过滤文本，避免触发 handleFilterSearch\r\n      this.filterText = \"\";\r\n      this.selectedClassify = null;\r\n\r\n      // 然后设置重置标记\r\n      this.isLeftReset = true;\r\n\r\n      // 清空选中状态\r\n      this.checkList = [];\r\n\r\n      // 清空保存的勾选数据（永久保存）\r\n      this.savedCheckboxData = [];\r\n\r\n      // 重置页码并查询列表数据\r\n      this.queryParams.pageNum = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n      this.scrollToTopImmediately();\r\n\r\n      // 重新查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 重置树选择（保留原方法名以兼容）\r\n    treeClear() {\r\n      this.handleReset();\r\n    },\r\n\r\n    // 处理树分页\r\n    handleTreeCurrentChange(page) {\r\n      this.treeCurrentPage = page;\r\n      this.queryParams.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    handleTreePageSizeChange(size) {\r\n      this.treePageSize = size;\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 检索词库全选处理\r\n    handleCheckAllTagsSubset(val) {\r\n      this.queryParams.tagsSubset = val\r\n        ? this.tagsList.map((item) => item.name)\r\n        : [];\r\n      this.isIndeterminate = false;\r\n      this.queryParams.pageNum = 1;\r\n\r\n      if (!this.initializationCompleted) return;\r\n\r\n      this.scrollToTopImmediately();\r\n    },\r\n\r\n    // 处理过滤搜索（来自 TreeTable 组件）\r\n    handleFilterSearch(keyword) {\r\n      if (this.isLeftReset) {\r\n        return;\r\n      }\r\n\r\n      // 更新过滤文本\r\n      this.filterText = keyword || \"\";\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n\r\n      // 调用树数据查询接口并恢复选中状态（使用永久保存的勾选数据）\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理数据源分类变化（来自 TreeTable 组件）\r\n    handleClassifyChange(classifyValue) {\r\n      // 更新选中的分类\r\n      this.selectedClassify = classifyValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 检索词库多选处理\r\n    handleCheckedChange(value) {\r\n      let checkedCount = value.length;\r\n      this.checkAll = checkedCount === this.tagsList.length;\r\n      this.isIndeterminate =\r\n        checkedCount > 0 && checkedCount < this.tagsList.length;\r\n      this.queryParams.pageNum = 1;\r\n\r\n      if (!this.initializationCompleted) return;\r\n\r\n      this.scrollToTopImmediately();\r\n      this.handleRightFilterChange();\r\n    },\r\n\r\n    // 搜索处理\r\n    handleSearch(flag) {\r\n      this.scrollToTopImmediately();\r\n      if (!flag) {\r\n        this.queryParams.pageNum = 1;\r\n      }\r\n      this.queryArticleList(\"isKeywordsSearch\");\r\n    },\r\n\r\n    // 刷新\r\n    handleRefresh() {\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 关键词历史选择\r\n    keywordsChange(item) {\r\n      this.queryParams.keywords = item.keyword;\r\n      this.dialogVisible1 = false;\r\n      this.scrollToTopImmediately();\r\n      this.queryParams.pageNum = 1;\r\n      // this.queryTreeAndList();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 右侧表格多选框选中数据\r\n    handleTableSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      if (selection.length == this.ArticleList.length) {\r\n        this.checked = true;\r\n      } else {\r\n        this.checked = false;\r\n      }\r\n      this.multiple = !selection.length;\r\n    },\r\n    // 全选\r\n    handleCheckAllChange(val) {\r\n      if (val) {\r\n        this.$refs[\"table\"].toggleAllSelection();\r\n      } else {\r\n        this.$refs[\"table\"].clearSelection();\r\n      }\r\n    },\r\n    // 打开添加到报告\r\n    openReport() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要添加的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.dialogVisible = true;\r\n    },\r\n    // 确定添加到报告\r\n    async reportSubmit() {\r\n      if (!this.reportId)\r\n        return this.$message({\r\n          message: \"请选择要添加到的报告\",\r\n          type: \"warning\",\r\n        });\r\n      let keyWordList = this.ids.map((item) => {\r\n        return { reportId: this.reportId, listId: item };\r\n      });\r\n      let res = await api.AddReport(keyWordList);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"已添加到报告\", type: \"success\" });\r\n        this.queryArticleList();\r\n      } else {\r\n        this.$message({\r\n          message: \"添加到报告失败,请联系管理员\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n      this.$refs[\"table\"].clearSelection();\r\n      this.checked = false;\r\n      this.closeReport();\r\n    },\r\n    // 关闭添加到报告\r\n    closeReport() {\r\n      this.reportId = \"\";\r\n      this.dialogVisible = false;\r\n    },\r\n    // 批量删除\r\n    batchDelete() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要删除的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.$confirm(\"是否确认删除已勾选的数据项?\")\r\n        .then(() => {\r\n          API.batchRemove(this.ids.join(\",\")).then((response) => {\r\n            this.$message({ message: \"删除成功\", type: \"success\" });\r\n            this.queryArticleList();\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 添加到台账\r\n    openTaizhang() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要添加的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.$confirm(\"是否确认添加已勾选的数据项到台账统计?\")\r\n        .then(() => {\r\n          addWork(this.ids).then(() => {\r\n            this.$message({ type: \"success\", message: \"添加成功!\" });\r\n            this.queryArticleList();\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 发布到每日最新热点\r\n    publishHot() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要发布到每日最新热点的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.$confirm(\"是否确认发布已勾选的数据项到每日最新热点?\")\r\n        .then(() => {\r\n          API.publishEverydayHot(this.ids.join(\",\")).then(() => {\r\n            this.$message({ type: \"success\", message: \"发布成功!\" });\r\n            this.queryArticleList();\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 历史记录相关方法\r\n    async removeHistory(item, type) {\r\n      clearTimeout(this.historyTimeout);\r\n      await delArticleHistory([item.id]);\r\n      if (type == 1) {\r\n        this.$refs[\"keywordRef\"].focus();\r\n        this.getArticleHistory();\r\n      } else {\r\n        this.getArticleHistory();\r\n        this.getArticleHistory1();\r\n      }\r\n    },\r\n\r\n    showHistoryList() {\r\n      this.showHistory = true;\r\n    },\r\n\r\n    hideHistoryList() {\r\n      this.historyTimeout = setTimeout(() => {\r\n        this.showHistory = false;\r\n      }, 500);\r\n    },\r\n\r\n    getArticleHistory() {\r\n      this.historyLoading = true;\r\n      listArticleHistory({ pageNum: 1, pageSize: 5, type: 1 }).then(\r\n        (response) => {\r\n          this.historyList = response.rows;\r\n          this.historyLoading = false;\r\n        }\r\n      );\r\n    },\r\n\r\n    async clearHistory() {\r\n      clearTimeout(this.historyTimeout);\r\n      this.$refs[\"keywordRef\"].focus();\r\n      await cleanArticleHistory(1);\r\n      this.getArticleHistory();\r\n    },\r\n\r\n    moreHistory() {\r\n      clearTimeout(this.historyTimeout);\r\n      this.showHistory = false;\r\n      this.historyLoading = true;\r\n      this.getArticleHistory1();\r\n      this.dialogVisible1 = true;\r\n    },\r\n\r\n    getArticleHistory1() {\r\n      this.historyLoading = true;\r\n      listArticleHistory({ ...this.queryParams1, type: 1 }).then((response) => {\r\n        this.historyList1 = response.rows;\r\n        this.total1 = response.total;\r\n        this.historyLoading = false;\r\n      });\r\n    },\r\n\r\n    // 文章详情\r\n    openNewView(item) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n\r\n    // 处理科技相关字段的显示映射\r\n    getTechnologyLabel(value) {\r\n      const mapping = {\r\n        0: \"排除\",\r\n        1: \"选中\",\r\n        2: \"待定\",\r\n        3: \"排队中\",\r\n      };\r\n      return mapping[value];\r\n    },\r\n\r\n    // 检查文本是否有实际内容\r\n    hasActualContent(text) {\r\n      if (!text) return false;\r\n      const contentWithoutTags = text.replace(/<[^>]*>/g, \"\");\r\n      return /[\\u4e00-\\u9fa5a-zA-Z0-9]/.test(contentWithoutTags);\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTopImmediately() {\r\n      if (this.$refs.rightMain) {\r\n        this.$refs.rightMain.scrollTop = 0;\r\n      }\r\n\r\n      if (this.$refs.table) {\r\n        const bodyWrapper = this.$refs.table.$el.querySelector(\r\n          \".el-table__body-wrapper\"\r\n        );\r\n        if (bodyWrapper) {\r\n          bodyWrapper.scrollTop = 0;\r\n        }\r\n      }\r\n    },\r\n\r\n    // 关键字高亮\r\n    changeColor(str) {\r\n      const regex = /<img\\b[^>]*>/gi;\r\n      let originalStr = str && str.replace(regex, \"\");\r\n\r\n      if (!originalStr) {\r\n        return originalStr;\r\n      }\r\n\r\n      // 检查是否有关键词需要高亮\r\n      const hasTagsSubset =\r\n        this.queryParams.tags &&\r\n        this.queryParams.tagsSubset &&\r\n        this.queryParams.tagsSubset.length;\r\n      const hasKeywords = this.queryParams.keywords;\r\n\r\n      if (!hasTagsSubset && !hasKeywords) {\r\n        return originalStr;\r\n      }\r\n\r\n      let result = originalStr;\r\n      let keywords = [];\r\n\r\n      // 构建关键词数组\r\n      if (hasKeywords) {\r\n        if (this.queryParams.keywords.includes(\",\")) {\r\n          keywords = [\r\n            ...(this.queryParams.tagsSubset || []),\r\n            ...this.queryParams.keywords.split(\",\"),\r\n          ];\r\n        } else if (this.queryParams.keywords.includes(\"|\")) {\r\n          keywords = [\r\n            ...(this.queryParams.tagsSubset || []),\r\n            ...this.queryParams.keywords.split(\"|\"),\r\n          ];\r\n        } else {\r\n          keywords = [\r\n            ...(this.queryParams.tagsSubset || []),\r\n            this.queryParams.keywords,\r\n          ];\r\n        }\r\n      } else {\r\n        keywords = [...(this.queryParams.tagsSubset || [])];\r\n      }\r\n\r\n      // 过滤空关键词\r\n      keywords = keywords.filter(\r\n        (keyword) => keyword && keyword.trim().length > 0\r\n      );\r\n\r\n      keywords.forEach((keyitem) => {\r\n        const trimmedKeyword = keyitem.trim();\r\n        if (trimmedKeyword.length > 0) {\r\n          // 转义特殊正则字符\r\n          const escapedKeyword = trimmedKeyword.replace(\r\n            /[.*+?^${}()|[\\]\\\\]/g,\r\n            \"\\\\$&\"\r\n          );\r\n\r\n          // 高亮替换字符串\r\n          const replaceString = `<span class=\"highlight\" style=\"color: red;\">${trimmedKeyword}</span>`;\r\n\r\n          // 使用更安全的方法：先分离HTML标签和文本内容\r\n          const htmlTagRegex = /<[^>]*>/g;\r\n          const parts = [];\r\n          let lastIndex = 0;\r\n          let match;\r\n\r\n          // 分离HTML标签和文本\r\n          while ((match = htmlTagRegex.exec(result)) !== null) {\r\n            // 添加标签前的文本\r\n            if (match.index > lastIndex) {\r\n              parts.push({\r\n                type: \"text\",\r\n                content: result.substring(lastIndex, match.index),\r\n              });\r\n            }\r\n            // 添加HTML标签\r\n            parts.push({\r\n              type: \"html\",\r\n              content: match[0],\r\n            });\r\n            lastIndex = match.index + match[0].length;\r\n          }\r\n\r\n          // 添加最后剩余的文本\r\n          if (lastIndex < result.length) {\r\n            parts.push({\r\n              type: \"text\",\r\n              content: result.substring(lastIndex),\r\n            });\r\n          }\r\n\r\n          // 如果没有HTML标签，直接处理整个字符串\r\n          if (parts.length === 0) {\r\n            parts.push({\r\n              type: \"text\",\r\n              content: result,\r\n            });\r\n          }\r\n\r\n          // 只在文本部分进行关键词替换\r\n          parts.forEach((part) => {\r\n            if (part.type === \"text\") {\r\n              // 使用全局替换\r\n              const regex = new RegExp(escapedKeyword, \"gi\");\r\n              part.content = part.content.replace(regex, replaceString);\r\n            }\r\n          });\r\n\r\n          // 重新组合结果\r\n          result = parts.map((part) => part.content).join(\"\");\r\n        }\r\n      });\r\n\r\n      return result;\r\n    },\r\n\r\n    // 快照生成\r\n    resultEvent() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message.warning(\"请先选择文章\");\r\n      }\r\n      let ids = this.ids;\r\n      let zhuangtai = \"生成\";\r\n      let url = \"\";\r\n      if (ids.length == 1) {\r\n        let row = this.ArticleList.filter((item) => item.id == ids[0]);\r\n        if (row && row.snapshotUrl) zhuangtai = \"查看\";\r\n        url = row.snapshotUrl;\r\n      }\r\n      if (zhuangtai == \"生成\") {\r\n        this.$msgbox({\r\n          title: \"提示\",\r\n          message: \"快照正在生成中，请稍后查看\",\r\n          showCancelButton: false,\r\n          confirmButtonText: \"关闭\",\r\n          beforeClose: (_, __, done) => {\r\n            done();\r\n          },\r\n        });\r\n        API.downLoadExportKe(ids)\r\n          .then((response) => {\r\n            if (response.code != 200) {\r\n              this.$message({\r\n                message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                type: \"error\",\r\n              });\r\n            }\r\n          })\r\n          .catch(() => {});\r\n      } else {\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/server-api/\", \"g\"), \"/\");\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/\", \"g\"), \"/\");\r\n        window.open(window.location.origin + url, \"_blank\");\r\n      }\r\n    },\r\n\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // ai相关\r\n    // dify\r\n    async difyAiChat() {\r\n      if (this.ids.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.ids.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(this.ids.join(\",\"));\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.ids.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.ids.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await difyAiQa(\r\n          articlesContent,\r\n          \"streaming\",\r\n          \"dify.article.apikey\"\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader;\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let pendingBuffer = \"\"; // 用于存储待处理的不完整数据\r\n        let isInThinkTag = false; // 新增：标记是否在think标签内\r\n\r\n        // 将Unicode转义字符(\\uXXXX)转换为实际字符\r\n        const decodeUnicode = (str) => {\r\n          return str.replace(/\\\\u[\\dA-Fa-f]{4}/g, (match) => {\r\n            return String.fromCharCode(parseInt(match.replace(/\\\\u/g, \"\"), 16));\r\n          });\r\n        };\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          try {\r\n            const renderedContent = marked(newContent, this.markdownOptions);\r\n            aiMessage.content = renderedContent;\r\n\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          } catch (error) {\r\n            console.error(\"渲染内容时出错:\", error);\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        while (true) {\r\n          // 检查是否已中断\r\n          if (this.isAborted) {\r\n            throw new Error(\"AbortError\");\r\n          }\r\n\r\n          const { done, value } = await reader.read();\r\n\r\n          if (done) {\r\n            // 处理最后可能剩余的数据\r\n            if (pendingBuffer) {\r\n              try {\r\n                const lastData = JSON.parse(pendingBuffer);\r\n                if (lastData.answer) {\r\n                  // 解码Unicode转义字符\r\n                  const decodedAnswer = decodeUnicode(lastData.answer);\r\n                  buffer += decodedAnswer;\r\n                  updateContent(buffer);\r\n                }\r\n              } catch (e) {\r\n                console.warn(\"处理最后的数据时出错:\", e);\r\n              }\r\n            }\r\n            break;\r\n          }\r\n\r\n          const chunk = decoder.decode(value);\r\n          pendingBuffer += chunk;\r\n\r\n          // 处理完整的数据行\r\n          while (pendingBuffer.includes(\"\\n\")) {\r\n            const newlineIndex = pendingBuffer.indexOf(\"\\n\");\r\n            const line = pendingBuffer.slice(0, newlineIndex).trim();\r\n            pendingBuffer = pendingBuffer.slice(newlineIndex + 1);\r\n\r\n            if (!line || line === \"data:\" || !line.startsWith(\"data:\")) {\r\n              continue;\r\n            }\r\n\r\n            try {\r\n              const data = line.slice(5).trim();\r\n              if (data === \"[DONE]\") {\r\n                continue;\r\n              }\r\n\r\n              const jsonData = JSON.parse(data);\r\n              if (!jsonData.answer) {\r\n                continue;\r\n              }\r\n\r\n              // 跳过特殊字符\r\n              if (jsonData.answer === \"```\" || jsonData.answer === \"markdown\") {\r\n                continue;\r\n              }\r\n\r\n              // 解码Unicode转义字符\r\n              let answer = decodeUnicode(jsonData.answer);\r\n\r\n              // 检查是否包含<think>开始标签\r\n              if (answer.includes(\"<think>\")) {\r\n                isInThinkTag = true;\r\n                continue; // 跳过包含<think>的部分\r\n              }\r\n\r\n              // 检查是否包含</think>结束标签\r\n              if (answer.includes(\"</think>\")) {\r\n                isInThinkTag = false;\r\n                continue; // 跳过包含</think>的部分\r\n              }\r\n\r\n              // 只有不在think标签内的内容才会被添加到buffer中\r\n              if (!isInThinkTag && answer) {\r\n                buffer += answer;\r\n                updateContent(buffer);\r\n              }\r\n            } catch (parseError) {\r\n              console.warn(\"解析数据行时出错:\", {\r\n                line,\r\n                error: parseError.message,\r\n                pendingBuffer,\r\n              });\r\n              continue;\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null;\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // Ollama\r\n    async ollamaAiChat() {\r\n      if (this.ids.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.ids.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(this.ids.join(\",\"));\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.ids.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.ids.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await ollamaAiQa(prompt, true);\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader; // 保存当前的 reader\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let lastUpdateTime = Date.now();\r\n        let isThinkContent = false;\r\n        let tempBuffer = \"\";\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          const currentTime = Date.now();\r\n          // 控制更新频率，避免过于频繁的DOM更新\r\n          if (currentTime - lastUpdateTime >= 50) {\r\n            aiMessage.content = newContent;\r\n            lastUpdateTime = currentTime;\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        const processStream = async () => {\r\n          try {\r\n            while (true) {\r\n              // 检查是否已中断\r\n              if (this.isAborted) {\r\n                throw new Error(\"AbortError\");\r\n              }\r\n\r\n              const { done, value } = await reader.read();\r\n              if (done) {\r\n                if (buffer.length > 0) {\r\n                  updateContent(buffer);\r\n                }\r\n                break;\r\n              }\r\n\r\n              const chunk = decoder.decode(value);\r\n              const lines = chunk.split(\"\\n\").filter((line) => line.trim());\r\n\r\n              for (const line of lines) {\r\n                try {\r\n                  const jsonData = JSON.parse(line);\r\n                  if (!jsonData.response) continue;\r\n\r\n                  const response = jsonData.response;\r\n\r\n                  // 跳过特殊字符\r\n                  if (response === \"```\" || response === \"markdown\") {\r\n                    continue;\r\n                  }\r\n\r\n                  tempBuffer += response;\r\n\r\n                  // 检查是否包含完整的think标签对\r\n                  while (true) {\r\n                    const thinkStartIndex = tempBuffer.indexOf(\"<think>\");\r\n                    const thinkEndIndex = tempBuffer.indexOf(\"</think>\");\r\n\r\n                    if (thinkStartIndex === -1 && thinkEndIndex === -1) {\r\n                      // 没有think标签，直接添加到buffer\r\n                      if (!isThinkContent) {\r\n                        buffer += tempBuffer;\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = \"\";\r\n                      break;\r\n                    } else if (thinkStartIndex !== -1 && thinkEndIndex === -1) {\r\n                      // 只有开始标签，等待结束标签\r\n                      isThinkContent = true;\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkStartIndex);\r\n                      break;\r\n                    } else if (thinkStartIndex === -1 && thinkEndIndex !== -1) {\r\n                      // 只有结束标签，移除之前的内容\r\n                      isThinkContent = false;\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      continue;\r\n                    } else {\r\n                      // 有完整的think标签对\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      isThinkContent = false;\r\n                      continue;\r\n                    }\r\n                  }\r\n                } catch (parseError) {\r\n                  console.warn(\"无效的JSON行，已跳过\", {\r\n                    line,\r\n                    error: parseError.message,\r\n                  });\r\n                }\r\n              }\r\n            }\r\n          } catch (streamError) {\r\n            if (streamError.message === \"AbortError\") {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n            console.error(\"处理流式响应时出错:\", streamError);\r\n            throw streamError;\r\n          }\r\n        };\r\n\r\n        await processStream();\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // deepseek\r\n    async deepseekAiChat() {\r\n      if (this.ids.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      const selectedArticles = this.ArticleList.filter((article) =>\r\n        this.ids.includes(article.id)\r\n      );\r\n      const titles = selectedArticles\r\n        .map((article) => `《${article.cnTitle || article.title}》`)\r\n        .join(\"\\n\");\r\n\r\n      try {\r\n        const articlesResponse = await getListByIds(this.ids.join(\",\"));\r\n        if (!articlesResponse.data || !articlesResponse.data.length) {\r\n          throw new Error(\"Failed to get article contents\");\r\n        }\r\n\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.ids.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息并添加到对话中\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n        this.isThinking = true;\r\n\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.ids.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `\\n\\n**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        const response = await deepseekAiQa(prompt, true);\r\n\r\n        if (response.ok) {\r\n          const reader = response.body.getReader();\r\n          this.currentReader = reader; // 保存当前的 reader\r\n          const decoder = new TextDecoder();\r\n          let buffer = \"\";\r\n          let lastUpdateTime = Date.now();\r\n\r\n          const updateContent = (newContent) => {\r\n            const currentTime = Date.now();\r\n            if (currentTime - lastUpdateTime >= 50) {\r\n              aiMessage.content = newContent;\r\n              lastUpdateTime = currentTime;\r\n              this.$nextTick(() => {\r\n                const chatMessages = this.$refs.chatMessages;\r\n                if (chatMessages) {\r\n                  chatMessages.scrollTop = chatMessages.scrollHeight;\r\n                }\r\n              });\r\n            }\r\n          };\r\n\r\n          while (true) {\r\n            // 检查是否已中断\r\n            if (this.isAborted) {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n\r\n            const { done, value } = await reader.read();\r\n            if (done) {\r\n              if (buffer.length > 0) {\r\n                updateContent(buffer);\r\n              }\r\n              break;\r\n            }\r\n\r\n            const chunk = decoder.decode(value);\r\n            try {\r\n              const lines = chunk.split(\"\\n\");\r\n\r\n              for (const line of lines) {\r\n                if (!line.trim() || !line.startsWith(\"data: \")) continue;\r\n\r\n                const data = line.slice(5);\r\n                if (data === \"[DONE]\") break;\r\n\r\n                try {\r\n                  const jsonData = JSON.parse(data);\r\n                  if (jsonData.choices?.[0]?.delta?.content) {\r\n                    let content = jsonData.choices[0].delta.content;\r\n\r\n                    // 跳过特殊字符\r\n                    if (content === \"```\" || content === \"markdown\") {\r\n                      continue;\r\n                    }\r\n\r\n                    buffer += content;\r\n                    updateContent(buffer);\r\n                  }\r\n                } catch (parseError) {\r\n                  console.error(\"Error parsing JSON:\", parseError);\r\n                }\r\n              }\r\n            } catch (e) {\r\n              console.error(\"Error processing chunk:\", e);\r\n            }\r\n          }\r\n        } else {\r\n          throw new Error(\"Request failed\");\r\n        }\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI Chat Error:\", error);\r\n        this.$message.error(\"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // 关闭AI对话\r\n    closeAiDialog() {\r\n      this.isAborted = true; // 设置中断标志\r\n      if (this.currentReader) {\r\n        this.currentReader.cancel(); // 中断当前的读取\r\n      }\r\n      this.aiDialogVisible = false;\r\n      this.chatMessages = [];\r\n      this.isThinking = false;\r\n      this.isRequesting = false;\r\n      this.currentReader = null;\r\n    },\r\n    articleAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyAiChat();\r\n      } else if (this.aiPlatform === \"ollama\") {\r\n        this.ollamaAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekAiChat();\r\n      }\r\n    },\r\n    chartAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyChartAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekChartAiChat();\r\n      }\r\n    },\r\n    // dify图表看板\r\n    async difyChartAiChat() {\r\n      // 参数检查\r\n      if (this.ids.length === 0) {\r\n        this.$message.warning(\"请先选择要解读的文章\");\r\n        return;\r\n      }\r\n\r\n      if (this.ids.length > 1) {\r\n        this.$message.warning(\"生成Deepseek图表看板只能选择一篇内容\");\r\n        return;\r\n      }\r\n\r\n      // 显示对话框与加载状态\r\n      this.chartDialogVisible = true;\r\n      this.chartLoading = true;\r\n\r\n      // 确保清空上次的内容\r\n      if (this.$refs.chartContent) {\r\n        this.$refs.chartContent.innerHTML = \"\";\r\n      }\r\n\r\n      try {\r\n        // 1. 获取文章内容\r\n        const articleResult = await API.AreaInfo(this.ids[0]);\r\n        if (!articleResult.data || !articleResult.data.content) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 2. 调用AI接口\r\n        const aiResult = await difyAiQa(\r\n          articleResult.data.content,\r\n          \"blocking\",\r\n          \"dify.chart.apikey\"\r\n        );\r\n\r\n        if (!aiResult.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        const aiData = await aiResult.json();\r\n        if (!aiData || !aiData.answer) {\r\n          throw new Error(\"AI返回数据格式错误\");\r\n        }\r\n\r\n        // 3. 处理HTML内容\r\n        let content2 = \"\";\r\n\r\n        try {\r\n          // 尝试解析JSON格式（有些返回可能是JSON字符串）\r\n          const parsedData = JSON.parse(aiData.answer);\r\n          content2 =\r\n            parsedData.answer ||\r\n            parsedData.html ||\r\n            parsedData.content ||\r\n            aiData.answer;\r\n        } catch (e) {\r\n          // 如果不是JSON格式，直接使用原始内容\r\n          content2 = aiData.answer;\r\n        }\r\n\r\n        // 处理思考标记\r\n        const thinkStartIndex = content2.indexOf(\"<think>\");\r\n        const thinkEndIndex = content2.indexOf(\"</think>\");\r\n\r\n        // 提取有效内容\r\n        if (thinkStartIndex !== -1 && thinkEndIndex !== -1) {\r\n          // 如果存在思考标记，只取</think>后面的内容\r\n          content2 = content2.substring(thinkEndIndex + 8).trim();\r\n        }\r\n\r\n        // 清理html标记和其他特殊字符\r\n        content2 = content2\r\n          // 移除html代码块标记\r\n          .replace(/```html\\s*|```\\s*/g, \"\")\r\n          // 移除可能存在的其他html语言标记，如```json等\r\n          .replace(/```[a-zA-Z]*\\s*/g, \"\")\r\n          // 移除多余的空行\r\n          .replace(/\\n\\s*\\n\\s*\\n/g, \"\\n\\n\")\r\n          // 移除行首行尾空白字符\r\n          .trim();\r\n\r\n        // 确保内容非空\r\n        if (!content2 || content2.length < 10) {\r\n          throw new Error(\"返回的图表内容无效\");\r\n        }\r\n\r\n        // 检查HTML结构的完整性，如果不完整则添加必要的标签\r\n        // 这是为了兼容可能不返回完整HTML的情况\r\n        let finalHtml = content2;\r\n\r\n        // 将各种形式的外部CDN引用替换为本地文件\r\n        // 替换双引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换单引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /\\'https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\'/g,\r\n          \"'/chart.js'\"\r\n        );\r\n        finalHtml = finalHtml.replace(\r\n          /\\\"https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\\"/g,\r\n          '\"/chart.js\"'\r\n        );\r\n        // 替换可能带有版本号的引用\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js@\\d+\\.\\d+\\.\\d+/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换其他可能的CDN\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdnjs\\.cloudflare\\.com\\/ajax\\/libs\\/Chart\\.js\\/\\d+\\.\\d+\\.\\d+\\/chart(\\.min)?\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n\r\n        if (!finalHtml.includes(\"<!DOCTYPE\") && !finalHtml.includes(\"<html\")) {\r\n          // 内容只是HTML片段，需要添加完整结构\r\n          finalHtml =\r\n            \"<!DOCTYPE html>\" +\r\n            \"<html>\" +\r\n            \"<head>\" +\r\n            '  <meta charset=\"UTF-8\">' +\r\n            '  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">' +\r\n            '  <script src=\"/chart.js\"><\\/script>' +\r\n            \"</head>\" +\r\n            \"<body>\" +\r\n            \"  \" +\r\n            content2 +\r\n            \"</body>\" +\r\n            \"</html>\";\r\n        } else if (\r\n          !finalHtml.includes(\"<script\") &&\r\n          finalHtml.includes(\"<canvas\")\r\n        ) {\r\n          // 有canvas但没有script标签，可能缺少Chart.js引用\r\n          finalHtml = finalHtml.replace(\r\n            \"<head>\",\r\n            \"<head>\" + '  <script src=\"/chart.js\"><\\/script>'\r\n          );\r\n        }\r\n\r\n        // 4. 创建iframe并渲染\r\n        this.$nextTick(() => {\r\n          if (this.$refs.chartContent) {\r\n            // 清理之前的iframe\r\n            if (this.currentChartIframe) {\r\n              try {\r\n                this.currentChartIframe.onload = null;\r\n                this.currentChartIframe.onerror = null;\r\n              } catch (e) {\r\n                console.error(\"清理之前的iframe失败:\", e);\r\n              }\r\n            }\r\n\r\n            // 创建iframe\r\n            const iframe = document.createElement(\"iframe\");\r\n            iframe.style.width = \"100%\";\r\n            iframe.style.height = \"600px\";\r\n            iframe.style.border = \"none\";\r\n            iframe.style.display = \"block\";\r\n            iframe.style.overflow = \"auto\";\r\n\r\n            // 保存iframe引用\r\n            this.currentChartIframe = iframe;\r\n\r\n            // 清空容器并添加iframe\r\n            this.$refs.chartContent.innerHTML = \"\";\r\n            this.$refs.chartContent.appendChild(iframe);\r\n\r\n            // 在iframe加载完成后重新执行脚本并隐藏加载状态\r\n            iframe.onload = () => {\r\n              try {\r\n                // 检查是否需要加载本地Chart.js\r\n                if (\r\n                  !iframe.contentWindow.Chart &&\r\n                  !iframe.contentWindow.document.querySelector(\r\n                    'script[src*=\"chart.js\" i]'\r\n                  )\r\n                ) {\r\n                  // 如果iframe内没有加载Chart.js，手动添加本地Chart.js\r\n                  const chartScript =\r\n                    iframe.contentWindow.document.createElement(\"script\");\r\n                  chartScript.src = \"/chart.js\";\r\n                  iframe.contentWindow.document.head.appendChild(chartScript);\r\n\r\n                  // 等待Chart.js加载完成后再执行后续脚本\r\n                  chartScript.onload = () => {\r\n                    this.executeIframeScripts(iframe);\r\n                  };\r\n\r\n                  // 如果脚本加载失败，也需要隐藏加载动画\r\n                  chartScript.onerror = () => {\r\n                    console.error(\"加载本地Chart.js失败\");\r\n                    this.chartLoading = false;\r\n                  };\r\n                } else {\r\n                  // 直接执行内联脚本\r\n                  this.executeIframeScripts(iframe);\r\n                }\r\n              } catch (e) {\r\n                console.error(\"脚本执行出错:\", e);\r\n                this.chartLoading = false;\r\n              }\r\n            };\r\n\r\n            // 添加错误处理\r\n            iframe.onerror = () => {\r\n              console.error(\"iframe加载失败\");\r\n              this.chartLoading = false;\r\n            };\r\n\r\n            // 写入内容\r\n            const doc = iframe.contentWindow.document;\r\n            doc.open();\r\n            doc.write(finalHtml);\r\n            doc.close();\r\n          }\r\n        });\r\n      } catch (error) {\r\n        console.error(\"生成图表失败:\", error);\r\n        this.$message.error(error.message || \"生成图表失败，请稍后重试\");\r\n        this.closeChartDialog();\r\n      }\r\n    },\r\n    // deepseek图表看板\r\n    async deepseekChartAiChat() {\r\n      // 参数检查\r\n      if (this.ids.length === 0) {\r\n        this.$message.warning(\"请先选择要解读的文章\");\r\n        return;\r\n      }\r\n\r\n      if (this.ids.length > 1) {\r\n        this.$message.warning(\"生成Deepseek图表看板只能选择一篇内容\");\r\n        return;\r\n      }\r\n\r\n      // 显示对话框与加载状态\r\n      this.chartDialogVisible = true;\r\n      this.chartLoading = true;\r\n\r\n      // 确保清空上次的内容\r\n      if (this.$refs.chartContent) {\r\n        this.$refs.chartContent.innerHTML = \"\";\r\n      }\r\n\r\n      try {\r\n        // 1. 获取文章内容\r\n        const articleResult = await API.AreaInfo(this.ids[0]);\r\n        if (!articleResult.data || !articleResult.data.content) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        const prompt = this.chartPrompt + `\\n\\n${articleResult.data.content}`;\r\n\r\n        // 2. 调用AI接口\r\n        const aiResult = await deepseekAiQa(prompt, false);\r\n\r\n        if (!aiResult.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        const aiData = await aiResult.json();\r\n        if (!aiData || !aiData.choices) {\r\n          throw new Error(\"AI返回数据格式错误\");\r\n        }\r\n\r\n        // 3. 处理HTML内容\r\n        let content2 = aiData.choices[0].message.content;\r\n\r\n        // 清理html标记和其他特殊字符\r\n        content2 = content2\r\n          // 移除html代码块标记\r\n          .replace(/```html\\s*|```\\s*/g, \"\")\r\n          // 移除可能存在的其他html语言标记，如```json等\r\n          .replace(/```[a-zA-Z]*\\s*/g, \"\")\r\n          // 移除多余的空行\r\n          .replace(/\\n\\s*\\n\\s*\\n/g, \"\\n\\n\")\r\n          // 移除行首行尾空白字符\r\n          .trim();\r\n\r\n        // 确保内容非空\r\n        if (!content2 || content2.length < 10) {\r\n          throw new Error(\"返回的图表内容无效\");\r\n        }\r\n\r\n        // 检查HTML结构的完整性，如果不完整则添加必要的标签\r\n        // 这是为了兼容可能不返回完整HTML的情况\r\n        let finalHtml = content2;\r\n\r\n        // 将各种形式的外部CDN引用替换为本地文件\r\n        // 替换双引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换单引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /\\'https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\'/g,\r\n          \"'/chart.js'\"\r\n        );\r\n        finalHtml = finalHtml.replace(\r\n          /\\\"https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\\"/g,\r\n          '\"/chart.js\"'\r\n        );\r\n        // 替换可能带有版本号的引用\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js@\\d+\\.\\d+\\.\\d+/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换其他可能的CDN\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdnjs\\.cloudflare\\.com\\/ajax\\/libs\\/Chart\\.js\\/\\d+\\.\\d+\\.\\d+\\/chart(\\.min)?\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n\r\n        if (!finalHtml.includes(\"<!DOCTYPE\") && !finalHtml.includes(\"<html\")) {\r\n          // 内容只是HTML片段，需要添加完整结构\r\n          finalHtml =\r\n            \"<!DOCTYPE html>\" +\r\n            \"<html>\" +\r\n            \"<head>\" +\r\n            '  <meta charset=\"UTF-8\">' +\r\n            '  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">' +\r\n            '  <script src=\"/chart.js\"><\\/script>' +\r\n            \"</head>\" +\r\n            \"<body>\" +\r\n            \"  \" +\r\n            content2 +\r\n            \"</body>\" +\r\n            \"</html>\";\r\n        } else if (\r\n          !finalHtml.includes(\"<script\") &&\r\n          finalHtml.includes(\"<canvas\")\r\n        ) {\r\n          // 有canvas但没有script标签，可能缺少Chart.js引用\r\n          finalHtml = finalHtml.replace(\r\n            \"<head>\",\r\n            \"<head>\" + '  <script src=\"/chart.js\"><\\/script>'\r\n          );\r\n        }\r\n\r\n        // 4. 创建iframe并渲染\r\n        this.$nextTick(() => {\r\n          if (this.$refs.chartContent) {\r\n            // 清理之前的iframe\r\n            if (this.currentChartIframe) {\r\n              try {\r\n                this.currentChartIframe.onload = null;\r\n                this.currentChartIframe.onerror = null;\r\n              } catch (e) {\r\n                console.error(\"清理之前的iframe失败:\", e);\r\n              }\r\n            }\r\n\r\n            // 创建iframe\r\n            const iframe = document.createElement(\"iframe\");\r\n            iframe.style.width = \"100%\";\r\n            iframe.style.height = \"600px\";\r\n            iframe.style.border = \"none\";\r\n            iframe.style.display = \"block\";\r\n            iframe.style.overflow = \"auto\";\r\n\r\n            // 保存iframe引用\r\n            this.currentChartIframe = iframe;\r\n\r\n            // 清空容器并添加iframe\r\n            this.$refs.chartContent.innerHTML = \"\";\r\n            this.$refs.chartContent.appendChild(iframe);\r\n\r\n            // 在iframe加载完成后重新执行脚本并隐藏加载状态\r\n            iframe.onload = () => {\r\n              try {\r\n                // 检查是否需要加载本地Chart.js\r\n                if (\r\n                  !iframe.contentWindow.Chart &&\r\n                  !iframe.contentWindow.document.querySelector(\r\n                    'script[src*=\"chart.js\" i]'\r\n                  )\r\n                ) {\r\n                  // 如果iframe内没有加载Chart.js，手动添加本地Chart.js\r\n                  const chartScript =\r\n                    iframe.contentWindow.document.createElement(\"script\");\r\n                  chartScript.src = \"/chart.js\";\r\n                  iframe.contentWindow.document.head.appendChild(chartScript);\r\n\r\n                  // 等待Chart.js加载完成后再执行后续脚本\r\n                  chartScript.onload = () => {\r\n                    this.executeIframeScripts(iframe);\r\n                  };\r\n\r\n                  // 如果脚本加载失败，也需要隐藏加载动画\r\n                  chartScript.onerror = () => {\r\n                    console.error(\"加载本地Chart.js失败\");\r\n                    this.chartLoading = false;\r\n                  };\r\n                } else {\r\n                  // 直接执行内联脚本\r\n                  this.executeIframeScripts(iframe);\r\n                }\r\n              } catch (e) {\r\n                console.error(\"脚本执行出错:\", e);\r\n                this.chartLoading = false;\r\n              }\r\n            };\r\n\r\n            // 添加错误处理\r\n            iframe.onerror = () => {\r\n              console.error(\"iframe加载失败\");\r\n              this.chartLoading = false;\r\n            };\r\n\r\n            // 写入内容\r\n            const doc = iframe.contentWindow.document;\r\n            doc.open();\r\n            doc.write(finalHtml);\r\n            doc.close();\r\n          }\r\n        });\r\n      } catch (error) {\r\n        console.error(\"生成图表失败:\", error);\r\n        this.$message.error(error.message || \"生成图表失败，请稍后重试\");\r\n        this.closeChartDialog();\r\n      }\r\n    },\r\n    // 关闭图表对话框\r\n    closeChartDialog() {\r\n      this.isAborted = true;\r\n      this.chartDialogVisible = false;\r\n      this.chartHtml = \"\";\r\n      this.chartLoading = false;\r\n      this.isRequesting = false;\r\n\r\n      // 清理Chart实例\r\n      if (this.currentChartIframe && this.currentChartIframe.contentWindow) {\r\n        try {\r\n          // 尝试销毁所有Chart实例\r\n          if (this.currentChartIframe.contentWindow.Chart) {\r\n            const instances =\r\n              this.currentChartIframe.contentWindow.Chart.instances;\r\n            if (instances) {\r\n              Object.values(instances).forEach((instance) => {\r\n                if (instance && typeof instance.destroy === \"function\") {\r\n                  instance.destroy();\r\n                }\r\n              });\r\n            }\r\n          }\r\n        } catch (e) {\r\n          console.error(\"清理Chart实例失败:\", e);\r\n        }\r\n      }\r\n\r\n      // 清空图表容器内容\r\n      if (this.$refs.chartContent) {\r\n        this.$refs.chartContent.innerHTML = \"\";\r\n      }\r\n\r\n      // 清理iframe引用\r\n      if (this.currentChartIframe) {\r\n        try {\r\n          this.currentChartIframe.onload = null;\r\n          this.currentChartIframe.onerror = null;\r\n          this.currentChartIframe = null;\r\n        } catch (e) {\r\n          console.error(\"清理iframe失败:\", e);\r\n        }\r\n      }\r\n    },\r\n    // 执行iframe内的所有内联脚本\r\n    executeIframeScripts(iframe) {\r\n      // 简化后的方法，不再尝试手动执行脚本\r\n      console.log(\"图表iframe已加载，等待自然渲染...\");\r\n\r\n      // 确保所有图表都有机会渲染后再隐藏loading\r\n      setTimeout(() => {\r\n        this.chartLoading = false;\r\n      }, 800);\r\n    },\r\n\r\n    // 文章去重方法\r\n    deduplicateArticles(articles) {\r\n      if (!articles || articles.length === 0) {\r\n        return articles;\r\n      }\r\n\r\n      const titleMap = new Map();\r\n      const result = [];\r\n\r\n      // 统计相同标题的文章数量\r\n      articles.forEach((article) => {\r\n        // 去除HTML标签和所有空格来比较标题\r\n        const cleanTitle = article.title\r\n          ? article.title.replace(/<[^>]*>/g, \"\").replace(/\\s+/g, \"\")\r\n          : \"\";\r\n\r\n        if (titleMap.has(cleanTitle)) {\r\n          titleMap.get(cleanTitle).count++;\r\n        } else {\r\n          titleMap.set(cleanTitle, {\r\n            article: { ...article },\r\n            count: 1,\r\n            originalTitle: article.title, // 保存原始标题（可能包含HTML标签）\r\n          });\r\n        }\r\n      });\r\n\r\n      // 生成去重后的文章列表\r\n      titleMap.forEach(({ article, count, originalTitle }) => {\r\n        if (count > 1) {\r\n          // 如果有重复，在标题后面加上数量标记\r\n          // 使用原始标题（保持HTML格式）\r\n          article.title = `${originalTitle || \"\"}（${count}）`;\r\n        }\r\n        result.push(article);\r\n      });\r\n\r\n      return result;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.input_Fixed {\r\n  width: 100%;\r\n}\r\n\r\n.treeBox {\r\n  // margin-top:70px;\r\n  width: 100%;\r\n  height: calc(100vh - 178px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.tree-pagination {\r\n  padding: 10px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n\r\n  ::v-deep .el-pagination {\r\n    .el-pagination__sizes {\r\n      margin-top: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n.rightMain {\r\n  height: calc(100vh - 60px);\r\n  overflow: hidden;\r\n\r\n  .TopBtnGroup {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n    // background: #dbdbd8;\r\n    // margin-bottom: 20px;\r\n    height: 60px;\r\n    box-shadow: 0 0px 10px 0px #cecdcd;\r\n    border-bottom: solid 1px #e2e2e2;\r\n\r\n    .TopBtnGroup_left {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n\r\n    .toolTitle {\r\n      margin: 0 10px;\r\n      display: flex;\r\n      align-items: center;\r\n      cursor: pointer;\r\n\r\n      .deepseek-text {\r\n        color: #5589f5; // 使用与图标相同的颜色\r\n        margin-left: 4px;\r\n        font-size: 14px;\r\n        line-height: 24px;\r\n      }\r\n\r\n      &:nth-of-type(1) {\r\n        margin-left: 30px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ArticlMain {\r\n    padding: 0 0 0 30px;\r\n    color: #3f3f3f;\r\n    font-size: 14px;\r\n    line-height: 24px;\r\n  }\r\n\r\n  .ArticlMain > span:hover {\r\n    color: #1889f3;\r\n    border-bottom: solid 1px #0798f8;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n::v-deep .drawer_Title {\r\n  text-overflow: ellipsis;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n}\r\n\r\n::v-deep .drawer_Style {\r\n  z-index: 2;\r\n  margin: 0 15px 0 15px;\r\n  width: 661px;\r\n  height: 80vh;\r\n\r\n  .title {\r\n    font-size: 16px;\r\n    font-weight: 500px;\r\n    text-align: center;\r\n  }\r\n\r\n  .source {\r\n    color: #0798f8;\r\n    text-align: center;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .time {\r\n    font-size: 14px;\r\n    text-align: center;\r\n    margin-left: 10px;\r\n    color: #9b9b9b;\r\n  }\r\n}\r\n\r\n::v-deep .el-icon-document:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-icon-document-add:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-icon-chat-dot-round:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-icon-pie-chart:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-table td.el-table__cell div {\r\n  padding-left: 10px;\r\n}\r\n\r\n::v-deep .el-table-column--selection .cell {\r\n  padding-right: 0px;\r\n  padding-left: 14px;\r\n  margin-left: 5px;\r\n}\r\n\r\n::v-deep .el-form-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.treeMain {\r\n  position: relative;\r\n}\r\n\r\n.treeQuery {\r\n  ::v-deep .el-input--mini .el-input__inner {\r\n    height: 24px;\r\n    line-height: 24px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  ::v-deep .el-input__suffix {\r\n    // height: 20px;\r\n    right: -2px;\r\n    // top: 5px;\r\n  }\r\n}\r\n\r\n.keyword {\r\n  width: 100%;\r\n  position: relative;\r\n  margin-bottom: 10px;\r\n\r\n  .history {\r\n    width: 430px;\r\n    position: absolute;\r\n    background: #fff;\r\n    z-index: 9999;\r\n    left: 0;\r\n    border: 1px solid rgb(221, 219, 219);\r\n\r\n    .historyItem {\r\n      padding-left: 20px;\r\n\r\n      .historyText {\r\n        width: 450px;\r\n        height: 34px;\r\n        line-height: 34px;\r\n      }\r\n\r\n      &:nth-last-of-type(1) {\r\n        padding-left: 0;\r\n\r\n        ::v-deep .el-button--text {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.keyword-tip {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-left: 90px;\r\n  line-height: 1;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.history {\r\n  width: 530px;\r\n\r\n  .historyItem {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n    overflow: hidden;\r\n\r\n    .historyText {\r\n      width: 350px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-table--medium .el-table__cell {\r\n  padding: 10px 0;\r\n}\r\n\r\n.article_title {\r\n  margin-left: 10px;\r\n  font-size: 15px;\r\n}\r\n\r\n.article_title:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n  cursor: pointer;\r\n}\r\n\r\n// ai相关\r\n.ai-chat-container {\r\n  height: 550px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f7fa;\r\n\r\n  .chat-messages {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 24px;\r\n\r\n    .message {\r\n      margin-bottom: 28px;\r\n      display: flex;\r\n      align-items: flex-start;\r\n\r\n      .avatar {\r\n        width: 42px;\r\n        height: 42px;\r\n        border-radius: 50%;\r\n        overflow: hidden;\r\n        flex-shrink: 0;\r\n        border: 2px solid #fff;\r\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n        background-color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: contain;\r\n          background-color: #fff;\r\n        }\r\n      }\r\n\r\n      .message-wrapper {\r\n        margin: 0 16px;\r\n        max-width: calc(100% - 100px);\r\n      }\r\n\r\n      .message-content {\r\n        padding: 12px 16px;\r\n        border-radius: 12px;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n        position: relative;\r\n\r\n        &::before {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 14px;\r\n          width: 0;\r\n          height: 0;\r\n          border: 6px solid transparent;\r\n        }\r\n      }\r\n    }\r\n\r\n    .user-message {\r\n      flex-direction: row-reverse;\r\n\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #e6f3ff;\r\n        color: #2d2d2d;\r\n        line-height: 1.8em;\r\n        font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n          Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n          Arial, sans-serif;\r\n\r\n        &::before {\r\n          right: -12px;\r\n          border-left-color: #e6f3ff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .ai-message {\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #fff;\r\n        color: #2d2d2d;\r\n\r\n        &::before {\r\n          left: -12px;\r\n          border-right-color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .thinking-animation {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    padding: 12px 16px;\r\n    min-height: 45px;\r\n    background: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n    position: relative;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 14px;\r\n      left: -12px;\r\n      width: 0;\r\n      height: 0;\r\n      border: 6px solid transparent;\r\n      border-right-color: #fff;\r\n    }\r\n\r\n    span {\r\n      display: inline-block;\r\n      width: 6px;\r\n      height: 6px;\r\n      margin: 0 3px;\r\n      background-color: #409eff;\r\n      border-radius: 50%;\r\n      opacity: 0.7;\r\n      animation: thinking 1.4s infinite ease-in-out both;\r\n\r\n      &:nth-child(1) {\r\n        animation-delay: -0.32s;\r\n      }\r\n\r\n      &:nth-child(2) {\r\n        animation-delay: -0.16s;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message-content {\r\n    min-height: 45px;\r\n    white-space: pre-wrap;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n\r\n    ::v-deep {\r\n      h1,\r\n      h2,\r\n      h3,\r\n      h4,\r\n      h5,\r\n      h6 {\r\n        margin: 0.05em 0 0.02em 0;\r\n        font-weight: 600;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      h1 {\r\n        font-size: 1.6em;\r\n        margin-top: 0;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h2 {\r\n        font-size: 1.4em;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 1.2em;\r\n      }\r\n\r\n      p {\r\n        margin: 0;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      strong {\r\n        font-weight: 600;\r\n        color: #1a1a1a;\r\n      }\r\n\r\n      em {\r\n        font-style: italic;\r\n        color: #2c5282;\r\n      }\r\n\r\n      ul,\r\n      ol {\r\n        margin: 0;\r\n        padding-left: 1em;\r\n        display: flex !important;\r\n        flex-direction: column !important;\r\n        // row-gap: 20px !important;\r\n\r\n        li {\r\n          margin: 0;\r\n          line-height: 1.8em;\r\n          color: #2d3748;\r\n\r\n          // 如果li中包含p标签，则设置行高为1\r\n          &:has(p) {\r\n            line-height: 1;\r\n          }\r\n\r\n          // 如果li中没有p标签，保持默认行高1.8em（已在上面设置）\r\n\r\n          p {\r\n            margin: 0;\r\n            line-height: 1.8em;\r\n          }\r\n        }\r\n      }\r\n\r\n      blockquote {\r\n        margin: 0.05em 0;\r\n        padding: 0.05em 0.4em;\r\n        color: #2c5282;\r\n        background: #ebf8ff;\r\n        border-left: 4px solid #4299e1;\r\n\r\n        p {\r\n          margin: 0.02em 0;\r\n          line-height: 1.8em;\r\n        }\r\n\r\n        > :first-child {\r\n          margin-top: 0;\r\n        }\r\n\r\n        > :last-child {\r\n          margin-bottom: 0;\r\n        }\r\n      }\r\n\r\n      code {\r\n        padding: 0.05em 0.1em;\r\n        margin: 0;\r\n        font-size: 0.9em;\r\n        background: #edf2f7;\r\n        border-radius: 3px;\r\n        color: #2d3748;\r\n      }\r\n\r\n      hr {\r\n        height: 1px;\r\n        margin: 0.1em 0;\r\n        border: none;\r\n        background-color: #e2e8f0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.chat-messages {\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background-color: rgba(192, 196, 204, 0.5);\r\n    border-radius: 3px;\r\n\r\n    &:hover {\r\n      background-color: rgba(192, 196, 204, 0.8);\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes thinking {\r\n  0%,\r\n  80%,\r\n  100% {\r\n    transform: scale(0);\r\n  }\r\n  40% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n// 修改弹窗样式\r\n::v-deep .ai-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 15px 20px;\r\n    background: #fff;\r\n    border-top: 1px solid #e4e7ed;\r\n\r\n    .el-button {\r\n      padding: 9px 20px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n\r\n.chart-container {\r\n  min-height: 600px;\r\n  width: 100%;\r\n  overflow: auto;\r\n  padding: 0;\r\n  position: relative;\r\n\r\n  .chart-content {\r\n    width: 100%;\r\n    height: 600px;\r\n    overflow: auto;\r\n    display: block;\r\n  }\r\n}\r\n\r\n::v-deep .chart-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .el-dialog__header {\r\n    // padding: 15px;\r\n    border-bottom: 1px solid #e4e7ed;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 10px 15px;\r\n    border-top: 1px solid #e4e7ed;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 0 !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2gBA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAOA,IAAAE,SAAA,GAAAF,OAAA;AAEA,IAAAG,eAAA,GAAAH,OAAA;AAOA,IAAAI,WAAA,GAAAJ,OAAA;AACAA,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,OAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,GAAA,GAAAP,OAAA;AACA,IAAAQ,OAAA,GAAAR,OAAA;AACA,IAAAS,OAAA,GAAAT,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAU,UAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,IAAA,EAAAA,gBAAA;IAAAC,SAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,YAAA;MAAA;MACAC,WAAA;QACAC,EAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,IAAA;QACAC,UAAA;QACAC,QAAA;QACAC,YAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,KAAA;MACAC,gBAAA;MAAA;MACAC,UAAA;MAAA;MACAC,SAAA;MAAA;MACAC,WAAA;MAAA;MACAC,OAAA;MAAA;MACAC,GAAA;MAAA;MACA;MACAC,QAAA;MACAC,aAAA;MAAA;MACAC,aAAA;MAAA;MACAC,QAAA;MAAA;MACAC,QAAA;MAAA;MACAC,SAAA;MAAA;MACAC,QAAA;MAAA;MACAC,eAAA;MAAA;MACAC,WAAA;MACAC,WAAA;MACAC,cAAA;MACAC,cAAA;MACAC,cAAA;MACAC,YAAA;QACA9B,OAAA;QACAC,QAAA;MACA;MACA8B,MAAA;MACAC,YAAA;MACAC,WAAA;MACA;MACAC,eAAA;MACAC,YAAA;MACAC,SAAA;MACA;MACAC,uBAAA;MACA;MACAC,mBAAA;MACA;MACAC,kBAAA;MACA;MACAC,UAAA;MACA;MACAC,aAAA;MACA;MACAC,WAAA;MACA;MACAC,gBAAA;MACA;MACAC,iBAAA;MACA;MACAC,eAAA;MACAC,YAAA;MACAC,UAAA;MACAC,UAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,eAAA;QACAC,GAAA;QACAC,MAAA;QACAC,SAAA;QACAC,MAAA;QACAC,YAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,KAAA;MACA;MACAC,YAAA;MAAA;MACAC,SAAA;MAAA;MACAC,aAAA;MAAA;MACAC,UAAA;MACAC,eAAA;MACAC,aAAA;MACAC,kBAAA;MACAC,SAAA;MACAC,YAAA;MACAC,kBAAA;MAAA;MACAC,UAAA;QACAC,OAAA;QACAC,KAAA;MACA;MACAC,WAAA;MACAC,aAAA;IACA;EACA;EACAC,KAAA;IACA;IACA;MACAC,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAA1C,uBAAA,IAAAyC,MAAA,KAAAC,MAAA;QACA,KAAAC,uBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAA1C,uBAAA,IAAAyC,MAAA,KAAAC,MAAA;QACA,KAAAC,uBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAA1C,uBAAA,IAAAyC,MAAA,KAAAC,MAAA;QACA,KAAAC,uBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QAAA,IAAAE,KAAA;QACA,UAAA5C,uBAAA,IAAAyC,MAAA,KAAAC,MAAA;;QAEA;QACA,KAAAxD,QAAA;QACA,KAAAC,eAAA;QAEA,IAAAsD,MAAA;UACA;UACA,IAAAI,sBAAA;YAAAC,QAAA,EAAAL,MAAA;YAAA9E,OAAA;YAAAC,QAAA;UAAA,GACAmF,IAAA,WAAAC,GAAA;YACAJ,KAAA,CAAA5D,QAAA,GAAAgE,GAAA,CAAA1F,IAAA;YACAsF,KAAA,CAAAK,wBAAA;YACA;UACA,GACAC,KAAA,WAAAC,KAAA;YACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;YACAP,KAAA,CAAAS,QAAA,CAAAF,KAAA;UACA;QACA;UACA,KAAAR,uBAAA;QACA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,IACA,MAAA1C,uBAAA,IACAsD,IAAA,CAAAC,SAAA,CAAAd,MAAA,MAAAa,IAAA,CAAAC,SAAA,CAAAb,MAAA,GAEA;QACA,KAAAC,uBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAA1C,uBAAA,IAAAyC,MAAA,KAAAC,MAAA;QACA,KAAAc,sBAAA;QACA,KAAAC,gBAAA;MACA;IACA;IACA5E,aAAA,WAAAA,cAAA6E,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,GAAA;QACAE,cAAA,CAAAC,WAAA;UAAAC,UAAA;QAAA,GAAAf,IAAA,WAAAzF,IAAA;UACA,IAAAA,IAAA,CAAAyG,IAAA;YACAJ,MAAA,CAAA7E,aAAA,GAAAxB,IAAA,CAAAA,IAAA;UACA;YACAqG,MAAA,CAAAN,QAAA;cAAAW,OAAA;cAAAC,IAAA;YAAA;YACAN,MAAA,CAAAO,WAAA;UACA;QACA;MACA;IACA;EACA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MACA,IAAAC,gBAAA,aACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,WAAAC,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACA,IAAAC,oBAAA,qBAAAnC,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA,CAAAe,IAAA;gBACAS,MAAA,CAAA7C,UAAA,GAAAqB,GAAA,CAAAmC,GAAA;cACA;YACA;YACA,IAAAD,oBAAA,6BAAAnC,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA,CAAAe,IAAA;gBACAS,MAAA,CAAA5C,eAAA,GAAAoB,GAAA,CAAAmC,GAAA;cACA;YACA;YACA,IAAAD,oBAAA,2BAAAnC,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA,CAAAe,IAAA;gBACAS,MAAA,CAAAnC,WAAA,GAAAW,GAAA,CAAAmC,GAAA;cACA;YACA;YACA;YACAX,MAAA,CAAA7D,UAAA,GAAA6D,MAAA,CAAAY,MAAA,CAAAC,OAAA,CAAAC,MAAA;YAAAP,QAAA,CAAAC,IAAA;YAEA;YACAO,OAAA,CAAAC,GAAA,EACAhB,MAAA,CAAAiB,iBAAA,IACA,IAAA5C,sBAAA;cAAAC,QAAA;cAAAnF,OAAA;cAAAC,QAAA;YAAA,GAAAmF,IAAA,WAAAC,GAAA;cACAwB,MAAA,CAAAvF,SAAA,GAAA+D,GAAA,CAAA1F,IAAA,CAAAoI,MAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAA7C,QAAA;cAAA;YACA,GACA;;YAEA;YAAAiC,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAoB,cAAA;UAAA;YAEA,IAAApB,MAAA,CAAAqB,KAAA,CAAAC,QAAA;cACAtB,MAAA,CAAA5E,WAAA;YACA;;YAEA;YACA4E,MAAA,CAAAxE,uBAAA;YAAA+E,QAAA,CAAAE,IAAA;YAAA;UAAA;YAAAF,QAAA,CAAAC,IAAA;YAAAD,QAAA,CAAAgB,EAAA,GAAAhB,QAAA;YAEA3B,OAAA,CAAAD,KAAA,aAAA4B,QAAA,CAAAgB,EAAA;YACAvB,MAAA,CAAAnB,QAAA,CAAAF,KAAA;UAAA;UAAA;YAAA,OAAA4B,QAAA,CAAAiB,IAAA;QAAA;MAAA,GAAApB,OAAA;IAAA;EAEA;EAEAqB,OAAA,WAAAA,QAAA;EACAC,OAAA;IACA;IACAC,cAAA,WAAAA,eAAAR,IAAA;MACA,IAAAS,SAAA,GAAAT,IAAA,CAAAS,SAAA;MACA,IAAAC,OAAA,GAAAV,IAAA,CAAAU,OAAA;MAEA,IAAAC,kBAAA,GAAAF,SAAA,GACAA,SAAA,CACAG,OAAA,6DACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAC,IAAA,KACA;MAEA,IAAAC,gBAAA,GAAAJ,OAAA,GACAA,OAAA,CACAE,OAAA,6DACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAC,IAAA,KACA;MAEA,OAAAF,kBAAA,IAAAG,gBAAA;IACA;IACA;IACAb,cAAA,WAAAA,eAAA;MAAA,IAAAc,MAAA;MAAA,WAAAjC,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAgC,SAAA;QAAA,WAAAjC,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAA+B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7B,IAAA,GAAA6B,SAAA,CAAA5B,IAAA;YAAA;cAAA4B,SAAA,CAAA7B,IAAA;cAEA;cACA;cACA0B,MAAA,CAAAjD,gBAAA;cACA;cAAAoD,SAAA,CAAA5B,IAAA;cAAA,OACAyB,MAAA,CAAAI,aAAA;YAAA;cAAAD,SAAA,CAAA5B,IAAA;cAAA,OAEAyB,MAAA,CAAAK,SAAA;YAAA;cAAAF,SAAA,CAAA5B,IAAA;cAAA;YAAA;cAAA4B,SAAA,CAAA7B,IAAA;cAAA6B,SAAA,CAAAd,EAAA,GAAAc,SAAA;cAyBAzD,OAAA,CAAAD,KAAA,aAAA0D,SAAA,CAAAd,EAAA;cACAW,MAAA,CAAArD,QAAA,CAAAF,KAAA;cACA;YAAA;YAAA;cAAA,OAAA0D,SAAA,CAAAb,IAAA;UAAA;QAAA,GAAAW,QAAA;MAAA;IAEA;IAEA;IACAhE,uBAAA,WAAAA,wBAAA;MACA,KAAAvC,aAAA;;MAEA;MACA;;MAEA;MACA,KAAA3C,WAAA,CAAAE,OAAA;MACA,KAAAkC,eAAA;MACA,KAAApC,WAAA,CAAAW,QAAA;;MAEA;MACA,KAAAoF,sBAAA;;MAEA;MACA,KAAAwD,gBAAA;IACA;IAEA;IACAA,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAxC,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAuC,SAAA;QAAA,IAAAC,SAAA;QAAA,WAAAzC,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAuC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArC,IAAA,GAAAqC,SAAA,CAAApC,IAAA;YAAA;cAAAoC,SAAA,CAAArC,IAAA;cAEA;cACAmC,SAAA,OAAAG,mBAAA,CAAAjD,OAAA,EAAA4C,MAAA,CAAA1G,iBAAA,GAEA;cACA,IAAA4G,SAAA,IAAAA,SAAA,CAAAI,MAAA;gBACAN,MAAA,CAAAzI,SAAA,OAAA8I,mBAAA,CAAAjD,OAAA,EAAA8C,SAAA;cACA;gBACA;gBACAF,MAAA,CAAAzI,SAAA;cACA;;cAEA;cAAA6I,SAAA,CAAApC,IAAA;cAAA,OACAM,OAAA,CAAAC,GAAA,EACAyB,MAAA,CAAAH,aAAA,IACAG,MAAA,CAAAxD,gBAAA;cAAA,CACA;YAAA;cAEA;cACAwD,MAAA,CAAA1G,iBAAA,GAAA4G,SAAA;;cAEA;cACA,IAAAF,MAAA,CAAA1G,iBAAA,IAAA0G,MAAA,CAAA1G,iBAAA,CAAAgH,MAAA;gBACAN,MAAA,CAAAO,4BAAA;cACA;;cAEA;cACAP,MAAA,CAAA7G,aAAA;cACAqH,UAAA;gBACAR,MAAA,CAAA5G,WAAA;cACA;cAAAgH,SAAA,CAAApC,IAAA;cAAA;YAAA;cAAAoC,SAAA,CAAArC,IAAA;cAAAqC,SAAA,CAAAtB,EAAA,GAAAsB,SAAA;cAEAjE,OAAA,CAAAD,KAAA,gBAAAkE,SAAA,CAAAtB,EAAA;cACAkB,MAAA,CAAA5D,QAAA,CAAAF,KAAA;cACA;cACA8D,MAAA,CAAA7G,aAAA;cACAqH,UAAA;gBACAR,MAAA,CAAA5G,WAAA;cACA;YAAA;YAAA;cAAA,OAAAgH,SAAA,CAAArB,IAAA;UAAA;QAAA,GAAAkB,QAAA;MAAA;IAEA;IAEA;IAEA;IACAM,4BAAA,WAAAA,6BAAA;MAAA,IAAAE,MAAA;MACA,UAAAnH,iBAAA,SAAAA,iBAAA,CAAAgH,MAAA;QACA;MACA;;MAEA;MACA,IAAAI,YAAA;MACA,KAAApH,iBAAA,CAAAqH,OAAA,WAAAC,SAAA;QACA,IAAAC,SAAA,GAAAJ,MAAA,CAAApJ,gBAAA,CAAAyJ,IAAA,CACA,UAAAC,QAAA;UAAA,OAAAA,QAAA,CAAAC,QAAA,KAAAJ,SAAA,CAAAI,QAAA;QAAA,CACA;QACA,IAAAH,SAAA;UACAH,YAAA,CAAAO,IAAA,CAAAJ,SAAA;QACA;MACA;MAEA,IAAAH,YAAA,CAAAJ,MAAA;QACA;QACA,KAAA/I,SAAA,GAAAmJ,YAAA;QACA;QACA,KAAAZ,SAAA;UACA,IAAAW,MAAA,CAAAS,KAAA,CAAAC,SAAA;YACAV,MAAA,CAAAS,KAAA,CAAAC,SAAA,CAAAC,wBAAA,CAAAV,YAAA;UACA;QACA;MACA;QACA;QACA,KAAAnJ,SAAA;MACA;IACA;IAEA;IAEA;IACA8J,iCAAA,WAAAA,kCAAA;MAAA,IAAAC,MAAA;MAAA,WAAA9D,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAA6D,SAAA;QAAA,WAAA9D,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAA4D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1D,IAAA,GAAA0D,SAAA,CAAAzD,IAAA;YAAA;cAAAyD,SAAA,CAAA1D,IAAA;cAEA;cACA,IAAAuD,MAAA,CAAAhI,iBAAA,IAAAgI,MAAA,CAAAhI,iBAAA,CAAAgH,MAAA;gBACAgB,MAAA,CAAA/J,SAAA,OAAA8I,mBAAA,CAAAjD,OAAA,EAAAkE,MAAA,CAAAhI,iBAAA;cACA;gBACAgI,MAAA,CAAA/J,SAAA;cACA;;cAEA;cAAAkK,SAAA,CAAAzD,IAAA;cAAA,OACAsD,MAAA,CAAAzB,aAAA;YAAA;cAEA;cACA,IAAAyB,MAAA,CAAAhI,iBAAA,IAAAgI,MAAA,CAAAhI,iBAAA,CAAAgH,MAAA;gBACAgB,MAAA,CAAAf,4BAAA;cACA;cAAAkB,SAAA,CAAAzD,IAAA;cAAA;YAAA;cAAAyD,SAAA,CAAA1D,IAAA;cAAA0D,SAAA,CAAA3C,EAAA,GAAA2C,SAAA;cAEAtF,OAAA,CAAAD,KAAA,CACA,6BAAAuF,SAAA,CAAA3C,EAEA;YAAA;YAAA;cAAA,OAAA2C,SAAA,CAAA1C,IAAA;UAAA;QAAA,GAAAwC,QAAA;MAAA;IAEA;IAEA;IACAG,gBAAA,WAAAA,iBAAA;MACA,KAAAnF,sBAAA;MACA,KAAAC,gBAAA;IACA;IAEA;IACAmF,uBAAA,WAAAA,wBAAA;MACA,KAAAC,kBAAA;MACA,KAAA9B,SAAA;QACA,IAAA+B,aAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAF,aAAA;UACAA,aAAA,CAAAG,SAAA;QACA;MACA;IACA;IAEA;IACAnC,aAAA,WAAAA,cAAA;MAAA,IAAAoC,MAAA;MAAA,WAAAzE,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAwE,SAAA;QAAA,IAAAC,MAAA,EAAApG,GAAA,EAAAqG,QAAA,EAAAhL,KAAA,EAAAiL,OAAA;QAAA,WAAA5E,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAA0E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxE,IAAA,GAAAwE,SAAA,CAAAvE,IAAA;YAAA;cACAiE,MAAA,CAAA3L,OAAA;cAAAiM,SAAA,CAAAxE,IAAA;cAEAoE,MAAA;gBACAK,YAAA;gBACA/L,EAAA,EAAAwL,MAAA,CAAAzL,WAAA,CAAAC,EAAA;gBACAC,OAAA,EAAAuL,MAAA,CAAArJ,eAAA;gBACAjC,QAAA,EAAAsL,MAAA,CAAApJ,YAAA;gBACA4J,CAAA;gBACA7L,QAAA,EACAqL,MAAA,CAAAzL,WAAA,CAAAI,QAAA,QAAAqL,MAAA,CAAAzL,WAAA,CAAAI,QAAA;gBACAC,IAAA,EAAAoL,MAAA,CAAAzL,WAAA,CAAAK,IAAA;gBACAC,UAAA,EAAAmL,MAAA,CAAAzL,WAAA,CAAAM,UAAA;gBACAC,QAAA,EAAAkL,MAAA,CAAAzL,WAAA,CAAAO,QAAA;gBACAC,YAAA,EAAAiL,MAAA,CAAAzL,WAAA,CAAAQ,YAAA;gBACAE,OAAA,EAAA+K,MAAA,CAAAzL,WAAA,CAAAU,OAAA;gBACAwL,KAAA,EAAAT,MAAA,CAAAzL,WAAA,CAAAM,UAAA,CAAA6L,IAAA;gBACA;gBACAC,WAAA,EAAAX,MAAA,CAAA3K,UAAA;gBACA;gBACAuL,uBAAA,EAAAZ,MAAA,CAAA5I,gBAAA;gBACAlC,QAAA,EAAA8K,MAAA,CAAAzL,WAAA,CAAAW,QAAA;gBACA;gBACA2L,SAAA,EAAAb,MAAA,CAAAzL,WAAA,CAAAU,OAAA;gBACA6L,QAAA,EAAAd,MAAA,CAAAzL,WAAA,CAAAU,OAAA;gBACA8L,QAAA,EAAAf,MAAA,CAAAzL,WAAA,CAAAU,OAAA;gBACA+L,mBAAA,EAAAhB,MAAA,CAAAzL,WAAA,CAAAU,OAAA;gBACAgM,YAAA,EAAAjB,MAAA,CAAAzL,WAAA,CAAAU;cACA;cAEA,KAAA+K,MAAA,CAAAzL,WAAA,CAAAK,IAAA;gBACAsL,MAAA,CAAArL,UAAA;gBACAqL,MAAA,CAAAO,KAAA;cACA;cAAAH,SAAA,CAAAvE,IAAA;cAAA,OAEArB,cAAA,CAAAwG,gBAAA,CAAAhB,MAAA;YAAA;cAAApG,GAAA,GAAAwG,SAAA,CAAAa,IAAA;cAEA,IAAArH,GAAA,CAAAe,IAAA;gBACAsF,QAAA,GAAArG,GAAA,CAAAsH,IAAA;gBACAjM,KAAA,GAAA2E,GAAA,CAAA3E,KAAA;gBAEAiL,OAAA,YAAAA,QAAAhM,IAAA;kBAAA,OACAA,IAAA,CAAAiN,GAAA,WAAA5E,IAAA,EAAA6E,KAAA;oBAAA;sBACA9M,EAAA,KAAA+M,MAAA,CACA9E,IAAA,CAAAsC,QAAA,oBAAAwC,MAAA,CACAD,KAAA,OAAAC,MAAA,CAAAC,IAAA,CAAAC,GAAA,SAAAF,MAAA,CAAAG,IAAA,CAAAC,MAAA,GACAC,QAAA,KACAC,SAAA;sBAAA;sBACApB,KAAA,EAAAhE,IAAA,CAAAqF,MAAA;sBACAC,KAAA,EAAAtF,IAAA,CAAAuF,YAAA;sBACAC,QAAA,EAAAxF,IAAA,CAAAwF,QAAA;sBACAC,OAAA,EAAAzF,IAAA,CAAA0F,eAAA;sBACApD,QAAA,EAAAtC,IAAA,CAAAsC,QAAA;sBACAqD,GAAA,EAAA3F,IAAA,CAAA2F,GAAA;oBACA;kBAAA;gBAAA;gBAEApC,MAAA,CAAA5K,gBAAA,GAAAgL,OAAA,CAAAD,QAAA;gBACAH,MAAA,CAAAnJ,SAAA,GAAA1B,KAAA;cACA;cAAAmL,SAAA,CAAAvE,IAAA;cAAA;YAAA;cAAAuE,SAAA,CAAAxE,IAAA;cAAAwE,SAAA,CAAAzD,EAAA,GAAAyD,SAAA;cAEApG,OAAA,CAAAD,KAAA,aAAAqG,SAAA,CAAAzD,EAAA;cACAmD,MAAA,CAAA7F,QAAA,CAAAF,KAAA;YAAA;cAAAqG,SAAA,CAAAxE,IAAA;cAEAkE,MAAA,CAAA3L,OAAA;cAAA,OAAAiM,SAAA,CAAA+B,MAAA;YAAA;YAAA;cAAA,OAAA/B,SAAA,CAAAxD,IAAA;UAAA;QAAA,GAAAmD,QAAA;MAAA;IAEA;IAEA;IACA1F,gBAAA,WAAAA,iBAAA+H,IAAA;MAAA,IAAAC,MAAA;MAAA,WAAAhH,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAA+G,SAAA;QAAA,WAAAhH,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAA8G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5G,IAAA,GAAA4G,SAAA,CAAA3G,IAAA;YAAA;cAAA,KAEAwG,MAAA,CAAAtL,UAAA;gBAAAyL,SAAA,CAAA3G,IAAA;gBAAA;cAAA;cAAA,OAAA2G,SAAA,CAAAC,MAAA;YAAA;cAIA,KAAAL,IAAA,IAAAA,IAAA;gBACAC,MAAA,CAAAjO,YAAA;cACA;;cAEA;cACA,IAAAiO,MAAA,CAAAvL,kBAAA;gBACA4L,YAAA,CAAAL,MAAA,CAAAvL,kBAAA;cACA;;cAEA;cACAuL,MAAA,CAAAvL,kBAAA,GAAAuH,UAAA,kBAAAhD,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAoH,SAAA;gBAAA,IAAA3C,MAAA,EAAA9L,IAAA,EAAA2K,QAAA,EAAAjF,GAAA,EAAAgJ,WAAA;gBAAA,WAAAtH,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAoH,UAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAAlH,IAAA,GAAAkH,SAAA,CAAAjH,IAAA;oBAAA;sBAAAiH,SAAA,CAAAlH,IAAA;sBAEA,IAAAwG,IAAA;wBACAC,MAAA,CAAAnJ,aAAA;sBACA;sBAEAmJ,MAAA,CAAAtL,UAAA;sBAEAiJ,MAAA;wBACAM,CAAA;wBACA/L,OAAA,EAAA8N,MAAA,CAAAhO,WAAA,CAAAE,OAAA;wBACAC,QAAA,EAAA6N,MAAA,CAAAhO,WAAA,CAAAG,QAAA;wBACAF,EAAA,EAAA+N,MAAA,CAAAhO,WAAA,CAAAC,EAAA;wBACAyO,MAAA,EAAAV,MAAA,CAAAhO,WAAA,CAAAS,QAAA;wBACAL,QAAA,EACA4N,MAAA,CAAAhO,WAAA,CAAAI,QAAA,QAAA4N,MAAA,CAAAhO,WAAA,CAAAI,QAAA;wBACAC,IAAA,EAAA2N,MAAA,CAAAhO,WAAA,CAAAK,IAAA;wBACAC,UAAA,EAAA0N,MAAA,CAAAhO,WAAA,CAAAM,UAAA;wBACAC,QAAA,EAAAyN,MAAA,CAAAhO,WAAA,CAAAO,QAAA;wBACAC,YAAA,EAAAwN,MAAA,CAAAhO,WAAA,CAAAQ,YAAA;wBACAE,OAAA,EAAAsN,MAAA,CAAAhO,WAAA,CAAAU,OAAA;wBACAwL,KAAA,EAAA8B,MAAA,CAAAhO,WAAA,CAAAM,UAAA,CAAA6L,IAAA;wBACAH,YAAA;wBACA;wBACAM,SAAA,EAAA0B,MAAA,CAAAhO,WAAA,CAAAU,OAAA;wBACA6L,QAAA,EAAAyB,MAAA,CAAAhO,WAAA,CAAAU,OAAA;wBACA8L,QAAA,EAAAwB,MAAA,CAAAhO,WAAA,CAAAU,OAAA;wBACA+L,mBAAA,EAAAuB,MAAA,CAAAhO,WAAA,CAAAU,OAAA;wBACAgM,YAAA,EAAAsB,MAAA,CAAAhO,WAAA,CAAAU;sBACA;sBAEA,KAAAsN,MAAA,CAAAhO,WAAA,CAAAK,IAAA;wBACAsL,MAAA,CAAArL,UAAA;wBACAqL,MAAA,CAAAO,KAAA;sBACA;;sBAEA;sBACA,IAAA8B,MAAA,CAAAlL,iBAAA,IAAAkL,MAAA,CAAAlL,iBAAA,CAAAgH,MAAA;wBACAjK,IAAA,GAAAmO,MAAA,CAAAlL,iBAAA,CAAAgK,GAAA,WAAA5E,IAAA;0BAAA,OAAAA,IAAA,CAAAgE,KAAA;wBAAA;wBACA1B,QAAA,GAAAwD,MAAA,CAAAlL,iBAAA,CAAAgK,GAAA,CACA,UAAA5E,IAAA;0BAAA,OAAAA,IAAA,CAAAsC,QAAA;wBAAA,CACA;wBAEAmB,MAAA,CAAAgD,UAAA,GAAAC,MAAA,CAAA/O,IAAA;wBACA8L,MAAA,CAAAnB,QAAA,GAAAoE,MAAA,CAAApE,QAAA;sBACA;;sBAEA;sBACA,IACAmB,MAAA,CAAApL,QAAA,IACAoL,MAAA,CAAApL,QAAA,CAAAwI,IAAA,aACAgF,IAAA,yBACA;wBACA,IAAAc,iCAAA;0BAAAC,OAAA,EAAAnD,MAAA,CAAApL,QAAA;0BAAAiG,IAAA;wBAAA,GAAAlB,IAAA,CACA;0BACA0I,MAAA,CAAAhG,iBAAA;wBACA,CACA;sBACA;sBAAAyG,SAAA,CAAAjH,IAAA;sBAAA,OAEArB,cAAA,CAAA4I,WAAA,CAAApD,MAAA;oBAAA;sBAAApG,GAAA,GAAAkJ,SAAA,CAAA7B,IAAA;sBAAA,MAEArH,GAAA,CAAAe,IAAA;wBAAAmI,SAAA,CAAAjH,IAAA;wBAAA;sBAAA;sBACA+G,WAAA,GAAAhJ,GAAA,CAAA1F,IAAA,CAAAmP,IAAA,GACAzJ,GAAA,CAAA1F,IAAA,CAAAmP,IAAA,CAAAlC,GAAA,WAAA5E,IAAA;wBACAA,IAAA,CAAA+G,OAAA,GAAA/G,IAAA,CAAA+G,OAAA,GACAjB,MAAA,CAAAkB,WAAA,CAAAhH,IAAA,CAAA+G,OAAA,IACA;wBACA/G,IAAA,CAAAiH,KAAA,GAAAnB,MAAA,CAAAkB,WAAA,CAAAhH,IAAA,CAAAiH,KAAA;wBACA,OAAAjH,IAAA;sBACA,KACA,IAEA;sBACA,IACA,CAAA8F,MAAA,CAAAhO,WAAA,CAAAO,QAAA,IACAyN,MAAA,CAAAhO,WAAA,CAAAO,QAAA,CAAAwI,IAAA,WACA;wBACAwF,WAAA,GAAAP,MAAA,CAAAoB,mBAAA,CAAAb,WAAA;sBACA;sBAEAP,MAAA,CAAAhN,WAAA,GAAAuN,WAAA;sBACAP,MAAA,CAAApN,KAAA,GAAA2E,GAAA,CAAA1F,IAAA,CAAAe,KAAA;;sBAEA;sBACA,IAAAoN,MAAA,CAAAlL,iBAAA,IAAAkL,MAAA,CAAAlL,iBAAA,CAAAgH,MAAA;wBACAkE,MAAA,CAAAjE,4BAAA;sBACA;;sBAEA;sBAAA,MAEAiE,MAAA,CAAAhN,WAAA,CAAA8I,MAAA,SACAkE,MAAA,CAAAhO,WAAA,CAAAG,QAAA,IAAA6N,MAAA,CAAAhO,WAAA,CAAAE,OAAA,SACA8N,MAAA,CAAApN,KAAA,IACAoN,MAAA,CAAApN,KAAA;wBAAA6N,SAAA,CAAAjH,IAAA;wBAAA;sBAAA;sBAEAwG,MAAA,CAAAhO,WAAA,CAAAE,OAAA,GAAAiN,IAAA,CAAAkC,GAAA,CACA,GACAlC,IAAA,CAAAmC,IAAA,CAAAtB,MAAA,CAAApN,KAAA,GAAAoN,MAAA,CAAAhO,WAAA,CAAAG,QAAA,CACA;sBACA;sBAAAsO,SAAA,CAAAjH,IAAA;sBAAA,OACAwG,MAAA,CAAAhI,gBAAA;oBAAA;sBAAA,OAAAyI,SAAA,CAAAL,MAAA;oBAAA;sBAAAK,SAAA,CAAAjH,IAAA;sBAAA;oBAAA;sBAIAwG,MAAA,CAAApI,QAAA,CAAAF,KAAA,CAAAH,GAAA,CAAAmC,GAAA;oBAAA;sBAAA+G,SAAA,CAAAjH,IAAA;sBAAA;oBAAA;sBAAAiH,SAAA,CAAAlH,IAAA;sBAAAkH,SAAA,CAAAnG,EAAA,GAAAmG,SAAA;sBAGA9I,OAAA,CAAAD,KAAA,cAAA+I,SAAA,CAAAnG,EAAA;sBACA0F,MAAA,CAAApI,QAAA,CAAAF,KAAA;oBAAA;sBAAA+I,SAAA,CAAAlH,IAAA;sBAEAyG,MAAA,CAAAtL,UAAA;sBACAsL,MAAA,CAAAnJ,aAAA;sBACAmJ,MAAA,CAAAjO,YAAA;sBAAA,OAAA0O,SAAA,CAAAX,MAAA;oBAAA;oBAAA;sBAAA,OAAAW,SAAA,CAAAlG,IAAA;kBAAA;gBAAA,GAAA+F,QAAA;cAAA,CAEA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAA5F,IAAA;UAAA;QAAA,GAAA0F,QAAA;MAAA;IACA;IAEA;IAEA;IACAsB,qBAAA,WAAAA,sBAAAC,YAAA,EAAAC,aAAA;MACA,IAAAA,aAAA,oBAAAA,aAAA;QACA;QACA,KAAA1O,SAAA,OAAA8I,mBAAA,CAAAjD,OAAA,EAAA4I,YAAA;QACA,KAAA1M,iBAAA,OAAA+G,mBAAA,CAAAjD,OAAA,EAAA4I,YAAA;MACA,WACAC,aAAA,0BACAA,aAAA,mBACA;QACA;QACA;QACA,IAAAC,cAAA,QAAA7O,gBAAA,CAAAiM,GAAA,CACA,UAAA5E,IAAA;UAAA,OAAAA,IAAA,CAAAsC,QAAA;QAAA,CACA;QACA,IAAAmF,iBAAA,QAAA5O,SAAA,CAAAkH,MAAA,CACA,UAAAC,IAAA;UAAA,QAAAwH,cAAA,CAAArH,QAAA,CAAAH,IAAA,CAAAsC,QAAA;QAAA,CACA;QACA,IAAAoF,iBAAA,QAAA9M,iBAAA,CAAAmF,MAAA,CACA,UAAAC,IAAA;UAAA,QAAAwH,cAAA,CAAArH,QAAA,CAAAH,IAAA,CAAAsC,QAAA;QAAA,CACA;;QAEA;QACA,IAAAqF,iBAAA,MAAA7C,MAAA,KAAAnD,mBAAA,CAAAjD,OAAA,EAAA+I,iBAAA,OAAA9F,mBAAA,CAAAjD,OAAA,EAAA4I,YAAA;QACA,IAAAM,iBAAA,MAAA9C,MAAA,KAAAnD,mBAAA,CAAAjD,OAAA,EAAAgJ,iBAAA,OAAA/F,mBAAA,CAAAjD,OAAA,EAAA4I,YAAA;;QAEA;QACA,KAAAzO,SAAA,QAAAgP,qBAAA,CAAAF,iBAAA;QACA,KAAA/M,iBAAA,QAAAiN,qBAAA,CAAAD,iBAAA;MACA;QACA;QACA,KAAA/O,SAAA,OAAA8I,mBAAA,CAAAjD,OAAA,EAAA4I,YAAA;QACA,KAAA1M,iBAAA,OAAA+G,mBAAA,CAAAjD,OAAA,EAAA4I,YAAA;MACA;;MAEA;MACA,KAAAxP,WAAA,CAAAE,OAAA;MACA,KAAA6F,sBAAA;MACA,UAAApD,aAAA;QACA,KAAAqD,gBAAA;MACA;IACA;IAEA;IACA+J,qBAAA,WAAAA,sBAAAC,SAAA;MACA,IAAAC,IAAA,OAAAC,GAAA;MACA,OAAAF,SAAA,CAAA/H,MAAA,WAAAC,IAAA;QACA,IAAA+H,IAAA,CAAAE,GAAA,CAAAjI,IAAA,CAAAsC,QAAA;UACA;QACA;QACAyF,IAAA,CAAAG,GAAA,CAAAlI,IAAA,CAAAsC,QAAA;QACA;MACA;IACA;IAEA;IACA6F,WAAA,WAAAA,YAAA;MACA;MACA,KAAAvP,UAAA;MACA,KAAA+B,gBAAA;;MAEA;MACA,KAAAD,WAAA;;MAEA;MACA,KAAA7B,SAAA;;MAEA;MACA,KAAA+B,iBAAA;;MAEA;MACA,KAAA9C,WAAA,CAAAE,OAAA;MACA,KAAAkC,eAAA;MACA,KAAApC,WAAA,CAAAW,QAAA;MACA,KAAAoF,sBAAA;;MAEA;MACA,KAAAwD,gBAAA;IACA;IAEA;IACA+G,SAAA,WAAAA,UAAA;MACA,KAAAD,WAAA;IACA;IAEA;IACAE,uBAAA,WAAAA,wBAAAC,IAAA;MACA,KAAApO,eAAA,GAAAoO,IAAA;MACA,KAAAxQ,WAAA,CAAAW,QAAA;MACA,KAAAkK,iCAAA;IACA;IAEA4F,wBAAA,WAAAA,yBAAAC,IAAA;MACA,KAAArO,YAAA,GAAAqO,IAAA;MACA,KAAAtO,eAAA;MACA,KAAApC,WAAA,CAAAW,QAAA;MACA,KAAAkK,iCAAA;IACA;IAEA;IACArF,wBAAA,WAAAA,yBAAAS,GAAA;MACA,KAAAjG,WAAA,CAAAM,UAAA,GAAA2F,GAAA,GACA,KAAA1E,QAAA,CAAAuL,GAAA,WAAA5E,IAAA;QAAA,OAAAA,IAAA,CAAAyI,IAAA;MAAA,KACA;MACA,KAAAjP,eAAA;MACA,KAAA1B,WAAA,CAAAE,OAAA;MAEA,UAAAqC,uBAAA;MAEA,KAAAwD,sBAAA;IACA;IAEA;IACA6K,kBAAA,WAAAA,mBAAA9B,OAAA;MACA,SAAAlM,WAAA;QACA;MACA;;MAEA;MACA,KAAA9B,UAAA,GAAAgO,OAAA;;MAEA;MACA,KAAA1M,eAAA;MACA,KAAApC,WAAA,CAAAW,QAAA;;MAEA;MACA,KAAAkK,iCAAA;IACA;IAEA;IACAgG,oBAAA,WAAAA,qBAAAC,aAAA;MACA;MACA,KAAAjO,gBAAA,GAAAiO,aAAA;;MAEA;MACA,KAAA1O,eAAA;MACA,KAAApC,WAAA,CAAAW,QAAA;;MAEA;MACA,KAAAkK,iCAAA;IACA;IAEA;IACAkG,mBAAA,WAAAA,oBAAAC,KAAA;MACA,IAAAC,YAAA,GAAAD,KAAA,CAAAlH,MAAA;MACA,KAAArI,QAAA,GAAAwP,YAAA,UAAA1P,QAAA,CAAAuI,MAAA;MACA,KAAApI,eAAA,GACAuP,YAAA,QAAAA,YAAA,QAAA1P,QAAA,CAAAuI,MAAA;MACA,KAAA9J,WAAA,CAAAE,OAAA;MAEA,UAAAqC,uBAAA;MAEA,KAAAwD,sBAAA;MACA,KAAAb,uBAAA;IACA;IAEA;IACAgM,YAAA,WAAAA,aAAAnD,IAAA;MACA,KAAAhI,sBAAA;MACA,KAAAgI,IAAA;QACA,KAAA/N,WAAA,CAAAE,OAAA;MACA;MACA,KAAA8F,gBAAA;IACA;IAEA;IACAmL,aAAA,WAAAA,cAAA;MACA,KAAApL,sBAAA;MACA,KAAAC,gBAAA;IACA;IAEA;IACAoL,cAAA,WAAAA,eAAAlJ,IAAA;MACA,KAAAlI,WAAA,CAAAO,QAAA,GAAA2H,IAAA,CAAA4G,OAAA;MACA,KAAAhN,cAAA;MACA,KAAAiE,sBAAA;MACA,KAAA/F,WAAA,CAAAE,OAAA;MACA;MACA,KAAA8F,gBAAA;IACA;IAEA;IACAqL,0BAAA,WAAAA,2BAAAC,SAAA;MACA,KAAApQ,GAAA,GAAAoQ,SAAA,CAAAxE,GAAA,WAAA5E,IAAA;QAAA,OAAAA,IAAA,CAAAjI,EAAA;MAAA;MACA,IAAAqR,SAAA,CAAAxH,MAAA,SAAA9I,WAAA,CAAA8I,MAAA;QACA,KAAA7I,OAAA;MACA;QACA,KAAAA,OAAA;MACA;MACA,KAAAE,QAAA,IAAAmQ,SAAA,CAAAxH,MAAA;IACA;IACA;IACAyH,oBAAA,WAAAA,qBAAAtL,GAAA;MACA,IAAAA,GAAA;QACA,KAAAyE,KAAA,UAAA8G,kBAAA;MACA;QACA,KAAA9G,KAAA,UAAA+G,cAAA;MACA;IACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,SAAAxQ,GAAA,CAAA4I,MAAA;QACA,YAAAlE,QAAA;UACAW,OAAA;UACAC,IAAA;QACA;MACA;MACA,KAAApF,aAAA;IACA;IACA;IACAuQ,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5K,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAA2K,SAAA;QAAA,IAAAC,WAAA,EAAAvM,GAAA;QAAA,WAAA0B,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAA2K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzK,IAAA,GAAAyK,SAAA,CAAAxK,IAAA;YAAA;cAAA,IACAoK,OAAA,CAAAtQ,QAAA;gBAAA0Q,SAAA,CAAAxK,IAAA;gBAAA;cAAA;cAAA,OAAAwK,SAAA,CAAA5D,MAAA,WACAwD,OAAA,CAAAhM,QAAA;gBACAW,OAAA;gBACAC,IAAA;cACA;YAAA;cACAsL,WAAA,GAAAF,OAAA,CAAA1Q,GAAA,CAAA4L,GAAA,WAAA5E,IAAA;gBACA;kBAAA5G,QAAA,EAAAsQ,OAAA,CAAAtQ,QAAA;kBAAA2Q,MAAA,EAAA/J;gBAAA;cACA;cAAA8J,SAAA,CAAAxK,IAAA;cAAA,OACArB,cAAA,CAAA+L,SAAA,CAAAJ,WAAA;YAAA;cAAAvM,GAAA,GAAAyM,SAAA,CAAApF,IAAA;cACA,IAAArH,GAAA,CAAAe,IAAA;gBACAsL,OAAA,CAAAhM,QAAA;kBAAAW,OAAA;kBAAAC,IAAA;gBAAA;gBACAoL,OAAA,CAAA5L,gBAAA;cACA;gBACA4L,OAAA,CAAAhM,QAAA;kBACAW,OAAA;kBACAC,IAAA;gBACA;cACA;cACAoL,OAAA,CAAAlH,KAAA,UAAA+G,cAAA;cACAG,OAAA,CAAA3Q,OAAA;cACA2Q,OAAA,CAAAnL,WAAA;YAAA;YAAA;cAAA,OAAAuL,SAAA,CAAAzJ,IAAA;UAAA;QAAA,GAAAsJ,QAAA;MAAA;IACA;IACA;IACApL,WAAA,WAAAA,YAAA;MACA,KAAAnF,QAAA;MACA,KAAAF,aAAA;IACA;IACA;IACA+Q,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MACA,SAAAlR,GAAA,CAAA4I,MAAA;QACA,YAAAlE,QAAA;UACAW,OAAA;UACAC,IAAA;QACA;MACA;MACA,KAAA6L,QAAA,mBACA/M,IAAA;QACAgN,cAAA,CAAAC,WAAA,CAAAH,OAAA,CAAAlR,GAAA,CAAAiL,IAAA,OAAA7G,IAAA,WAAAkN,QAAA;UACAJ,OAAA,CAAAxM,QAAA;YAAAW,OAAA;YAAAC,IAAA;UAAA;UACA4L,OAAA,CAAApM,gBAAA;QACA;MACA,GACAP,KAAA;IACA;IACA;IACAgN,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,SAAAxR,GAAA,CAAA4I,MAAA;QACA,YAAAlE,QAAA;UACAW,OAAA;UACAC,IAAA;QACA;MACA;MACA,KAAA6L,QAAA,wBACA/M,IAAA;QACA,IAAAqN,aAAA,EAAAD,OAAA,CAAAxR,GAAA,EAAAoE,IAAA;UACAoN,OAAA,CAAA9M,QAAA;YAAAY,IAAA;YAAAD,OAAA;UAAA;UACAmM,OAAA,CAAA1M,gBAAA;QACA;MACA,GACAP,KAAA;IACA;IACA;IACAmN,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,SAAA3R,GAAA,CAAA4I,MAAA;QACA,YAAAlE,QAAA;UACAW,OAAA;UACAC,IAAA;QACA;MACA;MACA,KAAA6L,QAAA,0BACA/M,IAAA;QACAgN,cAAA,CAAAQ,kBAAA,CAAAD,OAAA,CAAA3R,GAAA,CAAAiL,IAAA,OAAA7G,IAAA;UACAuN,OAAA,CAAAjN,QAAA;YAAAY,IAAA;YAAAD,OAAA;UAAA;UACAsM,OAAA,CAAA7M,gBAAA;QACA;MACA,GACAP,KAAA;IACA;IACA;IACAsN,aAAA,WAAAA,cAAA7K,IAAA,EAAA1B,IAAA;MAAA,IAAAwM,OAAA;MAAA,WAAAhM,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAA+L,SAAA;QAAA,WAAAhM,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAA8L,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5L,IAAA,GAAA4L,SAAA,CAAA3L,IAAA;YAAA;cACA6G,YAAA,CAAA2E,OAAA,CAAAnR,cAAA;cAAAsR,SAAA,CAAA3L,IAAA;cAAA,OACA,IAAA4L,iCAAA,GAAAlL,IAAA,CAAAjI,EAAA;YAAA;cACA,IAAAuG,IAAA;gBACAwM,OAAA,CAAAtI,KAAA,eAAA2I,KAAA;gBACAL,OAAA,CAAAhL,iBAAA;cACA;gBACAgL,OAAA,CAAAhL,iBAAA;gBACAgL,OAAA,CAAA5H,kBAAA;cACA;YAAA;YAAA;cAAA,OAAA+H,SAAA,CAAA5K,IAAA;UAAA;QAAA,GAAA0K,QAAA;MAAA;IACA;IAEAK,eAAA,WAAAA,gBAAA;MACA,KAAA3R,WAAA;IACA;IAEA4R,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA,KAAA3R,cAAA,GAAAmI,UAAA;QACAwJ,OAAA,CAAA7R,WAAA;MACA;IACA;IAEAqG,iBAAA,WAAAA,kBAAA;MAAA,IAAAyL,OAAA;MACA,KAAA1R,cAAA;MACA,IAAA2R,kCAAA;QAAAxT,OAAA;QAAAC,QAAA;QAAAqG,IAAA;MAAA,GAAAlB,IAAA,CACA,UAAAkN,QAAA;QACAiB,OAAA,CAAA7R,WAAA,GAAA4Q,QAAA,CAAA3F,IAAA;QACA4G,OAAA,CAAA1R,cAAA;MACA,CACA;IACA;IAEA4R,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5M,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAA2M,UAAA;QAAA,WAAA5M,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAA0M,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxM,IAAA,GAAAwM,UAAA,CAAAvM,IAAA;YAAA;cACA6G,YAAA,CAAAuF,OAAA,CAAA/R,cAAA;cACA+R,OAAA,CAAAlJ,KAAA,eAAA2I,KAAA;cAAAU,UAAA,CAAAvM,IAAA;cAAA,OACA,IAAAwM,mCAAA;YAAA;cACAJ,OAAA,CAAA5L,iBAAA;YAAA;YAAA;cAAA,OAAA+L,UAAA,CAAAxL,IAAA;UAAA;QAAA,GAAAsL,SAAA;MAAA;IACA;IAEAI,WAAA,WAAAA,YAAA;MACA5F,YAAA,MAAAxM,cAAA;MACA,KAAAF,WAAA;MACA,KAAAI,cAAA;MACA,KAAAqJ,kBAAA;MACA,KAAAtJ,cAAA;IACA;IAEAsJ,kBAAA,WAAAA,mBAAA;MAAA,IAAA8I,OAAA;MACA,KAAAnS,cAAA;MACA,IAAA2R,kCAAA,MAAA/M,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,WAAA5E,YAAA;QAAAwE,IAAA;MAAA,IAAAlB,IAAA,WAAAkN,QAAA;QACA0B,OAAA,CAAAhS,YAAA,GAAAsQ,QAAA,CAAA3F,IAAA;QACAqH,OAAA,CAAAjS,MAAA,GAAAuQ,QAAA,CAAA5R,KAAA;QACAsT,OAAA,CAAAnS,cAAA;MACA;IACA;IAEA;IACAoS,WAAA,WAAAA,YAAAjM,IAAA;MACAkM,MAAA,CAAAC,IAAA,uBAAArH,MAAA,CACA9E,IAAA,CAAAjI,EAAA,aAAA+M,MAAA,CAAA9E,IAAA,CAAAoM,KAAA,kBAAAtH,MAAA,CAAA9E,IAAA,CAAA7B,UAAA,GACA,QACA;IACA;IAEA;IACAkO,kBAAA,WAAAA,mBAAAvD,KAAA;MACA,IAAAwD,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAxD,KAAA;IACA;IAEA;IACAyD,gBAAA,WAAAA,iBAAAC,IAAA;MACA,KAAAA,IAAA;MACA,IAAAC,kBAAA,GAAAD,IAAA,CAAA5L,OAAA;MACA,kCAAA8L,IAAA,CAAAD,kBAAA;IACA;IAEA;IACA5O,sBAAA,WAAAA,uBAAA;MACA,SAAA2E,KAAA,CAAAmK,SAAA;QACA,KAAAnK,KAAA,CAAAmK,SAAA,CAAArJ,SAAA;MACA;MAEA,SAAAd,KAAA,CAAAoK,KAAA;QACA,IAAAC,WAAA,QAAArK,KAAA,CAAAoK,KAAA,CAAAE,GAAA,CAAAzJ,aAAA,CACA,yBACA;QACA,IAAAwJ,WAAA;UACAA,WAAA,CAAAvJ,SAAA;QACA;MACA;IACA;IAEA;IACA0D,WAAA,WAAAA,YAAA+F,GAAA;MACA,IAAAC,KAAA;MACA,IAAAC,WAAA,GAAAF,GAAA,IAAAA,GAAA,CAAAnM,OAAA,CAAAoM,KAAA;MAEA,KAAAC,WAAA;QACA,OAAAA,WAAA;MACA;;MAEA;MACA,IAAAC,aAAA,GACA,KAAApV,WAAA,CAAAK,IAAA,IACA,KAAAL,WAAA,CAAAM,UAAA,IACA,KAAAN,WAAA,CAAAM,UAAA,CAAAwJ,MAAA;MACA,IAAAuL,WAAA,QAAArV,WAAA,CAAAO,QAAA;MAEA,KAAA6U,aAAA,KAAAC,WAAA;QACA,OAAAF,WAAA;MACA;MAEA,IAAAG,MAAA,GAAAH,WAAA;MACA,IAAA5U,QAAA;;MAEA;MACA,IAAA8U,WAAA;QACA,SAAArV,WAAA,CAAAO,QAAA,CAAA8H,QAAA;UACA9H,QAAA,MAAAyM,MAAA,KAAAnD,mBAAA,CAAAjD,OAAA,EACA,KAAA5G,WAAA,CAAAM,UAAA,aAAAuJ,mBAAA,CAAAjD,OAAA,EACA,KAAA5G,WAAA,CAAAO,QAAA,CAAAgV,KAAA,OACA;QACA,gBAAAvV,WAAA,CAAAO,QAAA,CAAA8H,QAAA;UACA9H,QAAA,MAAAyM,MAAA,KAAAnD,mBAAA,CAAAjD,OAAA,EACA,KAAA5G,WAAA,CAAAM,UAAA,aAAAuJ,mBAAA,CAAAjD,OAAA,EACA,KAAA5G,WAAA,CAAAO,QAAA,CAAAgV,KAAA,OACA;QACA;UACAhV,QAAA,MAAAyM,MAAA,KAAAnD,mBAAA,CAAAjD,OAAA,EACA,KAAA5G,WAAA,CAAAM,UAAA,UACA,KAAAN,WAAA,CAAAO,QAAA,EACA;QACA;MACA;QACAA,QAAA,OAAAsJ,mBAAA,CAAAjD,OAAA,OAAA5G,WAAA,CAAAM,UAAA;MACA;;MAEA;MACAC,QAAA,GAAAA,QAAA,CAAA0H,MAAA,CACA,UAAA6G,OAAA;QAAA,OAAAA,OAAA,IAAAA,OAAA,CAAA/F,IAAA,GAAAe,MAAA;MAAA,CACA;MAEAvJ,QAAA,CAAA4J,OAAA,WAAAqL,OAAA;QACA,IAAAC,cAAA,GAAAD,OAAA,CAAAzM,IAAA;QACA,IAAA0M,cAAA,CAAA3L,MAAA;UACA;UACA,IAAA4L,cAAA,GAAAD,cAAA,CAAA3M,OAAA,CACA,uBACA,MACA;;UAEA;UACA,IAAA6M,aAAA,sDAAA3I,MAAA,CAAAyI,cAAA;;UAEA;UACA,IAAAG,YAAA;UACA,IAAAC,KAAA;UACA,IAAAC,SAAA;UACA,IAAAC,KAAA;;UAEA;UACA,QAAAA,KAAA,GAAAH,YAAA,CAAAI,IAAA,CAAAV,MAAA;YACA;YACA,IAAAS,KAAA,CAAAhJ,KAAA,GAAA+I,SAAA;cACAD,KAAA,CAAApL,IAAA;gBACAjE,IAAA;gBACAyP,OAAA,EAAAX,MAAA,CAAAhI,SAAA,CAAAwI,SAAA,EAAAC,KAAA,CAAAhJ,KAAA;cACA;YACA;YACA;YACA8I,KAAA,CAAApL,IAAA;cACAjE,IAAA;cACAyP,OAAA,EAAAF,KAAA;YACA;YACAD,SAAA,GAAAC,KAAA,CAAAhJ,KAAA,GAAAgJ,KAAA,IAAAjM,MAAA;UACA;;UAEA;UACA,IAAAgM,SAAA,GAAAR,MAAA,CAAAxL,MAAA;YACA+L,KAAA,CAAApL,IAAA;cACAjE,IAAA;cACAyP,OAAA,EAAAX,MAAA,CAAAhI,SAAA,CAAAwI,SAAA;YACA;UACA;;UAEA;UACA,IAAAD,KAAA,CAAA/L,MAAA;YACA+L,KAAA,CAAApL,IAAA;cACAjE,IAAA;cACAyP,OAAA,EAAAX;YACA;UACA;;UAEA;UACAO,KAAA,CAAA1L,OAAA,WAAA+L,IAAA;YACA,IAAAA,IAAA,CAAA1P,IAAA;cACA;cACA,IAAA0O,MAAA,OAAAiB,MAAA,CAAAT,cAAA;cACAQ,IAAA,CAAAD,OAAA,GAAAC,IAAA,CAAAD,OAAA,CAAAnN,OAAA,CAAAoM,MAAA,EAAAS,aAAA;YACA;UACA;;UAEA;UACAL,MAAA,GAAAO,KAAA,CAAA/I,GAAA,WAAAoJ,IAAA;YAAA,OAAAA,IAAA,CAAAD,OAAA;UAAA,GAAA9J,IAAA;QACA;MACA;MAEA,OAAAmJ,MAAA;IACA;IAEA;IACAc,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MACA,SAAAnV,GAAA,CAAA4I,MAAA;QACA,YAAAlE,QAAA,CAAA0Q,OAAA;MACA;MACA,IAAApV,GAAA,QAAAA,GAAA;MACA,IAAAqV,SAAA;MACA,IAAA1I,GAAA;MACA,IAAA3M,GAAA,CAAA4I,MAAA;QACA,IAAA0M,GAAA,QAAAxV,WAAA,CAAAiH,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAjI,EAAA,IAAAiB,GAAA;QAAA;QACA,IAAAsV,GAAA,IAAAA,GAAA,CAAAC,WAAA,EAAAF,SAAA;QACA1I,GAAA,GAAA2I,GAAA,CAAAC,WAAA;MACA;MACA,IAAAF,SAAA;QACA,KAAAG,OAAA;UACAvH,KAAA;UACA5I,OAAA;UACAoQ,gBAAA;UACAC,iBAAA;UACAC,WAAA,WAAAA,YAAAC,CAAA,EAAAC,EAAA,EAAAC,IAAA;YACAA,IAAA;UACA;QACA;QACA1E,cAAA,CAAA2E,gBAAA,CAAA/V,GAAA,EACAoE,IAAA,WAAAkN,QAAA;UACA,IAAAA,QAAA,CAAAlM,IAAA;YACA+P,OAAA,CAAAzQ,QAAA;cACAW,OAAA;cACAC,IAAA;YACA;UACA;QACA,GACAf,KAAA;MACA;QACAoI,GAAA,GAAAA,GAAA,CAAA/E,OAAA,KAAAqN,MAAA;QACAtI,GAAA,GAAAA,GAAA,CAAA/E,OAAA,KAAAqN,MAAA;QACA/B,MAAA,CAAAC,IAAA,CAAAD,MAAA,CAAA8C,QAAA,CAAAC,MAAA,GAAAtJ,GAAA;MACA;IACA;IAEAuJ,OAAA,WAAAA,QAAAvJ,GAAA;MACAuG,MAAA,CAAAC,IAAA,CAAAxG,GAAA;IACA;IAEA;IACA;IACAwJ,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,WAAAtQ,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAqQ,UAAA;QAAA,IAAAC,qBAAA,EAAAC,gBAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,SAAA,EAAAC,MAAA,EAAAtF,QAAA,EAAAuF,MAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,aAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,kBAAA,EAAAtB,IAAA,EAAAhG,KAAA,EAAAuH,QAAA,EAAAC,aAAA,EAAAC,KAAA,EAAAC,YAAA,EAAAC,IAAA,EAAA9Y,IAAA,EAAA+Y,QAAA,EAAAC,MAAA;QAAA,WAAA5R,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAA0R,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxR,IAAA,GAAAwR,UAAA,CAAAvR,IAAA;YAAA;cAAA,MACA8P,OAAA,CAAApW,GAAA,CAAA4I,MAAA;gBAAAiP,UAAA,CAAAvR,IAAA;gBAAA;cAAA;cAAA,OAAAuR,UAAA,CAAA3K,MAAA,WACAkJ,OAAA,CAAA1R,QAAA;gBACAW,OAAA;gBACAC,IAAA;cACA;YAAA;cAAA,KAIA8Q,OAAA,CAAAvT,YAAA;gBAAAgV,UAAA,CAAAvR,IAAA;gBAAA;cAAA;cACA8P,OAAA,CAAAtT,SAAA;cAAA,KACAsT,OAAA,CAAArT,aAAA;gBAAA8U,UAAA,CAAAvR,IAAA;gBAAA;cAAA;cAAAuR,UAAA,CAAAxR,IAAA;cAAAwR,UAAA,CAAAvR,IAAA;cAAA,OAEA8P,OAAA,CAAArT,aAAA,CAAA+U,MAAA;YAAA;cAAAD,UAAA,CAAAvR,IAAA;cAAA;YAAA;cAAAuR,UAAA,CAAAxR,IAAA;cAAAwR,UAAA,CAAAzQ,EAAA,GAAAyQ,UAAA;cAEApT,OAAA,CAAAsT,GAAA,qOAAAF,UAAA,CAAAzQ,EAAA;YAAA;cAAAyQ,UAAA,CAAAvR,IAAA;cAAA,OAGA,IAAAM,OAAA,WAAAoR,OAAA;gBAAA,OAAAlP,UAAA,CAAAkP,OAAA;cAAA;YAAA;cAGA5B,OAAA,CAAAvT,YAAA;cACAuT,OAAA,CAAAtT,SAAA;cACAsT,OAAA,CAAAvU,eAAA;cACAuU,OAAA,CAAAtU,YAAA;cACAsU,OAAA,CAAArU,UAAA;cAAA8V,UAAA,CAAAxR,IAAA;cAGA;cACAkQ,gBAAA,GAAAH,OAAA,CAAAtW,WAAA,CAAAiH,MAAA,WAAAvD,OAAA;gBAAA,OACA4S,OAAA,CAAApW,GAAA,CAAAmH,QAAA,CAAA3D,OAAA,CAAAzE,EAAA;cAAA,CACA;cACAyX,MAAA,GAAAD,gBAAA,CACA3K,GAAA,WAAApI,OAAA;gBAAA,gBAAAsI,MAAA,CAAAtI,OAAA,CAAAuK,OAAA,IAAAvK,OAAA,CAAAyK,KAAA;cAAA,GACAhD,IAAA,QAEA;cAAA4M,UAAA,CAAAvR,IAAA;cAAA,OACA,IAAA2R,4BAAA,EAAA7B,OAAA,CAAApW,GAAA,CAAAiL,IAAA;YAAA;cAAAwL,gBAAA,GAAAoB,UAAA,CAAAnM,IAAA;cAAA,KAAA4K,qBAAA,GACAG,gBAAA,CAAA9X,IAAA,cAAA2X,qBAAA,eAAAA,qBAAA,CAAA1N,MAAA;gBAAAiP,UAAA,CAAAvR,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4R,KAAA;YAAA;cAGA;cACAxB,eAAA,GAAAD,gBAAA,CAAA9X,IAAA,CACAiN,GAAA,WAAApI,OAAA,EAAAqI,KAAA;gBAAA,IAAAsM,qBAAA,EAAAC,sBAAA;gBACA,IAAAnK,KAAA,GACA,EAAAkK,qBAAA,GAAA5B,gBAAA,CAAA1K,KAAA,eAAAsM,qBAAA,uBAAAA,qBAAA,CAAApK,OAAA,OAAAqK,sBAAA,GACA7B,gBAAA,CAAA1K,KAAA,eAAAuM,sBAAA,uBAAAA,sBAAA,CAAAnK,KAAA,KACA;gBACA,IAAA8G,OAAA,GAAAvR,OAAA,CAAAuR,OAAA;gBACA,uBAAAjJ,MAAA,CAAAD,KAAA,yCAAAC,MAAA,CAAAmC,KAAA,gBAAAnC,MAAA,CAAAiJ,OAAA;cACA,GACA9J,IAAA,yDAEA;cACAmL,OAAA,CAAAtU,YAAA,CAAAyH,IAAA;gBACA8O,IAAA;gBACAtD,OAAA,qDAAAjJ,MAAA,CAAAsK,OAAA,CAAApW,GAAA,CAAA4I,MAAA,gCAAAkD,MAAA,CAAA0K,MAAA;cACA;;cAEA;cACAG,SAAA;gBACA0B,IAAA;gBACAtD,OAAA;cACA;cACAqB,OAAA,CAAAtU,YAAA,CAAAyH,IAAA,CAAAoN,SAAA;;cAEA;cACAC,MAAA,GACAR,OAAA,CAAAnT,eAAA,CACA2E,OAAA,kBAAAwO,OAAA,CAAApW,GAAA,CAAA4I,MAAA,EACAhB,OAAA,yFAAAkE,MAAA,CACA4K,eAAA,GAEA;cAAAmB,UAAA,CAAAvR,IAAA;cAAA,OACA,IAAAgS,YAAA,EACA5B,eAAA,EACA,aACA,qBACA;YAAA;cAJApF,QAAA,GAAAuG,UAAA,CAAAnM,IAAA;cAAA,IAKA4F,QAAA,CAAAiH,EAAA;gBAAAV,UAAA,CAAAvR,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4R,KAAA;YAAA;cAGA;cACArB,MAAA,GAAAvF,QAAA,CAAAkH,IAAA,CAAAC,SAAA;cACArC,OAAA,CAAArT,aAAA,GAAA8T,MAAA;cACAC,OAAA,OAAA4B,WAAA;cACA3B,MAAA;cACAC,aAAA;cACAC,YAAA;cAEA;cACAC,aAAA,YAAAA,cAAAnD,GAAA;gBACA,OAAAA,GAAA,CAAAnM,OAAA,gCAAAiN,KAAA;kBACA,OAAAnH,MAAA,CAAAiL,YAAA,CAAAC,QAAA,CAAA/D,KAAA,CAAAjN,OAAA;gBACA;cACA,GAEA;cACAuP,aAAA,YAAAA,cAAA0B,UAAA;gBACA;kBACA,IAAAC,eAAA,OAAAC,cAAA,EAAAF,UAAA,EAAAzC,OAAA,CAAAlU,eAAA;kBACAyU,SAAA,CAAA5B,OAAA,GAAA+D,eAAA;;kBAEA;kBACA1C,OAAA,CAAAhO,SAAA;oBACA,IAAAtG,YAAA,GAAAsU,OAAA,CAAA5M,KAAA,CAAA1H,YAAA;oBACA,IAAAA,YAAA;sBACAA,YAAA,CAAAwI,SAAA,GAAAxI,YAAA,CAAAkX,YAAA;oBACA;kBACA;gBACA,SAAAxU,KAAA;kBACAC,OAAA,CAAAD,KAAA,aAAAA,KAAA;gBACA;cACA,GAEA;YAAA;cAAA,KACA;gBAAAqT,UAAA,CAAAvR,IAAA;gBAAA;cAAA;cAAA,KAEA8P,OAAA,CAAAtT,SAAA;gBAAA+U,UAAA,CAAAvR,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4R,KAAA;YAAA;cAAAL,UAAA,CAAAvR,IAAA;cAAA,OAGAuQ,MAAA,CAAAoC,IAAA;YAAA;cAAA7B,kBAAA,GAAAS,UAAA,CAAAnM,IAAA;cAAAoK,IAAA,GAAAsB,kBAAA,CAAAtB,IAAA;cAAAhG,KAAA,GAAAsH,kBAAA,CAAAtH,KAAA;cAAA,KAEAgG,IAAA;gBAAA+B,UAAA,CAAAvR,IAAA;gBAAA;cAAA;cACA;cACA,IAAA0Q,aAAA;gBACA;kBACAK,QAAA,GAAA1S,IAAA,CAAAuU,KAAA,CAAAlC,aAAA;kBACA,IAAAK,QAAA,CAAAM,MAAA;oBACA;oBACAL,aAAA,GAAAJ,aAAA,CAAAG,QAAA,CAAAM,MAAA;oBACAZ,MAAA,IAAAO,aAAA;oBACAH,aAAA,CAAAJ,MAAA;kBACA;gBACA,SAAAoC,CAAA;kBACA1U,OAAA,CAAA2U,IAAA,gBAAAD,CAAA;gBACA;cACA;cAAA,OAAAtB,UAAA,CAAA3K,MAAA;YAAA;cAIAqK,KAAA,GAAAT,OAAA,CAAAuC,MAAA,CAAAvJ,KAAA;cACAkH,aAAA,IAAAO,KAAA;;cAEA;YAAA;cAAA,KACAP,aAAA,CAAA7P,QAAA;gBAAA0Q,UAAA,CAAAvR,IAAA;gBAAA;cAAA;cACAkR,YAAA,GAAAR,aAAA,CAAAsC,OAAA;cACA7B,IAAA,GAAAT,aAAA,CAAAuC,KAAA,IAAA/B,YAAA,EAAA3P,IAAA;cACAmP,aAAA,GAAAA,aAAA,CAAAuC,KAAA,CAAA/B,YAAA;cAAA,MAEA,CAAAC,IAAA,IAAAA,IAAA,iBAAAA,IAAA,CAAA+B,UAAA;gBAAA3B,UAAA,CAAAvR,IAAA;gBAAA;cAAA;cAAA,OAAAuR,UAAA,CAAA3K,MAAA;YAAA;cAAA2K,UAAA,CAAAxR,IAAA;cAKA1H,IAAA,GAAA8Y,IAAA,CAAA8B,KAAA,IAAA1R,IAAA;cAAA,MACAlJ,IAAA;gBAAAkZ,UAAA,CAAAvR,IAAA;gBAAA;cAAA;cAAA,OAAAuR,UAAA,CAAA3K,MAAA;YAAA;cAIAwK,QAAA,GAAA/S,IAAA,CAAAuU,KAAA,CAAAva,IAAA;cAAA,IACA+Y,QAAA,CAAAC,MAAA;gBAAAE,UAAA,CAAAvR,IAAA;gBAAA;cAAA;cAAA,OAAAuR,UAAA,CAAA3K,MAAA;YAAA;cAAA,MAKAwK,QAAA,CAAAC,MAAA,cAAAD,QAAA,CAAAC,MAAA;gBAAAE,UAAA,CAAAvR,IAAA;gBAAA;cAAA;cAAA,OAAAuR,UAAA,CAAA3K,MAAA;YAAA;cAIA;cACAyK,MAAA,GAAAT,aAAA,CAAAQ,QAAA,CAAAC,MAAA,GAEA;cAAA,KACAA,MAAA,CAAAxQ,QAAA;gBAAA0Q,UAAA,CAAAvR,IAAA;gBAAA;cAAA;cACA2Q,YAAA;cAAA,OAAAY,UAAA,CAAA3K,MAAA;YAAA;cAAA,KAKAyK,MAAA,CAAAxQ,QAAA;gBAAA0Q,UAAA,CAAAvR,IAAA;gBAAA;cAAA;cACA2Q,YAAA;cAAA,OAAAY,UAAA,CAAA3K,MAAA;YAAA;cAIA;cACA,KAAA+J,YAAA,IAAAU,MAAA;gBACAZ,MAAA,IAAAY,MAAA;gBACAR,aAAA,CAAAJ,MAAA;cACA;cAAAc,UAAA,CAAAvR,IAAA;cAAA;YAAA;cAAAuR,UAAA,CAAAxR,IAAA;cAAAwR,UAAA,CAAA4B,EAAA,GAAA5B,UAAA;cAEApT,OAAA,CAAA2U,IAAA;gBACA3B,IAAA,EAAAA,IAAA;gBACAjT,KAAA,EAAAqT,UAAA,CAAA4B,EAAA,CAAApU,OAAA;gBACA2R,aAAA,EAAAA;cACA;cAAA,OAAAa,UAAA,CAAA3K,MAAA;YAAA;cAAA2K,UAAA,CAAAvR,IAAA;cAAA;YAAA;cAAAuR,UAAA,CAAAvR,IAAA;cAAA;YAAA;cAAAuR,UAAA,CAAAvR,IAAA;cAAA;YAAA;cAAAuR,UAAA,CAAAxR,IAAA;cAAAwR,UAAA,CAAA6B,EAAA,GAAA7B,UAAA;cAMApT,OAAA,CAAAD,KAAA,YAAAqT,UAAA,CAAA6B,EAAA;cACAtD,OAAA,CAAA1R,QAAA,CAAAF,KAAA,CAAAqT,UAAA,CAAA6B,EAAA,CAAArU,OAAA;cACA,IAAA+Q,OAAA,CAAAtU,YAAA;gBACAsU,OAAA,CAAAtU,YAAA,IAAAiT,OAAA;cACA;YAAA;cAAA8C,UAAA,CAAAxR,IAAA;cAEA+P,OAAA,CAAArT,aAAA;cACA,IAAAqT,OAAA,CAAAvU,eAAA;gBACAuU,OAAA,CAAArU,UAAA;gBACAqU,OAAA,CAAAvT,YAAA;cACA;cAAA,OAAAgV,UAAA,CAAAjL,MAAA;YAAA;YAAA;cAAA,OAAAiL,UAAA,CAAAxQ,IAAA;UAAA;QAAA,GAAAgP,SAAA;MAAA;IAEA;IACA;IACAsD,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAA9T,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAA6T,UAAA;QAAA,IAAAC,sBAAA,EAAAvD,gBAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAqD,UAAA,EAAAnD,MAAA,EAAAtF,QAAA,EAAAuF,MAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAiD,cAAA,EAAAC,cAAA,EAAAC,UAAA,EAAA/C,aAAA,EAAAgD,aAAA;QAAA,WAAApU,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAkU,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAhU,IAAA,GAAAgU,UAAA,CAAA/T,IAAA;YAAA;cAAA,MACAsT,OAAA,CAAA5Z,GAAA,CAAA4I,MAAA;gBAAAyR,UAAA,CAAA/T,IAAA;gBAAA;cAAA;cAAA,OAAA+T,UAAA,CAAAnN,MAAA,WACA0M,OAAA,CAAAlV,QAAA;gBACAW,OAAA;gBACAC,IAAA;cACA;YAAA;cAAA,KAIAsU,OAAA,CAAA/W,YAAA;gBAAAwX,UAAA,CAAA/T,IAAA;gBAAA;cAAA;cACAsT,OAAA,CAAA9W,SAAA;cAAA,KACA8W,OAAA,CAAA7W,aAAA;gBAAAsX,UAAA,CAAA/T,IAAA;gBAAA;cAAA;cAAA+T,UAAA,CAAAhU,IAAA;cAAAgU,UAAA,CAAA/T,IAAA;cAAA,OAEAsT,OAAA,CAAA7W,aAAA,CAAA+U,MAAA;YAAA;cAAAuC,UAAA,CAAA/T,IAAA;cAAA;YAAA;cAAA+T,UAAA,CAAAhU,IAAA;cAAAgU,UAAA,CAAAjT,EAAA,GAAAiT,UAAA;cAEA5V,OAAA,CAAAsT,GAAA,qOAAAsC,UAAA,CAAAjT,EAAA;YAAA;cAAAiT,UAAA,CAAA/T,IAAA;cAAA,OAIA,IAAAM,OAAA,WAAAoR,OAAA;gBAAA,OAAAlP,UAAA,CAAAkP,OAAA;cAAA;YAAA;cAGA4B,OAAA,CAAA/W,YAAA;cACA+W,OAAA,CAAA9W,SAAA;cACA8W,OAAA,CAAA/X,eAAA;cACA+X,OAAA,CAAA9X,YAAA;cACA8X,OAAA,CAAA7X,UAAA;cAAAsY,UAAA,CAAAhU,IAAA;cAGA;cACAkQ,gBAAA,GAAAqD,OAAA,CAAA9Z,WAAA,CAAAiH,MAAA,WAAAvD,OAAA;gBAAA,OACAoW,OAAA,CAAA5Z,GAAA,CAAAmH,QAAA,CAAA3D,OAAA,CAAAzE,EAAA;cAAA,CACA;cACAyX,MAAA,GAAAD,gBAAA,CACA3K,GAAA,WAAApI,OAAA;gBAAA,gBAAAsI,MAAA,CAAAtI,OAAA,CAAAuK,OAAA,IAAAvK,OAAA,CAAAyK,KAAA;cAAA,GACAhD,IAAA,QAEA;cAAAoP,UAAA,CAAA/T,IAAA;cAAA,OACA,IAAA2R,4BAAA,EAAA2B,OAAA,CAAA5Z,GAAA,CAAAiL,IAAA;YAAA;cAAAwL,gBAAA,GAAA4D,UAAA,CAAA3O,IAAA;cAAA,KAAAoO,sBAAA,GACArD,gBAAA,CAAA9X,IAAA,cAAAmb,sBAAA,eAAAA,sBAAA,CAAAlR,MAAA;gBAAAyR,UAAA,CAAA/T,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4R,KAAA;YAAA;cAGA;cACAxB,eAAA,GAAAD,gBAAA,CAAA9X,IAAA,CACAiN,GAAA,WAAApI,OAAA,EAAAqI,KAAA;gBAAA,IAAAyO,sBAAA,EAAAC,sBAAA;gBACA,IAAAtM,KAAA,GACA,EAAAqM,sBAAA,GAAA/D,gBAAA,CAAA1K,KAAA,eAAAyO,sBAAA,uBAAAA,sBAAA,CAAAvM,OAAA,OAAAwM,sBAAA,GACAhE,gBAAA,CAAA1K,KAAA,eAAA0O,sBAAA,uBAAAA,sBAAA,CAAAtM,KAAA,KACA;gBACA,IAAA8G,OAAA,GAAAvR,OAAA,CAAAuR,OAAA;gBACA,uBAAAjJ,MAAA,CAAAD,KAAA,yCAAAC,MAAA,CAAAmC,KAAA,gBAAAnC,MAAA,CAAAiJ,OAAA;cACA,GACA9J,IAAA,yDAEA;cACA2O,OAAA,CAAA9X,YAAA,CAAAyH,IAAA;gBACA8O,IAAA;gBACAtD,OAAA,qDAAAjJ,MAAA,CAAA8N,OAAA,CAAA5Z,GAAA,CAAA4I,MAAA,gCAAAkD,MAAA,CAAA0K,MAAA;cACA;;cAEA;cACAG,UAAA;gBACA0B,IAAA;gBACAtD,OAAA;cACA;cACA6E,OAAA,CAAA9X,YAAA,CAAAyH,IAAA,CAAAoN,UAAA;;cAEA;cACAC,MAAA,GACAgD,OAAA,CAAA3W,eAAA,CACA2E,OAAA,kBAAAgS,OAAA,CAAA5Z,GAAA,CAAA4I,MAAA,EACAhB,OAAA,yFAAAkE,MAAA,CACA4K,eAAA,GAEA;cAAA2D,UAAA,CAAA/T,IAAA;cAAA,OACA,IAAAkU,cAAA,EAAA5D,MAAA;YAAA;cAAAtF,QAAA,GAAA+I,UAAA,CAAA3O,IAAA;cAAA,IACA4F,QAAA,CAAAiH,EAAA;gBAAA8B,UAAA,CAAA/T,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4R,KAAA;YAAA;cAGA;cACArB,MAAA,GAAAvF,QAAA,CAAAkH,IAAA,CAAAC,SAAA;cACAmB,OAAA,CAAA7W,aAAA,GAAA8T,MAAA;cACAC,OAAA,OAAA4B,WAAA;cACA3B,MAAA;cACAiD,cAAA,GAAAjO,IAAA,CAAAC,GAAA;cACAiO,cAAA;cACAC,UAAA,OAEA;cACA/C,aAAA,YAAAA,cAAA0B,UAAA;gBACA,IAAA4B,WAAA,GAAA1O,IAAA,CAAAC,GAAA;gBACA;gBACA,IAAAyO,WAAA,GAAAT,cAAA;kBACArD,UAAA,CAAA5B,OAAA,GAAA8D,UAAA;kBACAmB,cAAA,GAAAS,WAAA;kBACA;kBACAb,OAAA,CAAAxR,SAAA;oBACA,IAAAtG,YAAA,GAAA8X,OAAA,CAAApQ,KAAA,CAAA1H,YAAA;oBACA,IAAAA,YAAA;sBACAA,YAAA,CAAAwI,SAAA,GAAAxI,YAAA,CAAAkX,YAAA;oBACA;kBACA;gBACA;cACA,GAEA;cACAmB,aAAA;gBAAA,IAAAO,KAAA,OAAA5U,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAA2U,UAAA;kBAAA,IAAAC,mBAAA,EAAA9E,IAAA,EAAAhG,KAAA,EAAAyH,KAAA,EAAAsD,KAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAtD,IAAA,EAAAC,QAAA,EAAAsD,SAAA,EAAAC,eAAA,EAAAC,aAAA;kBAAA,WAAAnV,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAiV,WAAAC,UAAA;oBAAA,kBAAAA,UAAA,CAAA/U,IAAA,GAAA+U,UAAA,CAAA9U,IAAA;sBAAA;wBAAA8U,UAAA,CAAA/U,IAAA;sBAAA;wBAAA,KAEA;0BAAA+U,UAAA,CAAA9U,IAAA;0BAAA;wBAAA;wBAAA,KAEAsT,OAAA,CAAA9W,SAAA;0BAAAsY,UAAA,CAAA9U,IAAA;0BAAA;wBAAA;wBAAA,MACA,IAAA4R,KAAA;sBAAA;wBAAAkD,UAAA,CAAA9U,IAAA;wBAAA,OAGAuQ,MAAA,CAAAoC,IAAA;sBAAA;wBAAA2B,mBAAA,GAAAQ,UAAA,CAAA1P,IAAA;wBAAAoK,IAAA,GAAA8E,mBAAA,CAAA9E,IAAA;wBAAAhG,KAAA,GAAA8K,mBAAA,CAAA9K,KAAA;wBAAA,KACAgG,IAAA;0BAAAsF,UAAA,CAAA9U,IAAA;0BAAA;wBAAA;wBACA,IAAAyQ,MAAA,CAAAnO,MAAA;0BACAuO,aAAA,CAAAJ,MAAA;wBACA;wBAAA,OAAAqE,UAAA,CAAAlO,MAAA;sBAAA;wBAIAqK,KAAA,GAAAT,OAAA,CAAAuC,MAAA,CAAAvJ,KAAA;wBACA+K,KAAA,GAAAtD,KAAA,CAAAlD,KAAA,OAAAtN,MAAA,WAAA0Q,IAAA;0BAAA,OAAAA,IAAA,CAAA5P,IAAA;wBAAA;wBAAAiT,SAAA,OAAAO,2BAAA,CAAA3V,OAAA,EAEAmV,KAAA;wBAAAO,UAAA,CAAA/U,IAAA;wBAAAyU,SAAA,CAAAQ,CAAA;sBAAA;wBAAA,KAAAP,KAAA,GAAAD,SAAA,CAAAS,CAAA,IAAAzF,IAAA;0BAAAsF,UAAA,CAAA9U,IAAA;0BAAA;wBAAA;wBAAAmR,IAAA,GAAAsD,KAAA,CAAAjL,KAAA;wBAAAsL,UAAA,CAAA/U,IAAA;wBAEAqR,QAAA,GAAA/S,IAAA,CAAAuU,KAAA,CAAAzB,IAAA;wBAAA,IACAC,QAAA,CAAApG,QAAA;0BAAA8J,UAAA,CAAA9U,IAAA;0BAAA;wBAAA;wBAAA,OAAA8U,UAAA,CAAAlO,MAAA;sBAAA;wBAEAoE,SAAA,GAAAoG,QAAA,CAAApG,QAAA,EAEA;wBAAA,MACAA,SAAA,cAAAA,SAAA;0BAAA8J,UAAA,CAAA9U,IAAA;0BAAA;wBAAA;wBAAA,OAAA8U,UAAA,CAAAlO,MAAA;sBAAA;wBAIAgN,UAAA,IAAA5I,SAAA;;wBAEA;sBAAA;wBAAA,KACA;0BAAA8J,UAAA,CAAA9U,IAAA;0BAAA;wBAAA;wBACA2U,eAAA,GAAAf,UAAA,CAAAZ,OAAA;wBACA4B,aAAA,GAAAhB,UAAA,CAAAZ,OAAA;wBAAA,MAEA2B,eAAA,WAAAC,aAAA;0BAAAE,UAAA,CAAA9U,IAAA;0BAAA;wBAAA;wBACA;wBACA,KAAA2T,cAAA;0BACAlD,MAAA,IAAAmD,UAAA;0BACA;0BACA/C,aAAA,KAAA4B,cAAA,EAAAhC,MAAA,EAAA6C,OAAA,CAAA1X,eAAA;wBACA;wBACAgY,UAAA;wBAAA,OAAAkB,UAAA,CAAAlO,MAAA;sBAAA;wBAAA,MAEA+N,eAAA,WAAAC,aAAA;0BAAAE,UAAA,CAAA9U,IAAA;0BAAA;wBAAA;wBACA;wBACA2T,cAAA;wBACA,IAAAgB,eAAA;0BACAlE,MAAA,IAAAmD,UAAA,CAAA9N,SAAA,IAAA6O,eAAA;0BACA;0BACA9D,aAAA,KAAA4B,cAAA,EAAAhC,MAAA,EAAA6C,OAAA,CAAA1X,eAAA;wBACA;wBACAgY,UAAA,GAAAA,UAAA,CAAA9N,SAAA,CAAA6O,eAAA;wBAAA,OAAAG,UAAA,CAAAlO,MAAA;sBAAA;wBAAA,MAEA+N,eAAA,WAAAC,aAAA;0BAAAE,UAAA,CAAA9U,IAAA;0BAAA;wBAAA;wBACA;wBACA2T,cAAA;wBACAC,UAAA,GAAAA,UAAA,CAAA9N,SAAA,CAAA8O,aAAA;wBAAA,OAAAE,UAAA,CAAAlO,MAAA;sBAAA;wBAGA;wBACA,IAAA+N,eAAA;0BACAlE,MAAA,IAAAmD,UAAA,CAAA9N,SAAA,IAAA6O,eAAA;0BACA;0BACA9D,aAAA,KAAA4B,cAAA,EAAAhC,MAAA,EAAA6C,OAAA,CAAA1X,eAAA;wBACA;wBACAgY,UAAA,GAAAA,UAAA,CAAA9N,SAAA,CAAA8O,aAAA;wBACAjB,cAAA;wBAAA,OAAAmB,UAAA,CAAAlO,MAAA;sBAAA;wBAAAkO,UAAA,CAAA9U,IAAA;wBAAA;sBAAA;wBAAA8U,UAAA,CAAA9U,IAAA;wBAAA;sBAAA;wBAAA8U,UAAA,CAAA/U,IAAA;wBAAA+U,UAAA,CAAAhU,EAAA,GAAAgU,UAAA;wBAKA3W,OAAA,CAAA2U,IAAA;0BACA3B,IAAA,EAAAA,IAAA;0BACAjT,KAAA,EAAA4W,UAAA,CAAAhU,EAAA,CAAA/B;wBACA;sBAAA;wBAAA+V,UAAA,CAAA9U,IAAA;wBAAA;sBAAA;wBAAA8U,UAAA,CAAA9U,IAAA;wBAAA;sBAAA;wBAAA8U,UAAA,CAAA/U,IAAA;wBAAA+U,UAAA,CAAA3B,EAAA,GAAA2B,UAAA;wBAAAN,SAAA,CAAA3B,CAAA,CAAAiC,UAAA,CAAA3B,EAAA;sBAAA;wBAAA2B,UAAA,CAAA/U,IAAA;wBAAAyU,SAAA,CAAAU,CAAA;wBAAA,OAAAJ,UAAA,CAAAxO,MAAA;sBAAA;wBAAAwO,UAAA,CAAA9U,IAAA;wBAAA;sBAAA;wBAAA8U,UAAA,CAAA9U,IAAA;wBAAA;sBAAA;wBAAA8U,UAAA,CAAA/U,IAAA;wBAAA+U,UAAA,CAAA1B,EAAA,GAAA0B,UAAA;wBAAA,MAKAA,UAAA,CAAA1B,EAAA,CAAArU,OAAA;0BAAA+V,UAAA,CAAA9U,IAAA;0BAAA;wBAAA;wBAAA,MACA,IAAA4R,KAAA;sBAAA;wBAEAzT,OAAA,CAAAD,KAAA,eAAA4W,UAAA,CAAA1B,EAAA;wBAAA,MAAA0B,UAAA,CAAA1B,EAAA;sBAAA;sBAAA;wBAAA,OAAA0B,UAAA,CAAA/T,IAAA;oBAAA;kBAAA,GAAAsT,SAAA;gBAAA,CAGA;gBAAA,gBAzFAR,cAAA;kBAAA,OAAAO,KAAA,CAAAe,KAAA,OAAAC,SAAA;gBAAA;cAAA;cAAArB,UAAA,CAAA/T,IAAA;cAAA,OA2FA6T,aAAA;YAAA;cAAAE,UAAA,CAAA/T,IAAA;cAAA;YAAA;cAAA+T,UAAA,CAAAhU,IAAA;cAAAgU,UAAA,CAAAZ,EAAA,GAAAY,UAAA;cAAA,MAGAA,UAAA,CAAAZ,EAAA,CAAApU,OAAA;gBAAAgV,UAAA,CAAA/T,IAAA;gBAAA;cAAA;cACA7B,OAAA,CAAAsT,GAAA;cAAA,OAAAsC,UAAA,CAAAnN,MAAA;YAAA;cAGAzI,OAAA,CAAAD,KAAA,YAAA6V,UAAA,CAAAZ,EAAA;cACAG,OAAA,CAAAlV,QAAA,CAAAF,KAAA,CAAA6V,UAAA,CAAAZ,EAAA,CAAApU,OAAA;cACA,IAAAuU,OAAA,CAAA9X,YAAA;gBACA8X,OAAA,CAAA9X,YAAA,IAAAiT,OAAA;cACA;YAAA;cAAAsF,UAAA,CAAAhU,IAAA;cAEAuT,OAAA,CAAA7W,aAAA;cACA;cACA,IAAA6W,OAAA,CAAA/X,eAAA;gBACA+X,OAAA,CAAA7X,UAAA;gBACA6X,OAAA,CAAA/W,YAAA;cACA;cAAA,OAAAwX,UAAA,CAAAzN,MAAA;YAAA;YAAA;cAAA,OAAAyN,UAAA,CAAAhT,IAAA;UAAA;QAAA,GAAAwS,SAAA;MAAA;IAEA;IACA;IACA8B,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MAAA,WAAA9V,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAA6V,UAAA;QAAA,IAAAtF,gBAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAoF,WAAA,EAAAlF,MAAA,EAAAtF,QAAA,EAAAuF,MAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAgF,eAAA,EAAA5E,aAAA,EAAA6E,mBAAA,EAAAlG,IAAA,EAAAhG,KAAA,EAAAyH,KAAA,EAAAsD,KAAA,EAAAoB,UAAA,EAAAC,MAAA,EAAAzE,IAAA,EAAA9Y,IAAA,EAAAwd,iBAAA,EAAAzE,QAAA,EAAA3C,OAAA;QAAA,WAAAhP,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAkW,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAhW,IAAA,GAAAgW,UAAA,CAAA/V,IAAA;YAAA;cAAA,MACAsV,OAAA,CAAA5b,GAAA,CAAA4I,MAAA;gBAAAyT,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cAAA,OAAA+V,UAAA,CAAAnP,MAAA,WACA0O,OAAA,CAAAlX,QAAA;gBACAW,OAAA;gBACAC,IAAA;cACA;YAAA;cAAA,KAIAsW,OAAA,CAAA/Y,YAAA;gBAAAwZ,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cACAsV,OAAA,CAAA9Y,SAAA;cAAA,KACA8Y,OAAA,CAAA7Y,aAAA;gBAAAsZ,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cAAA+V,UAAA,CAAAhW,IAAA;cAAAgW,UAAA,CAAA/V,IAAA;cAAA,OAEAsV,OAAA,CAAA7Y,aAAA,CAAA+U,MAAA;YAAA;cAAAuE,UAAA,CAAA/V,IAAA;cAAA;YAAA;cAAA+V,UAAA,CAAAhW,IAAA;cAAAgW,UAAA,CAAAjV,EAAA,GAAAiV,UAAA;cAEA5X,OAAA,CAAAsT,GAAA,qOAAAsE,UAAA,CAAAjV,EAAA;YAAA;cAAAiV,UAAA,CAAA/V,IAAA;cAAA,OAIA,IAAAM,OAAA,WAAAoR,OAAA;gBAAA,OAAAlP,UAAA,CAAAkP,OAAA;cAAA;YAAA;cAGA4D,OAAA,CAAA/Y,YAAA;cACA+Y,OAAA,CAAA9Y,SAAA;cACA8Y,OAAA,CAAA/Z,eAAA;cACA+Z,OAAA,CAAA9Z,YAAA;cACA8Z,OAAA,CAAA7Z,UAAA;cAEAwU,gBAAA,GAAAqF,OAAA,CAAA9b,WAAA,CAAAiH,MAAA,WAAAvD,OAAA;gBAAA,OACAoY,OAAA,CAAA5b,GAAA,CAAAmH,QAAA,CAAA3D,OAAA,CAAAzE,EAAA;cAAA,CACA;cACAyX,MAAA,GAAAD,gBAAA,CACA3K,GAAA,WAAApI,OAAA;gBAAA,gBAAAsI,MAAA,CAAAtI,OAAA,CAAAuK,OAAA,IAAAvK,OAAA,CAAAyK,KAAA;cAAA,GACAhD,IAAA;cAAAoR,UAAA,CAAAhW,IAAA;cAAAgW,UAAA,CAAA/V,IAAA;cAAA,OAGA,IAAA2R,4BAAA,EAAA2D,OAAA,CAAA5b,GAAA,CAAAiL,IAAA;YAAA;cAAAwL,gBAAA,GAAA4F,UAAA,CAAA3Q,IAAA;cAAA,MACA,CAAA+K,gBAAA,CAAA9X,IAAA,KAAA8X,gBAAA,CAAA9X,IAAA,CAAAiK,MAAA;gBAAAyT,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4R,KAAA;YAAA;cAGAxB,eAAA,GAAAD,gBAAA,CAAA9X,IAAA,CACAiN,GAAA,WAAApI,OAAA,EAAAqI,KAAA;gBAAA,IAAAyQ,sBAAA,EAAAC,sBAAA;gBACA,IAAAtO,KAAA,GACA,EAAAqO,sBAAA,GAAA/F,gBAAA,CAAA1K,KAAA,eAAAyQ,sBAAA,uBAAAA,sBAAA,CAAAvO,OAAA,OAAAwO,sBAAA,GACAhG,gBAAA,CAAA1K,KAAA,eAAA0Q,sBAAA,uBAAAA,sBAAA,CAAAtO,KAAA,KACA;gBACA,IAAA8G,OAAA,GAAAvR,OAAA,CAAAuR,OAAA;gBACA,uBAAAjJ,MAAA,CAAAD,KAAA,yCAAAC,MAAA,CAAAmC,KAAA,gBAAAnC,MAAA,CAAAiJ,OAAA;cACA,GACA9J,IAAA,yDAEA;cACA2Q,OAAA,CAAA9Z,YAAA,CAAAyH,IAAA;gBACA8O,IAAA;gBACAtD,OAAA,qDAAAjJ,MAAA,CAAA8P,OAAA,CAAA5b,GAAA,CAAA4I,MAAA,gCAAAkD,MAAA,CAAA0K,MAAA;cACA;;cAEA;cACAG,WAAA;gBACA0B,IAAA;gBACAtD,OAAA;cACA;cACA6G,OAAA,CAAA9Z,YAAA,CAAAyH,IAAA,CAAAoN,WAAA;cACAiF,OAAA,CAAA7Z,UAAA;cAEA6U,MAAA,GACAgF,OAAA,CAAA3Y,eAAA,CACA2E,OAAA,kBAAAgU,OAAA,CAAA5b,GAAA,CAAA4I,MAAA,EACAhB,OAAA,6FAAAkE,MAAA,CACA4K,eAAA;cAAA2F,UAAA,CAAA/V,IAAA;cAAA,OAEA,IAAAkW,gBAAA,EAAA5F,MAAA;YAAA;cAAAtF,QAAA,GAAA+K,UAAA,CAAA3Q,IAAA;cAAA,KAEA4F,QAAA,CAAAiH,EAAA;gBAAA8D,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cACAuQ,MAAA,GAAAvF,QAAA,CAAAkH,IAAA,CAAAC,SAAA;cACAmD,OAAA,CAAA7Y,aAAA,GAAA8T,MAAA;cACAC,OAAA,OAAA4B,WAAA;cACA3B,MAAA;cACAiD,eAAA,GAAAjO,IAAA,CAAAC,GAAA;cAEAmL,aAAA,YAAAA,cAAA0B,UAAA;gBACA,IAAA4B,WAAA,GAAA1O,IAAA,CAAAC,GAAA;gBACA,IAAAyO,WAAA,GAAAT,eAAA;kBACArD,WAAA,CAAA5B,OAAA,GAAA8D,UAAA;kBACAmB,eAAA,GAAAS,WAAA;kBACAmB,OAAA,CAAAxT,SAAA;oBACA,IAAAtG,YAAA,GAAA8Z,OAAA,CAAApS,KAAA,CAAA1H,YAAA;oBACA,IAAAA,YAAA;sBACAA,YAAA,CAAAwI,SAAA,GAAAxI,YAAA,CAAAkX,YAAA;oBACA;kBACA;gBACA;cACA;YAAA;cAAA,KAEA;gBAAAqD,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cAAA,KAEAsV,OAAA,CAAA9Y,SAAA;gBAAAuZ,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4R,KAAA;YAAA;cAAAmE,UAAA,CAAA/V,IAAA;cAAA,OAGAuQ,MAAA,CAAAoC,IAAA;YAAA;cAAA+C,mBAAA,GAAAK,UAAA,CAAA3Q,IAAA;cAAAoK,IAAA,GAAAkG,mBAAA,CAAAlG,IAAA;cAAAhG,KAAA,GAAAkM,mBAAA,CAAAlM,KAAA;cAAA,KACAgG,IAAA;gBAAAuG,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cACA,IAAAyQ,MAAA,CAAAnO,MAAA;gBACAuO,aAAA,CAAAJ,MAAA;cACA;cAAA,OAAAsF,UAAA,CAAAnP,MAAA;YAAA;cAIAqK,KAAA,GAAAT,OAAA,CAAAuC,MAAA,CAAAvJ,KAAA;cAAAuM,UAAA,CAAAhW,IAAA;cAEAwU,KAAA,GAAAtD,KAAA,CAAAlD,KAAA;cAAA4H,UAAA,OAAAZ,2BAAA,CAAA3V,OAAA,EAEAmV,KAAA;cAAAwB,UAAA,CAAAhW,IAAA;cAAA4V,UAAA,CAAAX,CAAA;YAAA;cAAA,KAAAY,MAAA,GAAAD,UAAA,CAAAV,CAAA,IAAAzF,IAAA;gBAAAuG,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cAAAmR,IAAA,GAAAyE,MAAA,CAAApM,KAAA;cAAA,MACA,CAAA2H,IAAA,CAAA5P,IAAA,OAAA4P,IAAA,CAAA+B,UAAA;gBAAA6C,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cAAA,OAAA+V,UAAA,CAAAnP,MAAA;YAAA;cAEAvO,IAAA,GAAA8Y,IAAA,CAAA8B,KAAA;cAAA,MACA5a,IAAA;gBAAA0d,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cAAA,OAAA+V,UAAA,CAAAnP,MAAA;YAAA;cAAAmP,UAAA,CAAAhW,IAAA;cAGAqR,QAAA,GAAA/S,IAAA,CAAAuU,KAAA,CAAAva,IAAA;cAAA,OAAAwd,iBAAA,GACAzE,QAAA,CAAA+E,OAAA,cAAAN,iBAAA,gBAAAA,iBAAA,GAAAA,iBAAA,iBAAAA,iBAAA,gBAAAA,iBAAA,GAAAA,iBAAA,CAAAO,KAAA,cAAAP,iBAAA,eAAAA,iBAAA,CAAApH,OAAA;gBAAAsH,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cACAyO,OAAA,GAAA2C,QAAA,CAAA+E,OAAA,IAAAC,KAAA,CAAA3H,OAAA,EAEA;cAAA,MACAA,OAAA,cAAAA,OAAA;gBAAAsH,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cAAA,OAAA+V,UAAA,CAAAnP,MAAA;YAAA;cAIA6J,MAAA,IAAAhC,OAAA;cACAoC,aAAA,CAAAJ,MAAA;YAAA;cAAAsF,UAAA,CAAA/V,IAAA;cAAA;YAAA;cAAA+V,UAAA,CAAAhW,IAAA;cAAAgW,UAAA,CAAA5C,EAAA,GAAA4C,UAAA;cAGA5X,OAAA,CAAAD,KAAA,wBAAA6X,UAAA,CAAA5C,EAAA;YAAA;cAAA4C,UAAA,CAAA/V,IAAA;cAAA;YAAA;cAAA+V,UAAA,CAAA/V,IAAA;cAAA;YAAA;cAAA+V,UAAA,CAAAhW,IAAA;cAAAgW,UAAA,CAAA3C,EAAA,GAAA2C,UAAA;cAAAJ,UAAA,CAAA9C,CAAA,CAAAkD,UAAA,CAAA3C,EAAA;YAAA;cAAA2C,UAAA,CAAAhW,IAAA;cAAA4V,UAAA,CAAAT,CAAA;cAAA,OAAAa,UAAA,CAAAzP,MAAA;YAAA;cAAAyP,UAAA,CAAA/V,IAAA;cAAA;YAAA;cAAA+V,UAAA,CAAAhW,IAAA;cAAAgW,UAAA,CAAAM,EAAA,GAAAN,UAAA;cAIA5X,OAAA,CAAAD,KAAA,4BAAA6X,UAAA,CAAAM,EAAA;YAAA;cAAAN,UAAA,CAAA/V,IAAA;cAAA;YAAA;cAAA+V,UAAA,CAAA/V,IAAA;cAAA;YAAA;cAAA,MAIA,IAAA4R,KAAA;YAAA;cAAAmE,UAAA,CAAA/V,IAAA;cAAA;YAAA;cAAA+V,UAAA,CAAAhW,IAAA;cAAAgW,UAAA,CAAAO,EAAA,GAAAP,UAAA;cAAA,MAIAA,UAAA,CAAAO,EAAA,CAAAvX,OAAA;gBAAAgX,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cACA7B,OAAA,CAAAsT,GAAA;cAAA,OAAAsE,UAAA,CAAAnP,MAAA;YAAA;cAGAzI,OAAA,CAAAD,KAAA,mBAAA6X,UAAA,CAAAO,EAAA;cACAhB,OAAA,CAAAlX,QAAA,CAAAF,KAAA;cACA,IAAAoX,OAAA,CAAA9Z,YAAA;gBACA8Z,OAAA,CAAA9Z,YAAA,IAAAiT,OAAA;cACA;YAAA;cAAAsH,UAAA,CAAAhW,IAAA;cAEAuV,OAAA,CAAA7Y,aAAA;cACA;cACA,IAAA6Y,OAAA,CAAA/Z,eAAA;gBACA+Z,OAAA,CAAA7Z,UAAA;gBACA6Z,OAAA,CAAA/Y,YAAA;cACA;cAAA,OAAAwZ,UAAA,CAAAzP,MAAA;YAAA;YAAA;cAAA,OAAAyP,UAAA,CAAAhV,IAAA;UAAA;QAAA,GAAAwU,SAAA;MAAA;IAEA;IACA;IACAgB,aAAA,WAAAA,cAAA;MACA,KAAA/Z,SAAA;MACA,SAAAC,aAAA;QACA,KAAAA,aAAA,CAAA+U,MAAA;MACA;MACA,KAAAjW,eAAA;MACA,KAAAC,YAAA;MACA,KAAAC,UAAA;MACA,KAAAc,YAAA;MACA,KAAAE,aAAA;IACA;IACA+Z,aAAA,WAAAA,cAAA;MACA,SAAA9Z,UAAA;QACA,KAAAmT,UAAA;MACA,gBAAAnT,UAAA;QACA,KAAA2W,YAAA;MACA,gBAAA3W,UAAA;QACA,KAAA2Y,cAAA;MACA;IACA;IACAoB,WAAA,WAAAA,YAAA;MACA,SAAA/Z,UAAA;QACA,KAAAga,eAAA;MACA,gBAAAha,UAAA;QACA,KAAAia,mBAAA;MACA;IACA;IACA;IACAD,eAAA,WAAAA,gBAAA;MAAA,IAAAE,OAAA;MAAA,WAAApX,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAmX,UAAA;QAAA,IAAAC,aAAA,EAAAC,QAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,UAAA,EAAAvC,eAAA,EAAAC,aAAA,EAAAuC,SAAA;QAAA,WAAA1X,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAwX,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAtX,IAAA,GAAAsX,UAAA,CAAArX,IAAA;YAAA;cAAA,MAEA4W,OAAA,CAAAld,GAAA,CAAA4I,MAAA;gBAAA+U,UAAA,CAAArX,IAAA;gBAAA;cAAA;cACA4W,OAAA,CAAAxY,QAAA,CAAA0Q,OAAA;cAAA,OAAAuI,UAAA,CAAAzQ,MAAA;YAAA;cAAA,MAIAgQ,OAAA,CAAAld,GAAA,CAAA4I,MAAA;gBAAA+U,UAAA,CAAArX,IAAA;gBAAA;cAAA;cACA4W,OAAA,CAAAxY,QAAA,CAAA0Q,OAAA;cAAA,OAAAuI,UAAA,CAAAzQ,MAAA;YAAA;cAIA;cACAgQ,OAAA,CAAA/Z,kBAAA;cACA+Z,OAAA,CAAA7Z,YAAA;;cAEA;cACA,IAAA6Z,OAAA,CAAA1T,KAAA,CAAAoU,YAAA;gBACAV,OAAA,CAAA1T,KAAA,CAAAoU,YAAA,CAAAC,SAAA;cACA;cAAAF,UAAA,CAAAtX,IAAA;cAAAsX,UAAA,CAAArX,IAAA;cAAA,OAIA8K,cAAA,CAAA0M,QAAA,CAAAZ,OAAA,CAAAld,GAAA;YAAA;cAAAod,aAAA,GAAAO,UAAA,CAAAjS,IAAA;cAAA,MACA,CAAA0R,aAAA,CAAAze,IAAA,KAAAye,aAAA,CAAAze,IAAA,CAAAoW,OAAA;gBAAA4I,UAAA,CAAArX,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4R,KAAA;YAAA;cAAAyF,UAAA,CAAArX,IAAA;cAAA,OAIA,IAAAgS,YAAA,EACA8E,aAAA,CAAAze,IAAA,CAAAoW,OAAA,EACA,YACA,mBACA;YAAA;cAJAsI,QAAA,GAAAM,UAAA,CAAAjS,IAAA;cAAA,IAMA2R,QAAA,CAAA9E,EAAA;gBAAAoF,UAAA,CAAArX,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4R,KAAA;YAAA;cAAAyF,UAAA,CAAArX,IAAA;cAAA,OAGA+W,QAAA,CAAAU,IAAA;YAAA;cAAAT,MAAA,GAAAK,UAAA,CAAAjS,IAAA;cAAA,MACA,CAAA4R,MAAA,KAAAA,MAAA,CAAA3F,MAAA;gBAAAgG,UAAA,CAAArX,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4R,KAAA;YAAA;cAGA;cACAqF,QAAA;cAEA;gBACA;gBACAC,UAAA,GAAA7Y,IAAA,CAAAuU,KAAA,CAAAoE,MAAA,CAAA3F,MAAA;gBACA4F,QAAA,GACAC,UAAA,CAAA7F,MAAA,IACA6F,UAAA,CAAAQ,IAAA,IACAR,UAAA,CAAAzI,OAAA,IACAuI,MAAA,CAAA3F,MAAA;cACA,SAAAwB,CAAA;gBACA;gBACAoE,QAAA,GAAAD,MAAA,CAAA3F,MAAA;cACA;;cAEA;cACAsD,eAAA,GAAAsC,QAAA,CAAAjE,OAAA;cACA4B,aAAA,GAAAqC,QAAA,CAAAjE,OAAA,cAEA;cACA,IAAA2B,eAAA,WAAAC,aAAA;gBACA;gBACAqC,QAAA,GAAAA,QAAA,CAAAnR,SAAA,CAAA8O,aAAA,MAAArT,IAAA;cACA;;cAEA;cACA0V,QAAA,GAAAA;cACA;cAAA,CACA3V,OAAA;cACA;cAAA,CACAA,OAAA;cACA;cAAA,CACAA,OAAA;cACA;cAAA,CACAC,IAAA;;cAEA;cAAA,MACA,CAAA0V,QAAA,IAAAA,QAAA,CAAA3U,MAAA;gBAAA+U,UAAA,CAAArX,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4R,KAAA;YAAA;cAGA;cACA;cACAuF,SAAA,GAAAF,QAAA,EAEA;cACA;cACAE,SAAA,GAAAA,SAAA,CAAA7V,OAAA,CACA,iDACA,WACA;cACA;cACA6V,SAAA,GAAAA,SAAA,CAAA7V,OAAA,CACA,qDACA,aACA;cACA6V,SAAA,GAAAA,SAAA,CAAA7V,OAAA,CACA,qDACA,aACA;cACA;cACA6V,SAAA,GAAAA,SAAA,CAAA7V,OAAA,CACA,+DACA,WACA;cACA;cACA6V,SAAA,GAAAA,SAAA,CAAA7V,OAAA,CACA,8FACA,WACA;cAEA,KAAA6V,SAAA,CAAAtW,QAAA,kBAAAsW,SAAA,CAAAtW,QAAA;gBACA;gBACAsW,SAAA,GACA,oBACA,WACA,WACA,6BACA,6EACA,yCACA,YACA,WACA,OACAF,QAAA,GACA,YACA;cACA,WACA,CAAAE,SAAA,CAAAtW,QAAA,eACAsW,SAAA,CAAAtW,QAAA,aACA;gBACA;gBACAsW,SAAA,GAAAA,SAAA,CAAA7V,OAAA,CACA,UACA,iDACA;cACA;;cAEA;cACAsV,OAAA,CAAA9U,SAAA;gBACA,IAAA8U,OAAA,CAAA1T,KAAA,CAAAoU,YAAA;kBACA;kBACA,IAAAV,OAAA,CAAA5Z,kBAAA;oBACA;sBACA4Z,OAAA,CAAA5Z,kBAAA,CAAA2a,MAAA;sBACAf,OAAA,CAAA5Z,kBAAA,CAAA4a,OAAA;oBACA,SAAA/E,CAAA;sBACA1U,OAAA,CAAAD,KAAA,mBAAA2U,CAAA;oBACA;kBACA;;kBAEA;kBACA,IAAAgF,MAAA,GAAA/T,QAAA,CAAAgU,aAAA;kBACAD,MAAA,CAAAE,KAAA,CAAAC,KAAA;kBACAH,MAAA,CAAAE,KAAA,CAAAE,MAAA;kBACAJ,MAAA,CAAAE,KAAA,CAAAG,MAAA;kBACAL,MAAA,CAAAE,KAAA,CAAAI,OAAA;kBACAN,MAAA,CAAAE,KAAA,CAAAK,QAAA;;kBAEA;kBACAxB,OAAA,CAAA5Z,kBAAA,GAAA6a,MAAA;;kBAEA;kBACAjB,OAAA,CAAA1T,KAAA,CAAAoU,YAAA,CAAAC,SAAA;kBACAX,OAAA,CAAA1T,KAAA,CAAAoU,YAAA,CAAAe,WAAA,CAAAR,MAAA;;kBAEA;kBACAA,MAAA,CAAAF,MAAA;oBACA;sBACA;sBACA,IACA,CAAAE,MAAA,CAAAS,aAAA,CAAAC,KAAA,IACA,CAAAV,MAAA,CAAAS,aAAA,CAAAxU,QAAA,CAAAC,aAAA,CACA,2BACA,GACA;wBACA;wBACA,IAAAyU,WAAA,GACAX,MAAA,CAAAS,aAAA,CAAAxU,QAAA,CAAAgU,aAAA;wBACAU,WAAA,CAAAC,GAAA;wBACAZ,MAAA,CAAAS,aAAA,CAAAxU,QAAA,CAAA4U,IAAA,CAAAL,WAAA,CAAAG,WAAA;;wBAEA;wBACAA,WAAA,CAAAb,MAAA;0BACAf,OAAA,CAAA+B,oBAAA,CAAAd,MAAA;wBACA;;wBAEA;wBACAW,WAAA,CAAAZ,OAAA;0BACAzZ,OAAA,CAAAD,KAAA;0BACA0Y,OAAA,CAAA7Z,YAAA;wBACA;sBACA;wBACA;wBACA6Z,OAAA,CAAA+B,oBAAA,CAAAd,MAAA;sBACA;oBACA,SAAAhF,CAAA;sBACA1U,OAAA,CAAAD,KAAA,YAAA2U,CAAA;sBACA+D,OAAA,CAAA7Z,YAAA;oBACA;kBACA;;kBAEA;kBACA8a,MAAA,CAAAD,OAAA;oBACAzZ,OAAA,CAAAD,KAAA;oBACA0Y,OAAA,CAAA7Z,YAAA;kBACA;;kBAEA;kBACA,IAAA6b,GAAA,GAAAf,MAAA,CAAAS,aAAA,CAAAxU,QAAA;kBACA8U,GAAA,CAAA/L,IAAA;kBACA+L,GAAA,CAAAC,KAAA,CAAA1B,SAAA;kBACAyB,GAAA,CAAAE,KAAA;gBACA;cACA;cAAAzB,UAAA,CAAArX,IAAA;cAAA;YAAA;cAAAqX,UAAA,CAAAtX,IAAA;cAAAsX,UAAA,CAAAvW,EAAA,GAAAuW,UAAA;cAEAlZ,OAAA,CAAAD,KAAA,YAAAmZ,UAAA,CAAAvW,EAAA;cACA8V,OAAA,CAAAxY,QAAA,CAAAF,KAAA,CAAAmZ,UAAA,CAAAvW,EAAA,CAAA/B,OAAA;cACA6X,OAAA,CAAAmC,gBAAA;YAAA;YAAA;cAAA,OAAA1B,UAAA,CAAAtW,IAAA;UAAA;QAAA,GAAA8V,SAAA;MAAA;IAEA;IACA;IACAF,mBAAA,WAAAA,oBAAA;MAAA,IAAAqC,OAAA;MAAA,WAAAxZ,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAuZ,UAAA;QAAA,IAAAnC,aAAA,EAAAxG,MAAA,EAAAyG,QAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAE,SAAA;QAAA,WAAA1X,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAsZ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAApZ,IAAA,GAAAoZ,UAAA,CAAAnZ,IAAA;YAAA;cAAA,MAEAgZ,OAAA,CAAAtf,GAAA,CAAA4I,MAAA;gBAAA6W,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cACAgZ,OAAA,CAAA5a,QAAA,CAAA0Q,OAAA;cAAA,OAAAqK,UAAA,CAAAvS,MAAA;YAAA;cAAA,MAIAoS,OAAA,CAAAtf,GAAA,CAAA4I,MAAA;gBAAA6W,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cACAgZ,OAAA,CAAA5a,QAAA,CAAA0Q,OAAA;cAAA,OAAAqK,UAAA,CAAAvS,MAAA;YAAA;cAIA;cACAoS,OAAA,CAAAnc,kBAAA;cACAmc,OAAA,CAAAjc,YAAA;;cAEA;cACA,IAAAic,OAAA,CAAA9V,KAAA,CAAAoU,YAAA;gBACA0B,OAAA,CAAA9V,KAAA,CAAAoU,YAAA,CAAAC,SAAA;cACA;cAAA4B,UAAA,CAAApZ,IAAA;cAAAoZ,UAAA,CAAAnZ,IAAA;cAAA,OAIA8K,cAAA,CAAA0M,QAAA,CAAAwB,OAAA,CAAAtf,GAAA;YAAA;cAAAod,aAAA,GAAAqC,UAAA,CAAA/T,IAAA;cAAA,MACA,CAAA0R,aAAA,CAAAze,IAAA,KAAAye,aAAA,CAAAze,IAAA,CAAAoW,OAAA;gBAAA0K,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4R,KAAA;YAAA;cAGAtB,MAAA,GAAA0I,OAAA,CAAA5b,WAAA,UAAAoI,MAAA,CAAAsR,aAAA,CAAAze,IAAA,CAAAoW,OAAA,GAEA;cAAA0K,UAAA,CAAAnZ,IAAA;cAAA,OACA,IAAAkW,gBAAA,EAAA5F,MAAA;YAAA;cAAAyG,QAAA,GAAAoC,UAAA,CAAA/T,IAAA;cAAA,IAEA2R,QAAA,CAAA9E,EAAA;gBAAAkH,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4R,KAAA;YAAA;cAAAuH,UAAA,CAAAnZ,IAAA;cAAA,OAGA+W,QAAA,CAAAU,IAAA;YAAA;cAAAT,MAAA,GAAAmC,UAAA,CAAA/T,IAAA;cAAA,MACA,CAAA4R,MAAA,KAAAA,MAAA,CAAAb,OAAA;gBAAAgD,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4R,KAAA;YAAA;cAGA;cACAqF,QAAA,GAAAD,MAAA,CAAAb,OAAA,IAAApX,OAAA,CAAA0P,OAAA,EAEA;cACAwI,QAAA,GAAAA;cACA;cAAA,CACA3V,OAAA;cACA;cAAA,CACAA,OAAA;cACA;cAAA,CACAA,OAAA;cACA;cAAA,CACAC,IAAA;;cAEA;cAAA,MACA,CAAA0V,QAAA,IAAAA,QAAA,CAAA3U,MAAA;gBAAA6W,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4R,KAAA;YAAA;cAGA;cACA;cACAuF,SAAA,GAAAF,QAAA,EAEA;cACA;cACAE,SAAA,GAAAA,SAAA,CAAA7V,OAAA,CACA,iDACA,WACA;cACA;cACA6V,SAAA,GAAAA,SAAA,CAAA7V,OAAA,CACA,qDACA,aACA;cACA6V,SAAA,GAAAA,SAAA,CAAA7V,OAAA,CACA,qDACA,aACA;cACA;cACA6V,SAAA,GAAAA,SAAA,CAAA7V,OAAA,CACA,+DACA,WACA;cACA;cACA6V,SAAA,GAAAA,SAAA,CAAA7V,OAAA,CACA,8FACA,WACA;cAEA,KAAA6V,SAAA,CAAAtW,QAAA,kBAAAsW,SAAA,CAAAtW,QAAA;gBACA;gBACAsW,SAAA,GACA,oBACA,WACA,WACA,6BACA,6EACA,yCACA,YACA,WACA,OACAF,QAAA,GACA,YACA;cACA,WACA,CAAAE,SAAA,CAAAtW,QAAA,eACAsW,SAAA,CAAAtW,QAAA,aACA;gBACA;gBACAsW,SAAA,GAAAA,SAAA,CAAA7V,OAAA,CACA,UACA,iDACA;cACA;;cAEA;cACA0X,OAAA,CAAAlX,SAAA;gBACA,IAAAkX,OAAA,CAAA9V,KAAA,CAAAoU,YAAA;kBACA;kBACA,IAAA0B,OAAA,CAAAhc,kBAAA;oBACA;sBACAgc,OAAA,CAAAhc,kBAAA,CAAA2a,MAAA;sBACAqB,OAAA,CAAAhc,kBAAA,CAAA4a,OAAA;oBACA,SAAA/E,CAAA;sBACA1U,OAAA,CAAAD,KAAA,mBAAA2U,CAAA;oBACA;kBACA;;kBAEA;kBACA,IAAAgF,MAAA,GAAA/T,QAAA,CAAAgU,aAAA;kBACAD,MAAA,CAAAE,KAAA,CAAAC,KAAA;kBACAH,MAAA,CAAAE,KAAA,CAAAE,MAAA;kBACAJ,MAAA,CAAAE,KAAA,CAAAG,MAAA;kBACAL,MAAA,CAAAE,KAAA,CAAAI,OAAA;kBACAN,MAAA,CAAAE,KAAA,CAAAK,QAAA;;kBAEA;kBACAY,OAAA,CAAAhc,kBAAA,GAAA6a,MAAA;;kBAEA;kBACAmB,OAAA,CAAA9V,KAAA,CAAAoU,YAAA,CAAAC,SAAA;kBACAyB,OAAA,CAAA9V,KAAA,CAAAoU,YAAA,CAAAe,WAAA,CAAAR,MAAA;;kBAEA;kBACAA,MAAA,CAAAF,MAAA;oBACA;sBACA;sBACA,IACA,CAAAE,MAAA,CAAAS,aAAA,CAAAC,KAAA,IACA,CAAAV,MAAA,CAAAS,aAAA,CAAAxU,QAAA,CAAAC,aAAA,CACA,2BACA,GACA;wBACA;wBACA,IAAAyU,WAAA,GACAX,MAAA,CAAAS,aAAA,CAAAxU,QAAA,CAAAgU,aAAA;wBACAU,WAAA,CAAAC,GAAA;wBACAZ,MAAA,CAAAS,aAAA,CAAAxU,QAAA,CAAA4U,IAAA,CAAAL,WAAA,CAAAG,WAAA;;wBAEA;wBACAA,WAAA,CAAAb,MAAA;0BACAqB,OAAA,CAAAL,oBAAA,CAAAd,MAAA;wBACA;;wBAEA;wBACAW,WAAA,CAAAZ,OAAA;0BACAzZ,OAAA,CAAAD,KAAA;0BACA8a,OAAA,CAAAjc,YAAA;wBACA;sBACA;wBACA;wBACAic,OAAA,CAAAL,oBAAA,CAAAd,MAAA;sBACA;oBACA,SAAAhF,CAAA;sBACA1U,OAAA,CAAAD,KAAA,YAAA2U,CAAA;sBACAmG,OAAA,CAAAjc,YAAA;oBACA;kBACA;;kBAEA;kBACA8a,MAAA,CAAAD,OAAA;oBACAzZ,OAAA,CAAAD,KAAA;oBACA8a,OAAA,CAAAjc,YAAA;kBACA;;kBAEA;kBACA,IAAA6b,GAAA,GAAAf,MAAA,CAAAS,aAAA,CAAAxU,QAAA;kBACA8U,GAAA,CAAA/L,IAAA;kBACA+L,GAAA,CAAAC,KAAA,CAAA1B,SAAA;kBACAyB,GAAA,CAAAE,KAAA;gBACA;cACA;cAAAK,UAAA,CAAAnZ,IAAA;cAAA;YAAA;cAAAmZ,UAAA,CAAApZ,IAAA;cAAAoZ,UAAA,CAAArY,EAAA,GAAAqY,UAAA;cAEAhb,OAAA,CAAAD,KAAA,YAAAib,UAAA,CAAArY,EAAA;cACAkY,OAAA,CAAA5a,QAAA,CAAAF,KAAA,CAAAib,UAAA,CAAArY,EAAA,CAAA/B,OAAA;cACAia,OAAA,CAAAD,gBAAA;YAAA;YAAA;cAAA,OAAAI,UAAA,CAAApY,IAAA;UAAA;QAAA,GAAAkY,SAAA;MAAA;IAEA;IACA;IACAF,gBAAA,WAAAA,iBAAA;MACA,KAAAvc,SAAA;MACA,KAAAK,kBAAA;MACA,KAAAC,SAAA;MACA,KAAAC,YAAA;MACA,KAAAR,YAAA;;MAEA;MACA,SAAAS,kBAAA,SAAAA,kBAAA,CAAAsb,aAAA;QACA;UACA;UACA,SAAAtb,kBAAA,CAAAsb,aAAA,CAAAC,KAAA;YACA,IAAAa,SAAA,GACA,KAAApc,kBAAA,CAAAsb,aAAA,CAAAC,KAAA,CAAAa,SAAA;YACA,IAAAA,SAAA;cACAC,MAAA,CAAAC,MAAA,CAAAF,SAAA,EAAAzW,OAAA,WAAA4W,QAAA;gBACA,IAAAA,QAAA,WAAAA,QAAA,CAAAC,OAAA;kBACAD,QAAA,CAAAC,OAAA;gBACA;cACA;YACA;UACA;QACA,SAAA3G,CAAA;UACA1U,OAAA,CAAAD,KAAA,iBAAA2U,CAAA;QACA;MACA;;MAEA;MACA,SAAA3P,KAAA,CAAAoU,YAAA;QACA,KAAApU,KAAA,CAAAoU,YAAA,CAAAC,SAAA;MACA;;MAEA;MACA,SAAAva,kBAAA;QACA;UACA,KAAAA,kBAAA,CAAA2a,MAAA;UACA,KAAA3a,kBAAA,CAAA4a,OAAA;UACA,KAAA5a,kBAAA;QACA,SAAA6V,CAAA;UACA1U,OAAA,CAAAD,KAAA,gBAAA2U,CAAA;QACA;MACA;IACA;IACA;IACA8F,oBAAA,WAAAA,qBAAAd,MAAA;MAAA,IAAA4B,OAAA;MACA;MACAtb,OAAA,CAAAsT,GAAA;;MAEA;MACAjP,UAAA;QACAiX,OAAA,CAAA1c,YAAA;MACA;IACA;IAEA;IACA6K,mBAAA,WAAAA,oBAAA8R,QAAA;MACA,KAAAA,QAAA,IAAAA,QAAA,CAAApX,MAAA;QACA,OAAAoX,QAAA;MACA;MAEA,IAAAC,QAAA,OAAAC,GAAA;MACA,IAAA9L,MAAA;;MAEA;MACA4L,QAAA,CAAA/W,OAAA,WAAAzF,OAAA;QACA;QACA,IAAA2c,UAAA,GAAA3c,OAAA,CAAAyK,KAAA,GACAzK,OAAA,CAAAyK,KAAA,CAAArG,OAAA,iBAAAA,OAAA,eACA;QAEA,IAAAqY,QAAA,CAAAhR,GAAA,CAAAkR,UAAA;UACAF,QAAA,CAAAG,GAAA,CAAAD,UAAA,EAAA7T,KAAA;QACA;UACA2T,QAAA,CAAAI,GAAA,CAAAF,UAAA;YACA3c,OAAA,MAAAiC,cAAA,CAAAC,OAAA,MAAAlC,OAAA;YACA8I,KAAA;YACAgU,aAAA,EAAA9c,OAAA,CAAAyK,KAAA;UACA;QACA;MACA;;MAEA;MACAgS,QAAA,CAAAhX,OAAA,WAAAsX,KAAA;QAAA,IAAA/c,OAAA,GAAA+c,KAAA,CAAA/c,OAAA;UAAA8I,KAAA,GAAAiU,KAAA,CAAAjU,KAAA;UAAAgU,aAAA,GAAAC,KAAA,CAAAD,aAAA;QACA,IAAAhU,KAAA;UACA;UACA;UACA9I,OAAA,CAAAyK,KAAA,MAAAnC,MAAA,CAAAwU,aAAA,kBAAAxU,MAAA,CAAAQ,KAAA;QACA;QACA8H,MAAA,CAAA7K,IAAA,CAAA/F,OAAA;MACA;MAEA,OAAA4Q,MAAA;IACA;EACA;AACA", "ignoreList": []}]}