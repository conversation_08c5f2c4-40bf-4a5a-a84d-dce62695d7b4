<template>
  <div v-if="funEsSeach">
    <splitpanes class="default-theme">
      <pane class="leftLink" ref="leftLink" min-size="20" max-size="50" size="25">
        <div class="treeMain" style="width: 100%;margin:0;">
          <div style="display:flex;justify-content:space-between;align-items:center;gap:10px">
            <el-input placeholder="输入关键字进行过滤" v-model="filterText" clearable class="input_Fixed">
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
          </div>
          <div class="treeBox">
            <el-tree :data="treeData" ref="tree" show-checkbox node-key="id" :default-expanded-keys="[1000]"
              @check-change="checkChange" :expand-on-click-node="false" :filter-node-method="filterNode">
              <template slot-scope="scoped">
                <div v-if="scoped.data.label != ''"><span>{{ scoped.data.label }}</span><b>{{ `${scoped.data.count ?
                  `(${scoped.data.count})` : ''}`
                }}</b></div>
                <div v-else>
                  {{ scoped.data.label }}
                  <div
                    style="position: absolute;z-index: 99;right: 10px;top: -7px;height: 35px;display: flex;align-items: center;">
                    <el-select v-model="treeQuery.isStability" size="mini" placeholder="请选择稳定源"
                      style="width: 60px;height: 20px;margin: 0px 10px 0 0;" class="treeQuery">
                      <el-option label="全部" :value="null"></el-option>
                      <el-option label="稳定源" :value="1"></el-option>
                      <el-option label="不稳定源" :value="0"></el-option>
                    </el-select>
                    <el-select v-model="sortMode" size="mini" placeholder="请选择排序方式" @change="treeSlot"
                      style="width: 60px;height: 20px;margin: 0px 10px 0 0;" class="treeQuery">
                      <el-option label="默认排序" :value="0"></el-option>
                      <el-option label="按数量倒向排序" :value="1"></el-option>
                      <el-option label="按数量正向排序" :value="2"></el-option>
                    </el-select>
                    <el-select v-model="treeQuery.industry" size="mini" placeholder="请选择行业"
                      style="width: 80px;height: 20px;margin: 0px 10px 0 0;" class="treeQuery" clearable>
                      <el-option v-for="item in industryList" :key="item.value" :label="item.industryName"
                        :value="item.id">
                      </el-option>
                    </el-select>
                    <el-select v-model="treeQuery.domain" size="mini" placeholder="请选择领域"
                      style="width: 80px;height: 20px;" class="treeQuery" clearable>
                      <el-option v-for="item in domainList" :key="item.value" :label="item.fieldName" :value="item.id">
                      </el-option>
                    </el-select>
                    <el-tooltip class="item" effect="dark" content="重置" placement="top">
                      <i class="el-input__icon el-icon-refresh" @click="treeClear"></i>
                    </el-tooltip>
                  </div>
                </div>
              </template>
            </el-tree>
          </div>
        </div>
      </pane>
      <pane min-size="50" max-size="80" size="75">
        <div class="rightMain" style="margin-left: 0;overflow: auto;">
          <div class="toolBox">
            <div class="title" :style="{ height: ActiveData.title ? '' : '50px' }">
              <p v-if="ActiveData.title">{{ ActiveData.title }}</p>
              <p v-else></p>
            </div>
            <div class="mainTool">
              <p>
                发布日期:
                <el-button size="mini" :type="SeachData.timeRange == '' ? 'primary' : ''"
                  @click="SeachData.timeRange = ''">24小时</el-button>
                <el-button size="mini" :type="SeachData.timeRange == 1 ? 'primary' : ''"
                  @click="SeachData.timeRange = 1">今天</el-button>
                <el-button size="mini" :type="SeachData.timeRange == 2 ? 'primary' : ''"
                  @click="SeachData.timeRange = 2">昨天</el-button>
                <el-button size="mini" :type="SeachData.timeRange == 4 ? 'primary' : ''"
                  @click="SeachData.timeRange = 4">近7天</el-button>
                <el-button size="mini" :type="SeachData.timeRange == 5 ? 'primary' : ''"
                  @click="SeachData.timeRange = 5">近30天</el-button>
                <el-button size="mini" :type="SeachData.timeRange == 6 ? 'primary' : ''"
                  @click="SeachData.timeRange = 6">自定义</el-button>
                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="SeachData.customDay"
                  v-if="SeachData.timeRange == 6" style="display: inline-block; width: 320px; margin-left: 10px"
                  size="mini" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  unlink-panels clearable></el-date-picker>
              </p>
              <p>
                采集日期:
                <el-button size="mini" :type="SeachData.collectionDateType == 0 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 0">24小时</el-button>
                <el-button size="mini" :type="SeachData.collectionDateType == 1 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 1">今天</el-button>
                <el-button size="mini" :type="SeachData.collectionDateType == 2 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 2">昨天</el-button>
                <el-button size="mini" :type="SeachData.collectionDateType == 4 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 4">近7天</el-button>
                <el-button size="mini" :type="SeachData.collectionDateType == 5 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 5">近30天</el-button>
                <el-button size="mini" :type="SeachData.collectionDateType == 6 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 6">自定义</el-button>
                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="SeachData.collectionTime"
                  v-if="SeachData.collectionDateType == 6" style="display: inline-block; width: 320px; margin-left: 10px"
                  size="mini" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  unlink-panels clearable></el-date-picker>
              </p>
              <p>
                <span style="width:60px;display:inline-block;text-align:right;margin-right:5px">关键词:</span>
                <el-input placeholder="请输入关键词,使用逗号分割(英文)" style="width:430px" v-model="SeachData.keyword"></el-input>
              </p>
            </div>
            <div class="btn">
              <el-button size="mini" @click="resetting">重置</el-button>
              <el-button type="primary" size="mini" @click="funEsSeach" :loading="buttonDisabled">搜索</el-button>
            </div>
          </div>
          <MainArticle v-loading="buttonDisabled" :flag="'MonitorUse'" :currentPage="currentPage" :pageSize="pageSize"
            :total="total" :ArticleList="ArticleList" :keywords="SeachData.keyword"
            @handleCurrentChange="handleCurrentChange" @handleSizeChange="handleSizeChange" @Refresh="funEsSeach"
            :SeachData="SeachData"></MainArticle>
        </div>
      </pane>
    </splitpanes>
  </div>
</template>

<script>
import api from '@/api/ScienceApi/index.js'
import topSeach from '@/views/components/topSeach.vue'
import MainArticle from '../components/MainArticle.vue'

import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'

export default {
  components: { topSeach, MainArticle, Splitpanes, Pane },
  data() {
    return {
      width: '258',
      isReSize: false,
      /* 文章主体组件数据 */
      currentPage: 1,
      pageSize: 10,
      total: 0,
      ArticleList: [],
      /* 左侧tree数据 */
      filterText: '',
      treeData: [],
      treeDataTransfer: [],
      checkList: [],
      defaultCheck: [],
      /* 搜索组件数据 */
      SeachData: {
        metaMode: '' /* 匹配模式 */,
        keyword: '' /* 关键词 */,
        sortMode: '0' /* 排序模式 */,
        timeRange: '' /* 时间范围 */,
        customDay: '' /* 自定义天 */,
        collectionDateType: null /* 时间范围 */,
        collectionTime: '' /* 自定义天 */,
      } /* 搜索条件 */,
      buttonDisabled: false /* 按钮防抖 */,
      ActiveData: {},
      seniorSerchFlag: false /* 普通检索或高级检索 */,
      areaList: [] /* 国内地区 */,
      countryList: [] /* 国家或地区 */,
      KeList: [],
      funEsSeach: false,
      treeQuery: {
        isStability: 1,
        industry: null,
        domain: null
      },
      countBySourceName: null,
      domainList: [],
      industryList: [],
      sortMode: 0,
    }
  },
  async created() {
    await api.areaList().then(data => {
      this.domainList = data.data
    })
    await api.industry().then(value => {
      this.industryList = value.data
    })
    this.getWechat()
    this.getTree()
    this.funEsSeach = this.debounce(this.EsSeach, 200)
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    'treeQuery': {
      handler(newValue, oldValue) {
        this.getTree();
      },
      deep: true // 开启深度监听
    },
    'SeachData.timeRange'(newVal, oldVal) {
      this.SeachData.customDay = []
    },
    'SeachData.collectionDateType'(newVal, oldVal) {
      this.SeachData.collectionTime = []
    },
  },
  methods: {
    EsSeach(flag) {
      this.buttonDisabled = true
      var regex = /\d+/g, regex1 = /\d/ // \d 表示匹配数字
      let data = this.checkList.map(item => {
        if (regex1.test(item.label)) {
          return item.label.slice(0, item.nameLength)
        } else {
          return item.label
        }
      })
      let params = {
        m: 1,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        id: this.$route.query.id,
        weChatName: String(data),
        isSort: this.SeachData.sortMode,
        dateType: this.SeachData.timeRange != 6 ? this.SeachData.timeRange : '',
        startTime: this.SeachData.customDay[0],
        endTime: this.SeachData.customDay[1],
        collectionDateType: this.SeachData.collectionDateType != 6 ? this.SeachData.collectionDateType : '',
        collectionStartTime: this.SeachData.collectionTime[0],
        collectionEndTime: this.SeachData.collectionTime[1],
        keywords: this.SeachData.keyword,
      }
      api.KeIntegration({ ...params, ...this.treeQuery }).then(Data => {
        if (Data.code == 200) {
          this.ArticleList = Data.data.list
          this.total = Data.data.total ? Data.data.total : 0
          if (this.ArticleList.length == 0 && this.pageSize * (this.currentPage - 1) >= this.total && this.total != 0) {
            this.currentPage = Math.trunc(this.total / this.pageSize) + 1
            this.EsSeach('source')
          }
        }
        if (flag == 'source') {
          this.buttonDisabled = false
        }
      }).catch(err => {
        this.buttonDisabled = false
      })
      if (flag != 'source') {
        let data = JSON.parse(JSON.stringify(params))
        delete data.weChatName
        api.KeLIstUse({ ...data, ...this.treeQuery }).then(res => {
          if (res.code == 200) {
            this.countBySourceName = res.data.countBySourceName
            this.treeListChange()
          }
          this.buttonDisabled = false
        }).catch(err => {
          this.buttonDisabled = false
        })
        return
      }
    },
    treeListChange(data) {
      if (this.countBySourceName) {
        if (this.checkList.length) {
          let checkList = JSON.parse(JSON.stringify(this.checkList))
          let list = JSON.parse(JSON.stringify(this.treeData[0].children))
          list.map((row, index) => {
            row.count = 0
            this.$set(this.treeData[0].children, index, row)
          })
          Object.keys(this.countBySourceName).forEach(item => {
            let spIndex = list.findIndex(row => { return row.label == item })
            if (spIndex > -1) {
              this.$set(this.treeData[0].children[spIndex], 'count', this.countBySourceName[item])
            }
          })
          this.$refs.tree.setCheckedKeys([])
          let ids = checkList.map(item => {
            return this.treeData[0].children.filter(row => { return row.label == item.label })[0].id
          })
          setTimeout(res => {
            this.$refs.tree.setCheckedKeys(ids);
          }, 100)
        } else {
          let list = JSON.parse(JSON.stringify(this.treeDataTransfer))
          Object.keys(this.countBySourceName).forEach(item => {
            let spIndex = list.findIndex(row => { return row.label == item })
            if (spIndex > -1) {
              this.$set(list[spIndex], 'count', this.countBySourceName[item])
            }
          })
          this.$set(this.treeData[0], 'children', list)
        }
      } else {
        let list = JSON.parse(JSON.stringify(this.treeDataTransfer))
        this.$set(this.treeData[0], 'children', list)
        let checkList = JSON.parse(JSON.stringify(this.checkList))
        this.$refs.tree.setCheckedKeys([])
        let ids = checkList.map(item => {
          return this.treeData[0].children.filter(row => { return row.label == item.label })[0].id
        })
        setTimeout(res => {
          this.$refs.tree.setCheckedKeys(ids);
        }, 100)
      }
      this.treeSlot()
    },
    handleCurrentChange(current) {
      this.currentPage = current
      this.funEsSeach()
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.funEsSeach()
    },
    SwitchInfo(data) {
      this.isInfo = data
    },
    seniorSerch() {
      this.seniorSerchFlag = !this.seniorSerchFlag
    },
    async getArea() {
      await api.getAreaList().then(Response => {
        if (Response.code == 200) {
          this.areaList = Response.data[0]
          this.countryList = Response.data[1]
        }
      }).catch(err => {
        this.$message({ message: '地区数据获取失败', type: 'error' })
      })
    },
    async getWechat() {
      await api.getspecialLIstUse(this.$route.query.id).then(res => {
        this.$set(this, 'ActiveData', res.data)
        this.getArea()
        this.treeListChange()
      })
    },
    async getTree() {
      await api.monitoringMedium({ id: this.$route.query.id, ...this.treeQuery }).then(item => {
        if (item.code == 200) {
          this.treeData = [
            {
              id: 1000,
              label: '',
              children: item.data.map((ITEM, index) => {
                this.defaultCheck.push(index + 1)
                return {
                  id: index + 1,
                  label: ITEM.cnName,
                  count: 0,
                  orderNum: ITEM.orderNum,
                }
              })
            }
          ]
          /*中转数据 */
          this.treeDataTransfer = item.data.map((ITEM, index) => {
            this.defaultCheck.push(index + 1)
            return {
              id: index + 1,
              label: ITEM.cnName,
              count: 0,
              orderNum: ITEM.orderNum,
            }
          })
          // this.$refs.tree.setCheckedKeys(this.defaultCheck, true)
        }
        this.funEsSeach()
      })
    },
    checkChange(item, isCheck, sonCheck) {
      if (isCheck) {
        if (item.label !== '') {
          this.checkList.push(item)
        }
      } else {
        this.checkList.splice(this.checkList.findIndex(row => row.label == item.label), 1)
      }
      this.funEsSeach('source')
    },
    // 防抖
    debounce(fn, delay) {
      let timer;
      return function () {
        let context = this;
        let args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(context, args);
        }, delay);
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    shrinkMove(e) {
      let wid = e.screenX - 175 //误差值
      this.width = wid
      if (this.isReSize) {
        this.$refs.leftLink.style.width = wid + 'px'
      }
    },
    shrinkUp(e) {
      this.isReSize = false
    },
    shrinkDown(e) {
      this.isReSize = true
    },
    reduction() {
      this.$refs.leftLink.style.width = '258px'
      this.width = 258
    },
    // 左侧列表重置
    treeClear() {
      this.$refs.tree.setCheckedKeys([]);
    },
    // 左侧树排序
    treeSlot(type) {
      let checkList = JSON.parse(JSON.stringify(this.checkList))
      let list = JSON.parse(JSON.stringify(this.treeData[0].children))
      let list1 = list.sort((a, b) => {
        if (this.sortMode == 1) {
          return b.count - a.count
        } else if (this.sortMode == 2) {
          return a.count - b.count
        } else {
          return b.orderNum - a.orderNum
        }
      }).map((item, index) => {
        item.id = index + 1
        return item
      })
      this.$set(this.treeData[0], 'children', list1)
      let ids = checkList.map(item => {
        return this.treeData[0].children.filter(row => { return row.label == item.label })[0].id
      })
      setTimeout(res => {
        this.$refs.tree.setCheckedKeys(ids);
      }, 100)
    },
    resetting() {
      this.SeachData = {
        metaMode: '' /* 匹配模式 */,
        keyword: '' /* 关键词 */,
        sortMode: '0' /* 排序模式 */,
        timeRange: '' /* 时间范围 */,
        customDay: '' /* 自定义天 */,
        collectionDateType: null /* 时间范围 */,
        collectionTime: '' /* 自定义天 */,
      }
      this.funEsSeach()
    },
  }
}
</script>

<style lang="scss" scoped>
.treeBox {
  width: 100%;
  height: calc(100vh - 93px);
  overflow-y: scroll;
}

.treeMain {
  position: relative;
}

.treeQuery {
  ::v-deep .el-input--mini .el-input__inner {
    height: 24px;
    line-height: 24px;
    padding: 0 4px;
  }

  ::v-deep .el-input__suffix {
    height: 20px;
    right: -2px;
    top: 5px;
  }
}

.toolBox {
  min-height: 130px;
  height: auto;
  padding-bottom: 15px;
  background-color: rgb(255, 255, 255);
  box-shadow: -1px 2px 15px #cecdcd;
  border-left: solid 1px rgb(221, 219, 219);

  .title {
    display: flex;
    justify-content: space-between;
    height: 70px;
    padding: 0 30px;
    font-size: 19px;
  }

  .mainTool {
    padding: 0 28px;
    margin-top: -30px;
    font-size: 14px;
    color: rgb(58, 58, 58);
  }

  .mainToolOne {
    margin-top: 15px;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    // align-items: center;
  }

  .mainToolTwo {
    display: flex;
    align-items: center;
    height: 40px;

    p {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .btn {
    margin: 15px 0 0 25px;
  }
}
</style>