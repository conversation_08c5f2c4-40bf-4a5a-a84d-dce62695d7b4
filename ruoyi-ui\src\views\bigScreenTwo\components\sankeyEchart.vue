<template>
  <div id="mainSankey" style="width: 100%; height: 100%"></div>
</template>

<script>
import * as echarts from "echarts";
import { achievementsResearch } from "@/api/bigScreen/index1";

export default {
  data() {
    return {
      myChart: null,
      option: {},
      color: ['#f18bbf', '#0078D7', '#3891A7', '#0037DA', '#964305', '#FEB80A', "#D34817", "#F89746", "#485FB5", "#744DA9", '"#744DA9"'],
    };
  },
  mounted() {
    this.initChart();
    // this.init();
  },
  props: {
  },
  watch: {
  },
  components: {},
  methods: {
    async initChart() {
      let chartDom = document.getElementById("mainSankey");
      let data = await achievementsResearch()
      const allItems = [...data.data.map(edge => edge.source), ...data.data.map(edge => edge.target)]
      const uniqueSet = Array.from(new Set(allItems))
      this.myChart = echarts.init(chartDom);
      this.option = {
        series: {
          type: "sankey",
          layout: "none",
          left: 10.0,
          top: 10.0,
          right: 10.0,
          bottom: 10.0,
          nodeGap: 1,
          draggable: false,
          emphasis: {
            focus: "adjacency",
          },
          data: uniqueSet.map(item => {
            let color = this.color[Math.floor(Math.random() * 10)]
            return {
              name: item,
              itemStyle: {
                color: color,
                borderColor: color,
              },
            }
          }),
          links: data.data,
          levels: [
            {
              depth: 0,
              itemStyle: {
                color: "#fbb4ae",
              },
              lineStyle: {
                color: "source",
                opacity: 0.6,
              },
            },
            {
              depth: 1,
              itemStyle: {
                color: "#b3cde3",
              },
              lineStyle: {
                color: "source",
                opacity: 0.6,
              },
            },
            {
              depth: 2,
              itemStyle: {
                color: "#ccebc5",
              },
              lineStyle: {
                color: "source",
                opacity: 0.6,
              },
              label: {
                position: "left",
              },
            },
            {
              depth: 3,
              itemStyle: {
                color: "#decbe4",
              },
              lineStyle: {
                color: "source",
                opacity: 0.6,
              },
            },
          ],
          lineStyle: {
            curveness: 0.5,
          },
          label: {
            color: "rgba(255, 255, 255, 1)",
            backgroundColor: '#0A183600',
            borderWidth: 0,
            fontSize: 14,
          }
        },
      };
      //随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
      setTimeout(() => {
        this.myChart.resize();
      }, 1);
      this.myChart.setOption(this.option);
      this.myChart.on("click", (params) => {
        if (this.isLastLevel(params)) {
          console.log(params);
        }
      });
    },
    isLastLevel(params) {
      const targetIndex = this.myChart.getOption().series[0].links.findIndex(link => link.target === params.name);
      const sourceIndex = this.myChart.getOption().series[0].links.findIndex(link => link.source === params.name);
      return sourceIndex === -1 && targetIndex !== -1;
    },
  },
  beforeDestroy() {
    /* 页面组件销毁的时候，别忘了移除绑定的监听resize事件，否则的话，多渲染几次
      容易导致内存泄漏和额外CPU或GPU占用哦*/
    window.removeEventListener("resize", () => {
      this.myChart.resize();
    });
  },
};
</script>

<style lang="scss">
.el-table .warning-row {
  background-color: #13436d;
}

.el-table .success-row {
  background-color: #113a65;
}
</style>
