/**
 * HTML工具类
 */

/**
 * 判断字符串是否包含HTML标签
 * @param {String} str 需要检测的字符串
 * @param {Object} options 配置选项
 * @param {Boolean} options.ignoreComments 是否忽略HTML注释，默认为false
 * @param {Boolean} options.ignoreDoctype 是否忽略DOCTYPE声明，默认为false
 * @param {Boolean} options.ignoreXmlTags 是否忽略XML标签，默认为false
 * @param {Array<String>} options.ignoreTags 需要忽略的特定标签列表
 * @returns {Boolean} 是否包含HTML标签
 */
export function containsHtmlTags(str, options = {}) {
  // 参数检查
  if (typeof str !== 'string') {
    return false;
  }
  
  if (str.trim() === '') {
    return false;
  }

  const {
    ignoreComments = false,
    ignoreDoctype = false,
    ignoreXmlTags = false,
    ignoreTags = []
  } = options;

  // 正则表达式模式列表
  const patterns = [];

  // 基础HTML标签模式 - 匹配所有HTML标签
  patterns.push(/<\/?([a-z][a-z0-9]*)\b[^>]*>/i);

  // 自闭合标签模式
  patterns.push(/<([a-z][a-z0-9]*)\b[^>]*\/>/i);

  // 特殊场景处理
  if (!ignoreComments) {
    // HTML注释模式
    patterns.push(/<!--[\s\S]*?-->/);
  }

  if (!ignoreDoctype) {
    // DOCTYPE声明模式
    patterns.push(/<!DOCTYPE[^>]*>/i);
  }

  if (!ignoreXmlTags) {
    // XML处理指令模式
    patterns.push(/<\?xml[^>]*\?>/i);
  }

  // 创建合并的正则表达式
  // 使用函数测试而不是直接合并，以便提供更好的性能和可读性
  
  // 先进行快速检查，提高性能
  if (str.indexOf('<') === -1 || str.indexOf('>') === -1) {
    return false;
  }

  // 处理需要忽略的标签
  if (ignoreTags && ignoreTags.length > 0) {
    for (const pattern of patterns) {
      const matches = str.match(pattern);
      if (matches) {
        // 匹配到标签
        for (const match of matches) {
          // 提取标签名
          const tagNameMatch = match.match(/<\/?([a-z][a-z0-9]*)\b[^>]*>/i);
          if (tagNameMatch && tagNameMatch[1]) {
            const tagName = tagNameMatch[1].toLowerCase();
            // 如果标签不在忽略列表中，则说明包含HTML标签
            if (!ignoreTags.includes(tagName)) {
              return true;
            }
          } else {
            // 匹配到了非普通标签（可能是注释、DOCTYPE等）
            return true;
          }
        }
      }
    }
    return false;
  }

  // 标准检测流程
  for (const pattern of patterns) {
    if (pattern.test(str)) {
      return true;
    }
  }

  // 特殊HTML实体检测（可选，有些场景可能需要）
  const htmlEntityPattern = /&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/i;
  if (htmlEntityPattern.test(str)) {
    // 这里可以根据需求决定HTML实体是否算作HTML标签
    // 默认不将HTML实体视为HTML标签
    // return true;
  }

  return false;
}

/**
 * 获取字符串中的所有HTML标签
 * @param {String} str 需要检测的字符串
 * @returns {Array} 标签列表
 */
export function extractHtmlTags(str) {
  if (typeof str !== 'string' || str.trim() === '') {
    return [];
  }

  const result = [];
  const tagPattern = /<\/?([a-z][a-z0-9]*)\b[^>]*>|<([a-z][a-z0-9]*)\b[^>]*\/>|<!--[\s\S]*?-->|<!DOCTYPE[^>]*>|<\?xml[^>]*\?>/gi;
  
  let match;
  while ((match = tagPattern.exec(str)) !== null) {
    result.push(match[0]);
  }

  return result;
}

/**
 * 检测字符串中的HTML标签是否闭合正确
 * @param {String} str 需要检测的字符串
 * @returns {Boolean} HTML标签是否闭合正确
 */
export function hasValidHtmlStructure(str) {
  if (typeof str !== 'string' || str.trim() === '') {
    return true;
  }

  // 移除注释、DOCTYPE和自闭合标签，以简化检测
  let processedStr = str.replace(/<!--[\s\S]*?-->/g, '')
                         .replace(/<!DOCTYPE[^>]*>/gi, '')
                         .replace(/<([a-z][a-z0-9]*)\b[^>]*\/>/gi, '');

  // 提取所有标签
  const tagPattern = /<\/?([a-z][a-z0-9]*)\b[^>]*>/gi;
  const tags = [];
  let match;

  while ((match = tagPattern.exec(processedStr)) !== null) {
    const fullTag = match[0];
    const tagName = match[1];
    
    // 检查是否是闭合标签
    if (fullTag.startsWith('</')) {
      // 是闭合标签
      if (tags.length === 0 || tags.pop() !== tagName) {
        return false; // 标签闭合不匹配
      }
    } else if (!fullTag.endsWith('/>')) {
      // 是开始标签且不是自闭合
      // 特殊处理一些自闭合标签
      const selfClosingTags = ['img', 'input', 'br', 'hr', 'meta', 'link'];
      if (!selfClosingTags.includes(tagName.toLowerCase())) {
        tags.push(tagName);
      }
    }
  }

  // 检查是否所有标签都已闭合
  return tags.length === 0;
} 