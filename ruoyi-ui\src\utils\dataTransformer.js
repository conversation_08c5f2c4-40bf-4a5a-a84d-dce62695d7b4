/**
 * 将简单数据格式转换为aiData接口格式
 * @param {Array} simpleData 简单数据格式
 * @param {Object} options 可选配置
 * @param {Object} options.layouts 布局配置，key为父节点名称，value为布局设置
 * @return {Array} 转换后的aiData格式数据
 */
export function transformToAiDataFormat(simpleData, options = {}) {
  const { layouts = {} } = options;

  return simpleData.map((parent, parentIndex) => {
    // 获取父节点的布局设置
    const layoutConfig = getLayoutConfig(
      parent.parentName,
      layouts,
      parent.children?.length
    );

    // 创建父节点
    const parentNode = {
      id: parent.parentSn,
      parentId: 0,
      ancestors: "0",
      name: parent.parentName,
      // 根据父节点索引设置不同的背景色
      bgColor: getParentBgColor(parentIndex),
      tab: layoutConfig.tab,
      children: [],
    };

    // 创建子节点
    if (parent.children && parent.children.length > 0) {
      parentNode.children = parent.children.map((child, childIndex) => {
        return {
          id: child.childSn,
          parentId: parent.parentSn,
          ancestors: `0,${parent.parentSn}`,
          name: child.childName,
          // 根据父节点索引设置子节点背景色
          bgColor: getChildBgColor(parentIndex),
          tab: null,
        };
      });
    }

    return parentNode;
  });
}

/**
 * 获取父节点的布局配置
 * @param {String} parentName 父节点名称
 * @param {Object} layouts 布局配置对象
 * @param {Number} childrenCount 子节点数量
 * @return {Object} 布局配置
 */
function getLayoutConfig(parentName, layouts, childrenCount = 0) {
  // 从布局配置中获取
  if (layouts[parentName]) {
    return layouts[parentName];
  }

  // 智能布局：根据子节点数量自动配置
  return getSmartLayout(childrenCount);
}

/**
 * 智能布局配置生成器
 * @param {Number} childrenCount 子节点数量
 * @return {Object} 布局配置
 */
function getSmartLayout(childrenCount) {
  // 默认配置
  const config = { tab: null };

  // 如果是奇数子项，最后一个单独一行
  if (childrenCount > 0 && childrenCount % 2 === 1) {
    // 最后一个子项的位置
    const lastItemPosition = childrenCount;
    config.tab = lastItemPosition.toString();
  }

  return config;
}

/**
 * 预定义的布局模式
 */
export const LayoutModes = {
  // 所有项目均匀分布
  EVEN: "even",
  // 每行最多2个项目
  TWO_PER_ROW: "twoPerRow",
  // 每行最多3个项目
  THREE_PER_ROW: "threePerRow",
  // 第一个独占一行，其余均匀分布
  FIRST_FULL: "firstFull",
  // 最后一个独占一行，其余均匀分布
  LAST_FULL: "lastFull",
  // 奇数时最后一个单独一行
  ODD_LAST_FULL: "oddLastFull",
};

/**
 * 创建布局配置
 * @param {String|Array|Function} mode 布局模式或自定义tab数组或函数
 * @param {Number} childrenCount 子节点数量(仅当mode为函数时使用)
 * @return {Object} 布局配置
 */
export function createLayout(mode, childrenCount = 0) {
  if (typeof mode === "function") {
    return { tab: mode(childrenCount) };
  }

  if (Array.isArray(mode)) {
    return { tab: mode.join(",") };
  }

  switch (mode) {
    case LayoutModes.TWO_PER_ROW:
      return { tab: generateBreakPoints(2) };
    case LayoutModes.THREE_PER_ROW:
      return { tab: generateBreakPoints(3) };
    case LayoutModes.FIRST_FULL:
      return { tab: "1" };
    case LayoutModes.LAST_FULL:
      return { tab: childrenCount > 0 ? childrenCount.toString() : null };
    case LayoutModes.ODD_LAST_FULL:
      if (childrenCount > 0 && childrenCount % 2 === 1) {
        return { tab: childrenCount.toString() };
      }
      return { tab: null };
    case LayoutModes.EVEN:
    default:
      return { tab: null };
  }
}

/**
 * 生成每N个项目后的断点
 * @param {Number} n 每组项目数
 * @param {Number} total 总项目数
 * @return {String} 断点字符串
 */
function generateBreakPoints(n, total = 10) {
  const points = [];
  for (let i = n; i < total; i += n) {
    points.push(i);
  }
  return points.length ? points.join(",") : null;
}

/**
 * 根据索引获取父节点背景色
 * @param {Number} index 父节点索引
 * @return {String} 颜色代码
 */
function getParentBgColor(index) {
  const colors = [
    "#F59A23",
    "#F59A23",
    "#95F204",
    "#95F204",
    "#02A7F0",
    "#02A7F0",
    "#02A7F0",
    "#02A7F0",
  ];
  return index < colors.length ? colors[index] : "#F59A23";
}

/**
 * 根据父索引获取子节点背景色
 * @param {Number} parentIndex 父节点索引
 * @return {String} 颜色代码
 */
function getChildBgColor(parentIndex) {
  if (parentIndex <= 1) {
    return "#FACD91";
  } else if (parentIndex <= 3) {
    return "#CAF982";
  } else {
    return "#81D3F8";
  }
}
