<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div
      class="custom-dialog"
      :class="{ 'article-details-fullscreen': isFullscreen }"
      :style="isFullscreen ? {} : { width: width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div style="display: flex; align-items: center">
          <div
            @click="handleScreen"
            :title="isFullscreen ? '退出全屏' : '全屏'"
            style="
              margin-right: 20px;
              cursor: pointer;
              color: #ffffff;
              font-size: 20px;
            "
          >
            <i
              :class="isFullscreen ? 'el-icon-rank' : 'el-icon-full-screen'"
              style="width: 20px; height: 20px"
            ></i>
          </div>
          <div class="custom-dialog-close" @click="closeDialog"></div>
        </div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="custom-dialog-body-header">
            <div class="custom-dialog-body-header-left">
              <div class="item-bottom-text" v-if="item.publishTime">
                {{ item.publishTime }}
              </div>
              <div
                class="item-bottom-text"
                v-if="item.sourceName"
                style="margin-left: 10px"
              >
                {{ item.sourceName }}
              </div>
            </div>
            <div class="language-box">
              <div
                :class="{ active: language === 'cn' }"
                @click="language = 'cn'"
              >
                中文
              </div>
              <div
                :class="{ active: language === 'en' }"
                @click="language = 'en'"
              >
                原文
              </div>
            </div>
          </div>
          <div
            v-if="processedContent && language === 'cn'"
            class="bg-box-html"
            v-html="processedContent"
          ></div>
          <div class="no-data" v-if="!processedContent && language === 'cn'">
            暂无数据
          </div>
          <div
            v-if="processedContentEn && language === 'en'"
            class="bg-box-html"
            v-html="processedContentEn"
          ></div>
          <div class="no-data" v-if="!processedContentEn && language === 'en'">
            暂无数据
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  containsHtmlTags,
  extractHtmlTags,
  hasValidHtmlStructure,
} from "@/utils/htmlUtils";

export default {
  name: "ArticleDetails",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    content: {
      type: String,
      default: "",
    },
    contentEn: {
      type: String,
      default: "",
    },
    closeOnClickMask: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 1000,
    },
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      language: "cn",
      // 全屏状态
      isFullscreen: false,
    };
  },

  mounted() {
    // 添加ESC键监听
    document.addEventListener("keydown", this.handleKeydown);
    // 添加窗口大小变化监听
    window.addEventListener("resize", this.handleWindowResize);
  },

  beforeDestroy() {
    // 移除ESC键监听
    document.removeEventListener("keydown", this.handleKeydown);
    // 移除窗口大小变化监听
    window.removeEventListener("resize", this.handleWindowResize);
  },

  watch: {
    visible: {
      handler(newVisible) {
        if (newVisible) {
          // 重置全屏状态
          this.isFullscreen = false;
          this.language = "cn";
          console.log(this.item);
        }
      },
    },
  },
  methods: {
    // 移除HTML标签的方法
    removeHtmlTags(htmlString) {
      if (!htmlString) return "";
      let result = htmlString;
      if (containsHtmlTags(result)) {
        result = result.replace(/<br\s*\/?>/gi, "");
        result = result.replace(/n/g, "");
        result = result.replace(/\n/g, "");
        result = result.replace(/\\n/g, "");
        result = result.replace(/\\\n/g, "");
        result = result.replace("|xa0", "");
        result = result.replace("opacity: 0", "");

        // 移除所有图片标签
        result = result.replace(/<img[^>]*>/gi, "");

        // 移除<p>标签，保留内容
        // result = result.replace(/<p[^>]*>(.*?)<\/p>/gi, '$1');

        // 移除带样式的标签，保留内容
        result = result.replace(
          /<(\w+)[^>]*style="[^"]*"[^>]*>(.*?)<\/\1>/gi,
          "$2"
        );

        // 移除任何其他样式标签
        result = result.replace(
          /<(\w+)[^>]*class="[^"]*"[^>]*>(.*?)<\/\1>/gi,
          "$2"
        );

        // 如果需要移除所有HTML标签，可以使用下面的代码（谨慎使用）
        // result = result.replace(/<[^>]*>/g, '');

        console.log("包含的HTML标签", extractHtmlTags(result));
        console.log("HTML是否结构正确", hasValidHtmlStructure(result));
      } else {
        result = result.replace(/n/g, "");
        result = result.replace(/\n/g, "<br>");
        result = result.replace(/\\n/g, "<br>");
        result = result.replace(/\\\n/g, "<br>");
        result = result.replace(/\${[^}]+}/g, "<br>");
        result = result.replace("|xa0", "");
        result = result.replace("opacity: 0", "");

        // 移除所有图片标签
        result = result.replace(/<img[^>]*>/gi, "");

        // 移除<p>标签，保留内容
        // result = result.replace(/<p[^>]*>(.*?)<\/p>/gi, '$1');

        // 移除带样式的标签，保留内容
        result = result.replace(
          /<(\w+)[^>]*style="[^"]*"[^>]*>(.*?)<\/\1>/gi,
          "$2"
        );

        // 移除任何其他样式标签
        result = result.replace(
          /<(\w+)[^>]*class="[^"]*"[^>]*>(.*?)<\/\1>/gi,
          "$2"
        );

        // 如果需要移除所有HTML标签，可以使用下面的代码（谨慎使用）
        // result = result.replace(/<[^>]*>/g, '');
      }
      return result;
    },

    // 关闭弹窗的方法
    closeDialog() {
      this.$emit("update:visible", false);
    },

    // 处理遮罩层点击事件
    handleMaskClick() {
      if (this.closeOnClickMask) {
        this.closeDialog();
      }
    },

    // 全屏切换
    handleScreen() {
      this.isFullscreen = !this.isFullscreen;

      // 延迟调整图表大小，确保DOM更新完成
      this.$nextTick(() => {
        setTimeout(() => {
          // 如果有图表组件，调整其大小
          // 这里可以根据实际的图表组件引用进行调整
        }, 300); // 等待CSS动画完成
      });
    },

    // 处理键盘事件
    handleKeydown(event) {
      // 按ESC键退出全屏
      if (event.key === "Escape" && this.isFullscreen && this.visible) {
        this.isFullscreen = false;
      }
    },

    // 处理窗口大小变化
    handleWindowResize() {
      if (this.isFullscreen) {
        // 重新调整图表大小
        // 这里可以根据实际的图表组件引用进行调整
      }
    },
  },
  computed: {
    processedContent() {
      return this.removeHtmlTags(this.content);
    },
    processedContentEn() {
      return this.removeHtmlTags(this.contentEn);
    },
  },
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  // 确保在所有分辨率下都能正确覆盖
  min-width: 100%;
  min-height: 100%;
  overflow: hidden;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("../../../assets/bigScreenSanhao/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;
    transition: all 0.3s ease;

    &.article-details-fullscreen {
      width: calc(100vw - 40px) !important;
      height: calc(100vh - 40px) !important;
      max-width: none !important;
      max-height: none !important;
      margin: 0 !important;
      // 确保在所有分辨率下都能正确显示
      min-width: calc(100% - 40px) !important;
      min-height: calc(100% - 40px) !important;

      .custom-dialog-body {
        height: calc(100% - 80px); // 减去header高度和padding
        max-height: calc(100% - 80px);
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("../../../assets/bigScreenSanhao/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("../../../assets/bigScreenSanhao/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      height: 800px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 16px 16px 0px;
        min-height: 100%;

        .bg-box-title {
          font-weight: 800;
          font-size: 16px;
          color: #ffffff;
          height: 40px;
          line-height: 40px;
        }

        .bg-box-html {
          padding: 20px 0;
          font-size: 16px;
          color: #ffffff;
          white-space: pre-wrap;
        }
      }
    }
  }
}

::v-deep img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.custom-dialog-body-header {
  display: flex;
  justify-content: space-between;

  .custom-dialog-body-header-left {
    display: flex;
    align-items: center;

    .item-bottom-text {
      min-width: 110px;
      width: fit-content;
      height: 30px;
      padding: 5px;
      background: rgba(47, 188, 254, 0.2);
      border-radius: 2px 2px 2px 2px;
      text-align: center;
      display: inline-block;

      &.ellipsis {
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .language-box {
    display: flex;
    align-items: center;
    height: 30px;
    line-height: 30px;
    font-size: 16px;
    color: #ffffff;
    justify-content: flex-end;

    div {
      width: 60px;
      height: 30px;
      background: #3a4d68;
      text-align: center;
      cursor: pointer;
    }

    .active {
      background: #ff9d00;
    }
  }
}

.no-data {
  text-align: center;
  font-size: 16px;
  color: #ffffff;
  line-height: 300px;
}
</style>

<style>
/* 全局样式，确保v-html渲染的内容中的文本也是白色 */
.bg-box-html * {
  color: #ffffff !important; /* 使用!important确保覆盖任何内联样式 */
}
</style>
