import request from '@/utils/request'

// 查询大屏-采集量统计列表
export function listGather(query) {
  return request({
    url: '/large/gather/list',
    method: 'get',
    params: query
  })
}

// 查询大屏-采集量统计详细
export function getGather(id) {
  return request({
    url: '/large/gather/' + id,
    method: 'get'
  })
}

// 新增大屏-采集量统计
export function addGather(data) {
  return request({
    url: '/large/gather',
    method: 'post',
    data: data
  })
}

// 修改大屏-采集量统计
export function updateGather(data) {
  return request({
    url: '/large/gather',
    method: 'put',
    data: data
  })
}

// 删除大屏-采集量统计
export function delGather(id) {
  return request({
    url: '/large/gather/' + id,
    method: 'delete'
  })
}
