<template>
  <div :class="type == '' ? 'app-container' : ''">
    <div v-if="type == ''">
      <h1>浏览历史</h1>
    </div>
    <div>
      <el-table v-loading="loading2" :data="browseList" border :header-cell-style="{ textAlign: 'center' }"
        style="width: 100%" height="calc(100vh - 220px)" ref="tableRef">
        <el-table-column label="文章来源" show-overflow-tooltip width="150" prop="sourceName" />
        <el-table-column label="文章标题" show-overflow-tooltip min-width="100" prop="title">
          <template slot-scope="scope">
            <span class="title" @click="openNewView(scope.row)">{{ scope.row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column label="浏览时间" align="center" prop="createTime" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="address" label="操作" width="80" align="center">
          <template slot-scope="scope">
            <span class="title" @click="handleBrowse(scope.row)">删除</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total2 > 0" :total="total2" :page.sync="queryParams2.pageNum"
        :limit.sync="queryParams2.pageSize" :autoScroll="false" @pagination="getList2" />
    </div>
  </div>
</template>
  
<script>
import API from '@/api/ScienceApi/index.js'

export default {
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading2: true,
      browseList: [],
      total2: 0,
      queryParams2: {
        pageNum: 1,
        pageSize: 50,
      },
    };
  },
  methods: {
    getList2() {
      this.loading2 = true;
      API.browseList(this.queryParams2).then(response => {
        this.browseList = response.rows;
        this.total2 = response.total;
        this.loading2 = false;
        this.$nextTick(() => {
          this.scrollToTop();
        });
      });
    },
    // 表格滚动到顶部
    scrollToTop() {
      if (this.$refs.tableRef) {
        const tableEl = this.$refs.tableRef.$el.querySelector(
          ".el-table__body-wrapper"
        );
        if (tableEl) {
          tableEl.scrollTop = 0;
        }
      }
    },
    handleBrowse(item) {
      this.$confirm('此操作将删除该条文章的浏览历史, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {
        let query = new FormData()
        query.append('id', item.id)
        API.browseCancel(query).then(res => {
          if (res.code == 200) {
            this.$message({ message: '已删除浏览历史', type: 'success' })
            this.getList2()
          } else {
            this.$message({ message: '删除浏览历史失败', type: 'info' })
          }
        })
      }).catch(() => { })
    },
    openNewView(item) {
      window.open(`/expressDetails?id=${item.articleId}&docId=${item.articleId}`, '_blank')
    },
  },
  computed: {
    // 在这里定义计算属性
  },
  created() {
    this.getList2();
  },
  mounted() {
    // 在这里定义挂载后的操作
  },
};
</script>
  
<style lang="scss" scoped>
.title:hover {
  color: #1d8af0;
  border-bottom: solid 1px #1d8af0;
  cursor: pointer;
}
</style>