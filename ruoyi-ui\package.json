{"name": "dpx", "version": "3.8.6", "description": "开源科技情报采集系统", "author": "地平线", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@antv/g6": "^4.8.24", "@riophae/vue-treeselect": "0.4.0", "autofit.js": "^3.2.3", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "3.25.3", "crypto-js": "^4.1.1", "docx-preview": "^0.1.20", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-ui": "2.15.13", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "html2canvas": "^1.4.1", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "js-md5": "^0.8.3", "jsencrypt": "3.0.0-rc.1", "jspdf": "^2.5.1", "marked": "^15.0.7", "markmap-common": "^0.15.3", "markmap-lib": "^0.15.4", "markmap-view": "^0.15.4", "nprogress": "0.2.0", "pdfjs-dist": "^2.0.943", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "splitpanes": "^2.4.1", "string-random": "^0.1.3", "swiper": "^3.4.2", "v-scale-screen": "^1.0.0", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vue-seamless-scroll": "^1.1.23", "vue-splitpane": "^1.0.6", "vuedraggable": "2.24.3", "vuex": "3.6.0", "webpack-bundle-analyzer": "^4.9.1"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-optional-chaining": "^7.25.9", "@intervolga/optimize-cssnano-plugin": "^1.0.6", "@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-enhance-log": "^0.4.2", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}