import request from '@/utils/request'

// 查询搜索词历史列表
export function listArticleHistory(query) {
  return request({
    url: '/article/articleHistory/list',
    method: 'get',
    params: query
  })
}

// 查询搜索词历史详细
export function getArticleHistory(id) {
  return request({
    url: '/article/articleHistory/' + id,
    method: 'get'
  })
}

// 新增搜索词历史
export function addArticleHistory(data) {
  return request({
    url: '/article/articleHistory',
    method: 'post',
    data: data
  })
}

// 修改搜索词历史
export function updateArticleHistory(data) {
  return request({
    url: '/article/articleHistory/edit',
    method: 'post',
    data: data
  })
}

// 删除搜索词历史
export function delArticleHistory(data) {
  return request({
    url: '/article/articleHistory/remove',
    method: 'post',
    data: data
  })
}

// 删除搜索词历史
export function cleanArticleHistory(type) {
  return request({
    url: `/article/articleHistory/clean/${type}`,
    method: 'get',
  })
}

// 科技无关
export function updateArticleTech0(data) {
  return request({
    url: '/article/articleList/relation/no',
    method: 'post',
    data: data
  })
}

// 科技有关
export function updateArticleTech1(data) {
  return request({
    url: '/article/articleList/relation/has',
    method: 'post',
    data: data
  })
}

// 其他
export function updateArticleTech2(data) {
  return request({
    url: '/article/articleList/relation/other',
    method: 'post',
    data: data
  })
}

// 其他
export function articlePass(data) {
  return request({
    url: '/article/articleList/review/pass',
    method: 'post',
    data: data
  })
}

export function articleNoPass(data) {
  return request({
    url: '/article/articleList/review/nopass',
    method: 'post',
    data: data
  })
}
// 其他
export function cancelArticlePass(data) {
  return request({
    url: '/myarticle/articleList/review/cancel',
    method: 'post',
    data: data
  })
}

export function getListByIds(data) {
  return request({
    url: '/large/hot/getListByIds',
    method: 'post',
    data: data
  })
}

export function deleteByIds(data) {
  return request({
    url: '/article/articleList/deleteByIds',
    method: 'post',
    data: data
  })
}

// 其他
export function screenSourcePass(data) {
  return request({
    url: '/screen/sourceArticle/review/pass',
    method: 'post',
    data: data
  })
}

export function screenSourceNoPass(data) {
  return request({
    url: '/screen/sourceArticle/review/nopass',
    method: 'post',
    data: data
  })
}

// 其他
export function screenRegionPass(data) {
  return request({
    url: '/screen/regionArticle/review/pass',
    method: 'post',
    data: data
  })
}

export function screenRegionNoPass(data) {
  return request({
    url: '/screen/regionArticle/review/nopass',
    method: 'post',
    data: data
  })
}
// 查询预警采集列表
export function listAlarm(query) {
  return request({
    url: '/myweb/alarm/list',
    method: 'get',
    params: query
  })
}

// 查询预警采集详细
export function getAlarm(id) {
  return request({
    url: '/myweb/alarm/' + id,
    method: 'get'
  })
}

// 新增预警采集
export function addAlarm(data) {
  return request({
    url: '/myweb/alarm',
    method: 'post',
    data: data
  })
}

// 修改预警采集
export function updateAlarm(data) {
  return request({
    url: '/myweb/alarm',
    method: 'put',
    data: data
  })
}

// 删除预警采集
export function delAlarm(id) {
  return request({
    url: '/myweb/alarm/' + id,
    method: 'delete'
  })
}

export function getArticleHot() {
  return request({
    url: `/reportData/list/getHotList`,
    method: 'get',
  })
}


