import request from "@/utils/request";

// 查询研究报告统计列表
export function listReport(query) {
  return request({
    url: "/screen/report/list",
    method: "get",
    params: query,
  });
}

// 查询研究报告统计详细
export function getReport(id) {
  return request({
    url: "/screen/report/" + id,
    method: "get",
  });
}

// 新增研究报告统计
export function addReport(data) {
  return request({
    url: "/screen/report",
    method: "post",
    data: data,
  });
}

// 修改研究报告统计
export function updateReport(data) {
  return request({
    url: "/screen/report/edit",
    method: "post",
    data: data,
  });
}

// 删除研究报告统计
export function delReport(id) {
  return request({
    url: "/screen/report/remove",
    method: "post",
    data: id,
  });
}
