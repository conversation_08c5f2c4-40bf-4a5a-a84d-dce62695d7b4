<template>
  <div id="mainLineY" style="width: 100%; height: 100%"></div>
</template>

<script>
import request from "@/utils/request";
import * as echarts from "echarts";

export default {
  data() {
    return {
      myChart: null,
      option: {},
    };
  },
  mounted() {
    this.initChart();
    // this.init();
  },
  props: {
    valueObj: {
      type: Object,
      default: () => {
        return {
          xData: [],
          yData: [],
        };
      },
    },
  },
  watch: {
    valueObj(value) {
      if (value) {
        this.option.xAxis.data = value.xData;
        this.option.series = value.yData;
        this.myChart.setOption(this.option);
      }
    },
  },
  components: {},
  methods: {
    /**
     *
     */
    initChart() {
      let chartDom = document.getElementById("mainLineY");

      this.myChart = echarts.init(chartDom);
      this.option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          show: false, //是否显示
          textStyle: { color: "#fff" },
          x: "right",
          padding: [25, 10],
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          boundaryGap: [0, 0.01],
          splitLine: {
            show: true,
            lineStyle: {
              color: "#ffffff70",
              type: "dotted",
            },
          },
          axisLabel: {
            fontSize: "14px",
            color: "#fff",
            formatter: function (value) {
              if (value.length > 4) {
                return `${value.slice(0, 4)}...`;
              }
              return value;
            },
          },
          axisLine: {
            // 坐标轴线的样式
            show: true,
            lineStyle: {
              color: "#ffffff80",
            },
          },
        },
        yAxis: {
          type: "category",
          data: [
            "人工智能技术",
            "零信任架构",
            "量子信息技术",
            "太空网络技术",
            "云安全技术",
            "区块链技术",
            "多矢量攻击防御",
            "终端安全",
          ],
          axisLabel: {
            //侧边栏的标题字
            interval: 0, //显示不全
            rotate: 20,
            fontSize: "14px",
            color: "#fff",
            // formatter: function (value) {
            //   if (value.length > 4) {
            //     return `${value.slice(0, 4)}...`;
            //   }
            //   return value;
            // },
          },
        },

        series: [
          {
            name: "系列一",
            type: "bar",
            showBackground: true,
            data: [7, 6, 12, 15, 18, 20, 25, 30],
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: "#1890FF35" },

                { offset: 1, color: "#1890FF" },
              ]),
            },
            backgroundStyle: {
              color: "rgba(255,255,255,0.08)", // 柱条的颜色
            },
          },
          //   {
          //     name: "系列二",
          //     type: "bar",
          //     showBackground: true,
          //     data: [19325, 23438, 31000, 121594, 134141, 681807],
          //     itemStyle: {
          //       color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          //         { offset: 0, color: "#1EE7E735" },
          //         { offset: 1, color: "#1EE7E7" },
          //       ]),
          //     },
          //     backgroundStyle: {
          //       color: "rgba(255,255,255,0.08)", // 柱条的颜色
          //     },
          //   },
        ],
      };
      //随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
      setTimeout(() => {
        this.myChart.resize();
      }, 1);
      this.myChart.setOption(this.option);
      this.myChart.on("click", (params) => {
        this.$emit("openList");
      });
    },
  },
  beforeDestroy() {
    /* 页面组件销毁的时候，别忘了移除绑定的监听resize事件，否则的话，多渲染几次
          容易导致内存泄漏和额外CPU或GPU占用哦*/
    window.removeEventListener("resize", () => {
      this.myChart.resize();
    });
  },
};
</script>

<style lang="scss"></style>
