import request from '@/utils/request'

// 查询大屏-重点推荐列表
export function listHot(query) {
  return request({
    url: '/large/hot/list',
    method: 'get',
    params: query
  })
}

// 查询大屏-重点推荐详细
export function getHot(id) {
  return request({
    url: '/large/hot/' + id,
    method: 'get'
  })
}

// 新增大屏-重点推荐
export function addHot(data) {
  return request({
    url: '/large/hot',
    method: 'post',
    data: data
  })
}

// 修改大屏-重点推荐
export function updateHot(data) {
  return request({
    url: '/large/hot',
    method: 'put',
    data: data
  })
}

// 删除大屏-重点推荐
export function delHot(id) {
  return request({
    url: '/large/hot/' + id,
    method: 'delete'
  })
}

export function hotPass(data) {
  return request({
    url: '/large/hot/review/pass',
    method: 'post',
    data: data
  })
}

export function hotNoPass(data) {
  return request({
    url: '/large/hot/review/nopass',
    method: 'post',
    data: data
  })
}
