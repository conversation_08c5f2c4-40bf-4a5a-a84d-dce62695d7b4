import request from "@/utils/request";
const addReport = (params) => {
  return request({
    url: "/article/report",
    method: "post",
    data: params,
  });
};
/* 查询简报列表 */
const queryReportList = (params) => {
  return request({
    url: "/article/report/list",
    method: "get",
    params: params,
  });
};
/* 简报详情 */
const briefingInfo = (params) => {
  return request({
    url: `/article/report/${params}`,
    method: "get",
  });
};
/* 删除简报 */
const removeBriefing = (params) => {
  return request({
    url: `/article/report/remove`,
    method: "post",
    data: params,
  });
};

/* 新增定时任务 */
const addTimeOut = (params) => {
  return request({
    url: "/article/cron",
    method: "post",
    data: params,
  });
};
/* 查询定时任务列表 */
const getTimeList = (params) => {
  return request({
    url: "/article/cron/list",
    method: "get",
    params: params,
  });
};
/* 删除定时任务 */
const removeTime = (params) => {
  return request({
    url: `/article/cron/remove`,
    method: "post",
    data: params,
  });
};
/* 修改定时任务 */
const editTime = (params) => {
  return request({
    url: "/article/cron/edit",
    method: "post",
    data: params,
  });
};
/* 暂停定时任务 */
const stopTImeOut = (params) => {
  return request({
    url: `/article/cron/timeOut/${params}`,
    method: "get",
  });
};
const firingTimeOut = (params) => {
  return request({
    url: `/article/cron/initiate/${params}`,
    method: "get",
  });
};
/* 定时任务详情 */
const TimeInfo = (params) => {
  return request({
    url: `/article/cron/${params}`,
    mehtod: "get",
  });
};
/* 已添加的文章统计 */
const statistics = (params) => {
  return request({
    url: `/result/report/result`,
    method: "get",
    params,
  });
};
/* 删除报告关联 */
const removeReport = (params) => {
  return request({
    url: "/result/report/remove",
    method: "post",
    data: params,
  });
};
/* 已添加的报告统计 */
const reportStatistics = (params) => {
  return request({
    url: `/result/report/statistics/${params}`,
    method: "get",
  });
};
/* 生成报告前_预览报告 */
const reportPriview = (params) => {
  return request({
    url: `/result/report/preview/${params}`,
    method: "get",
  });
};
/* 导出world文档 */
const exportWorld = (params) => {
  return request({
    url: `/result/report/generate/word/${params.reportId}`,
    method: "get",
    Headers: {
      "Content-Type": "application/json; charset=UTF-8",
    },
    responseType: "blob",
  });
};
/* 修改简报 */
const editBireFing = (params) => {
  return request({
    url: "/article/report/edit",
    method: "post",
    data: params,
  });
};
export default {
  addReport,
  queryReportList,
  briefingInfo,
  removeBriefing,
  addTimeOut,
  getTimeList,
  removeTime,
  editTime,
  TimeInfo,
  statistics,
  removeReport,
  reportStatistics,
  stopTImeOut,
  firingTimeOut,
  reportPriview,
  exportWorld,
  editBireFing,
};
