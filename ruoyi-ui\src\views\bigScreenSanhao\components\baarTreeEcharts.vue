<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div class="custom-dialog" :style="{ width: width + 'px' }" @click.stop>
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div class="custom-dialog-close" @click="closeDialog"></div>
      </div>
      <div class="custom-dialog-body">
        <div id="mainGraph11" style="width: 100%; min-height: 700px;height:100%; padding: 10px 20px"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { obj } from "../data/zhiku.js";

export default {
  name: "Baar3DEcharts",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: Number,
      default: 1,
    },
    title: {
      type: String,
      default: "",
    },
    width: {
      type: Number,
      default: 1400,
    },
  },
  data() {
    return {
      myChart: null,
      option: null,
      alldata: [],
      data: [],
    };
  },
  mounted() {
    this.alldata = JSON.parse(JSON.stringify(obj))
    this.alldata = Object.keys(this.alldata).map(key => {
      return this.alldata[key][0]
    })
    this.alldata = this.flattenEmptyNodes(this.alldata)
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
  },
  watch: {
    visible: {
      handler(newType) {
        if (newType) {
          this.data = [this.alldata[this.type - 1]]
          this.$nextTick(() => {
            this.initChart();
          });
          setTimeout(() => {
            this.myChart?.resize();
          }, 1);
        }
      }
    }
  },
  methods: {
    initChart() {
      let chartDom = document.getElementById("mainGraph11");
      this.myChart = echarts.init(chartDom);
      this.option = {
        tooltip: {
          trigger: "item",
          formatter: "{b}",
          triggerOn: "mousemove",
          backgroundColor: "#52aced40", // 设置背景颜色
          textStyle: {
            color: "#333",
            color: "#fff",
            fontSize: 14,
          },
          borderColor: "#52aced",
        },
        series: [
          {
            type: 'tree',
            data: this.recursionFun(this.data),
            top: '1%',
            left: '8%',
            bottom: '1%',
            right: '12%',
            symbolSize: 14,
            initialTreeDepth: 5,
            roam: true,
            label: {
              position: 'top',
              verticalAlign: 'middle',
              align: 'center',
              textStyle: {
                fontSize: 15,
                color: "#fff",
              },
              width: 200,
              overflow: "truncate",
            },
            leaves: {
              label: {
                position: 'top',
                verticalAlign: 'middle',
                align: 'center',
                overflow: "truncate",
              }
            },
            emphasis: {
              focus: 'descendant'
            },
            lineStyle: {
              color: "#7b7b7b", //连接线的颜色
              width: 1,
            },
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750
          }
        ],
      };

      this.myChart.setOption(this.option);

      // 添加点击事件
      this.myChart.on("click", (params) => {
        console.log("点击了节点:", params);
        if (params && params.data && params.data.sn) {
          console.log(params.data.sn);
          this.$emit("openNewView", params.data);
        }
      });
      setTimeout(() => {
        this.myChart?.resize();
      }, 1);
    },
    flattenEmptyNodes(data) {
      if (!Array.isArray(data)) return [];

      return data.reduce((acc, node) => {
        if (node.type === 'empty') {
          if (node.children && node.children.length > 0) {
            acc.push(...this.flattenEmptyNodes(node.children));
          }
        } else {
          if (node.children) {
            node.children = this.flattenEmptyNodes(node.children);
          }
          acc.push(node);
        }
        return acc;
      }, []);
    },
    recursionFun(data) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].type == 'level1') {
          data[i].lineStyle = {
            color: '#0052ff',
          }
        } else if (data[i].type == 'level2') {
          data[i].lineStyle = {
            color: '#009600',
          }
        } else if (data[i].type == 'level3') {
          data[i].lineStyle = {
            color: '#ff6600',
          }
        } else if (data[i].type == 'level4') {
          data[i].lineStyle = {
            color: '#8000ff',
          }
        }else {
          data[i].lineStyle = {
            color: '#ff0066',
          }
        }
        if (data[i].children && data[i].children.length > 0) {
          this.recursionFun(data[i].children);
        }
      }
      return data;
    },
    // 关闭弹窗的方法
    closeDialog() {
      this.$emit("update:visible", false);
    },

    // 处理遮罩层点击事件
    handleMaskClick() {
      if (this.closeOnClickMask) {
        this.closeDialog();
      }
    }
  }
}
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("../../../assets/bigScreenSanhao/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("../../../assets/bigScreenSanhao/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("../../../assets/bigScreenSanhao/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      max-height: 80vh;
      overflow: auto;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 16px 16px 0px;
        min-height: 400px;

        .bg-box-title {
          font-weight: 800;
          font-size: 16px;
          color: #ffffff;
          height: 40px;
          line-height: 40px;
        }

        .bg-box-html {
          padding: 20px 0;
          font-size: 16px;
          color: #ffffff;
          white-space: pre-wrap;
        }
      }
    }
  }
}

.no-data {
  text-align: center;
  font-size: 16px;
  color: #ffffff;
  line-height: 300px;
}
</style>