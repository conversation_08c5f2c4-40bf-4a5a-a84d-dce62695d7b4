<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="80px"
      class="queryForm"
    >
      <el-row type="flex" justify="space-between">
        <el-col :span="6">
          <el-form-item label="授权ID" prop="appId">
            <el-input
              v-model="queryParams.appId"
              placeholder="请输入授权ID"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="授权名称" prop="appName">
            <el-input
              v-model="queryParams.appName"
              placeholder="请输入授权名称"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="频次" prop="frequency">
            <el-input
              v-model="queryParams.frequency"
              placeholder="请输入任务执行频次，单位是秒"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="位置" prop="serviceLocation">
            <el-input
              v-model="queryParams.serviceLocation"
              placeholder="请输入服务所在位置"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6" style="padding-left: 20px">
          <el-form-item class="form-item-btn">
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:monitoring:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:monitoring:edit']"
          >修改</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:monitoring:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      height="calc(100vh - 230px)"
      :data="monitoringList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="监控状态"
        width="80"
        align="center"
        prop="serviceStatus"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.service_status"
            :value="scope.row.serviceStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="监控消息"
        align="center"
        prop="serviceStatusMessage"
      />
      <el-table-column
        label="监控更新时间"
        width="150"
        align="center"
        prop="serviceStatusUpdateTime"
      />
      <el-table-column label="授权名称" align="center" prop="appName" />
      <el-table-column label="授权状态" width="80" align="center" prop="status
      />
      <el-table-column
        label="执行频次"
        width="80"
        align="center"
        prop="frequency"
      />
      <el-table-column
        label="所在位置"
        width="120"
        align="center"
        prop="serviceLocation"
      />
      <el-table-column label="备注信息" align="center" prop="remark" />
      <el-table-column
        label="操作"
        width="100"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:monitoring:edit']"
            >修改</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item
          label="授权ID"
          prop="appId"
          v-if="title == '修改【服务监控】'"
        >
          <el-input
            v-model="form.appId"
            placeholder="请输入应用ID:dpx+15位随机数"
            :disabled="true"
          />
        </el-form-item>
        <el-form-item label="授权KEY" prop="appKey">
          <el-input
            v-model="form.appKey"
            placeholder="请输入应用KEY:由开发者手动填写或随机生成，将用作消息体加解密密钥"
          />
          <el-button
            @click="generateRandomString"
            :disabled="!!form.appKey && form.appKey.length == 64"
            >一键生成</el-button
          >
        </el-form-item>

        <el-form-item label="授权名称" prop="appName">
          <el-input v-model="form.appName" placeholder="请输入服务名称" />
        </el-form-item>
        <el-form-item label="授权状态" prop="status">
          <el-switch
            v-model="form.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-value="0"
            inactive-value="1"
          >
          </el-switch>
        </el-form-item>
        <el-form-item label="执行频次" prop="frequency">
          <el-input
            v-model="form.frequency"
            placeholder="请输入任务执行频次，单位是秒"
          />
        </el-form-item>
        <el-form-item label="所在位置" prop="serviceLocation">
          <el-input
            v-model="form.serviceLocation"
            placeholder="请输入服务所在位置"
          />
        </el-form-item>
        <el-form-item label="IP" prop="serviceIp">
          <el-input v-model="form.serviceIp" placeholder="请输入服务所在IP" />
        </el-form-item>
        <el-form-item label="服务状态" prop="serviceStatus">
          <el-switch
            v-model="form.serviceStatus"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-value="0"
            inactive-value="1"
          >
          </el-switch>
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMonitoring,
  getMonitoring,
  delMonitoring,
  addMonitoring,
  updateMonitoring,
} from "@/api/system/monitoring";
import random from "string-random";

export default {
  name: "Monitoring",
  dicts: ["service_status"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      monitoringList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        appId: null,
        serviceName: null,
        frequency: null,
        serviceLocation: null,
        serviceIp: null,
        serviceStatus: null,
        serviceStatusMessage: null,
        serviceStatusUpdateTime: null,
        deleteTime: null,
        deleteBy: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      listMonitoring(this.queryParams).then((response) => {
        this.monitoringList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        appId: null,
        appKey: null,
        serviceName: null,
        frequency: null,
        serviceLocation: null,
        serviceIp: null,
        serviceStatus: null,
        serviceStatusMessage: null,
        serviceStatusUpdateTime: null,
        delFlag: null,
        updateTime: null,
        updateBy: null,
        createTime: null,
        createBy: null,
        deleteTime: null,
        deleteBy: null,
        remark: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【服务监控】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getMonitoring(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【服务监控】";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateMonitoring(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMonitoring(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除【请填写功能名称】编号为"' + ids + '"的数据项？')
        .then(function () {
          return delMonitoring(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "monitoring/export",
        {
          ...this.queryParams,
        },
        `monitoring_${new Date().getTime()}.xlsx`
      );
    },
    // 随机生成64位随机字母（大小写）+数字
    generateRandomString() {
      this.form.appKey = random(64, { numeric: true, letters: true });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .queryForm {
  .el-form-item {
    width: 100%;
    margin-right: 0;
  }
  .el-form-item__content {
    width: calc(100% - 80px);
  }
  .form-item-btn {
    .el-form-item__content {
      width: 100%;
    }
  }
}
</style>
