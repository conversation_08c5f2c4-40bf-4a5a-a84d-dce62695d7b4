import request from "@/utils/request";
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询用户列表
export function listUser(query) {
  return request({
    url: "/system/user/list",
    method: "get",
    params: query,
  });
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: "/system/user/" + parseStrEmpty(userId),
    method: "get",
  });
}

// 新增用户
export function addUser(data) {
  return request({
    url: "/system/user",
    method: "post",
    data: data,
  });
}

// 修改用户
export function updateUser(data) {
  return request({
    url: "/system/user/edit",
    method: "post",
    data: data,
  });
}

// 删除用户
export function delUser(userId) {
  return request({
    url: "/system/user/remove",
    method: "post",
    data: userId
  });
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password,
  };
  return request({
    url: "/system/user/resetPwd",
    method: "post",
    data: data,
  });
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status,
  };
  return request({
    url: "/system/user/changeStatus",
    method: "post",
    data: data,
  });
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: "/system/user/profile",
    method: "get",
  });
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: "/system/user/profile/edit",
    method: "post",
    data: data,
  });
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  let data = new FormData();
  data.append("oldPassword", oldPassword);
  data.append("newPassword", newPassword);
  return request({
    url: "/system/user/profile/updatePwd",
    method: "post",
    data: data,
  });
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: "/system/user/profile/avatar",
    method: "post",
    data: data,
  });
}

// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: "/system/user/authRole/" + userId,
    method: "get",
  });
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: "/system/user/authRole",
    method: "post",
    data: data,
  });
}

// 查询部门下拉树结构
export function deptTreeSelect() {
  return request({
    url: "/system/user/deptTree",
    method: "get",
  });
}
/* 查询收藏列表数据 */
export function collectList(params) {
  return request({
    url: "/article/collection/list",
    method: "get",
    params,
  });
}
/* 删除文章收藏 */
export function removeCollect(params) {
  return request({
    url: "/article/collection/remove",
    method: "post",
    data: params,
  });
}

// 查询大屏字体
export function getScreenFont() {
  return request({
    url: "/system/user/getScreenFont",
    method: "get",
  });
}

// 保存大屏字体
export function saveScreenFont(screenFont) {
  return request({
    url: "/system/user/saveScreenFont/" + parseStrEmpty(screenFont),
    method: "get",
  });
}
