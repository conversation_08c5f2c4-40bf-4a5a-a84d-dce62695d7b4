<template>
  <div>
    <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
      <div
        class="custom-dialog"
        :class="{ 'policy-risk-fullscreen': isFullscreen }"
        :style="isFullscreen ? {} : { width: width + 'px' }"
        @click.stop
      >
        <div class="custom-dialog-header">
          <span>{{ title }}</span>
          <div style="display: flex; align-items: center">
            <div
              @click="handleScreen"
              :title="isFullscreen ? '退出全屏' : '全屏'"
              style="
                margin-right: 20px;
                cursor: pointer;
                color: #ffffff;
                font-size: 20px;
              "
            >
              <i
                :class="isFullscreen ? 'el-icon-rank' : 'el-icon-full-screen'"
                style="width: 20px; height: 20px"
              ></i>
            </div>
            <div class="custom-dialog-close" @click="closeDialog"></div>
          </div>
        </div>
        <div class="custom-dialog-body">
          <div class="bg-box">
            <div class="bg-box-title">美国提案分布情况</div>
            <div class="bg-box-content">
              <usaMap
                style="width: 530px; height: 284px"
                :external-data="usaMapData"
              ></usaMap>
            </div>
          </div>
          <div class="bg-box">
            <div class="bg-box-title">
              <span>美国提案影响</span>
              <div class="date-picker-container">
                <!-- 日期区间选择器 -->
                <el-date-picker
                  v-model="sankeyDateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  @change="onSankeyDateRangeChange"
                  size="small"
                  class="title-date-picker"
                  :picker-options="pickerOptions"
                />
                <!-- 全屏按钮 -->
                <div
                  class="title-fullscreen-btn"
                  @click="openSankeyFullscreen"
                  title="全屏查看"
                >
                  <i class="el-icon-full-screen"></i>
                </div>
              </div>
            </div>
            <div class="bg-box-content">
              <sankeyChart
                ref="sankeyChart"
                style="width: 100%; height: 700px"
                :date-range="sankeyDateRange"
              ></sankeyChart>
            </div>
          </div>
          <div class="bg-box">
            <div class="bg-box-title">与中国相关</div>
            <div class="bg-box-content">
              <el-descriptions
                style="width: 100%; margin-bottom: 20px"
                direction="vertical"
                :column="5"
                border
                v-for="item in list2"
                :key="item.proposalsSn"
              >
                <el-descriptions-item label="提案时间" :span="1" width="150">{{
                  item.publishTime
                }}</el-descriptions-item>
                <el-descriptions-item label="提案名称" :span="1" width="500">
                  <div
                    style="color: #0ec2f4e6; cursor: pointer; font-weight: bold"
                    @click="openpOlicyRiskContent(item.proposalsSn)"
                  >
                    {{ item.proposalsTitle }}
                  </div>
                </el-descriptions-item>
                <el-descriptions-item
                  label="提案人员"
                  :span="1"
                  class-name="experts"
                  width="250"
                >
                  <div class="descriptions-item-content">
                    <div
                      v-for="person in item.experts"
                      :key="person.expertsSn"
                      style="
                        color: #0ec2f4e6;
                        cursor: pointer;
                        font-weight: bold;
                      "
                      @click="openExpertsContent(person.expertsSn)"
                    >
                      {{ person.proposalsExperts }}
                    </div>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item
                  label="所属党派"
                  :span="1"
                  class-name="experts"
                >
                  <div class="descriptions-item-content">
                    <div v-for="group in item.experts" :key="group.expertsSn">
                      {{ group.belongToGroup }}
                    </div>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item
                  label="所属州"
                  :span="1"
                  class-name="experts"
                >
                  <div class="descriptions-item-content">
                    <div v-for="area in item.experts" :key="area.expertsSn">
                      {{ area.belongToArea }}
                    </div>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="法案摘要" :span="5">
                  <span v-html="item.summary"></span>
                </el-descriptions-item>
                <el-descriptions-item label="对中国的主要影响" :span="5">
                  <span v-html="item.proposalsEffect"></span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
          <div class="bg-box">
            <div class="bg-box-title">美国国会提案</div>
            <div class="bg-box-content" style="display: block">
              <el-table
                :data="processedTableData"
                border
                style="width: 100%"
                :span-method="calculateRowspan"
              >
                <el-table-column
                  prop="time"
                  label="提案时间"
                  align="center"
                  width="150"
                />
                <el-table-column
                  prop="title"
                  label="提案名称"
                  align="center"
                  width="500"
                >
                  <template slot-scope="scope">
                    <div
                      @click="openpOlicyRiskContent(scope.row.id)"
                      style="
                        color: #0ec2f4e6;
                        cursor: pointer;
                        font-weight: bold;
                      "
                    >
                      {{ scope.row.title }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="people.proposalsExperts"
                  label="提案人员"
                  align="center"
                  width="250"
                >
                  <template slot-scope="scope">
                    <div
                      style="
                        color: #0ec2f4e6;
                        cursor: pointer;
                        font-weight: bold;
                      "
                      @click="openExpertsContent(scope.row.people.expertsSn)"
                    >
                      {{ scope.row.people.proposalsExperts }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="labelName"
                  label="相关领域"
                  align="center"
                />
                <el-table-column
                  prop="people.belongToGroup"
                  label="所属党派"
                  align="center"
                />
                <el-table-column
                  prop="people.belongToArea"
                  label="所属州"
                  align="center"
                />
              </el-table>
              <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 子组件 -->
    <policyRiskContent
      :tianDetail="tianDetail"
      :visible="policyRiskContentVisible"
      @update:visible="policyRiskContentVisible = $event"
      @openExpertsContent="openExpertsContent"
      ref="policyRiskContentRef"
    >
    </policyRiskContent>
    <policyRiskExperts
      :tianExpertsDetail="tianExpertsDetail"
      :visible="policyRiskExpertsVisible"
      @update:visible="policyRiskExpertsVisible = $event"
      @openpOlicyRiskContent="openpOlicyRiskContent"
      @openArticleDetail="openArticleDetail"
      ref="policyRiskExpertsRef"
    >
    </policyRiskExperts>
  </div>
</template>

<script>
import usaMap from "../components/usaMap";
import sankeyChart from "../components/sankeyChart";
import policyRiskContent from "./policyRiskContent";
import policyRiskExperts from "./policyRiskExperts";
import {
  proposalsDetail,
  proposalsExpertDetail,
} from "@/api/bigScreen/sanhao.js";

export default {
  name: "PolicyRisk",
  components: {
    usaMap,
    sankeyChart,
    policyRiskContent,
    policyRiskExperts,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "自定义弹窗",
    },
    closeOnClickMask: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 1280,
    },
    list1: {
      type: Array,
      default: () => [],
    },
    list2: {
      type: Array,
      default: () => [],
    },
    total: {
      type: Number,
      default: 0,
    },
    // 美国地图数据
    usaMapData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      policyRiskContentVisible: false,
      policyRiskExpertsVisible: false,
      tianDetail: {},
      tianExpertsDetail: {},
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 用于存储合并信息
      mergeInfo: [],
      // 桑基图日期区间
      sankeyDateRange: [],
      // 全屏状态
      isFullscreen: false,
      // 日期选择器快捷选项
      pickerOptions: {
        shortcuts: [
          {
            text: "近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  mounted() {
    this.initSankeyDefaultDateRange();
    // 添加ESC键监听
    document.addEventListener("keydown", this.handleKeydown);
    // 添加窗口大小变化监听
    window.addEventListener("resize", this.handleWindowResize);
  },

  beforeDestroy() {
    // 移除ESC键监听
    document.removeEventListener("keydown", this.handleKeydown);
    // 移除窗口大小变化监听
    window.removeEventListener("resize", this.handleWindowResize);
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
        };
        // 每次打开对话框时都重新初始化日期范围
        this.initSankeyDefaultDateRange();
        // 重置全屏状态
        this.isFullscreen = false;
      }
    },
  },

  computed: {
    // 处理表格数据
    processedTableData() {
      const data = this.list1.flatMap((item) =>
        item.experts.map((person) => ({
          id: item.proposalsSn,
          time: item.publishTime,
          title: item.proposalsTitle,
          people: person,
        }))
      );

      // 清空合并信息数组
      this.mergeInfo.length = 0;

      for (let i = 0; i < data.length; i++) {
        let rowspan = 1;
        // 从当前行的下一行开始，查找 time 和 title 都相同的行
        for (let j = i + 1; j < data.length; j++) {
          if (
            data[j].time === data[i].time &&
            data[j].title === data[i].title
          ) {
            rowspan++;
          } else {
            break;
          }
        }
        // 存储当前行的合并行数
        for (let k = 0; k < rowspan; k++) {
          this.mergeInfo.push(k === 0 ? rowspan : 0);
        }
        i += rowspan - 1;
      }

      return data;
    },
  },
  methods: {
    // 初始化桑基图默认日期范围（最近三个月）
    initSankeyDefaultDateRange() {
      const today = new Date();
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(today.getMonth() - 3);

      this.sankeyDateRange = [
        this.formatDate(threeMonthsAgo),
        this.formatDate(today),
      ];
    },

    // 格式化日期为 yyyy-MM-dd 格式
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    // 计算合并行数
    calculateRowspan({ row, column, rowIndex, columnIndex }) {
      if (
        column.property === "time" ||
        column.property === "title" ||
        column.property === "labelName"
      ) {
        const rowspan = this.mergeInfo[rowIndex];
        return {
          rowspan: rowspan,
          colspan: 1,
        };
      }
      return {
        rowspan: 1,
        colspan: 1,
      };
    },

    // 桑基图日期区间变化处理
    onSankeyDateRangeChange() {
      // 只有在选择了日期时才重新查询
      if (
        this.sankeyDateRange &&
        this.sankeyDateRange.length === 2 &&
        this.$refs.sankeyChart
      ) {
        this.$refs.sankeyChart.updateDateRange(this.sankeyDateRange);
      }
    },

    // 打开桑基图全屏
    openSankeyFullscreen() {
      if (this.$refs.sankeyChart) {
        this.$refs.sankeyChart.openFullscreen();
      }
    },

    // 关闭弹窗的方法
    closeDialog() {
      this.$emit("update:visible", false);
    },

    getList() {
      this.$emit("pagination", this.queryParams);
    },

    // 处理遮罩层点击事件
    handleMaskClick() {
      if (this.closeOnClickMask) {
        this.closeDialog();
      }
    },

    openpOlicyRiskContent(id) {
      proposalsDetail({
        proposalsSn: id,
      }).then((res) => {
        this.tianDetail = res.data;

        // 如果内容组件已显示，则提升到前面
        if (this.policyRiskContentVisible) {
          this.$refs.policyRiskContentRef?.bringToFront();
        } else {
          this.policyRiskContentVisible = true;
        }
      });
    },

    openExpertsContent(id) {
      proposalsExpertDetail({
        expertsSn: id,
      }).then((res) => {
        if (res.code === 200) {
          this.tianExpertsDetail = res.data;
          console.log(this.tianExpertsDetail);

          // 如果专家组件已显示，则提升到前面
          if (this.policyRiskExpertsVisible) {
            this.$refs.policyRiskExpertsRef?.bringToFront();
          } else {
            this.policyRiskExpertsVisible = true;
          }
        } else {
          this.$message.error(res.msg);
        }
      });
    },

    // 打开文章详情
    openArticleDetail(item) {
      this.$emit("openArticleDetail", item);
    },

    handleScreen() {
      this.isFullscreen = !this.isFullscreen;

      // 延迟调整图表大小，确保DOM更新完成
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.$refs.sankeyChart) {
            this.$refs.sankeyChart.resizeChart();
          }
        }, 300); // 等待CSS动画完成
      });
    },

    // 处理键盘事件
    handleKeydown(event) {
      // 按ESC键退出全屏
      if (event.key === "Escape" && this.isFullscreen && this.visible) {
        this.isFullscreen = false;
      }
    },

    // 处理窗口大小变化
    handleWindowResize() {
      if (this.isFullscreen && this.$refs.sankeyChart) {
        // 重新调整图表大小
        this.$refs.sankeyChart.resizeChart();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  // 确保在所有分辨率下都能正确覆盖
  min-width: 100%;
  min-height: 100%;
  overflow: hidden;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("../../../assets/bigScreenSanhao/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;
    transition: all 0.3s ease;

    &.policy-risk-fullscreen {
      width: calc(100vw - 40px) !important;
      height: calc(100vh - 40px) !important;
      max-width: none !important;
      max-height: none !important;
      margin: 0 !important;
      // 确保在所有分辨率下都能正确显示
      min-width: calc(100% - 40px) !important;
      min-height: calc(100% - 40px) !important;

      .custom-dialog-body {
        height: calc(100% - 80px); // 减去header高度和padding
        max-height: calc(100% - 80px);
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("../../../assets/bigScreenSanhao/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("../../../assets/bigScreenSanhao/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      height: 800px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .date-picker-container {
            display: flex;
            align-items: center;
            gap: 10px;

            .title-fullscreen-btn {
              width: 32px;
              height: 32px;
              background: rgba(14, 194, 244, 0.8);
              border-radius: 4px;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                background: rgba(14, 194, 244, 1);
                transform: scale(1.1);
              }

              i {
                color: #ffffff;
                font-size: 16px;
              }
            }
          }
        }

        .bg-box-content {
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;

          ::v-deep .el-table__header th {
            background-color: #1f3850 !important;
            color: rgba(255, 255, 255);
            font-size: 16px;
          }

          ::v-deep .el-table__body td {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
          }
        }

        ::v-deep .el-descriptions-item__cell {
          background-color: #1f3850;
          color: rgba(255, 255, 255);
          font-size: 16px;
          text-align: center;
        }

        ::v-deep .el-descriptions__content {
          background-color: #1d3046;
          color: rgba(255, 255, 255, 0.9);
          font-size: 14px;
          text-align: center;
        }

        ::v-deep .experts {
          padding: 0;
          .descriptions-item-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            & > div {
              width: 100%;
              height: 40px;
              line-height: 40px;
              border-bottom: 1px solid #ffffff;
            }
            & > div:last-child {
              border-bottom: none;
            }
          }
        }
      }
    }
  }
}
::v-deep .pagination-container {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 0;

  .el-select .el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }

  .el-pagination__editor.el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }
}

::v-deep .el-pagination__total,
::v-deep .el-pagination__jump {
  color: #f2f2f2;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination button:disabled {
  background-color: #ffffff00 !important;
  color: #fff !important;
}

::v-deep .el-pager li {
  background: #ffffff00 !important;
  color: #fff !important;

  &.active {
    color: #1890ff !important;
  }
}

/* 自定义日期选择器样式 */
::v-deep .date-picker-container .el-date-editor {
  background: rgba(14, 194, 244, 0.1);
  border: 1px solid rgba(14, 194, 244, 0.5);
  border-radius: 4px;
}

::v-deep .date-picker-container .el-date-editor .el-range-input {
  background: transparent;
  color: #ffffff;
}

::v-deep .date-picker-container .el-date-editor .el-range-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

::v-deep .date-picker-container .el-date-editor .el-range-separator {
  color: #ffffff;
}

::v-deep .date-picker-container .el-date-editor .el-range-input {
  background-color: transparent;
  color: #ffffff;
}

::v-deep .date-picker-container .el-date-editor:hover {
  border-color: rgba(14, 194, 244, 0.8);
}
</style>
