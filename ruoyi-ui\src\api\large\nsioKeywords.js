import request from '@/utils/request'

// 查询信通院大屏  网络安全行业全景 检索词库列表
export function listNsioKeywords(query) {
  return request({
    url: '/large/nsioKeywords/list',
    method: 'get',
    params: query
  })
}

// 检索词库列表（排除节点）
export function excludeChild(deptId) {
  return request({
    url: '/large/nsioKeywords/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询信通院大屏  网络安全行业全景 检索词库详细
export function getNsioKeywords(id) {
  return request({
    url: '/large/nsioKeywords/' + id,
    method: 'get'
  })
}

// 新增信通院大屏  网络安全行业全景 检索词库
export function addNsioKeywords(data) {
  return request({
    url: '/large/nsioKeywords',
    method: 'post',
    data: data
  })
}

// 修改信通院大屏  网络安全行业全景 检索词库
export function updateNsioKeywords(data) {
  return request({
    url: '/large/nsioKeywords/edit',
    method: 'post',
    data: data
  })
}

// 删除信通院大屏  网络安全行业全景 检索词库
export function delNsioKeywords(id) {
  let data = new FormData()
  data.append('id', id)
  return request({
    url: '/large/nsioKeywords/remove',
    method: 'post',
    data: data
  })
}
