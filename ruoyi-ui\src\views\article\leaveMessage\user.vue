<template>
  <div style="height: 100%;" class="four">
    <el-card>
      <div slot="header" class="clearfix">
        <span>我的留言</span>
      </div>
      <div class="massageList" v-loading="loading">
        <div class="massage" v-for="(item, index) in massageList">
          <img :src="avatar" class="avatar">
          <div class="name">{{ name }}</div>
          <div class="content">{{ item.content }}</div>
          <div class="title" @click="openNewView(item)">{{ item.cnTitle }}</div>
          <div class="time">{{ parseTime(item.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</div>
          <el-divider></el-divider>
        </div>
      </div>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </el-card>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { feedbackOwnList } from "@/api/article/leaveMessage";
export default {
  data() {
    return {
      massageList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
    };
  },
  computed: {
    ...mapGetters(['roles', 'name', 'avatar']),
  },
  components: {},
  mounted() {
    this.getList();
  },
  beforeDestroy() { },
  methods: {
    /** 查询文章工作列表 */
    getList() {
      this.loading = true;
      feedbackOwnList(this.queryParams).then(response => {
        this.massageList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    openNewView(item) {
      window.open(`/expressDetails?id=${item.articleId}&docId=${item.docId}`, '_blank')
    },
  },
};
</script>
<style lang="scss" scoped>
.four {
  height: 100%;
  width: 100%;
}

.massageList {
  height: 100%;
  padding: 0 10px;

  .massage {
    position: relative;
    padding: 20px 10px 0 50px;
    // display: flex;

    img {
      position: absolute;
      top: 30px;
      left: 0px;
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }

    .name {
      font-size: 16px;
      padding: 5px 0;
    }

    .content {
      font-size: 20px;
      padding: 10px 0px;
    }

    .title {
      font-size: 18px;
      padding: 10px 10px;
      margin-bottom: 20px;
      background: #f1f3f3;
      border-radius: 10px;
      cursor: pointer;
      text-align: left;
    }

    .time {
      margin-top: 10px;
      font-size: 14px;
      color: #858282;
    }
  }
}
</style>
  