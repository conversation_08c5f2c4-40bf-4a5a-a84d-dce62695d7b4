import request from "@/utils/request";

// 查询舆情参考统计列表
export function listOpinions(query) {
  return request({
    url: "/screen/opinions/list",
    method: "get",
    params: query,
  });
}

// 查询舆情参考统计详细
export function getOpinions(id) {
  return request({
    url: "/screen/opinions/" + id,
    method: "get",
  });
}

// 新增舆情参考统计
export function addOpinions(data) {
  return request({
    url: "/screen/opinions",
    method: "post",
    data: data,
  });
}

// 修改舆情参考统计
export function updateOpinions(data) {
  return request({
    url: "/screen/opinions/edit",
    method: "post",
    data: data,
  });
}

// 删除舆情参考统计
export function delOpinions(id) {
  return request({
    url: "/screen/opinions/remove",
    method: "post",
    data: id,
  });
}
