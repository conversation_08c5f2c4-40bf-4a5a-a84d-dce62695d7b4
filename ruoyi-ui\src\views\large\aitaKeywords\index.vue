<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="检索词" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入检索词" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['large:aitaKeywords:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-if="refreshTable" v-loading="loading" :data="aitaKeywordsList" row-key="id"
      :default-expand-all="isExpandAll" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column label="检索词" prop="name" />
      <el-table-column label="背景颜色" align="center" prop="bgColor" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          {{ scope.row.status == 0 ? '启用' : '停用' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['large:aitaKeywords:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-plus" @click="handleAdd(scope.row)"
            v-hasPermi="['large:aitaKeywords:add']">新增</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['large:aitaKeywords:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改信通院大屏  人工智能技术架构  检索词库对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="上级检索词" prop="parentId">
          <treeselect v-model="form.parentId" :options="aitaKeywordsOptions" :normalizer="normalizer"
            placeholder="请选择上级检索词" />
        </el-form-item>
        <el-form-item label="检索词" prop="name">
          <el-input v-model="form.name" placeholder="请输入检索词" />
        </el-form-item>
        <el-form-item label="背景颜色" prop="bgColor">
          <el-color-picker v-model="form.bgColor"></el-color-picker>
        </el-form-item>
        <el-form-item label="高亮数据" prop="tab">
          <el-input v-model="form.tab" placeholder="请输入高亮数据" />
        </el-form-item>
        <el-form-item label="显示顺序" prop="orderNum">
          <el-input v-model="form.orderNum" placeholder="请输入显示顺序" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAitaKeywords, getAitaKeywords, delAitaKeywords, addAitaKeywords, updateAitaKeywords, excludeChild } from "@/api/large/aitaKeywords";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "AitaKeywords",
  dicts: ['sys_normal_disable'],
  components: {
    Treeselect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 信通院大屏  人工智能技术架构  检索词库表格数据
      aitaKeywordsList: [],
      // 信通院大屏  人工智能技术架构  检索词库树选项
      aitaKeywordsOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        parentId: null,
        ancestors: null,
        name: null,
        bgColor: null,
        tab: null,
        orderNum: null,
        status: null,
        tenantId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询信通院大屏  人工智能技术架构  检索词库列表 */
    getList() {
      this.loading = true;
      listAitaKeywords(this.queryParams).then(response => {
        this.aitaKeywordsList = this.handleTree(response.data, "id", "parentId");
        console.log(this.aitaKeywordsList);
        this.loading = false;
      });
    },
    /** 转换信通院大屏  人工智能技术架构  检索词库数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      };
    },
    /** 查询信通院大屏  人工智能技术架构  检索词库下拉树结构 */
    getTreeselect() {
      listAitaKeywords().then(response => {
        this.aitaKeywordsOptions = [];
        const data = { id: 0, name: '顶级节点', children: [] };
        data.children = this.handleTree(response.data, "id", "parentId");
        this.aitaKeywordsOptions.push(data);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        parentId: undefined,
        name: undefined,
        orderNum: undefined,
        status: "0",
        bgColor: null,
        tab: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.getTreeselect();
      if (row != null && row.id) {
        this.form.parentId = row.id;
      } else {
        this.form.parentId = 0;
      }
      this.open = true;
      this.title = "新增人工智能技术架构关键词";
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      if (row != null) {
        this.form.parentId = row.id;
      }
      getAitaKeywords(row.id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改人工智能技术架构关键词";
        excludeChild(row.id).then(response => {
          this.aitaKeywordsOptions = [{ id: 0, name: '顶级节点', children: this.handleTree(response.data, "id", "parentId") }];
          if (this.aitaKeywordsOptions.length == 0) {
            const noResultsOptions = { id: this.form.parentId, deptName: this.form.name, children: [] };
            this.aitaKeywordsOptions.push(noResultsOptions);
          }
        });
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAitaKeywords(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAitaKeywords(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除此项数据项？').then(function () {
        return delAitaKeywords(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    }
  }
};
</script>
