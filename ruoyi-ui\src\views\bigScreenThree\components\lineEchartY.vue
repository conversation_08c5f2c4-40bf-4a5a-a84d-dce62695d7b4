<template>
  <div id="mainLineY" style="width: 100%; height: 100%"></div>
</template>

<script>
import request from "@/utils/request";
import * as echarts from "echarts";

export default {
  data() {
    return {
      myChart: null,
      option: {},
    };
  },
  mounted() {
    this.initChart();
    // this.init();
  },
  props: {
    valueObj: {
      type: Object,
      default: () => {
        return {
          xData: [],
          yData: [],
        };
      },
    },
  },
  watch: {
    valueObj(value) {
      if (value) {
        this.option.xAxis.data = value.xData;
        this.option.series = value.yData;
        this.myChart.setOption(this.option);
      }
    },
  },
  components: {},
  methods: {
    initChart() {
      let chartDom = document.getElementById("mainLineY");

      this.myChart = echarts.init(chartDom);
      this.option = {
        toolbox: {
          iconStyle: {
            borderColor: '#ffffff',
          },
          feature: {
            dataZoom: {
              yAxisIndex: 'none'
            },
            restore: {}
          }
        },
        legend: {
          show: true, //是否显示
          textStyle: { color: "#fff" },
          padding: [25, 20],
          data: ['未来展望', '面临的挑战', '应用场景分析', '技术发展趋势']
        },
        series: [
          {
            roam: false,
            draggable: false,
            expandAndCollapse: false,
            selectedMode: false,
            type: 'treemap',
            name: '前沿技术分析',
            data: [
              {
                name: '未来展望',
                value: 10,
                children: [
                  {
                    name: '标准化与规范化',
                    value: 3
                  },
                  {
                    name: '公共服务化',
                    value: 3
                  },
                  {
                    name: '技术融合',
                    value: 4
                  }
                ]
              },
              {
                name: '面临的挑战',
                value: 20,
                children: [
                  {
                    name: '法规与伦理',
                    value: 6
                  },
                  {
                    name: '数据安全与隐私',
                    value: 6
                  },
                  {
                    name: '技术成熟度',
                    value: 8
                  }
                ]
              },
              {
                name: '应用场景分析',
                value: 10,
                children: [
                  {
                    name: '数据安全与隐私保护',
                    value: 3
                  },
                  {
                    name: '物联网安全',
                    value: 3
                  },
                  {
                    name: '企业网络安全',
                    value: 4
                  }
                ]
              },
              {
                name: '技术发展趋势',
                value: 10,
                children: [
                  {
                    name: '量子计算',
                    value: 3
                  },
                  {
                    name: '区块链技术',
                    value: 3
                  },
                  {
                    name: '人工智能与机器学习',
                    value: 4
                  },
                ]
              },
            ]
          }
        ],
      };
      //随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
      setTimeout(() => {
        this.myChart.resize();
      }, 1);
      this.myChart.setOption(this.option);
      this.myChart.on("click", (params) => {
        if (params.name) {
          console.log(params);
        }
        // this.$emit("openList");
      });
    },
  },
  beforeDestroy() {
    /* 页面组件销毁的时候，别忘了移除绑定的监听resize事件，否则的话，多渲染几次
          容易导致内存泄漏和额外CPU或GPU占用哦*/
    window.removeEventListener("resize", () => {
      this.myChart.resize();
    });
  },
};
</script>

<style lang="scss"></style>
