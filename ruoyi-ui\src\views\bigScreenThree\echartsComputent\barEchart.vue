<template>
  <div :id="idName" style="width: 100%; height: 100%"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  data() {
    return {
      myChart: null,
      option: {},
    };
  },
  props: {
    data: {
      type: Array,
      default: [],
    },
    idName: {
      type: String,
      default: "",
    },
    sourceType: {
      type: Number,
      default: 1,
    },
  },
  mounted() {},
  watch: {
    data(value) {
      if (value) {
        this.initChart();
      }
    },
  },
  components: {},
  methods: {
    async initChart() {
      let data1,
        data2,
        zzx1 = [],
        zzx2 = [];
      // await largeSourceList({}).then((res) => {
      //   this.data = res.rows;
      // data1 = this.data.filter((item) => item.sourceType === 1);
      // data2 = this.data.filter((item) => item.sourceType === 2);
      // });
      let data = this.data.map((item) => item.name);
      data.map((row) => {
        zzx1.push(this.data.filter((item) => item.name === row)[0].tally);
        // zzx2.push(data2.filter((item) => item.name === row)[0].tally);
      });
      // const zx = zzx1.map((item, index) => {
      //   return item + zzx2[index];
      // });
      var barWidth = 30;
      let chartDom = document.getElementById(this.idName);
      this.myChart = echarts.init(chartDom);
      this.myChart.resize();
      this.option = {
        tooltip: {
          showContent: true,
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            let tooltipText = `${params[0].axisValue}<br/>`;
            params.forEach(function (item) {
              if (!!item.seriesName && item.seriesType != "pictorialBar") {
                tooltipText += `<span style=" margin-top:-4px;display: inline-block;width:10px;height:10px;background-color:${item.color.colorStops[0].color};margin-right:10px;  vertical-align: middle; border-radius: 50%;"> </span>${item.seriesName}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${item.value}<br/>`;
              }
            });
            return tooltipText;
          },
        },
        legend: {
          show: false, //是否显示
          textStyle: { color: "#fff" },
          padding: [15, 10],
          x: "right",
        },
        grid: {
          left: "2%",
          right: "5%",
          bottom: "5%",
          top: "4%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: data,
          axisLine: {
            show: true,
            lineStyle: {
              color: "#ffffff80",
            },
          },
          axisLabel: {
            fontSize: "14px",
            color: "#fff",
            interval: 0,
            margin: 10,
            // rotate: 40,
            // formatter: function (params) {
            //   var str = ""; // 最终拼接成的字符串
            //   var paramsLen = params.length; // 获取每项文字的个数
            //   var len = 4; // 每行能显示的字的个数（根据实际情况自己设置）
            //   var rowNumber = Math.ceil(paramsLen / len); // 换行的话，需要显示几行，向上取整
            //   if (paramsLen > len) {
            //     //大于设定的len就换行，不大于就不变化
            //     for (var i = 0; i < rowNumber; i++) {
            //       var temp = ""; // 表示每一次截取的字符串
            //       var start = i * len; // 开始截取的位置
            //       var end = start + len; // 结束截取的位置
            //       if (i == rowNumber - 1) {
            //         // 最后一次不换行
            //         temp = params.substring(start, paramsLen);
            //       } else {
            //         // 每一次拼接字符串并换行
            //         temp = params.substring(start, end) + "\n";
            //       }
            //       str += temp; // 最终拼成的字符串
            //     }
            //   } else {
            //     // 给新的字符串赋值
            //     str = params;
            //   }
            //   return str; //返回字符串
            // },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            lineStyle: {
              color: "#ffffff70",
              type: "dotted",
            },
          },
          axisLabel: {
            interval: 0, //显示不全
            fontSize: "14px",
            color: "#fff",
          },
        },
        series: [],
      };
      if (this.sourceType === 1) {
        this.option.series = [
          {
            type: "pictorialBar",
            symbol: "diamond",
            symbolSize: [barWidth, 9],
            symbolOffset: [0, -4.5],
            symbolPosition: "end",
            z: 12,
            color: "#54afde",
            data: zzx1,
          },
          {
            name: "国内信息源",
            type: "bar",
            barWidth: barWidth,
            stack: "1",
            itemStyle: {
              normal: {
                opacity: 0.7,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: "#2364A4" },
                  { offset: 1, color: "#69C0FF" },
                ]),
                barBorderRadius: 0,
              },
            },
            label: {
              show: true,
              position: "inside",
              color: "#fff",
              fontSize: 14,
            },
            data: zzx1,
          },
          {
            name: "",
            type: "bar",
            barWidth: 0.5,
            barGap: "0%",
            itemStyle: {
              normal: {
                opacity: 0.7,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: "#2364A4" },
                  { offset: 1, color: "#69C0FF" },
                ]),
              },
            },
            data: zzx1,
          },
        ];
      } else {
        this.option.series = [
          {
            type: "pictorialBar",
            symbol: "diamond",
            stack: "1",
            symbolSize: [barWidth, 9],
            symbolOffset: [0, -4.5],
            symbolPosition: "end",
            z: 12,
            color: "#43E3E3",
            data: zzx1,
          },
          {
            name: "国外信息源",
            type: "bar",
            barWidth: barWidth,
            stack: "1",
            itemStyle: {
              normal: {
                opacity: 0.7,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: "#42E2E360" },
                  { offset: 1, color: "#43E3E3" },
                ]),
                barBorderRadius: 0,
              },
            },
            label: {
              show: true,
              position: "inside",
              color: "#fff",
              fontSize: 14,
            },
            data: zzx1,
          },
          {
            name: "",
            type: "bar",
            barWidth: 0.5,
            barGap: "0%",
            itemStyle: {
              normal: {
                opacity: 0.7,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: "#43E3E3" },
                  { offset: 1, color: "#072c2c" },
                ]),
              },
            },
            data: zzx1,
          },
        ];
      }
      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
      this.myChart.setOption(this.option);
      this.myChart.on("click", (params) => {
        let sourceName = {
          1: "国内信息源",
          2: "国外信息源",
        };
        let row = this.data.filter((item) => {
          return item.name == params.name;
        });
        this.$emit("openList", {
          query: { sourceId: row[0].id, sourceName: params.name },
          title: params.name,
          sourceType: this.sourceType,
        });
      });
    },
  },
  beforeDestroy() {
    window.removeEventListener("resize", () => {
      this.myChart.resize();
    });
  },
};
</script>

<style lang="scss">
.el-table .warning-row {
  background-color: #13436d;
}

.el-table .success-row {
  background-color: #113a65;
}
</style>
