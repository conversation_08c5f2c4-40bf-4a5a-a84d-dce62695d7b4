<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="舆情数量" prop="opinionsNumber">
        <el-input
          v-model="queryParams.opinionsNumber"
          placeholder="请输入舆情数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="舆情参考日期" prop="opinionsDate">
        <el-date-picker
          clearable
          v-model="queryParams.opinionsDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择舆情参考日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['screen:opinions:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['screen:opinions:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['screen:opinions:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['screen:opinions:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="opinionsList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index" />

      <el-table-column label="舆情类型" align="center" prop="opinionsType">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.screen_opinions_type"
            :value="scope.row.opinionsType"
          />
        </template>
      </el-table-column>
      <el-table-column label="舆情数量" align="center" prop="opinionsNumber" />
      <el-table-column
        label="舆情参考日期"
        align="center"
        prop="opinionsDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.opinionsDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:opinions:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['screen:opinions:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改舆情参考统计对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="舆情类型" prop="opinionsType">
          <el-select
            v-model="form.opinionsType"
            placeholder="舆情类型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in dict.type.screen_opinions_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="舆情数量" prop="opinionsNumber">
          <el-input
            v-model="form.opinionsNumber"
            placeholder="请输入舆情数量"
          />
        </el-form-item>
        <el-form-item label="舆情参考日期" prop="opinionsDate">
          <el-date-picker
            clearable
            v-model="form.opinionsDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择舆情参考日期"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listOpinions,
  getOpinions,
  delOpinions,
  addOpinions,
  updateOpinions,
} from "@/api/bigScreenManage/opinions";

export default {
  name: "Opinions",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 舆情参考统计表格数据
      opinionsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        opinionsType: null,
        opinionsNumber: null,
        opinionsDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  dicts: ["screen_opinions_type"],
  created() {
    this.getList();
  },
  methods: {
    /** 查询舆情参考统计列表 */
    getList() {
      this.loading = true;
      listOpinions(this.queryParams).then((response) => {
        this.opinionsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        opinionsType: null,
        opinionsNumber: null,
        opinionsDate: null,
        createTime: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加舆情参考统计";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getOpinions(id).then((response) => {
        this.form = {
          ...response.data,
          opinionsType: response.data.opinionsType
            ? String(response.data.opinionsType)
            : "",
        };
        this.open = true;
        this.title = "修改舆情参考统计";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateOpinions(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOpinions(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids;
      this.$modal
        .confirm('是否确认删除舆情参考统计编号为"' + ids + '"的数据项？')
        .then(function () {
          return delOpinions(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "screen/opinions/export",
        {
          ...this.queryParams,
        },
        `opinions_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
