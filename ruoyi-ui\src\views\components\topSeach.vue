<!-- 简报顶部搜索 -->
<template>
  <div>
    <div class="toolBox" @click="MoreEvent('parent')">
      <div class="title" :style="{ height: ActiveData.title ? '' : '50px' }">
        <p v-if="ActiveData.title">{{ ActiveData.title }}</p>
        <p v-else></p>
      </div>
      <div class="mainTool">
        <p>
          发布时间:
          <el-button
            size="mini"
            :type="SeachData.timeRange == '' ? 'primary' : ''"
            @click="SeachData.timeRange = ''"
            >24小时</el-button
          >
          <el-button
            size="mini"
            :type="SeachData.timeRange == 1 ? 'primary' : ''"
            @click="SeachData.timeRange = 1"
            >今天</el-button
          >
          <el-button
            size="mini"
            :type="SeachData.timeRange == 2 ? 'primary' : ''"
            @click="SeachData.timeRange = 2"
            >近2天</el-button
          >
          <el-button
            size="mini"
            :type="SeachData.timeRange == 4 ? 'primary' : ''"
            @click="SeachData.timeRange = 4"
            >近7天</el-button
          >
          <el-button
            size="mini"
            :type="SeachData.timeRange == 5 ? 'primary' : ''"
            @click="SeachData.timeRange = 5"
            >近30天</el-button
          >
          <el-button
            size="mini"
            :type="SeachData.timeRange == 6 ? 'primary' : ''"
            @click="SeachData.timeRange = 6"
            >自定义</el-button
          >
          <el-date-picker
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd HH:mm:ss"
            v-model="SeachData.customDay"
            v-if="SeachData.timeRange == 6"
            style="display: inline-block; width: 250px; margin-left: 10px"
            size="mini"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            unlink-panels
            clearable
            @change="customDayChange"
          ></el-date-picker>
        </p>
        <p>
          小信优选:
          <el-radio-group v-model="SeachData.isTechnology" size="small">
            <el-radio-button
              v-for="(dict, index) in dict.type.is_technology"
              :label="dict.value"
              :key="'is_technology' + dict.value"
              >{{ dict.label }}</el-radio-button
            >
          </el-radio-group>
        </p>
        <slot name="platform"></slot>
        <div class="mainToolOne" v-if="seniorSerchFlag">
          <div style="height: 28px">
            平台类型:&nbsp;
            <el-radio-group v-model="SeachData.radio">
              <el-radio :label="'2,1'" :disabled="typeShow">全部</el-radio>
              <el-radio :label="'1'" :disabled="typeShow">微信公众号</el-radio>
              <el-radio :label="'2'" :disabled="typeShow">网页</el-radio>
            </el-radio-group>
          </div>
          <p>
            所属行业:&nbsp;
            <!-- <el-select
              clearable
              multiple
              placeholder="请选择行业"
              :filterable="true"
              default-first-option
              v-model="SeachData.industry"
              :collapse-tags="SeachData.industry.length > 10 ?true:false"
              style="width:80%"
              :collapse-tags-max="5"
            >
              <el-option
                v-for="(item,index) in industry"
                :key="index"
                :label="item.industryName"
                :value="item.id"
              ></el-option>
                            </el-select>-->
            <el-button
              size="mini"
              :type="SeachData.industry.length == 0 ? 'primary' : ''"
              @click="SeachData.industry = []"
              >全部</el-button
            >
            <el-button
              size="mini"
              v-for="(item, index) in industry"
              :key="index"
              @click="industryClick(item)"
              :type="SeachData.industry.includes(item.id) ? 'primary' : ''"
              >{{ item.industryName }}</el-button
            >
            <el-button
              size="mini"
              type="primary"
              v-if="rightIndustry.length !== 0"
              style="margin-left: 10px"
              @click.stop="MoreEvent('industry')"
            >
              {{ industry.length <= 5 ? "更多" : "收起" }}
              <i
                :class="{
                  'el-icon-d-arrow-right': industry.length <= 5,
                  'el-icon-d-arrow-left': industry.length > 5,
                }"
              >
              </i>
            </el-button>
          </p>
          <!-- 更多下拉 -->
          <div
            v-show="industryShow"
            class="el-select-dropdown el-popper is-multiple"
            style="
              min-width: 200px;
              transform-origin: center bottom;
              z-index: 2026;
              position: absolute;
              top: 156px;
              left: 685px;
            "
            x-placement="bottom-start"
          >
            <div class="el-scrollbar" style>
              <div
                class="el-select-dropdown__wrap el-scrollbar__wrap"
                style="margin-bottom: -17px; margin-right: -17px"
              >
                <ul class="el-scrollbar__view el-select-dropdown__list">
                  <!--选项-->
                  <li
                    @click.stop="industryClick(item)"
                    :class="{
                      'el-select-dropdown__item': true,
                      selected: SeachData.industry.includes(item.id),
                    }"
                    v-for="(item, index) in rightIndustry"
                    :key="index"
                  >
                    <span>{{ item.industryName }}</span>
                  </li>
                </ul>
              </div>
              <div class="el-scrollbar__bar is-horizontal">
                <div
                  class="el-scrollbar__thumb"
                  style="transform: translateX(0%)"
                ></div>
              </div>
              <div class="el-scrollbar__bar is-vertical">
                <div
                  class="el-scrollbar__thumb"
                  style="transform: translateY(0%)"
                ></div>
              </div>
            </div>
            <!---->
            <div x-arrow class="popper__arrow" style="left: 35px"></div>
          </div>
          <p>
            所属领域:&nbsp;
            <el-button
              size="mini"
              :type="SeachData.area == 0 ? 'primary' : ''"
              @click="SeachData.area = []"
              >全部</el-button
            >
            <el-button
              size="mini"
              v-for="(item, index) in domainList"
              :key="index"
              @click="industryClick(item, 'domain')"
              :type="SeachData.area.includes(item.id) ? 'primary' : ''"
              >{{ item.fieldName }}</el-button
            >
            <el-button
              size="mini"
              type="primary"
              v-if="rightDomain.length !== 0"
              style="margin-left: 10px"
              @click.stop="MoreEvent('domain')"
            >
              {{ domainList.length <= 5 ? "更多" : "收起" }}
              <i
                :class="{
                  'el-icon-d-arrow-right': industry.length <= 5,
                  'el-icon-d-arrow-left': industry.length > 5,
                }"
              >
              </i>
            </el-button>
          </p>
          <!-- 更多下拉 -->
          <div
            v-show="domainShow"
            class="el-select-dropdown el-popper is-multiple"
            style="
              min-width: 240px;
              transform-origin: center bottom;
              z-index: 2026;
              position: absolute;
              top: 220px;
              left: 685px;
            "
            x-placement="bottom-start"
          >
            <div class="el-scrollbar" style>
              <div
                class="el-select-dropdown__wrap el-scrollbar__wrap"
                style="margin-bottom: -17px; margin-right: -17px"
              >
                <ul class="el-scrollbar__view el-select-dropdown__list">
                  <!--选项-->
                  <li
                    @click.stop="industryClick(item, 'domain')"
                    :class="{
                      'el-select-dropdown__item': true,
                      selected: SeachData.area.includes(item.id),
                    }"
                    v-for="(item, index) in rightDomain"
                    :key="index"
                  >
                    <span>{{ item.fieldName }}</span>
                  </li>
                </ul>
              </div>
              <div class="el-scrollbar__bar is-horizontal">
                <div
                  class="el-scrollbar__thumb"
                  style="transform: translateX(0%)"
                ></div>
              </div>
              <div class="el-scrollbar__bar is-vertical">
                <div
                  class="el-scrollbar__thumb"
                  style="transform: translateY(0%)"
                ></div>
              </div>
            </div>
            <!---->
            <div x-arrow class="popper__arrow" style="left: 35px"></div>
          </div>
          <p style="margin-bottom: 0" v-if="areaShow == '0'">
            发布地区:&nbsp;
            <el-radio-group v-model="SeachData.releaseArea">
              <el-radio label="0">全部</el-radio>
              <el-radio label="2">境外</el-radio>
              <el-radio label="1">国内</el-radio>
            </el-radio-group>
            <template>
              <el-select
                v-if="SeachData.releaseArea == 1"
                multiple
                collapse-tags
                style="margin-left: 15px"
                v-model="SeachData.country"
                placeholder="请选择省份"
              >
                <el-option
                  v-for="(item, key) in areaList"
                  :key="key"
                  :value="item.regionName"
                  >{{ item.regionName }}</el-option
                >
              </el-select>
              <el-select
                v-if="SeachData.releaseArea == 2"
                multiple
                collapse-tags
                style="margin-left: 15px"
                v-model="SeachData.country"
                placeholder="请选择国家"
              >
                <el-option
                  v-for="(item, key) in countryList"
                  :key="key"
                  :value="item.regionName"
                  >{{ item.regionName }}</el-option
                >
              </el-select>
            </template>
            <el-checkbox-group
              v-model="SeachData.country"
              v-if="areaShow != '0'"
              style="display: inline-block"
            >
              <el-checkbox
                v-for="(item, index) in areaListComp"
                :key="index"
                :label="item.regionName"
              ></el-checkbox>
            </el-checkbox-group>
            <span v-if="areaShow != '0' && areaList.length == 0"
              >未选择地区</span
            >
          </p>
        </div>
        <p>
          <span
            style="
              width: 60px;
              display: inline-block;
              text-align: right;
              margin-right: 5px;
            "
            >关键词:</span
          >
          <el-input
            placeholder="请输入关键词,使用逗号分割(英文)"
            style="width: 430px"
            v-model="SeachData.keyword"
          ></el-input>
          <el-button
            type="primary"
            size="mini"
            @click="seachEvent"
            :loading="buttonDisabled"
            style="height: 36px; margin-left: 10px"
            >搜索</el-button
          >
          <el-button
            v-if="SwitchShow"
            type="primary"
            size="mini"
            @click="seniorSerch"
            style="height: 36px"
            >{{ seniorSerchFlag ? "普通搜索" : "高级搜索" }}</el-button
          >
        </p>
      </div>
      <div class="btn" v-if="urlType != 'dailyOverview'"></div>
    </div>
  </div>
</template>

<script>
import API from "@/api/ScienceApi/index.js";
export default {
  name: "topSeach",
  dicts: ["is_technology"],
  data() {
    return {
      typeShow: false,
      sonType: false,
      areaShow: 0,
      isInfo: "信息模式",
      customSwitch: false,
      publishArea: [],
      domainList: [],
      rightDomain: [],
      industry: [],
      rightIndustry: [],
      industryShow: false,
      domainShow: false,
    };
  },

  props: {
    ActiveData: {
      required: true,
      type: Object,
    },
    SwitchShow: {
      required: false,
      type: Boolean,
      default: true,
    },
    SeachData: {
      required: true,
    },
    buttonDisabled: {
      type: Boolean,
      default: false,
    },
    seniorSerchFlag: {
      type: Boolean,
      default: false,
    },
    areaList: {
      required: false,
    },
    countryList: {
      required: false,
    },
    urlType: {
      default: "",
    },
  },
  watch: {
    isInfo(newVaslue, OldValue) {
      this.$emit("EmitInfo", newVaslue);
    },
    // 'SeachData.releaseArea': {
    //   handler (newVal, oldVal) {
    //     this.SeachData.country = []
    //   },
    //   deep: true
    // },
    ActiveData(newVal, oldVal) {
      this.SeachData.radio = newVal.type;
      this.SeachData.releaseArea = newVal.areaType;
      this.areaShow = newVal.areaType;
      this.$set(this.SeachData, "isTechnology", 1);
      /* 发布地区-详细地区 */
      if (newVal.publishArea !== "") {
        this.creaList = newVal.publishArea.split(",");
        this.SeachData.country = newVal.publishArea.split(",");
      } else {
        this.creaList = [];
        this.SeachData.country = [];
      }
      if (this.SeachData.radio.length !== 1) {
        this.typeShow = false;
      } else {
        this.typeShow = true;
      }
    },
    buttonDisabled(newVal, old) {
      // console.log(newVal);
    },
    "SeachData.timeRange"(newVal, oldVal) {
      this.SeachData.customDay = [];
      if (newVal && oldVal) {
        if (newVal != 6) {
          this.seachEvent();
        }
      }
    },
    "SeachData.customDay"(newVal, oldVal) {
      if (newVal.length == 0) {
        return;
      }
      if (newVal && oldVal) {
        this.seachEvent();
      }
    },
    "SeachData.isTechnology"(newVal, oldVal) {
      if (newVal && oldVal) {
        this.seachEvent();
      }
    },
    "SeachData.radio"(newVal, oldVal) {
      if (newVal && this.seniorSerchFlag) {
        this.seachEvent();
      }
    },
    "SeachData.industry"(newVal, oldVal) {
      if (newVal && this.seniorSerchFlag) {
        this.seachEvent();
      }
    },
    "SeachData.area"(newVal, oldVal) {
      if (newVal && this.seniorSerchFlag) {
        this.seachEvent();
      }
    },
    "SeachData.releaseArea"(newVal, oldVal) {
      if (newVal && this.seniorSerchFlag) {
        this.SeachData.country = [];
        this.seachEvent();
      }
    },
    "SeachData.country"(newVal, oldVal) {
      if (newVal && this.seniorSerchFlag) {
        if (newVal.length > 0) {
          this.seachEvent();
        }
      }
    },
    deep: true,
  },
  computed: {
    areaListComp() {
      return this.areaList.filter((item) => {
        if (this.SeachData.country.includes(item.regionName)) {
          return item;
        } else {
          return;
        }
      });
    },
  },
  created() {
    this.SeachData.radio = this.ActiveData.type;
    this.openDialog();
  },
  methods: {
    async openDialog() {
      await API.areaList().then((data) => {
        if (data.code == 200) {
          this.domainList = data.data.slice(0, 5);
          this.rightDomain = data.data.slice(6);
          this.options = data.data;
          API.industry().then((value) => {
            this.industry = value.data.slice(0, 5);
            this.rightIndustry = value.data.slice(6);
            this.options1 = value.data;
          });
        }
      });
    },
    isInfoEvent(value) {},
    /* 重置搜索 */
    resetting() {
      this.SeachData.metaMode = ""; /* 匹配模式 */
      this.SeachData.sortMode = false; /* 排序模式 */
      this.SeachData.releaseArea = ""; /* 发布地区 */
      this.SeachData.timeRange = ""; /* 时间范围 */
      this.SeachData.industry = [];
      this.SeachData.area = [];
      this.SeachData.fieldName = "";
      this.SeachData.radio = this.ActiveData.type;
      this.seachEvent();
    },
    customDayChange() {
      if (this.urlType == "dailyOverview") this.seachEvent();
    },
    seniorSerch() {
      this.$emit("seniorSerch");
    },
    seachEvent() {
      this.$emit("SeachEvent", true);
    },
    /*  */
    industryClick(item, flag) {
      if (flag == "domain") {
        if (this.SeachData.area.includes(item.id)) {
          this.SeachData.area.splice(this.SeachData.area.indexOf(item.id), 1);
          return;
        }
        this.SeachData.area.push(item.id);
        return;
      }
      if (this.SeachData.industry.includes(item.id)) {
        this.SeachData.industry.splice(
          this.SeachData.industry.indexOf(item.id),
          1
        );
        return;
      }
      this.SeachData.industry.push(item.id);
    },
    /* 更多 */
    MoreEvent(flag, type) {
      if (flag == "industry") {
        this.industryShow = !this.industryShow;
      } else if (flag == "domain") {
        this.domainShow = !this.domainShow;
      } else if (flag == "parent" && this.industryShow) {
        this.industryShow = false;
      } else if (flag == "parent" && this.domainShow) {
        this.domainShow = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.toolBox {
  min-height: 130px;
  height: auto;
  padding-bottom: 10px;
  background-color: rgb(255, 255, 255);
  box-shadow: -1px 2px 15px #cecdcd;
  border-left: solid 1px rgb(221, 219, 219);
  margin-top: 20px;
  margin-bottom: 10px;

  .title {
    display: flex;
    justify-content: space-between;
    height: 70px;
    padding: 0 30px;
    font-size: 19px;
  }

  .mainTool {
    padding: 0 28px;
    margin-top: -30px;
    font-size: 14px;
    color: rgb(58, 58, 58);
  }

  .mainToolOne {
    margin-top: 20px;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    // align-items: center;
  }

  .mainToolTwo {
    display: flex;
    align-items: center;
    height: 40px;

    p {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .btn {
    margin: 15px 0 0 25px;
  }
}

.el-select__tags .el-tag:nth-child(n + 6) {
  display: block;
  /* 显示前5个标签 */
}

.el-select__tags .el-tag:nth-child(n + 11) {
  display: none;
  /* 折叠后的标签从第11个开始隐藏 */
}
</style>
