<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="来源名称" prop="sourceName">
        <el-input
          v-model="queryParams.sourceName"
          placeholder="请输入来源名称"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="平台类型" prop="sourceType">
        <el-select
          v-model="queryParams.sourceType"
          placeholder="请选择平台类型"
          @change="handleQuery"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.source_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否审核" prop="isReviewed">
        <el-select
          v-model="queryParams.isReviewed"
          placeholder="审核状态"
          @change="handleQuery"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.is_reviewed"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="primary"-->
      <!--          plain-->
      <!--          icon="el-icon-plus"-->
      <!--          size="mini"-->
      <!--          @click="handleAdd"-->
      <!--          v-hasPermi="['hot:list:add']"-->
      <!--        >新增</el-button>-->
      <!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-edit"
          size="mini"
          @click="handleRefresh"
          >更新</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multiple"
          @click="handlePass"
          style="margin-left: 20px"
          >审核通过</el-button
        >
      </el-col> -->
      <!--      <el-col :span="1.5">-->
      <!--        <el-button type="primary" plain icon="el-icon-edit" size="mini" :disabled="multiple" @click="handleCancelPass" v-hasPermi="['model:tech:cancel']">撤回审核通过</el-button>-->
      <!--      </el-col>-->
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multiple"
          @click="handleNoPass"
          >审核不通过</el-button
        >
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="listList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 280px)"
      ref="tableRef"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!--      <el-table-column label="主键" align="center" prop="id" />-->
      <el-table-column prop="isReviewed" label="审核" align="center" width="90">
        <template slot-scope="scope">
          <el-radio-group
            v-model="scope.row.isReviewed"
            class="radio-group"
            @input="(e) => handleRadioChange(e, scope.row.articleId)"
          >
            <el-radio :label="'1'" style="color: #67c23a">通过</el-radio>
            <el-radio :label="'2'" style="color: #f56c6c">不通过</el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
      <el-table-column
        label="文章id"
        align="center"
        prop="articleId"
        width="70"
      />
      <el-table-column prop="title" label="标题" align="center">
        <template slot-scope="scope">
          <el-link
            :href="`/expressDetails?id=${scope.row.articleId}&docId=${scope.row.articleId}`"
            :underline="false"
            target="_blank"
          >
            <span class="el-icon-document"> {{ scope.row.title }} </span>
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="中文标题" align="center" prop="cnTitle">
        <template slot-scope="scope">
          <span
            class="el-icon-document"
            style="word-break: normal; word-wrap: break-word"
          >
            {{ scope.row.cnTitle }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="sourceType"
        label="平台类型"
        align="center"
        width="80"
      >
        <template slot-scope="scope">
          <dict-tag
            v-if="scope.row.sourceType"
            :options="dict.type.source_type"
            :value="scope.row.sourceType"
          />
          <span v-else>未定义</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="sourceArea"
        label="所属领域"
        align="center"
        width="80"
      >
        <template slot-scope="scope">
          <dict-tag
            v-if="scope.row.sourceArea"
            :options="dict.type.source_area"
            :value="scope.row.sourceArea"
          />
          <span v-else>未定义</span>
        </template>
      </el-table-column> -->
      <el-table-column
        label="来源名称"
        align="center"
        prop="sourceName"
        width="120"
      />
      <el-table-column
        label="发布时间"
        align="center"
        prop="publishTime"
        width="95"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publishTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="95"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="60"
      >
        <template slot-scope="scope">
          <div
            style="
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
            "
          >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              style="margin-left: 0"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改热点列对话框 -->
    <!--    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body :close-on-click-modal="false">-->
    <!--      <el-form ref="form" :model="form" :rules="rules" label-width="80px">-->
    <!--        <el-form-item label="标题" prop="title">-->
    <!--          <el-input v-model="form.title" type="textarea" placeholder="请输入内容" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="中文标题" prop="cnTitle">-->
    <!--          <el-input v-model="form.cnTitle" type="textarea" placeholder="请输入内容" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="来源名称" prop="sourceName">-->
    <!--          <el-input v-model="form.sourceName" placeholder="请输入来源名称" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="文章原文链接URL" prop="originalUrl">-->
    <!--          <el-input v-model="form.originalUrl" type="textarea" placeholder="请输入内容" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="发布时间" prop="publishTime">-->
    <!--          <el-date-picker clearable-->
    <!--            v-model="form.publishTime"-->
    <!--            type="date"-->
    <!--            value-format="yyyy-MM-dd"-->
    <!--            placeholder="请选择发布时间">-->
    <!--          </el-date-picker>-->
    <!--        </el-form-item>-->
    <!--      </el-form>-->
    <!--      <div slot="footer" class="dialog-footer">-->
    <!--        <el-button type="primary" @click="submitForm">确 定</el-button>-->
    <!--        <el-button @click="cancel">取 消</el-button>-->
    <!--      </div>-->
    <!--    </el-dialog>-->

    <el-dialog
      :title="'修改文章'"
      :visible.sync="open"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="dialog_Box">
        <el-form
          :model="form"
          class="form_Style"
          label-position="top"
          ref="form"
          :rules="rules"
          size="mini"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="文章标题" prop="title">
                <el-input v-model="form.title"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="中文标题" prop="cnTitle">
                <el-input v-model="form.cnTitle"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="平台类型" prop="sourceType">
                <el-select
                  v-model="form.sourceType"
                  style="width: 100%"
                  clearable
                >
                  <el-option
                    v-for="dict in dict.type.source_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="媒体来源" prop="sourceName">
                <!-- <el-select
                  v-model="form.sourceName"
                  style="width: 100%"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="(item, index) in sourceTypeLists"
                    :key="index"
                    :label="item.name"
                    :value="item.name"
                  ></el-option>
                </el-select> -->
                <el-input
                  v-model="form.sourceName"
                  style="width: 100%"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="发布时间" prop="publishTime">
                <el-date-picker
                  v-model="form.publishTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetime"
                  style="width: 100%"
                  clearable
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="文章作者" prop="author">
                <el-input
                  v-model="form.author"
                  style="width: 100%"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="原文链接" prop="originalUrl">
            <el-input v-model="form.originalUrl"></el-input>
          </el-form-item>
          <el-form-item label="摘要" prop="summary">
            <el-input
              v-model="form.summary"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
            ></el-input>
          </el-form-item>
          <el-form-item label="中文摘要" prop="cnSummary">
            <el-input
              v-model="form.cnSummary"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
            ></el-input>
          </el-form-item>
          <el-form-item label="中文内容" prop="cnContent">
            <editor v-model="form.cnContent" :minHeight="150"></editor>
          </el-form-item>
          <el-form-item label="文章内容" prop="content">
            <editor v-model="form.content" :minHeight="150"></editor>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 报告导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox
              v-model="upload.updateSupport"
            />是否更新已经存在的报告数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
            >下载模板</el-link
          >
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listList,
  getList,
  delList,
  addList,
  updateList,
} from "@/api/article/hot";
import { getToken } from "@/utils/auth";
import {
  articleNoPass,
  articlePass,
  getArticleHot,
} from "@/api/article/articleHistory";
import API from "@/api/ScienceApi/index.js";
import { articleListEdit, uploadCover } from "@/api/articleCrawler/list";

export default {
  name: "List",
  dicts: ["source_type", "is_reviewed", "source_area"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      articleIds: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 热点列表格数据
      listList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 报告导入参数
      upload: {
        // 是否显示弹出层（报告导入）
        open: false,
        // 弹出层标题（报告导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的报告数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/reportData/list/importData",
        // url: process.env.VUE_APP_BASE_API + "reportData/list/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        articleId: null,
        sn: null,
        title: null,
        cnTitle: null,
        sourceType: null,
        sourceName: null,
        originalUrl: null,
        shortUrl: null,
        publishTime: null,
        status: null,
        isReviewed: null,
        userId: null,
        deptId: null,
        deleteBy: null,
        deleteTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询热点列列表 */
    getList() {
      this.loading = true;
      listList(this.queryParams).then((response) => {
        this.listList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.$nextTick(() => {
          this.scrollToTop();
        });
      });
    },
    // 表格滚动到顶部
    scrollToTop() {
      if (this.$refs.tableRef) {
        const tableEl = this.$refs.tableRef.$el.querySelector(
          ".el-table__body-wrapper"
        );
        if (tableEl) {
          tableEl.scrollTop = 0;
        }
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        articleId: null,
        sn: null,
        title: null,
        cnTitle: null,
        sourceType: null,
        sourceName: null,
        originalUrl: null,
        shortUrl: null,
        publishTime: null,
        status: null,
        userId: null,
        deptId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        deleteBy: null,
        deleteTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.articleIds = selection.map((item) => item.articleId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加热点列";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      // const id = row.id || this.ids
      // getList(id).then(response => {
      //   this.form = response.data;
      //   this.open = true;
      //   this.title = "修改热点列";
      // });
      API.AreaInfo(row.articleId).then((response) => {
        this.form = response.data;
        // this.form.sourceType = Number(this.form.sourceType)
        this.form.docId = row.articleId.toString();
        this.open = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            // updateList(this.form).then(response => {
            //   this.$modal.msgSuccess("修改成功");
            //   this.open = false;
            //   this.getList();
            // });
            let queryForm = JSON.parse(JSON.stringify(this.form));
            articleListEdit(queryForm).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addList(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const articleIds = row.articleId || this.articleIds;
      this.$modal
        .confirm('是否确认删除热点列编号为"' + articleIds + '"的数据项？')
        .then(function () {
          return delList(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "报告数据导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download(
        "reportData/list/importTemplate",
        {},
        `report_data_template_${new Date().getTime()}.xlsx`
      );
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      // this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.$modal.msgSuccess("导入成功!");
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    processSourceType(state) {
      if (state == 1) return "国内";
      if (state == 2) return "国外";
      if (state == 3) return "境内科技";
      if (state == 4) return "境外科技";
      return "未定义";
    },
    processReviewedState(state) {
      if (state == 0) return "未审核";
      if (state == 1) return "审核通过";
      if (state == 2) return "审核不通过";
      if (state == 3) return "待修改";
      if (state == 4) return "待撤回";
      return "未定义";
    },
    handleRefresh() {
      this.$modal
        .confirm("是否确认更新数据？")
        .then(function () {
          return getArticleHot();
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("更新数据");
        })
        .catch(() => {});
    },
    handlePass(row) {
      const articleIds = row.articleId || this.articleIds;
      this.$modal
        .confirm("是否确认审核通过编号为" + articleIds + "的数据？")
        .then(function () {
          return articlePass(articleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("审核通过");
        })
        .catch(() => {});
    },
    handleNoPass(row) {
      const articleIds = row.articleId || this.articleIds;
      this.$modal
        .confirm("是否确认审核不通过编号为" + articleIds + "的数据？")
        .then(function () {
          return articleNoPass(articleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("审核不通过");
        })
        .catch(() => {});
    },
    handleRadioChange(e, id) {
      if (e == "1") {
        console.log("通过");
        return articlePass(id);
      } else if (e == "2") {
        console.log("不通过");
        return articleNoPass(id);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .radio-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .el-radio {
    margin-right: 0;
    margin-bottom: 2px;
  }
}
</style>
