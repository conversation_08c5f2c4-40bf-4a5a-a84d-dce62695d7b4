<template>
  <div class="remengwenzhang-box">
    <div
      class="scroll-wrapper"
      ref="scrollWrapper"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @scroll="updateScrollbar"
    >
      <div class="scroll-content" ref="scrollContent">
        <div
          class="remengwenzhang-list"
          v-for="(item, index) in remengwenzhangList"
          :key="index"
          @click="openNewView(item)"
        >
          <div
            class="block"
            :style="{ background: item.color ? item.color : '#1bdcff' }"
          ></div>
          <div class="title">{{ item.title }}</div>
          <div class="sourceName">{{ item.sourceName }}</div>
          <div class="time">
            {{ parseTime(item.publishTime, "{y}-{m}-{d}") }}
          </div>
        </div>
      </div>
    </div>
    <div class="scroll-bar"></div>
  </div>
</template>

<script>
import { largeHotList } from "@/api/bigScreen/index1";

export default {
  data() {
    return {
      remengwenzhangList: [],
      scrollTimer: null,
      isHovered: false,
      scrollStep: 1,
    };
  },
  mounted() {
    this.init();
    this.updateScrollbar();
  },
  beforeDestroy() {
    this.clearScrollTimer();
  },
  methods: {
    init() {
      largeHotList({}, { pageNum: 1, pageSize: 100 }).then((res) => {
        this.$set(this, "remengwenzhangList", res.rows);
        this.$nextTick(() => {
          this.startScroll();
        });
      });
    },
    startScroll() {
      this.clearScrollTimer();
      const wrapper = this.$refs.scrollWrapper;
      const content = this.$refs.scrollContent;

      if (!wrapper || !content) return;

      this.scrollTimer = setInterval(() => {
        if (this.isHovered) return;

        if (wrapper.scrollTop >= content.scrollHeight - wrapper.clientHeight) {
          wrapper.scrollTop = 0;
        } else {
          wrapper.scrollTop += this.scrollStep;
        }
        this.updateScrollbar();
      }, 20);
    },
    clearScrollTimer() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null;
      }
    },
    handleMouseEnter() {
      this.isHovered = true;
    },
    handleMouseLeave() {
      this.isHovered = false;
      this.startScroll();
    },
    openNewView(item) {
      this.$emit("openNewView", item);
    },
    updateScrollbar() {
      const wrapper = this.$refs.scrollWrapper;
      if (!wrapper) return;

      const { scrollTop, scrollHeight, clientHeight } = wrapper;
      const scrollPercent = clientHeight / scrollHeight;
      const scrollbarHeight = Math.max(30, scrollPercent * clientHeight);
      const scrollbarTop = (scrollTop / scrollHeight) * clientHeight;

      document.documentElement.style.setProperty(
        "--scrollbar-height",
        `${scrollbarHeight}px`
      );
      document.documentElement.style.setProperty(
        "--scrollbar-top",
        `${scrollbarTop}px`
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.remengwenzhang-box {
  width: 100%;
  height: 100%;
  padding: 20px;
  border: 1px solid rgba(16, 216, 255, 0.4);
  background: rgba(0, 0, 0, 0.15);
  box-shadow: 0px 0px 8px 0px #0056ad;
  overflow: hidden;
  position: relative;

  .scroll-wrapper {
    height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
    position: relative;

    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  &::after {
    content: "";
    position: absolute;
    top: 20px;
    right: 0;
    height: calc(100% - 40px);
    width: 6px;
    background: rgba(16, 216, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
  }

  .scroll-bar {
    position: absolute;
    top: 20px;
    right: 0;
    width: 6px;
    height: var(--scrollbar-height, 100px);
    background: rgba(16, 216, 255, 0.4);
    border-radius: 3px;
    opacity: 0;
    transition: opacity 0.3s;
    transform: translateY(var(--scrollbar-top, 0));
    pointer-events: none;
  }

  &:hover {
    &::after,
    .scroll-bar {
      opacity: 1;
    }
  }

  .remengwenzhang-list {
    position: relative;
    height: 40px;
    // padding: 10px 0px 10px 20px;
    padding-left: 20px;
    display: flex;
    justify-content: space-between;
    cursor: pointer;

    .title {
      width: 330px;
      overflow: hidden;
      color: rgba(216, 240, 255, 0.8);
      text-overflow: ellipsis;
      white-space: nowrap;
      font-family: "Source Han Sans CN";
      font-size: 18px;
      font-weight: 700;
      line-height: 20px;
    }

    .time,
    .sourceName {
      width: 150px;
      color: rgba(216, 240, 255, 0.8);
      text-align: right;
      font-family: "Source Han Sans CN";
      font-size: 18px;
      font-weight: 400;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .block {
      position: absolute;
      left: 0px;
      top: 6px;
      width: 10px;
      height: 10px;
      border-radius: 1px;
      background: #1bdcff;
    }
  }
}
</style>
