<template>
  <div class="father" :style="{ height: height + 'px' }" ref="scroll" @scroll="scrollEvent">
    <div class="lineParent">
      <div class="title_Info">数据汇总</div>
      <div class="lineStyle" id="lineBox"></div>
    </div>
    <div class="chartsBox"></div>
    <div class="lineParent">
      <div class="title_Info">媒体TOP10</div>
      <div class="lineStyle" id="colBox"></div>
    </div>
    <div class="chartsBox">
      <div class="lineParent">
        <div class="title_Info">热门文章</div>
        <div class="cakeBox">
          <div class="tableStyle">
            <div class="titleTOp">
              <p>序号</p>
              <p>标题</p>
              <p>平台类型</p>
              <p>发布源</p>
            </div>
            <template v-if="articleList">
              <div class="mainTable" v-for="(item, key) in articleList" :key="key">
                <p>{{ key + 1 }}</p>
                <p :title="item.cnTitle">
                  <span @click="openNewView(item)" class="title_style">{{ item.cnTitle }}</span>
                </p>
                <p>{{ item.sourceType == 1 ? '微信公众号' : '网页' }}</p>
                <p>
                  <span>{{ item.sourceName }}</span>
                </p>
              </div>
            </template>
            <template v-else>
              <div class="empry">暂无数据</div>
            </template>
          </div>
        </div>
      </div>
      <div class="lineParent">
        <div class="title_Info">文章数量</div>
        <div class="cakeBoxMedium">
          <div class="tableStyle">
            <div class="titleTOp">
              <p>序号</p>
              <p>公众号名称</p>

              <p>文章数量</p>
            </div>
            <template v-if="statisticsList">
              <div class="mainTable" v-for="(item, key) in SourceList" :key="key">
                <p>{{ key + 1 }}</p>
                <p>{{ returnTop(item) }}</p>
                <p>{{ returnNum(item) }}</p>
              </div>
              <div style="text-align: right;margin-top: 20px;">
                <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="curretnPage"
                  :page-size="pageSize"
                  layout="total, prev, pager, next, jumper"
                  :total="total"
                ></el-pagination>
              </div>
            </template>
            <template v-else>
              <div class="empry">暂无数据</div>
            </template>
          </div>
        </div>
        <!-- 
          <div class="cakeBox" id="Annular">
        </div> 
        -->
      </div>
    </div>
    <div
      :class="{'callBackBtn':true,'opacityBtn':isShow,'hiddenBtn':!isShow}"
      :style="{opacity:isShow?1:0}"
      @click="mainScorll"
    >
      <i class="el-icon-caret-top"></i>
      <span>返回顶部</span>
    </div>
  </div>
</template>

<script>
import { renderLineCharts, renderCol, renderAnnular, renderNationwide } from '@/utils/renderLine.js'
export default {
  props: {
    height: {
      required: false,
      type: Number,
      default: 800
    },
    statisticsList: {
      default: () => {
        return []
      }
    },
    articleList: {
      default: () => {
        return []
      }
    },
    lineData: {
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      num: 0,
      total: 0,
      pageSize: 50,
      curretnPage: 1,
      SourceList: {},
      isShow: false
    }
  },
  computed: {},
  watch: {
    lineData: function (newVal, oldVal) {
      this.renderFoldLine()
    },
    statisticsList: {
      handler(newVal, oldVal) {
        let data = []
        this.total = newVal.size
        newVal.forEach(item => {
          data.push(item)
        })
        this.SourceList = data.slice(0, 10)
      },
      deep: true
    }
  },
  mounted() {
    this.renderFoldLine()
  },
  created() {},
  methods: {
    /* 平台折线 */
    renderFoldLine() {
      let res = this.chrtsDataHandle(this.lineData)
      let obj = {
        WeChat: [358, 290, 543, 123, 231, 341],
        Web: [432, 257, 366, 368, 432, 213, 312]
      }
      let charts = new renderLineCharts({ dom: 'lineBox', data: res.chartsData, xAxis: res.xAxis })
      charts.render()
      this.renderColEvent()
    },
    /* 柱状图 */
    renderColEvent() {
      let xAxis = {
          type: 'category',
          data: []
        },
        data = []
      this.statisticsList.forEach(item => {
        let key = Object.keys(item)[0]
        xAxis.data.push(key)
        data.push(item[key])
      })
      let charts = new renderCol({ dom: 'colBox', data: data, xAxis: xAxis })
      charts.render()
    },
    /* 属地占比-环形图 */
    renderAnnu() {
      let data = {
        data: [
          {
            name: '黑龙江',
            value: 100
          },
          {
            name: '北京',
            value: 400
          },
          {
            name: '天津',
            value: 200
          },
          {
            name: '贵州',
            value: 120
          },
          {
            name: '新疆',
            value: 140
          },
          {
            name: '四川',
            value: 180
          },
          {
            name: '深圳',
            value: 380
          }
        ],
        title: true,
        legend: false,
        dom: 'Annular'
      }
      new renderAnnular(data)
    },
    /* 返回顶部动画 */
    mainScorll() {
      var scrollStep = -this.$refs.scroll.scrollTop / (800 / 15) // 计算每一步滚动的距离
      var scrollInterval = setInterval(() => {
        if (this.$refs.scroll.scrollTop !== 0) {
          this.$refs.scroll.scrollBy(0, scrollStep) // 按照给定步长滚动窗口
        } else {
          clearInterval(scrollInterval) // 到达顶部时清除定时器
        }
      }, 15)
    },
    scrollEvent() {
      console.log(this.$refs.scroll.scrollTop, this.isShow)
      if (this.$refs.scroll.scrollTop > 200) {
        this.isShow = true
      } else {
        this.isShow = false
      }
    },
    /* 返回数据 */
    returnTop(item) {
      let str
      try {
        Object.keys(item).forEach(element => {
          str = element
        })
      } catch (error) {
        console.log(error)
      }
      return str
    },
    returnNum(item) {
      let num
      try {
        Object.keys(item).forEach(element => {
          num = item[element]
        })
      } catch (error) {
        console.log(error)
      }
      return num
    },

    /* 数据汇总图表数据处理 */
    chrtsDataHandle(lineData) {
      let WeArr = [],
        WyArr = [],
        Xdata = [],
        end_Time,
        start_Time,
        Year,
        endYear
      // switch (lineData.dateType)
      // {
      //   case "hour":/* 按小时计算 */
      //     WeArr.length = 24
      //     WyArr.length = 24
      //     WeArr.fill(0)
      //     WyArr.fill(0)
      //     /* 处理x轴数据 */
      //     for (let i = 0; i < 24; i++)
      //     {
      //       Xdata.push(`${i.toString().length == 1 ? '0' + i : i}:00`)
      //     }
      //     /* 处理y轴数据 */
      //     Object.keys(lineData.wyTrendCount).forEach(item => {
      //       let key = item.slice(11, 13) + ':00'
      //       WyArr[Xdata.lastIndexOf(key)] = lineData.wyTrendCount[item]
      //     });
      //     Object.keys(lineData.wxTrendCount).forEach(item => {
      //       let key = item.slice(11, 13) + ':00'
      //       WeArr[Xdata.lastIndexOf(key)] = lineData.wyTrendCount[item]
      //     });
      //     break;
      //   case 'day':
      //     end_Time = Number(lineData.endTime.slice(8, 10)),
      //       start_Time = Number(lineData.startTime.slice(8, 10)),
      //       Year = lineData.startTime.slice(0, 7)
      //     endYear = lineData.endTime.slice(0, 7)
      //     /* 跨越两个月的情况 */
      //     let end = Number(lineData.endTime.slice(5, 7)),
      //       start = Number(lineData.startTime.slice(5, 7)),
      //       num = (30 - Number(start_Time));
      //     if (end > start)
      //     {
      //       end_Time = (end_Time + num)
      //     }
      //     WeArr.length = end_Time
      //     WyArr.length = end_Time
      //     /* 数据填充 */
      //     WeArr.fill(0)
      //     WyArr.fill(0)
      //     /* 循环数据 */
      //     let a,
      //       i = 1;
      //     while (Xdata.length < (end_Time))
      //     {
      //       a = i
      //       let item
      //       if (start_Time <= 30)
      //       {
      //         i = start_Time
      //       } else if (start_Time <= 31)
      //       {
      //         i = 1
      //         a = 1
      //       }
      //       if (start_Time > 30)
      //       {
      //         item = endYear + '-' + (i.toString().length == 1 ? '0' + i : i)
      //       } else
      //       {
      //         item = Year + '-' + (i.toString().length == 1 ? '0' + i : i)
      //       }
      //       Xdata.push(item)
      //       i = a
      //       ++start_Time
      //       ++i
      //     }
      //     /* 处理y轴数据 */

      //     Object.keys(lineData.wyTrendCount).forEach(item => {
      //       let key = item.slice(8, 11)
      //       WyArr[Xdata.lastIndexOf(Year + '-' + key)] = lineData.wyTrendCount[item]
      //     });
      //     Object.keys(lineData.wxTrendCount).forEach(item => {
      //       let key = item.slice(8, 11)
      //       WeArr[Xdata.lastIndexOf(Year + '-' + key)] = lineData.wyTrendCount[item]
      //     });
      //     break;
      //   case 'month':
      //     end_Time = Number(lineData.endTime.slice(5, 7));
      //     start_Time = Number(lineData.startTime.slice(5, 7));
      //     Year = lineData.startTime.slice(0, 4);
      //     WeArr.length = end_Time;
      //     WyArr.length = end_Time;
      //     WeArr.fill(0);
      //     WyArr.fill(0);
      //     for (let i = start_Time; i <= end_Time; i++)
      //     {
      //       let item = Year + '-' + (i.toString().length == 1 ? '0' + i : i)
      //       Xdata.push(item)
      //     }

      //     /* 处理y轴数据 */
      //     Object.keys(lineData.wyTrendCount).forEach(item => {
      //       let key = item.slice(5, 7)
      //       WyArr[Xdata.lastIndexOf(Year + '-' + key)] = lineData.wyTrendCount[item]
      //     });
      //     Object.keys(lineData.wxTrendCount).forEach(item => {
      //       let key = item.slice(5, 7)
      //       WeArr[Xdata.lastIndexOf(Year + '-' + key)] = lineData.wyTrendCount[item]
      //     });
      //     break;
      //   default:
      //     break;
      // }
      let obj = JSON.stringify(lineData.wxTrendCount)
      if (lineData.wxTrendCount && obj !== '{}') {
        for (const item of Object.keys(lineData.wxTrendCount)) {
          Xdata.push(item)
          WeArr.push(lineData.wxTrendCount[item])
        }
      }
      return {
        xAxis: {
          type: 'category',
          data: Xdata
        },
        chartsData: [
          {
            name: '微信',
            data: WeArr,
            type: 'line'
          }
        ]
      }
    },
    /* 打开外网链接 */
    openNewView(item) {
      if (item.sourceType == 1) {
        if (item.shortUrl) {
          window.open(item.shortUrl)
          return
        }
        this.$message({ message: '该文章没有原文链接' })
      } else if (item.sourceType == 2) {
        if (item.originalUrl) {
          window.open(item.originalUrl)
          return
        }
        this.$message({ message: '该文章没有原文链接' })
      }
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
    },
    handleCurrentChange(current) {
      this.curretnPage = current
      let arr = []
      this.statisticsList.forEach(item => {
        arr.push(item)
      })
      let data = ['0,10', '10,20', '20,30', '30,40', '40,6', '50,60', '60,70', '70,80', '80,90']
      let num = current - 1
      this.SourceList = arr.slice(Number(num + '' + 0), Number(current + '' + 0))
    }
  }
}
</script>

<style lang="scss" scoped>
/* 返回顶部按钮 */
.callBackBtn {
  position: fixed;
  right: 50px;
  bottom: 80px;
  background-color: #ffffff;
  box-shadow: 0px 0px 11px 2px #b4b3b3;
  border-radius: 30px;
  width: 60px;
  height: 60px;
  color: #000000;
  font-size: 10px;
  margin-top: 10px;
  display: flex;
  text-align: center;
  flex-direction: column;
  justify-content: center;
  i {
    font-size: 22px;
  }
  span {
    width: 25px;
    margin: 0 auto;
  }
}
.opacityBtn {
  animation: opacityBtn 1.5s;
}
@keyframes opacityBtn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.hiddenBtn {
  animation: hiddenBtn 1.5s;
}
@keyframes hiddenBtn {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.empry {
  width: 100%;
  text-align: center;
  height: 70px;
  line-height: 70px;
}

.father {
  height: 800px;
  overflow: scroll;
}

.topInfo {
  margin-top: 8px;
  box-shadow: 2px 2px 4px 0px #dddddd;
  background-color: rgb(255, 255, 255);
  height: 80px;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .itemStyle {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;

    div {
      margin-left: 20px;
      font-weight: 500;

      p {
        height: 16px;
      }
    }
  }

  .round {
    background-color: rgb(61, 43, 226);
    font-size: 40px;
    text-align: center;
    line-height: 60px;
    color: #ffff;
    width: 60px;
    height: 60px;
    border-radius: 35px;
    margin: 10px 0;
  }
}

.lineParent {
  margin-top: 8px;
  width: 100%;
  padding: 10px 0 0 10px;
  min-height: 460px;
  box-shadow: 0px 1px 10px 2px #dddddd;
}

.nationwide {
  width: 100%;
  height: 730px;
  box-shadow: 0px 1px 10px 2px #dddddd;
  margin-top: 8px;
  padding: 10px 0 0 10px;
}

.nationwideCharts {
  width: 100%;
  height: 700px;
  // box-shadow: 0px 1px 10px 2px #dddddd;
  // margin-top: 8px;
  // padding: 10px 0 0 10px;
}

.lineStyle {
  width: 100%;
  height: 400px;
}

.chartsBox {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .cakeBox {
    width: 95%;
    margin: 0 auto;
    height: 400px;
    background-color: rgb(255, 255, 255);

    // box-shadow: 2px 0px 9px 0px #bebebe;
    .tableStyle {
      width: 98%;
      margin: 0 auto;
      padding-top: 10px;
      list-style: none;
      padding: none;
      display: flex;
      flex-direction: column;

      .titleTOp {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 40px;
        background-color: #4682b4;

        :first-child {
          flex: 1;
        }

        :nth-child(2) {
          flex: 4;
        }

        p {
          flex: 2;
          font-size: 14px;
          text-align: center;
          line-height: 40px;
          height: 40px;
          width: 100%;
          color: #ffffff;
          border: solid 1px rgb(219, 218, 218);
        }
      }

      .mainTable {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 32px;

        :first-child {
          flex: 1;
        }

        :nth-child(2) {
          flex: 4;
          overflow: hidden;
          text-align: left;
          white-space: nowrap;
          text-overflow: ellipsis;

          span {
            margin-left: 5px;
          }
        }

        :nth-child(4) {
          overflow: hidden;
          text-align: left;
          white-space: nowrap;
          text-overflow: ellipsis;

          span {
            margin-left: 5px;
          }
        }

        p {
          flex: 2;
          font-size: 14px;
          text-align: center;
          line-height: 32px;
          height: 32px;
          width: 100%;
          color: #252525;
          border: solid 1px rgb(219, 218, 218);
        }

        .title_style:hover {
          color: #0798f8;
          border-bottom: solid 1px #0798f8;
        }
      }
    }
  }

  .cakeBoxMedium {
    width: 95%;
    height: 400px;
    margin: 0 auto;
    background-color: rgb(255, 255, 255);

    // box-shadow: 2px 0px 9px 0px #bebebe;
    .tableStyle {
      width: 98%;
      margin: 0 auto;
      padding-top: 10px;
      list-style: none;
      padding: none;
      display: flex;
      flex-direction: column;

      .titleTOp {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 38px;
        background-color: #4682b4;

        :first-child {
          flex: 1;
        }

        :nth-child(2) {
          flex: 8;
        }

        :last-child {
          flex: 2;
        }

        p {
          flex: 2;
          font-size: 14px;
          text-align: center;
          line-height: 40px;
          height: 38px;
          width: 100%;
          color: #ffffff;
          border: solid 1px rgb(219, 218, 218);
        }
      }

      .mainTable {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 32px;

        :first-child {
          flex: 1;
        }

        :nth-child(2) {
          flex: 8;
          text-align: left;
          padding-left: 5px;
        }

        :last-child {
          flex: 2;
        }

        p {
          flex: 2;
          font-size: 14px;
          text-align: center;
          line-height: 32px;
          height: 32px;
          width: 100%;
          color: #252525;
          border: solid 1px rgb(219, 218, 218);
        }
      }
    }
  }
}
</style>