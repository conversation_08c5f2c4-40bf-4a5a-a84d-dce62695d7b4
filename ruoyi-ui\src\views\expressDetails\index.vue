<template>
  <div class="drawer_box">
    <div class="drawer_Style" :style="{ marginBottom: type ? '40px' : '100px' }">
      <div class="tabs-all">
        <div class="tabs" v-if="translationBtnShow">
          <span @click="translateEvent(drawerInfo)">翻译文章</span>
        </div>
      </div>
      <p class="title">
        {{ drawerInfo.cnTitle || drawerInfo.title }}
      </p>
      <p>
        <span class="source">{{ drawerInfo.sourceName }}</span>
        <span class="time">{{ drawerInfo.publishTime }}</span>
        <span class="author">{{ drawerInfo.author }}</span>
      </p>
      <el-divider></el-divider>
      <div style="line-height:30px" v-html="drawerInfo.cnContent && drawerInfo.cnContent.replace(/<img\b[^>]*>/gi, '')">
      </div>
      <el-empty description="当前文章暂无数据" v-if="!drawerInfo.cnContent"></el-empty>
    </div>
    <div class="liuyanBox" :style="{ height: type ? '50px' : '110px' }">
      <div class="morenzhuangtai" v-if="type">
        <div class="uesr">
          <img :src="avatar" class="avatar">
          <div class="name">{{ name }}</div>
        </div>
        <div class="button">
          <el-button type="text" icon="el-icon-edit" @click="type = false">写留言</el-button>
        </div>
      </div>
      <div class="shuruzhuangtai" v-else>
        <div class="top">
          <div class="uesr">
            <img :src="avatar" class="avatar">
            <div class="name">{{ name }}</div>
          </div>
          <div class="button">
            <el-button size="mini" type="primary" @click="submit">确定</el-button>
            <el-button size="mini" type="info" @click="close">取消</el-button>
          </div>
        </div>
        <div class="bottom">
          <el-input type="textarea" placeholder="请输入内容" v-model="textarea" maxlength="300" show-word-limit
            resize="none"></el-input>
        </div>
      </div>
    </div>
  </div>
</template>
    
<script>
import { mapGetters } from 'vuex'
import { feedbackAdd } from "@/api/article/leaveMessage";
import API from '@/api/ScienceApi/index.js'

export default {
  dicts: [],
  data() {
    return {
      drawerInfo: {},
      translationBtnShow: true,
      type: true,
      textarea: '',
    };
  },
  computed: {
    ...mapGetters(['roles', 'name', 'avatar']),
  },
  created() {
    this.translationBtnShow = true
    this.details()
  },
  methods: {
    async details() {
      await API.AreaInfo(this.$route.query.id).then(res => {
        if (res.code == 200) {
          this.drawerInfo = res.data
          this.drawerInfo.sourceType != '1' ? this.translationBtnShow = true : this.translationBtnShow = false
          if (this.drawerInfo.cnContent || this.drawerInfo.content) {
            this.drawerInfo.cnContent = (this.drawerInfo.cnContent || this.drawerInfo.content).replace(/\\n/g, (a, b, c) => {
              return '<br>'
            })
            this.drawerInfo.cnContent = (this.drawerInfo.cnContent || this.drawerInfo.content).replace(/\${[^}]+}/g, '<br>')
            this.drawerInfo.cnContent = (this.drawerInfo.cnContent || this.drawerInfo.content).replace('|xa0', '')
            this.drawerInfo.cnContent = (this.drawerInfo.cnContent || this.drawerInfo.content).replace('opacity: 0', '')
          }
        }
      })
    },
    close() {
      this.type = true
      this.textarea = ''
    },
    submit() {
      feedbackAdd({ articleId: this.$route.query.id, docId: this.$route.query.docId, cnTitle: this.drawerInfo.cnTitle || this.drawerInfo.title, content: this.textarea }).then(res => {
        if (res.code == 200) {
          this.$message({
            message: '留言成功',
            type: 'success'
          })
          this.close()
        }
      })
    },
    // 翻译文章
    translateEvent(row) {
      const loading = this.$loading({ lock: true, text: 'Loading', spinner: 'el-icon-loading', background: 'rgba(0, 0, 0, 0.7)' });
      API.translationTitle({ originalText: row.content, docId: this.$route.query.docId, id: row.id, translationField: 'content', translationType: 1 }).then(res => {
        this.drawerInfo.cnContent = res.data
        loading.close();
      }).catch(err => {
        loading.close();
      })
    },
  }
};
</script>
    
<style scoped lang="scss">
.drawer_box {
  display: flex;
  justify-content: center;
  user-select: text !important;
  background: #f5f7fa;
  min-height: 100vh;


  .drawer_Style {
    position: relative;
    z-index: 2;
    margin: 0px 20px 40px;
    width: 677px;
    // min-height: 80vh;
    background: #ffffff;
    padding: 10px 30px;

    .title {
      font-size: 22px;
      font-weight: 500px;
      // text-align: center;
    }

    .source {
      color: #0798f8;
      // text-align: center;
      font-size: 14px;
    }

    .time {
      font-size: 14px;
      // text-align: center;
      margin-left: 10px;
      color: #9b9b9b;
    }

    .author {
      color: #1a0997;
      // text-align: center;
      margin-left: 10px;
      font-size: 14px;
    }
  }
}

.liuyanBox {
  position: fixed;
  bottom: 0;
  width: 677px;
  z-index: 99;
  background-color: #e3e3ef;
  padding: 5px 10px;
  border-radius: 5px;

  .morenzhuangtai {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .shuruzhuangtai {
    height: 90px;

    .top {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .bottom {
      height: 50px;
    }
  }
}

.uesr {
  display: flex;
  align-items: center;
  height: 30px;

  .avatar {
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
  }

  .name {
    margin-left: 10px;
  }
}

.tabs-all {
  position: absolute;
  left: -40px;
  top: 0px;
  z-index: 99;
  height: 800px;
  width: 40px;

  .tabs {
    writing-mode: vertical-rl;
    /* 文字从上到下，从右到左 */
    height: 120px;
    width: 40px;
    font-weight: 800;
    font-size: 16px;
    color: #ffffff;
    line-height: 35px;
    text-align: center;
    font-style: normal;
    background: url("../../assets/bigScreenTwo/tab-active.png") no-repeat 0px 0px !important;
    background-size: 100% 100% !important;
    letter-spacing: 2px;
    margin-bottom: 10px;
    cursor: pointer;
  }
}
</style>
    
    