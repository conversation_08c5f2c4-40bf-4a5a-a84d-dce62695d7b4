<template>
  <div
    class="app-container"
    v-loading="loading"
    element-loading-text="检索中..."
  >
    <div class="search-init" v-if="isInit">
      <div class="search-init-img">
        <img src="@/assets/images/xiaoxinsousuo.png" />
      </div>
      <el-input
        placeholder="请输入内容"
        v-model="searchInput"
        clearable
        class="search-input"
      >
        <el-select
          v-model="searchInputSelect"
          slot="prepend"
          placeholder="请选择"
          class="search-input-select"
          size="medium"
        >
          <el-option
            v-for="item in searchInputSelectList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-button
          slot="append"
          icon="el-icon-search"
          @click="fetchSearchResults"
          >检索</el-button
        >
      </el-input>
    </div>
    <div class="search-result" v-else>
      <div class="search-result-header">
        <div class="search-result-header-img">
          <img src="@/assets/images/xiaoxinsousuo.png" />
        </div>
        <div class="search-result-header-input">
          <el-input
            placeholder="请输入内容"
            v-model="searchInput"
            clearable
            class="search-input"
          >
            <el-select
              v-model="searchInputSelect"
              slot="prepend"
              placeholder="请选择"
              class="search-input-select"
              size="medium"
            >
              <el-option
                v-for="item in searchInputSelectList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="fetchSearchResults"
              @keyup.enter.native="fetchSearchResults"
              >检索</el-button
            >
          </el-input>
        </div>
      </div>
      <div class="search-container">
        <div class="search-left">
          <div class="search-item">
            <div class="search-item-title">相近结果筛选</div>
            <div class="checkedList">
              <el-checkbox-group
                v-model="isCheckedList"
                @change="handleCheckChange"
              >
                <el-row>
                  <el-col :span="12" v-for="item in checkedList" :key="item">
                    <el-checkbox :label="item">{{ item }}</el-checkbox>
                  </el-col>
                </el-row>
              </el-checkbox-group>
            </div>
          </div>
          <div class="search-item">
            <div class="search-item-title">基本信息</div>
            <div class="search-item2-content" v-if="currentPerson">
              <div class="search-item2-content-item">
                <div class="search-item2-content-item-title">姓名：</div>
                <div class="search-item2-content-item-value">
                  {{ currentPerson.name }}
                </div>
              </div>
              <div class="search-item2-content-item">
                <div class="search-item2-content-item-title">联系方式：</div>
                <div class="search-item2-content-item-value">
                  {{ currentPerson.phone }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="search-right">
          <div class="search-item">
            <div class="search-item-title">人物历年所属机构</div>
            <div class="search-item3-content">
              <el-button
                v-for="(item, index) in allCompanies"
                :key="'company-' + index"
                >{{ item }}</el-button
              >
            </div>
          </div>
          <div class="search-item">
            <div class="search-item-title">知识图谱</div>
            <div class="knowledge-graph-legend">
              <div class="legend-item">
                <div class="legend-color" style="background-color: #1890ff;"></div>
                <span>中心人物</span>
              </div>
              <div class="legend-item">
                <div class="legend-color" style="background-color: #5AD8A6;"></div>
                <span>所属机构</span>
              </div>
              <div class="legend-item">
                <div class="legend-color" style="background-color: #FF9845;"></div>
                <span>相关观点</span>
              </div>
              <div class="legend-item">
                <div class="legend-color" style="background-color: #F04864;"></div>
                <span>被提及事由</span>
              </div>
            </div>
            <div class="knowledge-graph-container">
              <div id="knowledge-graph" ref="knowledgeGraph"></div>
            </div>
          </div>
          <div class="search-item">
            <div class="search-item-title">人物相关观点</div>
            <div class="search-item4-content">
              <div
                class="search-item4-content-item"
                v-for="(item, index) in allViews"
                :key="'view-' + index"
              >
                <div class="search-item4-content-item-title">
                  {{ item.view }}
                </div>
                <div
                  class="search-item4-content-item-value"
                  @click="openArticle(item)"
                >
                  引自《{{ item.articleName }}》
                </div>
              </div>
            </div>
          </div>
          <div class="search-item">
            <div class="search-item-title">人物被提及事由</div>
            <div class="search-item4-content">
              <div
                class="search-item4-content-item"
                v-for="(item, index) in allStaffs"
                :key="'staff-' + index"
              >
                <div class="search-item4-content-item-title">
                  {{ item.staff }}
                </div>
                <div
                  class="search-item4-content-item-value"
                  @click="openArticle(item)"
                >
                  引自《{{ item.articleName }}》
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script>
import API from "@/api/ScienceApi/index.js";
import G6 from "@antv/g6";

export default {
  data() {
    return {
      checkedList: [], // 搜索结果的人名列表
      isCheckedList: [], // 当前选中的人名列表
      searchResults: [], // 搜索结果数据
      searchInput: "", // 搜索输入内容
      loading: false, // 加载状态
      isInit: true, // 是否初始化
      searchInputSelect: "人物", // 搜索输入选择
      searchInputSelectList: [
        { label: "人物检索", value: "人物" },
        { label: "企业检索", value: "企业检索" },
        // { label: "机构检索", value: "机构" },
        { label: "智能检索", value: "智能检索" },
      ],
      knowledgeGraph: null, // G6图实例
    };
  },
  computed: {
    currentPerson() {
      // 获取第一个被勾选的人物的详细信息
      if (this.isCheckedList.length > 0) {
        // 按照页面显示顺序，查找第一个被勾选的人物
        for (const name of this.checkedList) {
          if (this.isCheckedList.includes(name)) {
            return this.searchResults.find((item) => item.name === name);
          }
        }
      }
      return null;
    },
    // 计算所有选中人物的所属机构
    allCompanies() {
      if (!this.searchResults || !this.isCheckedList.length) return [];

      // 过滤出已勾选的人物数据
      const selectedPersons = this.searchResults.filter((person) =>
        this.isCheckedList.includes(person.name)
      );

      // 收集所有选中人物的所有机构
      const companies = [];
      selectedPersons.forEach((person) => {
        if (person.company && person.company.length) {
          companies.push(...person.company);
        }
      });

      // 返回去重后的机构列表
      return [...new Set(companies)];
    },
    // 计算所有选中人物的观点
    allViews() {
      if (!this.searchResults || !this.isCheckedList.length) return [];

      // 过滤出已勾选的人物数据
      const selectedPersons = this.searchResults.filter((person) =>
        this.isCheckedList.includes(person.name)
      );

      // 收集所有选中人物的所有观点
      const views = [];
      selectedPersons.forEach((person) => {
        if (person.views && person.views.length) {
          views.push(...person.views);
        }
      });

      return views;
    },
    // 计算所有选中人物的被提及事由
    allStaffs() {
      if (!this.searchResults || !this.isCheckedList.length) return [];

      // 过滤出已勾选的人物数据
      const selectedPersons = this.searchResults.filter((person) =>
        this.isCheckedList.includes(person.name)
      );

      // 收集所有选中人物的所有被提及事由
      const staffs = [];
      selectedPersons.forEach((person) => {
        if (person.staffs && person.staffs.length) {
          staffs.push(...person.staffs);
        }
      });

      return staffs;
    },
    // 计算知识图谱数据
    knowledgeGraphData() {
      if (!this.searchResults || !this.isCheckedList.length) {
        console.log("没有搜索结果或选中项");
        return { nodes: [], edges: [] };
      }

      console.log("开始生成知识图谱数据:", {
        searchResults: this.searchResults,
        isCheckedList: this.isCheckedList,
        allCompanies: this.allCompanies,
        allViews: this.allViews,
        allStaffs: this.allStaffs
      });

      const nodes = [];
      const edges = [];

      // 中心节点 - 选中的人物名称
      const centerNodeId = 'center';
      const centerNodeLabel = this.isCheckedList.join('，');
      // 限制中心节点文字长度
      const displayLabel = centerNodeLabel.length > 5 ? centerNodeLabel.substring(0, 5) + '...' : centerNodeLabel;
      nodes.push({
        id: centerNodeId,
        label: displayLabel,
        originalLabel: centerNodeLabel, // 保存完整文字用于悬停显示
        type: 'center',
        x: 400,
        y: 300,
        size: [60, 25],
        style: {
          fill: '#1890ff',
          stroke: '#1890ff',
          lineWidth: 2,
          radius: 5,
        },
        labelCfg: {
          style: {
            fill: '#fff',
            fontSize: 11,
            fontWeight: 'bold',
          },
        },
      });

      // 机构父节点
      const companies = this.allCompanies;
      if (companies.length > 0) {
        const companyParentId = 'company_parent';
        nodes.push({
          id: companyParentId,
          label: '机构',
          type: 'category',
          x: 400 + 150 * Math.cos(Math.PI * 5 / 6), // 左下方 150度角
          y: 300 + 150 * Math.sin(Math.PI * 5 / 6),
          size: [40, 20],
          style: {
            fill: '#5AD8A6',
            stroke: '#5AD8A6',
            lineWidth: 2,
            radius: 4,
          },
          labelCfg: {
            style: {
              fill: '#fff',
              fontSize: 10,
              fontWeight: 'bold',
            },
          },
        });

        edges.push({
          source: centerNodeId,
          target: companyParentId,
          style: {
            stroke: '#5AD8A6',
            lineWidth: 2,
          },
        });

        // 机构子节点 - 花朵状分布
        companies.forEach((company, index) => {
          const nodeId = `company_${index}`;
          // 360度均匀分布，像花朵一样
          const angle = (index / companies.length) * 2 * Math.PI;
          const radius = 80; // 减少半径，让子节点更靠近分类节点
          const displayLabel = company.length > 5 ? company.substring(0, 5) + '...' : company;

          nodes.push({
            id: nodeId,
            label: displayLabel,
            originalLabel: company, // 保存完整文字
            type: 'company',
            x: (400 + 150 * Math.cos(Math.PI * 5 / 6)) + radius * Math.cos(angle),
            y: (300 + 150 * Math.sin(Math.PI * 5 / 6)) + radius * Math.sin(angle),
            size: [50, 18],
            style: {
              fill: '#A8E6CF',
              stroke: '#5AD8A6',
              lineWidth: 1,
              radius: 3,
            },
            labelCfg: {
              style: {
                fill: '#000',
                fontSize: 9,
              },
            },
          });

          edges.push({
            source: companyParentId,
            target: nodeId,
            style: {
              stroke: '#5AD8A6',
              lineWidth: 1,
            },
          });
        });
      }

      // 观点父节点
      const views = this.allViews.slice(0, 6); // 限制显示数量
      if (views.length > 0) {
        const viewParentId = 'view_parent';
        nodes.push({
          id: viewParentId,
          label: '观点',
          type: 'category',
          x: 400 + 150 * Math.cos(Math.PI / 6), // 右下方 30度角
          y: 300 + 150 * Math.sin(Math.PI / 6),
          size: [40, 20],
          style: {
            fill: '#FF9845',
            stroke: '#FF9845',
            lineWidth: 2,
            radius: 4,
          },
          labelCfg: {
            style: {
              fill: '#fff',
              fontSize: 10,
              fontWeight: 'bold',
            },
          },
        });

        edges.push({
          source: centerNodeId,
          target: viewParentId,
          style: {
            stroke: '#FF9845',
            lineWidth: 2,
          },
        });

        // 观点子节点 - 花朵状分布
        views.forEach((view, index) => {
          const nodeId = `view_${index}`;
          // 360度均匀分布，像花朵一样
          const angle = (index / views.length) * 2 * Math.PI;
          const radius = 80; // 减少半径，让子节点更靠近分类节点
          const displayLabel = view.view.length > 5 ? view.view.substring(0, 5) + '...' : view.view;

          nodes.push({
            id: nodeId,
            label: displayLabel,
            originalLabel: view.view, // 保存完整文字
            type: 'view',
            x: (400 + 150 * Math.cos(Math.PI / 6)) + radius * Math.cos(angle),
            y: (300 + 150 * Math.sin(Math.PI / 6)) + radius * Math.sin(angle),
            size: [50, 18],
            style: {
              fill: '#FFD4A3',
              stroke: '#FF9845',
              lineWidth: 1,
              radius: 3,
            },
            labelCfg: {
              style: {
                fill: '#000',
                fontSize: 9,
              },
            },
          });

          edges.push({
            source: viewParentId,
            target: nodeId,
            style: {
              stroke: '#FF9845',
              lineWidth: 1,
            },
          });
        });
      }

      // 事由父节点
      const staffs = this.allStaffs.slice(0, 6); // 限制显示数量
      if (staffs.length > 0) {
        const staffParentId = 'staff_parent';
        nodes.push({
          id: staffParentId,
          label: '事由',
          type: 'category',
          x: 400 + 150 * Math.cos(Math.PI * 3 / 2), // 正上方 270度角（向上）
          y: 300 + 150 * Math.sin(Math.PI * 3 / 2),
          size: [40, 20],
          style: {
            fill: '#F04864',
            stroke: '#F04864',
            lineWidth: 2,
            radius: 4,
          },
          labelCfg: {
            style: {
              fill: '#fff',
              fontSize: 10,
              fontWeight: 'bold',
            },
          },
        });

        edges.push({
          source: centerNodeId,
          target: staffParentId,
          style: {
            stroke: '#F04864',
            lineWidth: 2,
          },
        });

        // 事由子节点 - 花朵状分布
        staffs.forEach((staff, index) => {
          const nodeId = `staff_${index}`;
          // 360度均匀分布，像花朵一样
          const angle = (index / staffs.length) * 2 * Math.PI;
          const radius = 80; // 减少半径，让子节点更靠近分类节点
          const displayLabel = staff.staff.length > 5 ? staff.staff.substring(0, 5) + '...' : staff.staff;

          nodes.push({
            id: nodeId,
            label: displayLabel,
            originalLabel: staff.staff, // 保存完整文字
            type: 'staff',
            x: (400 + 150 * Math.cos(Math.PI * 3 / 2)) + radius * Math.cos(angle),
            y: (300 + 150 * Math.sin(Math.PI * 3 / 2)) + radius * Math.sin(angle),
            size: [50, 18],
            style: {
              fill: '#FFB3C1',
              stroke: '#F04864',
              lineWidth: 1,
              radius: 3,
            },
            labelCfg: {
              style: {
                fill: '#000',
                fontSize: 9,
              },
            },
          });

          edges.push({
            source: staffParentId,
            target: nodeId,
            style: {
              stroke: '#F04864',
              lineWidth: 1,
            },
          });
        });
      }

      return { nodes, edges };
    },

  },
  created() {
    // 在组件创建时获取路由参数并调用搜索接口
    this.searchInput = this.$route.query.searchInput || "";
    if (this.searchInput) {
      this.fetchSearchResults();
    }

    // 监听路由变化，当路由参数改变时重新搜索
    this.$watch(
      () => this.$route.query.searchInput,
      (newVal) => {
        if (newVal !== this.searchInput) {
          this.searchInput = newVal || "";
          this.fetchSearchResults();
        }
      }
    );
  },
  methods: {
    // 处理复选框变化
    handleCheckChange() {
      // 更新知识图谱
      this.$nextTick(() => {
        this.updateKnowledgeGraph();
      });
    },
    // 获取搜索结果
    fetchSearchResults() {
      // 显示加载中
      this.loading = true;
      // 构建请求参数
      const params = {
        text_query: this.searchInput,
        find_type: this.searchInputSelect,
        return_type: "json",
      };

      API.searchGraphData(params)
        .then((response) => {
          // 判断响应是否成功
          if (response.result) {
            this.handleSearchResults(response.result);
          } else {
            this.$message.error("获取搜索结果失败：返回数据格式不正确");
          }
        })
        .catch((error) => {
          console.error("API请求错误:", error);
          this.$message.error(
            "获取搜索结果失败：" + (error.message || "未知错误")
          );


        })
        .finally(() => {
          this.isInit = false;
          this.loading = false;
        });
    },

    // 处理搜索结果数据
    handleSearchResults(data) {
      try {
        let parsedData = data;

        // 处理API返回的字符串格式(带有\n的JSON字符串)
        if (typeof data === "string") {
          if (data === "[]") {
            parsedData = [];
            return;
          }
          parsedData = JSON.parse(JSON.parse(data));
          console.log("解析后的数据:", parsedData);
        }

        // 保存原始数据
        this.searchResults = Array.isArray(parsedData) ? parsedData : [];

        if (this.searchResults.length === 0) {
          this.$message.warning("没有找到相关人物信息");
          return;
        }

        // 处理数据，确保每个对象的结构一致
        this.searchResults = this.searchResults.map((person) => {
          // 处理company字段 - API返回字符串数组
          if (person.company && Array.isArray(person.company)) {
            // 如果是字符串数组，直接使用
            person.company = person.company
              .map(item => typeof item === 'string' ? item : (item.company || ''))
              .filter(Boolean); // 过滤掉null或空值
          } else {
            person.company = [];
          }

          // 处理views字段，确保有必要的字段
          if (person.views && Array.isArray(person.views)) {
            person.views = person.views.filter(
              (item) => item && item.view && item.articleName
            );
          } else {
            person.views = [];
          }

          // 处理staffs字段，确保有必要的字段
          if (person.staffs && Array.isArray(person.staffs)) {
            person.staffs = person.staffs.filter(
              (item) => item && item.staff && item.articleName
            );
          } else {
            person.staffs = [];
          }

          return person;
        });

        console.log("处理后的搜索结果:", this.searchResults);

        // 提取所有人名到复选框列表
        this.checkedList = this.searchResults.map((item) => item.name);

        // 默认只勾选第一个，而不是全部勾选
        this.isCheckedList =
          this.checkedList.length > 0 ? [this.checkedList[0]] : [];



        // 初始化知识图谱 - 确保在DOM更新后执行
        console.log("准备初始化知识图谱，当前数据:", {
          searchResults: this.searchResults,
          isCheckedList: this.isCheckedList,
          isInit: this.isInit
        });

        // 使用setTimeout确保DOM完全更新后再初始化
        setTimeout(() => {
          this.initKnowledgeGraph();
        }, 100);
      } catch (error) {
        console.error("处理搜索结果数据错误:", error);
        this.$message.error("处理搜索结果失败：" + error.message);
      }
    },

    // 打开文章
    openArticle(item) {
      if (item && item.articleSn) {
        window.open(`/expressDetails?articleSn=${item.articleSn}`, "_blank");
      }
    },
    // 初始化知识图谱
    initKnowledgeGraph() {
      console.log("开始初始化知识图谱");
      console.log("当前状态:", {
        isInit: this.isInit,
        hasRef: !!this.$refs.knowledgeGraph,
        searchResultsLength: this.searchResults.length,
        isCheckedListLength: this.isCheckedList.length
      });

      if (!this.$refs.knowledgeGraph) {
        console.log("知识图谱容器不存在，可能是因为页面还在初始化状态");
        // 如果容器不存在，可能是因为页面还在初始化，延迟重试
        if (!this.isInit && this.searchResults.length > 0) {
          setTimeout(() => {
            this.initKnowledgeGraph();
          }, 500);
        }
        return;
      }

      // 如果已存在图实例，先销毁
      if (this.knowledgeGraph) {
        this.knowledgeGraph.destroy();
      }

      // 获取容器尺寸
      const container = this.$refs.knowledgeGraph;
      const containerWidth = container.offsetWidth || 800;
      const containerHeight = container.offsetHeight || 600;

      // 创建G6图实例
      this.knowledgeGraph = new G6.Graph({
        container: this.$refs.knowledgeGraph,
        width: containerWidth,
        height: containerHeight,
        modes: {
          default: ['drag-canvas', 'zoom-canvas'], // 简化交互模式
        },
        defaultNode: {
          size: [70, 25],
          type: 'rect',
          style: {
            lineWidth: 1,
            stroke: '#5B8FF9',
            fill: '#C6E5FF',
            radius: 4,
          },
          labelCfg: {
            style: {
              fill: '#000',
              fontSize: 10,
              fontWeight: 'normal',
            },
          },
        },
        defaultEdge: {
          style: {
            stroke: '#e2e2e2',
            lineWidth: 2,
          },
        },
        nodeStateStyles: {
          hover: {
            // fill: '#d3f261',
            // stroke: '#96c93f',
            lineWidth: 3,
          },
        },
        edgeStateStyles: {
          hover: {
            stroke: '#1890ff',
            lineWidth: 3,
          },
        },
        // 移除布局配置，避免enableTick错误
      });

      // 添加鼠标悬停效果和提示
      try {
        this.knowledgeGraph.on('node:mouseenter', (e) => {
          const nodeItem = e.item;
          if (nodeItem && this.knowledgeGraph) {
            this.knowledgeGraph.setItemState(nodeItem, 'hover', true);

            // 显示完整文字提示
            const model = nodeItem.getModel();
            if (model.originalLabel && model.originalLabel !== model.label) {
              this.showTooltip(e, model.originalLabel);
            }
          }
        });

        this.knowledgeGraph.on('node:mouseleave', (e) => {
          const nodeItem = e.item;
          if (nodeItem && this.knowledgeGraph) {
            this.knowledgeGraph.setItemState(nodeItem, 'hover', false);
            this.hideTooltip();
          }
        });

        this.knowledgeGraph.on('edge:mouseenter', (e) => {
          const edgeItem = e.item;
          if (edgeItem && this.knowledgeGraph) {
            this.knowledgeGraph.setItemState(edgeItem, 'hover', true);
          }
        });

        this.knowledgeGraph.on('edge:mouseleave', (e) => {
          const edgeItem = e.item;
          if (edgeItem && this.knowledgeGraph) {
            this.knowledgeGraph.setItemState(edgeItem, 'hover', false);
          }
        });
      } catch (error) {
        console.error("添加事件监听器错误:", error);
      }

      // 渲染数据
      this.updateKnowledgeGraph();

      // 添加窗口大小变化监听器
      this.resizeHandler = () => {
        if (this.knowledgeGraph && this.$refs.knowledgeGraph) {
          const container = this.$refs.knowledgeGraph;
          const containerWidth = container.offsetWidth || 800;
          const containerHeight = container.offsetHeight || 600;
          this.knowledgeGraph.changeSize(containerWidth, containerHeight);
          this.knowledgeGraph.fitView();
        }
      };
      window.addEventListener('resize', this.resizeHandler);
    },
    // 更新知识图谱数据
    updateKnowledgeGraph() {
      if (!this.knowledgeGraph) {
        console.log("知识图谱实例不存在");
        return;
      }

      const data = this.knowledgeGraphData;
      console.log("知识图谱数据:", data);

      if (data.nodes.length === 0) {
        console.log("没有节点数据");
        return;
      }

      try {
        // 清除之前的数据
        this.knowledgeGraph.clear();

        // 设置新数据
        this.knowledgeGraph.data(data);

        // 渲染
        this.knowledgeGraph.render();

        // 适应画布
        setTimeout(() => {
          this.knowledgeGraph.fitView();
        }, 100);

        console.log("知识图谱渲染完成");
      } catch (error) {
        console.error("知识图谱渲染错误:", error);
      }
    },

    // 显示提示框
    showTooltip(e, text) {
      // 移除已存在的提示框
      this.hideTooltip();

      // 创建提示框
      const tooltip = document.createElement('div');
      tooltip.id = 'knowledge-graph-tooltip';
      tooltip.style.cssText = `
        position: absolute;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        max-width: 200px;
        word-wrap: break-word;
        z-index: 1000;
        pointer-events: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      `;
      tooltip.textContent = text;

      // 添加到页面
      document.body.appendChild(tooltip);

      // 设置位置
      const rect = this.$refs.knowledgeGraph.getBoundingClientRect();
      tooltip.style.left = (e.canvasX + rect.left + 10) + 'px';
      tooltip.style.top = (e.canvasY + rect.top - 30) + 'px';
    },

    // 隐藏提示框
    hideTooltip() {
      const tooltip = document.getElementById('knowledge-graph-tooltip');
      if (tooltip) {
        tooltip.remove();
      }
    },

  },
  mounted() {
    // 如果页面已经有搜索结果，初始化知识图谱
    if (!this.isInit && this.searchResults.length > 0 && this.isCheckedList.length > 0) {
      setTimeout(() => {
        this.initKnowledgeGraph();
      }, 200);
    }
  },
  beforeDestroy() {
    // 移除窗口大小变化监听器
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler);
    }

    // 销毁G6图实例
    if (this.knowledgeGraph) {
      this.knowledgeGraph.destroy();
      this.knowledgeGraph = null;
    }
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  height: calc(100vh - 56px);
  overflow-y: auto;
  position: relative;
}
.search-init {
  width: 50%;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;

  .search-init-img {
    width: 60%;
    margin: 0 auto;
    margin-bottom: 10px;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}

::v-deep .search-input {
  height: 60px;

  .search-input-select {
    width: 150px;
  }

  & > .el-input__inner {
    height: 60px;
    line-height: 60px;
    border: 2px solid #304156;
    border-left: none;
    border-right: none;
    font-size: 18px;
    color: #000;
  }

  .el-input-group__prepend {
    border: 2px solid #304156;
    font-size: 18px;
    color: #000;
    border-radius: 10px 0 0 10px;

    .el-select__caret {
      color: #000;
    }
  }

  .el-input-group__append {
    border: 2px solid #304156;
    font-size: 18px;
    background-color: #304156;
    color: #fff;
    border-radius: 0 10px 10px 0;
    padding: 0 30px;
  }
}

.search-result {
  padding: 0 10%;

  .search-result-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .search-result-header-img {
      width: 220px;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    .search-result-header-input {
      flex: 1;
      padding-left: 10px;
    }
  }

  .search-container {
    display: flex;
    .search-left {
      width: 30%;
    }
    .search-right {
      flex: 1;
      padding-left: 30px;
    }

    .search-item {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px 20px;
      margin-bottom: 20px;

      .search-item2-content {
        .search-item2-content-item {
          margin-bottom: 10px;
          .search-item2-content-item-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 4px;
          }
          .search-item2-content-item-value {
            font-size: 14px;
            color: #606266;
          }
        }
      }

      .search-item3-content {
        display: flex;
        flex-wrap: wrap;
        .el-button {
          margin-bottom: 10px;
          margin-left: 0px;
          margin-right: 10px;
        }
      }

      .search-item4-content {
        display: flex;
        flex-direction: column;
        max-height: 268px;
        overflow-y: auto;
        .search-item4-content-item {
          margin-bottom: 20px;
          .search-item4-content-item-title {
            margin-bottom: 4px;
            color: #303133;
            font-size: 14px;
          }
          .search-item4-content-item-value {
            color: #636ae8;
            cursor: pointer;
            font-size: 14px;
          }
        }
      }
    }

    .search-item-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 15px;
    }
  }
}

::v-deep .el-checkbox__label {
  vertical-align: text-top;
  white-space: normal;
  word-break: break-all;
  width: calc(100% - 10px);
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #636ae8;
  border-color: #636ae8;
}

::v-deep .el-checkbox__inner:hover {
  border-color: #636ae8;
}

::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #636ae8;
}

.knowledge-graph-legend {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
  gap: 20px;

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #606266;

    .legend-color {
      width: 16px;
      height: 16px;
      border-radius: 3px;
      border: 1px solid #ddd;
    }
  }
}

.knowledge-graph-container {
  width: 100%;
  height: 700px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fafafa;

  #knowledge-graph {
    width: 100%;
    height: 100%;
  }
}

// 知识图谱样式
.knowledge-graph-legend {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
  gap: 20px;

  .legend-item {
    display: flex;
    align-items: center;
    font-size: 12px;

    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;
      margin-right: 6px;
    }
  }
}

.knowledge-graph-container {
  display: flex;
  justify-content: center;
  width: 100%;
  height: 600px; // 设置固定高度

  #knowledge-graph {
    width: 100%;
    height: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #fafafa;
  }
}
</style>
