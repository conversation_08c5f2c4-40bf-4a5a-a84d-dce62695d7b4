<!-- 简报新增&&定时任务 -->
<template>
  <div>
    <el-drawer
      :title="distinguish"
      :visible.sync="visble"
      direction="rtl"
      :before-close="handleClose"
      @open="drawerEvent"
      size="800px"
    >
      <div class="formDiv">
        <el-form :model="FormModel" ref="Form" label-position="top">
          <el-form-item
            :label="title + '名称'"
            prop="briefingName"
            :rules="[
              { required: true, message: '请填写名称', trigger: 'blur' },
            ]"
          >
            <el-input
              v-model="FormModel.briefingName"
              :disabled="distinguish == '查看定时任务'"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="选择模板"
            label-position="top"
            v-if="distinguish == '新建简报'"
          >
            <el-select
              v-model="FormModel.report"
              placeholder="请选择模板,当前为默认模板"
              style="width: 100%"
            >
              <el-option label="微信公众号模板" :value="1"></el-option>
              <el-option label="科技前沿资讯" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="平台类型"
            prop="platformType"
            v-if="distinguish == '新建简报' && switchSourceType"
          >
            <el-select
              v-model="FormModel.platformType"
              multiple
              clearable
              :disabled="distinguish == '查看定时任务'"
              style="width: 100%"
            >
              <el-option label="微信公众号" value="1"></el-option>
              <el-option label="网站" value="2"></el-option>
            </el-select>
          </el-form-item>

          <!-- 新增编辑和主编选择框 -->
          <el-form-item
            label="主编"
            prop="chiefEditor"
            v-if="distinguish == '新建简报'"
          >
            <el-input
              v-model="currentUserName"
              disabled
              placeholder="主编（默认为当前登录人，不能修改）"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item
            label="编辑"
            prop="editors"
            v-if="distinguish == '新建简报'"
          >
            <el-select
              v-model="FormModel.editors"
              multiple
              clearable
              placeholder="请选择编辑"
              style="width: 100%"
            >
              <el-option
                v-for="user in editorUsers"
                :key="user.userId"
                :label="user.userName"
                :value="user.userId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <template v-if="distinguish !== '新建简报'">
            <el-form-item
              v-if="title !== '定时任务'"
              label="分析时段"
              prop="TImeInterval"
              :rules="[
                {
                  required: true,
                  message: '请选择时间范围',
                  trigger: 'change',
                },
              ]"
            >
              <!-- <el-select v-model="FormModel.analysisTime">
                <el-option label="全部" value="全部" />
                <el-option label="时间段选择" value="时间段选择" />
                </el-select>-->
              <!-- v-if="FormModel.analysisTime == '时间段选择'" -->
              <el-date-picker
                value-format="yyyy-MM-dd HH:mm:ss"
                unlink-panels
                v-model="FormModel.TImeInterval"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
            <el-form-item
              v-else
              label="分析时段"
              prop="sendTime.time"
              :rules="[
                { required: true, message: '请选择时间', trigger: 'change' },
              ]"
            >
              <el-tabs v-model="FormModel.switchTime" type="card">
                <el-tab-pane label="按天" name="1">
                  <el-row>
                    <el-col :span="2">每天:</el-col>
                    <el-col :span="4">
                      <el-time-picker
                        :disabled="distinguish == '查看定时任务'"
                        value-format="HH:mm:ss"
                        format="HH:mm"
                        v-model="FormModel.sendTime.time"
                        :picker-options="{
                          selectableRange: '00:00:00 - 23:00:00',
                        }"
                        placeholder="任意时间点"
                      ></el-time-picker>
                    </el-col>
                  </el-row>
                </el-tab-pane>
                <el-tab-pane label="按周" name="2">
                  <el-row :gutter="10">
                    <el-col :span="3">每周:</el-col>
                    <el-col :span="5">
                      <el-select
                        v-model="FormModel.sendTime.week"
                        :disabled="distinguish == '查看定时任务'"
                      >
                        <el-option
                          v-for="(item, key) in aWeak"
                          :key="key"
                          :label="item"
                          :value="item"
                        ></el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="4">
                      <el-time-picker
                        value-format="HH:mm:ss"
                        format="HH:mm"
                        v-model="FormModel.sendTime.time"
                        :picker-options="{
                          selectableRange: '00:00:00 - 23:00:00',
                        }"
                        placeholder="任意时间点"
                      ></el-time-picker>
                    </el-col>
                  </el-row>
                </el-tab-pane>
                <el-tab-pane label="按月" name="3">
                  <el-row :gutter="10">
                    <el-col :span="3">每月:</el-col>
                    <el-col :span="5">
                      <el-select
                        v-model="FormModel.sendTime.month"
                        :disabled="distinguish == '查看定时任务'"
                      >
                        <el-option
                          v-for="(item, key) in monthList"
                          :key="key"
                          :label="item"
                          :value="item"
                        ></el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="4">
                      <el-time-picker
                        value-format="HH:mm:ss"
                        format="HH:mm"
                        v-model="FormModel.sendTime.time"
                        :picker-options="{
                          selectableRange: '00:00:00 - 23:00:00',
                        }"
                        placeholder="任意时间点"
                      ></el-time-picker>
                    </el-col>
                  </el-row>
                </el-tab-pane>
              </el-tabs>
            </el-form-item>
            <el-form-item
              label="平台类型"
              prop="platformType"
              v-if="switchSourceType"
            >
              <el-select
                v-model="FormModel.platformType"
                multiple
                clearable
                :disabled="distinguish == '查看定时任务'"
                style="width: 100%"
              >
                <el-option label="微信公众号" value="1"></el-option>
                <el-option label="网站" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="关键词" prop="keyWords">
              <el-input
                type="textarea"
                :rows="4"
                :disabled="distinguish == '查看定时任务'"
                placeholder="请输入内容"
                v-model="FormModel.keyWords"
              ></el-input>
            </el-form-item>
            <el-form-item label="排除词" prop="excludeWords">
              <el-input
                type="textarea"
                :rows="2"
                :disabled="distinguish == '查看定时任务'"
                placeholder="请输入内容"
                v-model="FormModel.excludeWords"
              ></el-input>
            </el-form-item>
          </template>
          <el-form-item style="margin-top: 40px; text-align: center">
            <el-button @click="handleClose">取消</el-button>
            <el-button
              type="primary"
              @click="onSubmit"
              :disabled="distinguish == '查看定时任务'"
              >确定</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  props: {
    visble: {
      required: true,
      type: Boolean,
    },
    switchView: {
      required: true,
      type: String,
    },
    distinguish: {
      reuqired: false,
    },
    // 是否可选平台类型
    switchSourceType: {
      default: true,
    },
    editData: {
      required: false,
      default: () => {
        return {};
      },
    },
    // 编辑角色用户列表
    editorUsers: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      FormModel: {
        briefingName: "" /* 简报名称 */,
        analysisTime: "" /* 分析时间 */,
        platformType: "" /* 平台类型 */,
        emotion: "" /* 情感属性 */,
        metaWay: "" /* 匹配方式 */,
        release: "" /* 发布地区 */,
        area: "" /* 国家或地区 */,
        keyWords: "" /* 关键词 */,
        excludeWords: "" /* 排除词 */,
        report: "" /* 模板 */,
        sendTime: {
          type: "",
          time: "",
          date: "" /* 天 */,
          month: "" /* 月 */,
          week: "" /* 周 */,
        } /* 发送时间 */,
        TImeInterval: "",
        switchTime: "1",
        editors: [] /* 编辑 */,
        chiefEditor: "" /* 主编 */,
      },
      title: "简报",
      aWeak: ["1", "2", "3", "4", "5", "6", "7"],
      monthList: [],
    };
  },
  computed: {
    // 获取当前登录用户ID
    currentUserId() {
      const userInfo = JSON.parse(
        window.sessionStorage.getItem("userInfo") || "{}"
      );
      return userInfo.userId;
    },
    // 获取当前登录用户姓名
    currentUserName() {
      const userInfo = JSON.parse(
        window.sessionStorage.getItem("userInfo") || "{}"
      );
      return userInfo.userName || "";
    },
  },
  watch: {
    editData: function (newVal, oldVal) {},

  },
  beforeMount() {},
  mounted() {},
  beforeCreate() {},
  created() {
    for (let i = 1; i <= 31; i++) {
      this.monthList.push(i);
    }
  },
  beforeUpdate() {},
  updated() {
    switch (this.switchView) {
      case "定时任务":
        this.title = "定时任务";
        break;

      default:
        this.title = "简报";
        break;
    }
  },
  beforeDestroy() {},
  destroyed() {},
  methods: {
    handleClose(data) {
      this.$emit("beforCloe", false);
      this.resetting();
    },
    /* 重置 */
    resetting() {
      this.FormModel = {
        briefingName: "" /* 简报名称 */,
        analysisTime: "" /* 分析时间 */,
        platformType: "" /* 平台类型 */,
        emotion: "" /* 情感属性 */,
        metaWay: "" /* 匹配方式 */,
        release: "" /* 发布地区 */,
        area: "" /* 国家或地区 */,
        keyWords: "" /* 关键词 */,
        excludeWords: "" /* 排除词 */,
        sendTime: {
          type: "",
          time: "",
          date: "" /* 天 */,
          month: "" /* 月 */,
          week: "" /* 周 */,
        } /* 发送时间 */,
        TImeInterval: "",
        switchTime: "1",
        editors: [] /* 编辑 */,
        chiefEditor: "" /* 主编 */,
      };

      // this.$refs.Form.resetFields();
    },
    onSubmit() {
      this.$refs.Form.validate((vold) => {
        if (vold) {
          // let model = JSON.parse(JSON.stringify(this.FormModel))
          this.$emit("onsubmit", this.FormModel);
          this.resetting();
        }
      });
    },
    /* 表单回显 */
    formInfo() {
      let time = "";
      try {
        time = this.editData.createTime.slice(11, 19);
      } catch (error) {}
      if (Object.keys(this.editData).length) {
        this.FormModel = {
          id: this.editData.id,
          briefingName: this.editData.title /* 简报名称 */,
          analysisTime: "" /* 分析时间 */,
          platformType: this.editData.sourceType.split(",") /* 平台类型 */,
          keyWords: this.editData.keywords /* 关键词 */,
          excludeWords: this.editData.exclusions /* 排除词 */,
          report: "",
          sendTime: {
            type: this.editData.type,
            time: time,
            date: "" /* 天 */,
            month: this.editData.dayOfMonth /* 月 */,
            week: this.editData.dayOfWeek /* 周 */,
          } /* 发送时间 */,
          TImeInterval: "",
          switchTime: this.editData.type,
          editors: this.editData.editors || [] /* 编辑 */,
          chiefEditor: this.editData.chiefEditor || "" /* 主编 */,
        };
      }
    },
    drawerEvent() {
      if (this.distinguish == "定时任务") {
        this.resetting();
        return;
      }

      if (this.distinguish === "新建简报") {
        // 新建简报时，设置默认主编为当前登录用户
        this.FormModel.chiefEditor = this.currentUserId;
      } else {
        this.formInfo();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.formDiv {
  width: 92%;
  margin: 0 4%;

  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
