/* 热词图 */

import * as echarts from "echarts";
require("echarts-wordcloud");
const obj = {
  detail: "微博热搜关键词",
  data: [
    { name: "世界杯", rank: 1.0, heat: 185, count: 1 },
    { name: "直播间", rank: 1.0, heat: 100, count: 1 },
    { name: "377面霜", rank: 1.0, heat: 66, count: 1 },
    { name: "李佳琪", rank: 1.0, heat: 55, count: 1 },
    { name: "捡漏", rank: 1.0, heat: 55, count: 1 },
    { name: "377精华", rank: 1.0, heat: 66, count: 1 },
    { name: "广州疫情", rank: 1.0, heat: 165, count: 1 },
    { name: "海珠疫情", rank: 1.0, heat: 155, count: 1 },
    { name: "疫情", rank: 1.0, heat: 225, count: 1 },
    { name: "城中村", rank: 1.0, heat: 85, count: 1 },
    { name: "石家庄", rank: 1.0, heat: 105, count: 1 },
    { name: "摆烂政策", rank: 1.0, heat: 105, count: 1 },
    { name: "大学生", rank: 1.0, heat: 95, count: 1 },
    { name: "阿根廷vs沙特阿拉伯", rank: 1.0, heat: 55, count: 1 },
    { name: "梅西世界杯首秀", rank: 1.0, heat: 55, count: 1 },
    { name: "广州新增本土感染者8210例", rank: 1.0, heat: 85, count: 1 },
    { name: "成都疫情防控", rank: 1.0, heat: 65, count: 1 },
    { name: "今晚看梅西阿根廷", rank: 1.0, heat: 55, count: 1 },
    { name: "美国vs威尔士", rank: 1.0, heat: 55, count: 1 },
    { name: "中国男足", rank: 1.0, heat: 55, count: 1 },
    { name: "世界杯", rank: 1.0, heat: 185, count: 1 },
    { name: "直播间", rank: 1.0, heat: 100, count: 1 },
    { name: "377面霜", rank: 1.0, heat: 66, count: 1 },
    { name: "李佳琪", rank: 1.0, heat: 55, count: 1 },
    { name: "捡漏", rank: 1.0, heat: 55, count: 1 },
    { name: "377精华", rank: 1.0, heat: 66, count: 1 },
    { name: "广州疫情", rank: 1.0, heat: 165, count: 1 },
    { name: "海珠疫情", rank: 1.0, heat: 155, count: 1 },
    { name: "疫情", rank: 1.0, heat: 225, count: 1 },
    { name: "城中村", rank: 1.0, heat: 85, count: 1 },
    { name: "石家庄", rank: 1.0, heat: 105, count: 1 },
    { name: "摆烂政策", rank: 1.0, heat: 105, count: 1 },
    { name: "大学生", rank: 1.0, heat: 95, count: 1 },
    { name: "阿根廷vs沙特阿拉伯", rank: 1.0, heat: 55, count: 1 },
    { name: "梅西世界杯首秀", rank: 1.0, heat: 55, count: 1 },
    { name: "广州新增本土感染者8210例", rank: 1.0, heat: 85, count: 1 },
    { name: "成都疫情防控", rank: 1.0, heat: 65, count: 1 },
    { name: "今晚看梅西阿根廷", rank: 1.0, heat: 55, count: 1 },
    { name: "美国vs威尔士", rank: 1.0, heat: 55, count: 1 },
    { name: "中国男足", rank: 1.0, heat: 55, count: 1 },
  ],
};
const chartEffect = () => {
  // 处理数据
  const originData = obj.data.map((item) => ({
    name: item.name,
    value: item.heat,
  }));
  // 随机生成颜色
  const randomColor = () => {
    return (
      "rgb(" +
      [
        Math.round(Math.random() * 255),
        Math.round(Math.random() * 255),
        Math.round(Math.random() * 255),
      ].join(",") +
      ")"
    );
  };
  return { originData, randomColor };
};
export function renderTextDom(dom) {
  let { originData, randomColor } = chartEffect();
  const data = originData.map((val) => ({
    ...val,
    textStyle: {
      color: randomColor(),
    },
  }));
  const Ele = document.getElementById(dom);

  const chart = echarts.init(Ele);

  let options = {
    series: [
      {
        type: "wordCloud",
        shape: "circle",
        left: "center",
        top: "center",
        right: null,
        bottom: null,
        width: "100%",
        height: "100%",
        sizeRange: [10, 80],
        rotationRange: [-90, 90],
        rotationStep: 45,
        gridSize: 8,
        drawOutOfBound: false, // 超出画布部分不显示，与sizeRange相关
        textStyle: {
          normal: {
            fontFamily: "sans-serif",
            fontWeight: "normal",
          },
          emphasis: {
            shadowBlur: 10,
            shadowColor: "#333",
          },
        },
        data: data,
      },
    ],
  };
  let optionThird = {
    title: {
      text: "",
    },
    series: [
      {
        type: "wordCloud",
        gridSize: 20,
        sizeRange: [12, 50],
        rotationRange: [-90, 90],
        shape: "pentagon",
        textStyle: {
          emphasis: {
            shadowBlur: 10,
            shadowColor: "#333",
          },
        },
        data: data,
      },
    ],
  };

  optionThird && chart.setOption(optionThird);
}
