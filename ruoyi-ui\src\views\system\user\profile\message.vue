<template>
  <div :class="type == '' ? 'app-container' : ''">
    <div v-if="type == ''">
      <h1>我的留言</h1>
    </div>
    <div>
      <el-table v-loading="loading1" :data="messageList" border :header-cell-style="{ textAlign: 'center' }"
        style="width: 100%" height="calc(100vh - 220px)" ref="tableRef">
        <el-table-column label="留言内容" show-overflow-tooltip min-width="100" prop="content" />
        <el-table-column label="文章标题" show-overflow-tooltip min-width="100" prop="cnTitle">
          <template slot-scope="scope">
            <span class="title" @click="openNewView(scope.row)">{{ scope.row.cnTitle }}</span>
          </template>
        </el-table-column>
        <el-table-column label="留言时间" align="center" prop="createTime" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total1 > 0" :total="total1" :page.sync="queryParams1.pageNum"
        :limit.sync="queryParams1.pageSize" :autoScroll="false" @pagination="getList1" />
    </div>
  </div>
</template>
    
<script>
import { feedbackOwnList } from "@/api/article/leaveMessage";

export default {
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading1: true,
      messageList: [],
      total1: 0,
      queryParams1: {
        pageNum: 1,
        pageSize: 50,
      },
    };
  },
  methods: {
    getList1() {
      this.loading1 = true;
      feedbackOwnList(this.queryParams1).then(response => {
        this.messageList = response.rows;
        this.total1 = response.total;
        this.loading1 = false;
        this.$nextTick(() => {
          this.scrollToTop();
        });
      });
    },
    // 表格滚动到顶部
    scrollToTop() {
      if (this.$refs.tableRef) {
        const tableEl = this.$refs.tableRef.$el.querySelector(
          ".el-table__body-wrapper"
        );
        if (tableEl) {
          tableEl.scrollTop = 0;
        }
      }
    },
    openNewView(item) {
      window.open(`/expressDetails?id=${item.articleId}&docId=${item.articleId}`, '_blank')
    },
  },
  computed: {
    // 在这里定义计算属性
  },
  created() {
    this.getList1();
  },
  mounted() {
    // 在这里定义挂载后的操作
  },
};
</script>
    
<style lang="scss" scoped>
.title:hover {
  color: #1d8af0;
  border-bottom: solid 1px #1d8af0;
  cursor: pointer;
}
</style>