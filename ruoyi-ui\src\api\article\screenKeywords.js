import request from '@/utils/request'

// 查询信通院大屏领域检索词库列表
export function listKeywords(query) {
  return request({
    url: '/screen/keywords/list',
    method: 'get',
    params: query
  })
}

// 查询信通院大屏领域检索词库详细
export function getKeywords(id) {
  return request({
    url: '/screen/keywords/' + id,
    method: 'get'
  })
}

// 新增信通院大屏领域检索词库
export function addKeywords(data) {
  return request({
    url: '/screen/keywords',
    method: 'post',
    data: data
  })
}

// 修改信通院大屏领域检索词库
export function updateKeywords(data) {
  return request({
    url: '/screen/keywords',
    method: 'put',
    data: data
  })
}

// 删除信通院大屏领域检索词库
export function delKeywords(id) {
  return request({
    url: '/screen/keywords/' + id,
    method: 'delete'
  })
}
