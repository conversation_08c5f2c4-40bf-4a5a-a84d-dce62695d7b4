import request from '@/utils/request'

// 查询微信告警列表
export function listAlarm(query) {
  return request({
    url: '/wechat/alarm/list',
    method: 'get',
    params: query
  })
}

// 查询微信告警详细
export function getAlarm(id) {
  return request({
    url: '/wechat/alarm/' + id,
    method: 'get'
  })
}

// 新增微信告警
export function addAlarm(data) {
  return request({
    url: '/wechat/alarm',
    method: 'post',
    data: data
  })
}

// 修改微信告警
export function updateAlarm(data) {
  return request({
    url: '/wechat/alarm',
    method: 'put',
    data: data
  })
}

// 删除微信告警
export function delAlarm(id) {
  return request({
    url: '/wechat/alarm/' + id,
    method: 'delete'
  })
}
