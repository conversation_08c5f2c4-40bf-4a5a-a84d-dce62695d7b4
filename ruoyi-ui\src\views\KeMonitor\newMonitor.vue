<template>
  <div>
    <splitpanes class="default-theme">
      <pane class="leftLink" ref="leftLink" min-size="10" max-size="50" size="20">
        <div class="treeMain" style="width: 100%;margin:0;">
          <div style="display:flex;justify-content:space-between;align-items:center;gap:10px">
            <el-input placeholder="输入关键字进行过滤" v-model="filterText" clearable class="input_Fixed">
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
          </div>
          <div class="treeBox">
            <el-tree :data="treeData" ref="tree" show-checkbox node-key="id" :default-expanded-keys="[1000]"
              @check-change="checkChange" :expand-on-click-node="false" :filter-node-method="filterNode">
              <template slot-scope="scoped">
                <div v-if="scoped.data.label != '全部'"><span>{{ scoped.data.label }}</span><b>{{ `${scoped.data.count ?
                  `(${scoped.data.count})` : ''}`
                }}</b></div>
                <div v-else>
                  {{ scoped.data.label }}
                  <div style="position: absolute;z-index: 99;right: 10px;top: -5px;">
                    <el-tooltip class="item" effect="dark" content="重置" placement="top">
                      <i class="el-input__icon el-icon-refresh" @click="treeClear"></i>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="按数量倒向排序" placement="top">
                      <i class="el-input__icon el-icon-caret-bottom" @click="treeSlot(true)"></i>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="按数量正向排序" placement="top">
                      <i class="el-input__icon el-icon-caret-top" @click="treeSlot(false)"></i>
                    </el-tooltip>
                  </div>
                </div>
              </template>
            </el-tree>
          </div>
        </div>
      </pane>
      <pane min-size="30" max-size="90" size="80">
        <div class="rightMain" style="margin-left: 0;overflow: auto;">
          <top-seach :buttonDisabled="buttonDisabled" :SeachData="SeachData" :ActiveData="ActiveData"
            @EmitInfo="SwitchInfo" @SeachEvent="funEsSeach" :seniorSerchFlag="seniorSerchFlag" @seniorSerch="seniorSerch"
            :areaList="areaList" :countryList="countryList"></top-seach>
          <MainArticle v-loading="buttonDisabled" :flag="'MonitorUse'" :currentPage="currentPage" :pageSize="pageSize"
            :total="total" :ArticleList="ArticleList" :keywords="SeachData.keyword"
            @handleCurrentChange="handleCurrentChange" @handleSizeChange="handleSizeChange" @Refresh="funEsSeach"
            :SeachData="SeachData"></MainArticle>
        </div>
      </pane>
    </splitpanes>
  </div>
</template>
  
<script>
import api from '@/api/ScienceApi/index.js'
import topSeach from '@/views/components/topSeach.vue'
import MainArticle from '../components/MainArticle.vue'

import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'

export default {
  components: { topSeach, MainArticle, Splitpanes, Pane },
  dicts: ['inventory_source'],
  data() {
    return {
      width: '258',
      isReSize: false,
      /* 文章主体组件数据 */
      currentPage: 1,
      pageSize: 10,
      total: 0,
      ArticleList: [],
      /* 左侧tree数据 */
      filterText: '',
      treeData: [],
      treeDataTransfer: [],
      checkList: [],
      defaultCheck: [],
      /* 搜索组件数据 */
      SeachData: {
        metaMode: '' /* 匹配模式 */,
        keyword: '' /* 关键词 */,
        sortMode: false /* 排序模式 */,
        releaseArea: '' /* 发布地区 */,
        timeRange: '' /* 时间范围 */,
        country: [] /* 国家或地区 */,
        customDay: '' /* 自定义天 */,
        radio: '' /* 平台类型 */,
        area: [] /* 领域 */,
        industry: [] /* 行业 */
      } /* 搜索条件 */,
      buttonDisabled: false /* 按钮防抖 */,
      ActiveData: {},
      seniorSerchFlag: false /* 普通检索或高级检索 */,
      areaList: [] /* 国内地区 */,
      countryList: [] /* 国家或地区 */,
      KeList: [],
      funEsSeach: null,
    }
  },
  created() {
    this.getWechat()
    this.funEsSeach = this.debounce(this.EsSeach, 200)
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  methods: {
    EsSeach(flag) {
      this.buttonDisabled = true
      let industry = this.SeachData.industry.map(item => String(item)),
        area = this.SeachData.area.map(item => String(item))
      var regex = /\d+/g, regex1 = /\d/ // \d 表示匹配数字
      let data = this.checkList.map(item => {
        if (regex1.test(item.label)) {
          return item.label.slice(0, item.nameLength)
        } else {
          return item.label
        }
      })
      if (flag) {
        let params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          id: this.$route.query.id,
          dateType: this.SeachData.timeRange != 6 ? this.SeachData.timeRange : '',
          startTime: this.SeachData.customDay[0],
          endTime: this.SeachData.customDay[1],
          weChatName: String(data),
          isSort: this.SeachData.sortMode,
          m: 1,
          publishType: this.SeachData.releaseArea,
          publishArea: String(this.SeachData.country),
          sourceType: this.SeachData.radio,
          keywords: this.SeachData.keyword,
          matchType: this.SeachData.metaMode,
          industryList: industry,
          fieldList: area,
        }
        api.newKeIntegration(params).then(Data => {
          if (Data.code == 200) {
            this.ArticleList = Data.data.list
            this.total = Data.data.total ? Data.data.total : 0
            if (this.ArticleList.length == 0 && this.pageSize * (this.currentPage - 1) >= this.total && this.total != 0) {
              this.currentPage = Math.trunc(this.total / this.pageSize) + 1
              this.EsSeach('source')
            }
          }
          this.buttonDisabled = false
        }).catch(err => {
          this.buttonDisabled = false
        })
        return
      } else if (!flag) {
        let params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          id: this.$route.query.id,
          dateType: this.SeachData.timeRange != 6 ? this.SeachData.timeRange : '',
          startTime: this.SeachData.customDay[0],
          endTime: this.SeachData.customDay[1],
          publishType: this.SeachData.releaseArea,
          publishArea: String(this.SeachData.country),
          isSort: this.SeachData.sortMode,
          sourceType: this.SeachData.radio,
          matchObject: '',
          keywords: this.SeachData.keyword,
          matchType: this.SeachData.metaMode,
          industryList: industry,
          fieldList: area,
          m: 1
        }
        api.newKeIntegration(params).then(res => {
          if (res.code == 200) {
            this.ArticleList = res.data.list
            this.total = res.data.total ? res.data.total : 0
            if (res.data.countBySourceName) {
              if (this.checkList.length) {
                let checkList = JSON.parse(JSON.stringify(this.checkList))
                let list = JSON.parse(JSON.stringify(this.treeData[0].children))
                list.map((row, index) => {
                  row.count = 0
                  this.$set(this.treeData[0].children, index, row)
                })
                Object.keys(res.data.countBySourceName).forEach(item => {
                  let spIndex = list.findIndex(row => { return row.label == item })
                  if (spIndex > -1) {
                    this.$set(this.treeData[0].children[spIndex], 'count', res.data.countBySourceName[item])
                  }
                })
                this.$refs.tree.setCheckedKeys([])
                let ids = checkList.map(item => {
                  return this.treeData[0].children.filter(row => { return row.label == item.label })[0].id
                })
                setTimeout(res => {
                  this.$refs.tree.setCheckedKeys(ids);
                }, 100)
              } else {
                let list = JSON.parse(JSON.stringify(this.treeDataTransfer))
                let list1 = []
                Object.keys(res.data.countBySourceName).forEach(item => {
                  let spIndex = list.findIndex(row => { return row.label == item })
                  if (spIndex >= 0) {
                    list.splice(spIndex, 1)
                  }
                  list1.push({
                    label: item,
                    count: res.data.countBySourceName[item]
                  })
                })
                list = list1.concat(list).sort((a, b) => b.count - a.count).map((item, index) => {
                  item.id = index + 1
                  return item
                })
                this.$set(this.treeData[0], 'children', list)
              }
            } else {
              let list = JSON.parse(JSON.stringify(this.treeDataTransfer))
              this.$set(this.treeData[0], 'children', list)
              let checkList = JSON.parse(JSON.stringify(this.checkList))
              this.$refs.tree.setCheckedKeys([])
              let ids = checkList.map(item => {
                return this.treeData[0].children.filter(row => { return row.label == item.label })[0].id
              })
              setTimeout(res => {
                this.$refs.tree.setCheckedKeys(ids);
              }, 100)
            }
            if (this.ArticleList.length == 0 && this.pageSize * (this.currentPage - 1) >= this.total && this.total != 0) {
              this.currentPage = Math.trunc(this.total / this.pageSize) + 1
              this.EsSeach()
            }
          }
          this.buttonDisabled = false
        }).catch(err => {
          this.buttonDisabled = false
        })
        return
      }
      this.buttonDisabled = false
    },
    handleCurrentChange(current) {
      this.currentPage = current
      this.funEsSeach()
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.funEsSeach()
    },
    SwitchInfo(data) {
      this.isInfo = data
    },
    seniorSerch() {
      this.seniorSerchFlag = !this.seniorSerchFlag
    },
    async getArea() {
      await api.getAreaList().then(Response => {
        if (Response.code == 200) {
          this.areaList = Response.data[0]
          this.countryList = Response.data[1]
        }
      }).catch(err => {
        this.$message({ message: '地区数据获取失败', type: 'error' })
      })
    },
    async getWechat() {
      await api.getspecialLIstUse(this.$route.query.id).then(res => {
        this.$set(this, 'ActiveData', res.data)
        this.getArea()
      })
      console.log(this.dict);
      setTimeout(() => {
        this.treeData = [
          {
            id: 1000,
            label: '全部',
            children: this.dict.type['inventory_source'].map((item, index) => {
              this.defaultCheck.push(index + 1)
              return {
                id: index + 1,
                label: item.label,
                count: 0
              }
            })
          }
        ]
        /*中转数据 */
        this.treeDataTransfer = this.dict.type['inventory_source'].map((item, index) => {
          this.defaultCheck.push(index + 1)
          return {
            id: index + 1,
            label: item.label,
            count: 0
          }
        })
      }, 100)
      // this.$refs.tree.setCheckedKeys(this.defaultCheck, true)
      this.funEsSeach()
    },
    checkChange(item, isCheck, sonCheck) {
      if (isCheck) {
        if (item.label !== '全部') {
          this.checkList.push(item)
        }
      } else {
        this.checkList.splice(this.checkList.findIndex(row => row.label == item.label), 1)
      }
      this.funEsSeach('source')
    },
    // 防抖
    debounce(fn, delay) {
      let timer;
      return function () {
        let context = this;
        let args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(context, args);
        }, delay);
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    shrinkMove(e) {
      let wid = e.screenX - 175 //误差值
      this.width = wid
      if (this.isReSize) {
        this.$refs.leftLink.style.width = wid + 'px'
      }
    },
    shrinkUp(e) {
      this.isReSize = false
    },
    shrinkDown(e) {
      this.isReSize = true
    },
    reduction() {
      this.$refs.leftLink.style.width = '258px'
      this.width = 258
    },
    // 左侧列表重置
    treeClear() {
      this.$refs.tree.setCheckedKeys([]);
    },
    // 左侧树排序
    treeSlot(type) {
      let checkList = JSON.parse(JSON.stringify(this.checkList))
      let list = JSON.parse(JSON.stringify(this.treeData[0].children))
      let list1 = list.sort((a, b) => {
        if (type) {
          return b.count - a.count
        } else {
          return a.count - b.count
        }
      }).map((item, index) => {
        item.id = index + 1
        return item
      })
      this.$set(this.treeData[0], 'children', list1)
      let ids = checkList.map(item => {
        return this.treeData[0].children.filter(row => { return row.label == item.label })[0].id
      })
      setTimeout(res => {
        this.$refs.tree.setCheckedKeys(ids);
      }, 100)
    }
  }
}
</script>
  
<style lang="scss" scoped>
.treeBox {
  width: 100%;
  height: calc(100vh - 93px);
  overflow-y: scroll;
}

.treeMain {
  position: relative;
}
</style>