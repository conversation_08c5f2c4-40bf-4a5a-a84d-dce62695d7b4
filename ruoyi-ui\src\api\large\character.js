import request from '@/utils/request'

// 查询信通院大屏 关键人物列表
export function listCharacter(query) {
  return request({
    url: '/large/character/list',
    method: 'get',
    params: query
  })
}

// 查询信通院大屏 关键人物详细
export function getCharacter(id) {
  return request({
    url: '/large/character/' + id,
    method: 'get'
  })
}

// 新增信通院大屏 关键人物
export function addCharacter(data) {
  return request({
    url: '/large/character',
    method: 'post',
    data: data
  })
}

// 修改信通院大屏 关键人物
export function updateCharacter(data) {
  return request({
    url: '/large/character/edit',
    method: 'post',
    data: data
  })
}

// 删除信通院大屏 关键人物
export function delCharacter(data) {
  return request({
    url: '/large/character/remove',
    method: 'post',
    data: data
  })
}
