<!-- 信息集成 -->
<template>
  <div
    v-loading="loadingOpen"
    class="brieFing"
    element-loading-text="数据加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <TopSeach
      :ActiveData="ActiveData"
      :SwitchShow="false"
      :buttonDisabled="buttonDisabled"
      :SeachData="SeachData"
      @SeachEvent="EsSeach"
    ></TopSeach>
    <MainArticle
      :flag="'infoInter'"
      :ArticleList="ArticleList"
      :downLoadShow="false"
      :editShow="false"
      :copyShow="false"
      :height="640"
      :pageCurrent="pageCurrent"
      :pageSize="pageSize"
      @handleSizeChange="handleSizeChange"
      @handleCurrentChange="handleCurrentChange"
      @Refresh="EsSeach"
      :total="total"
      :SeachData="SeachData"
    />
  </div>
</template>

<script>
import TopSeach from '@/views/components/topSeach.vue'
import MainArticle from '../components/MainArticle.vue'
import api from '@/api/ScienceApi/index.js'
export default {
  components: {
    TopSeach,
    MainArticle
  },
  data() {
    return {
      ActiveData: {
        title: '信息上报专题'
      },
      ArticleList: [],
      WeChat: '',
      SeachData: {
        metaMode: '' /* 匹配模式 */,
        keyword: '' /* 关键词 */,
        sortMode: false /* 排序模式 */,
        releaseArea: '' /* 发布地区 */,
        timeRange: '' /* 时间范围 */,
        country: '' /* 国家或地区 */,
        customDay: '' /* 自定义天 */,
        radio: '' /* 平台类型 */
      },
      loadingOpen: false,
      pageCurrent: 1,
      pageSize: 10,
      total: 0,
      KeList: [],
      buttonDisabled: false
    }
  },
  watch: {
    'SeachData.timeRange': {
      handler(newVal, oldVal) {
        this.SeachData.customDay = ''
      },
      deep: true
    }
  },
  created() {
    this.EsSeach()
  },
  methods: {
    // async getList () {
    //   let res = await api.monitoringList({ pageSize: 20, pageNum: 1 })
    //   if (res.code == 200)
    //   {
    //     this.KeList = res.rows
    //     this.ActiveData = this.KeList[0]
    //     this.total = res.total
    //     this.EsSeach()
    //   } else
    //   {
    //     this.$message({ message: '科情列表获取失败，请联系管理员', type: 'error' })
    //   }
    // },

    async EsSeach() {
      this.loadingOpen = true
      this.buttonDisabled = true
      if (this.buttonDisabled == false) return
      let params = {
        pageNum: this.pageCurrent,
        pageSize: this.pageSize,
        dateType: this.SeachData.timeRange != 6 ? this.SeachData.timeRange : '',
        startTime: this.SeachData.customDay[0],
        endTime: this.SeachData.customDay[1],
        publishType: this.SeachData.releaseArea,
        publishArea: String(this.SeachData.country),
        isSort: this.SeachData.sortMode,
        sourceType: this.SeachData.radio,
        matchObject: '',
        matchType: this.SeachData.metaMode,
        id: 100
      }
      let res = await api.SpecialEs(params)
      if (res.code == 200) {
        this.ArticleList = res.data.list
        this.total = res.data.total
        this.loadingOpen = false
      } else {
        this.$message({ message: '文章获取失败，请联系管理员', type: 'error' })
      }
      setTimeout(() => {
        this.buttonDisabled = false
      }, 1000)
    },
    handleCurrentChange(current) {
      this.pageCurrent = current
      this.EsSeach()
    },
    handleSizeChange(size) {
      this.pageSize = size
      this.EsSeach()
    }
  }
}
</script>

<style lang="scss" scoped></style>
