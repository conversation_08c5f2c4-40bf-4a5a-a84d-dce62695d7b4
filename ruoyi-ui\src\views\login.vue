<template>
  <div class="page flex-col">
    <div class="box_1 flex-col">
      <div class="box_2 flex-row">
        <img
          class="image_1"
          referrerpolicy="no-referrer"
          src="@/assets/images/home/<USER>"
        />
        <!-- <div class="text_1">开源科技情报采集系统</div> -->
      </div>
      <img
        class="image_2"
        referrerpolicy="no-referrer"
        src="@/assets/images/home/<USER>"
      />
      <div class="title_box">
        <div class="text_2">热门文章</div>
        <img
          class="image_3"
          referrerpolicy="no-referrer"
          src="@/assets/images/home/<USER>"
        />
      </div>
      <div class="box_content flex-row justify-between">
        <div class="section_1 flex-col justify-between">
          <div class="table">
            <div class="art-list" v-if="indexData.hotListVoList.length > 0">
              <div class="art-item" v-for="item in indexData.hotListVoList">
                <div class="art-item-left">
                  <img
                    style="width: 10px; margin-right: 10px"
                    referrerpolicy="no-referrer"
                    src="@/assets/images/home/<USER>"
                  />
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="item.cnTitle"
                    placement="top"
                  >
                    <div class="text-group_1" @click="openNewView(item)">
                      {{ item.cnTitle }}
                    </div>
                  </el-tooltip>
                </div>
                <div class="text_3">
                  {{ formatDate(item.publishTime) }}
                </div>
              </div>
            </div>
            <div
              style="text-align: center; color: #999; margin-top: 30px"
              v-else
            >
              数据正在加载中
            </div>
          </div>
        </div>
        <div class="section_2 flex-col">
          <el-form
            ref="loginForm"
            :model="loginForm"
            :rules="loginRules"
            class="login-form"
            v-show="loginFormShow"
          >
            <div class="group_22 flex-row">
              <div class="group_23 flex-col"></div>
              <span class="text_13">用户登录</span>
              <span class="text_14">USER&nbsp;LOGIN</span>
              <!--            <span class="text_15">登录说明</span>-->
            </div>
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                class="text-group_11"
                type="text"
                auto-complete="off"
                placeholder="请输入用户名称"
              >
                <svg-icon
                  slot="prefix"
                  icon-class="user"
                  class="el-input__icon input-icon"
                />
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                class="text-group_12"
                type="password"
                auto-complete="off"
                placeholder="请输入登录密码"
                @keyup.enter.native="handleLogin"
              >
                <svg-icon
                  slot="prefix"
                  icon-class="password"
                  class="el-input__icon input-icon"
                />
              </el-input>
            </el-form-item>
            <el-form-item prop="smsCode" v-if="codeShow">
              <el-row :gutter="20">
                <el-col :span="17">
                  <el-input
                    v-model="loginForm.smsCode"
                    size="mini"
                    type=""
                    auto-complete="off"
                    placeholder="验证码"
                    @keyup.enter.native="handleLogin"
                  >
                    <i
                      slot="prefix"
                      class="el-input__icon el-icon-s-comment"
                    ></i>
                  </el-input>
                </el-col>
                <el-col :span="6">
                  <el-button
                    type="primary"
                    size="mini"
                    @click="getCode"
                    :loading="codeLoading"
                    >获取验证码</el-button
                  >
                </el-col>
              </el-row>
            </el-form-item>
            <el-checkbox
              v-model="loginForm.rememberMe"
              style="margin: 0px 0px 30px 0px"
              >记住密码</el-checkbox
            >
            <el-form-item style="width: 100%">
              <el-button
                :loading="loading"
                size="medium"
                type="primary"
                class="login_button"
                @click.native.prevent="handleLogin"
              >
                <span v-if="!loading">登 录</span>
                <span v-else>登 录 中...</span>
              </el-button>
              <div style="float: right" v-if="register">
                <router-link class="link-type" :to="'/register'"
                  >立即注册</router-link
                >
              </div>
            </el-form-item>
          </el-form>
          <Verify
            @success="capctchaCheckSuccess"
            :mode="'pop'"
            :captchaType="'blockPuzzle'"
            :imgSize="{ width: '330px', height: '155px' }"
            ref="verify"
            @VerifyCloe="cloeEvent"
          ></Verify>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import Verify from "@/components/Verifition/Verify";
import { phoneCode, codeShow, getConfigKey } from "@/api/system/config.js";
import { getInfo } from "@/api/login";
import api from "@/api/infoEscalation/index";

export default {
  components: { Verify },
  name: "Login",
  data() {
    return {
      loginForm: {
        username: "" /* admin */,
        password: "" /* admin123 */,
        rememberMe: false,
        code: "",
        smsCode: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" },
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" },
        ],
      },
      loading: false,
      // 注册开关
      register: false,
      redirect: undefined,
      codeLoading: false,
      codeShow: false,
      loginFormShow: true,
      verify: true,
      needMd5: true,
      logintitle: "开源科技情报采集系统",
      indexData: {
        monitoringTotal: {
          wyTotal: 0,
          totalCount: 0,
          wxTotal: 0,
        },
        specialTotal: {
          wyTotal: 0,
          totalCount: 0,
          wxTotal: 0,
        },
        toDayMonitoringCount: 0,
        toDaySpecialCount: 0,
        toDayReportCount: 0,
        proportion: {
          wyPercentage: "0",
          wxPercentage: "0",
        },
        hotListVoList: [],
        scrolltimer: "", // 自动滚动的定时任务
        timer: null,
      },
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  created() {
    this.getCookie();
    this.getIndexData();
  },
  methods: {
    async getCode() {
      this.codeLoading = true;
      await phoneCode({
        password: this.needMd5
          ? encrypt(this.loginForm.password)
          : this.loginForm.password,
        username: this.loginForm.username,
      }).then((data) => {
        if (data.code == 200) {
          this.codeLoading = false;
          this.loginForm.smsUuid = data.data;
        } else {
          this.$message({ message: "验证码获取失败,请稍候重试" });
        }
      });
    },
    async getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password:
          password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
        smsUuid: "",
        smsCode: this.loginForm.smsCode,
      };
    },
    async capctchaCheckSuccess(params) {
      this.loginForm.code = params.captchaVerification;
      this.loading = true;
      if (this.loginForm.rememberMe) {
        Cookies.set("username", this.loginForm.username, { expires: 30 });
        Cookies.set("password", encrypt(this.loginForm.password), {
          expires: 30,
        });
        Cookies.set("rememberMe", this.loginForm.rememberMe, { expires: 30 });
      } else {
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      let form = JSON.parse(JSON.stringify(this.loginForm));
      if (this.needMd5) {
        form.password = encrypt(form.password);
      }
      this.$store
        .dispatch("Login", form)
        .then(async () => {
          let a = true;
          await getInfo().then(async (res) => {
            await getConfigKey("role_menu").then((data) => {
              if (data.msg != "") {
                let role_menu = JSON.parse(data.msg);
                if (res.roles && role_menu[res.roles[0]]) {
                  this.$router
                    .push({ path: `${role_menu[res.roles[0]]}` })
                    .catch(() => {});
                  a = false;
                }
              }
            });
          });
          if (a) {
            this.$router.push({ path: "/" }).catch(() => {});
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          if (this.verify) {
            this.$refs.verify.show();
            // this.loginFormShow = false
            this.loginFormShow = true;
          } else {
            this.capctchaCheckSuccess({ captchaVerification: null });
          }
        }
      });
    },
    cloeEvent() {
      this.loginFormShow = true;
    },
    async getIndexData() {
      await api.indexPageData().then((response) => {
        if (response.code == 200) {
          this.indexData = response.data;
          // this.renderRing(response.data);
        } else {
          this.$message({
            message: response.msg,
            type: "error",
          });
        }
        setTimeout(() => {
          // this.autoScroll();
        }, 10);
      });
    },
    formatDate(data) {
      if (data == null) {
        return null;
      }
      let dt = new Date(data);
      return dt.getFullYear() + "-" + (dt.getMonth() + 1) + "-" + dt.getDate();
    },
    /* 跳转新页面 */
    openNewView(item) {
      this.$message({ message: "请先登录!", type: "warning" });
    },
  },
};
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}

.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background-color: rgba(35, 145, 252, 0.1);
  //width: 400px;
  width: 520px;
  height: 440px;
  padding: 30px;

  .input-icon {
    width: 20px;
    margin: 0 10px;
    color: #275cbf;
    margin-bottom: 2px;
  }
}

::v-deep .el-input__prefix {
  line-height: 67px;
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
} //12/17
//
.page {
  position: relative;
  width: 100%;
  //height: 1080px;
  height: 100%;
  //overflow: hidden;
  background-color: #e5e5dd;
  overflow: auto;
  background: url(../assets/images/home/<USER>
  background-size: cover;
  background-position: center;
  min-height: 100vh;
}

.box_1 {
  width: 100%;
  height: 100%;
}

.box_2 {
  position: relative;
  width: 1200px;
  height: 60px;
  margin: 0 auto;
  //background-color: #0000FF;
  padding-top: 30px;
  display: flex;
  align-items: center;
}

.text_1 {
  font-size: 40px;
  color: #0764b0;
  line-height: 46px;
  text-align: left;
  font-style: normal;
  margin-left: 80px;
}

.image_1 {
  width: 450px;
  // height: 70px;
  // position: absolute;
  // top: 30px;
}

.image_2 {
  width: 100%;
  // height: 300px;
  margin-top: 30px;
  object-fit: contain;
}

.text_2 {
  width: 1200px;
  height: 30px;
  overflow-wrap: break-word;
  color: rgba(39, 92, 191, 1);
  font-size: 30px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 30px;
  margin: 0 auto;
}

.image_3 {
  width: 100%;
  height: 19px;
}

.box_content {
  width: 1200px;
  margin: 0 auto;
  //background-color: #00FFFF;
  display: flex;
  justify-content: space-between;
  padding-bottom: 80px;
}

.section_1 {
  width: 640px;
  height: 440px;
  //background-color: #1ab394;

  .table {
    height: 100%;
    width: 100%;
    background-color: transparent;
    overflow-y: auto;
    .art-list {
      border-top: 1px solid #e3e3e3;
      border-bottom: 1px solid #e3e3e3;
      .art-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 13px 0;
        border-bottom: 1px solid #e3e3e3;
        cursor: pointer;

        .art-item-left {
          display: flex;
          align-items: center;
          flex: 1;
          .text-group_1 {
            width: 460px;
            color: #333;
            font-size: 18px;
            font-family: PingFangSC-Regular;
            font-weight: normal;
            line-height: 18px;
            -webkit-line-clamp: 1;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
          }
        }
      }
      .art-item:last-child {
        border-bottom: none;
      }
    }
  }
}

.group_1 {
  background-color: rgba(227, 227, 227, 1);
  width: 640px;
  height: 1px;
}

.group_2 {
  width: 640px;
  height: 18px;
  margin-top: 12px;
}

.image-text_1 {
  width: 382px;
  height: 18px;
}

.thumbnail_1 {
  width: 8px;
  height: 8px;
  margin-top: 5px;
}

.text_3 {
  width: 105px;
  overflow-wrap: break-word;
  color: #999;
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 18px;
  padding-right: 10px;
}

.group_3 {
  background-color: rgba(227, 227, 227, 1);
  width: 640px;
  height: 1px;
  margin-top: 12px;
}

.group_4 {
  width: 640px;
  height: 18px;
  margin-top: 12px;
}

.image-text_2 {
  width: 400px;
  height: 18px;
}

.thumbnail_2 {
  width: 8px;
  height: 8px;
  margin-top: 5px;
}

.text-group_2 {
  width: 382px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.text_4 {
  width: 102px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.group_5 {
  background-color: rgba(227, 227, 227, 1);
  width: 640px;
  height: 1px;
  margin-top: 12px;
}

.group_6 {
  width: 640px;
  height: 18px;
  margin-top: 12px;
}

.image-text_3 {
  width: 270px;
  height: 18px;
}

.thumbnail_3 {
  width: 8px;
  height: 8px;
  margin-top: 5px;
}

.text-group_3 {
  width: 252px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.text_5 {
  width: 105px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.group_7 {
  background-color: rgba(227, 227, 227, 1);
  width: 640px;
  height: 1px;
  margin-top: 12px;
}

.group_8 {
  width: 640px;
  height: 18px;
  margin-top: 12px;
}

.image-text_4 {
  width: 378px;
  height: 18px;
}

.thumbnail_4 {
  width: 8px;
  height: 8px;
  margin-top: 5px;
}

.text-group_4 {
  width: 360px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.text_6 {
  width: 105px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.group_9 {
  background-color: rgba(227, 227, 227, 1);
  width: 640px;
  height: 1px;
  margin-top: 12px;
}

.group_10 {
  width: 640px;
  height: 18px;
  margin-top: 12px;
}

.image-text_5 {
  width: 288px;
  height: 18px;
}

.thumbnail_5 {
  width: 8px;
  height: 8px;
  margin-top: 5px;
}

.text-group_5 {
  width: 270px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.text_7 {
  width: 109px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.group_11 {
  background-color: rgba(227, 227, 227, 1);
  width: 640px;
  height: 1px;
  margin-top: 12px;
}

.group_12 {
  width: 640px;
  height: 18px;
  margin-top: 12px;
}

.image-text_6 {
  width: 382px;
  height: 18px;
}

.thumbnail_6 {
  width: 8px;
  height: 8px;
  margin-top: 5px;
}

.text-group_6 {
  width: 364px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.text_8 {
  width: 105px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.group_13 {
  background-color: rgba(227, 227, 227, 1);
  width: 640px;
  height: 1px;
  margin-top: 12px;
}

.group_14 {
  width: 640px;
  height: 18px;
  margin-top: 12px;
}

.image-text_7 {
  width: 400px;
  height: 18px;
}

.thumbnail_7 {
  width: 8px;
  height: 8px;
  margin-top: 5px;
}

.text-group_7 {
  width: 382px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.text_9 {
  width: 102px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.group_15 {
  background-color: rgba(227, 227, 227, 1);
  width: 640px;
  height: 1px;
  margin-top: 12px;
}

.group_16 {
  width: 640px;
  height: 18px;
  margin-top: 12px;
}

.image-text_8 {
  width: 270px;
  height: 18px;
}

.thumbnail_8 {
  width: 8px;
  height: 8px;
  margin-top: 5px;
}

.text-group_8 {
  width: 252px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.text_10 {
  width: 105px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.group_17 {
  background-color: rgba(227, 227, 227, 1);
  width: 640px;
  height: 1px;
  margin-top: 12px;
}

.group_18 {
  width: 640px;
  height: 18px;
  margin-top: 12px;
}

.image-text_9 {
  width: 378px;
  height: 18px;
}

.thumbnail_9 {
  width: 8px;
  height: 8px;
  margin-top: 5px;
}

.text-group_9 {
  width: 360px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.text_11 {
  width: 105px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.group_19 {
  background-color: rgba(227, 227, 227, 1);
  width: 640px;
  height: 1px;
  margin-top: 12px;
}

.group_20 {
  width: 640px;
  height: 18px;
  margin-top: 12px;
}

.image-text_10 {
  width: 288px;
  height: 18px;
}

.thumbnail_10 {
  width: 8px;
  height: 8px;
  margin-top: 5px;
}

.text-group_10 {
  width: 270px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.text_12 {
  width: 109px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.group_21 {
  background-color: rgba(227, 227, 227, 1);
  width: 640px;
  height: 1px;
  margin-top: 12px;
}

.section_2 {
  border-radius: 10px;
  width: 520px;
  height: 440px;
}

.group_22 {
  width: 460px;
  height: 33px;
  margin-bottom: 30px;
}

.group_23 {
  background-color: rgba(39, 92, 191, 1);
  width: 5px;
  height: 30px;
  margin-top: 2px;
  float: left;
}

.text_13 {
  width: 96px;
  height: 33px;
  overflow-wrap: break-word;
  color: #275cbf;
  font-size: 24px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin-left: 10px;
}

.text_14 {
  overflow-wrap: break-word;
  color: #275cbf;
  font-size: 18px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 8px 0 0 10px;
  vertical-align: bottom;
}

.text_15 {
  width: 72px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(39, 92, 191, 1);
  font-size: 18px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 8px 0 0 156px;
}

.group_24 {
  background-color: rgba(240, 247, 255, 1);
  border-radius: 10px;
  width: 460px;
  height: 67px;
  margin: 30px 0 0 30px;
}

.image-text_11 {
  width: 172px;
  height: 28px;
  margin: 20px 0 0 15px;
}

.label_1 {
  width: 22px;
  height: 22px;
  margin-top: 3px;
}

.text-group_11 {
  width: 460px;
  height: 67px;
  overflow-wrap: break-word;
  color: #999;
  font-size: 20px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 28px;

  input {
    height: 67px !important;
    line-height: 67px !important;
  }
}

::v-deep .el-input--medium .el-input__inner {
  height: 67px;
  line-height: 67px;
  padding-left: 50px;
}

.group_25 {
  background-color: rgba(240, 247, 255, 1);
  border-radius: 10px;
  width: 460px;
  height: 67px;
  margin: 30px 0 0 30px;
}

.image-text_12 {
  width: 172px;
  height: 28px;
  margin: 20px 0 0 15px;
}

.thumbnail_11 {
  width: 20px;
  height: 20px;
  margin-top: 4px;
}

.text-group_12 {
  width: 460px;
  height: 67px;
  overflow-wrap: break-word;
  color: #999;
  font-size: 20px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 28px;
}

.group_26 {
  width: 460px;
  height: 25px;
  margin: 30px 0 0 30px;
}

.image-text_13 {
  width: 102px;
  height: 25px;
}

.thumbnail_12 {
  width: 20px;
  height: 20px;
  margin-top: 3px;
}

.text-group_13 {
  width: 72px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(39, 92, 191, 1);
  font-size: 18px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_16 {
  width: 90px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(39, 92, 191, 1);
  font-size: 18px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text-wrapper_1 {
  background-color: rgba(39, 92, 191, 1);
  border-radius: 10px;
  height: 67px;
  width: 460px;
  margin: 30px 0 31px 30px;
}

.text_17 {
  width: 48px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 17px 0 0 206px;
}

.login_button {
  width: 100%;
  height: 67px;
  background: #275cbf;
  border-radius: 10px;
  //background-color: #1d58d7;
}

.title_box {
  width: 1200px;
  margin: 0 auto;
  margin-top: 20px;
  margin-bottom: 20px;
}

::v-deep .el-checkbox__label {
  font-size: 18px;
  color: #275cbf;
}

::v-deep .el-checkbox__inner {
  width: 18px;
  height: 18px;
}

::v-deep .el-checkbox__inner::after {
  height: 9px;
  width: 4px;
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #275cbf;
  color: #275cbf;
  border-color: #275cbf;
}

::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #275cbf;
}

::v-deep .el-button--primary {
  border-color: #275cbf;
}

::v-deep .el-form-item {
  margin-bottom: 30px;
}
</style>
