import request from "@/utils/request";

// 查询大屏 科技安全监控 实体内容列表
export function listData(query) {
  return request({
    url: "/screen/data/list",
    method: "get",
    params: query,
  });
}

// 查询大屏 科技安全监控 实体内容详细
export function getData(id) {
  return request({
    url: "/screen/data/" + id,
    method: "get",
  });
}

// 新增大屏 科技安全监控 实体内容
export function addData(data) {
  return request({
    url: "/screen/data",
    method: "post",
    data: data,
  });
}

// 修改大屏 科技安全监控 实体内容
export function updateData(data) {
  return request({
    url: "/screen/data/edit",
    method: "post",
    data: data,
  });
}

// 删除大屏 科技安全监控 实体内容
export function delData(id) {
  return request({
    url: "/screen/data/remove",
    method: "post",
    data: id,
  });
}
