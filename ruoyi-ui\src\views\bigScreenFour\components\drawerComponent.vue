<template>
  <el-drawer
    :title="title"
    :visible.sync="drawer"
    direction="rtl"
    append-to-body
    size="850px"
  >
    <el-table
      :data="list"
      style="width: 100%"
      :show-header="false"
      ref="table"
      @cell-click="openNewView"
    >
      <el-table-column
        prop="title"
        label="标题"
        width="560"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span
            v-html="changeColor(scope.row.cnTitle || scope.row.title)"
          ></span>
        </template>
      </el-table-column>
      <el-table-column
        prop="sourceName"
        label="数据源"
        width="100"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="publishTime"
        label="发布时间"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(
              scope.row.publishTime || scope.row.gatherTime,
              "{y}-{m}-{d}"
            )
          }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :background="false"
      @pagination="getList"
    />
  </el-drawer>
</template>

<script>
export default {
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      list: [],
      title: "阿里巴巴",
    };
  },
  mounted() {
    // this.init();
  },

  props: {
    open: {
      required: true,
      default: false,
    },
  },
  watch: {},

  computed: {
    drawer: {
      get() {
        return this.open;
      },
      set(val) {
        this.$emit("update:open", val);
      },
    },
  },
  methods: {
    /**
     *
     */
    getList() {},

    changeColor(str) {
      let Str = str;
      if (Str) {
        let keywords = this.keywords;
        keywords.map((keyitem, keyindex) => {
          if (keyitem && keyitem.length > 0) {
            // 匹配关键字正则
            let replaceReg = new RegExp(keyitem, "g");
            // 高亮替换v-html值
            let replaceString =
              '<span class="highlight"' +
              ' style="color: #ff7500;">' +
              keyitem +
              "</span>";
            Str = Str.replace(replaceReg, replaceString);
          }
        });
      }
      return Str;
    },

    async openNewView(item) {
      this.$emit("openDialogVisible");
    },
  },
  beforeDestroy() {},
};
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__open {
  .el-drawer {
    background: url("../../../assets/bigScreenTwo/drawerBackground.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
  }

  .el-dialog__close {
    background: url("../../../assets/bigScreenTwo/关闭小.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
    width: 31px;
    height: 31px;
    margin-right: 26px;
    &::before {
      content: none;
    }
  }

  .el-drawer__header {
    background-color: #1d233400;
    font-size: 30px;
    color: #ffffff;

    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);
    height: 120px;
    line-height: 107px !important;
    > span {
      padding-left: 31px;
    }
  }
}

::v-deep .el-table {
  background-color: #2a304000;

  tr {
    color: #f2f2f2;
    background: url("../../../assets/bigScreenTwo/弹窗列表.png") no-repeat;
    background-size: 100% 100% !important;
    height: 68px;
    padding: 0 0 0 65px;
    margin-bottom: 10px;
    display: block;
    width: 790px;
  }

  td.el-table__cell {
    border-bottom: 1px solid #1d233400;
    height: 68px;
    line-height: 68px;
    font-size: 16px;
    text-shadow: 0px 0px 9px rgba(30, 198, 255, 0.8);
  }

  &::before {
    height: 0;
  }
}

::v-deep
  .el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell,
::v-deep .el-table__empty-block {
  background-color: #2a304000;
  color: #f2f2f2;
  cursor: pointer;
}

::v-deep .pagination-container {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 0;

  .el-select .el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }

  .el-pagination__editor.el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }
}

::v-deep .el-pagination__total,
::v-deep .el-pagination__jump {
  color: #f2f2f2;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination button:disabled {
  background-color: #ffffff00;
  color: #fff;
}

::v-deep .el-pager li {
  background: #ffffff00;
  color: #fff;

  &.active {
    color: #1890ff;
  }
}
</style>
<style lang="scss">
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}

/* 保持滚动效果 */
.scrollable {
  overflow-y: scroll;
}
/* 隐藏滚动条 */
.scrollable {
  scrollbar-width: none; /* Firefox */
}

/* 隐藏滚动条 */
.scrollable {
  -ms-scrollbar-face-color: transparent; /* IE and Edge */
  -ms-scrollbar-3dlight-color: transparent; /* IE and Edge */
}
/* 保持滚动效果 */
</style>
