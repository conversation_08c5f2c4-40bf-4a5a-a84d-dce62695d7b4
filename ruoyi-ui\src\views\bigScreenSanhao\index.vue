<template>
  <v-scale-screen fullScreen width="1920" height="1080">
    <div class="bigMap">
      <div class="bigMap-bg">
        <div class="top">
          <div class="center-content">
            <div class="text">全球开源科技情报地平线扫描分析平台</div>
          </div>
        </div>

        <div class="bottom">
          <tabOne
            ref="tabOne"
            :notification-articles="notificationArticles"
            :show-notification="showNotificationFlag"
            @notification-close="handleNotificationClose"
            @notification-view-article="handleViewArticle"
          />
        </div>
      </div>
    </div>
  </v-scale-screen>
</template>

<script>
import tabOne from "./tabOne";
import articleNotificationManager from "@/utils/articleNotificationManager";

export default {
  name: "BigScreenSanhao",
  components: {
    tabOne,
  },
  data() {
    return {
      currentTime: "",
      timer: null,
      notificationArticles: [],
      showNotificationFlag: false,
    };
  },
  created() {
    this.initNotification();
  },
  mounted() {
    // this.timerFn()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    // 销毁通知管理器
    articleNotificationManager.destroy();
  },
  methods: {
    timerFn() {
      this.timer = setInterval(() => {
        this.currentTime = new Date();
        let weekDays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
        let week = weekDays[this.currentTime.getDay()];
      }, 1000);
    },
    async initNotification() {
      // 初始化文章通知管理器
      await articleNotificationManager.init(this.showNotification);

      // 监听路由变化来判断是否登录
      this.$router.afterEach((to) => {
        this.checkLoginStatus(to.path);
      });

      // 初始检查登录状态
      this.$nextTick(() => {
        this.checkLoginStatus(this.$route.path);
      });
    },

    async checkLoginStatus(currentPath) {
      const isLoginPage = currentPath === "/login";
      const hasToken = this.$store.getters.token;
      const isLoggedIn = !isLoginPage && hasToken;

      console.log("检查登录状态:", {
        currentPath,
        isLoginPage,
        hasToken: !!hasToken,
        isLoggedIn,
      });

      await articleNotificationManager.setLoginStatus(isLoggedIn);

      // 添加调试信息
      console.log("文章通知管理器状态:", {
        isEnabled: articleNotificationManager.isEnabled,
        isLoggedIn: articleNotificationManager.isLoggedIn,
        pollingInterval: articleNotificationManager.pollingInterval,
        hasTimer: !!articleNotificationManager.timer,
      });

      // 如果已登录，测试手动触发通知
      // if (isLoggedIn) {
      //   setTimeout(() => {
      //     console.log("测试手动触发通知...");
      //     this.showNotification([
      //       {
      //         id: 1,
      //         title: "测试文章标题",
      //         sourceName: "测试来源",
      //         createTime: "2024-01-01 10:00:00",
      //         isShow: "3",
      //       },
      //     ]);
      //   }, 3000);
      // }
    },

    // 显示文章通知
    showNotification(articles) {
      this.notificationArticles = articles;
      this.showNotificationFlag = true;

      // 当有文章通知时，通知tabOne组件更新热点推荐列表
      if (articles && articles.length > 0) {
        this.$nextTick(() => {
          if (this.$refs.tabOne && this.$refs.tabOne.updateHotArticlesList) {
            this.$refs.tabOne.updateHotArticlesList();
          }
        });
      }
    },

    // 处理通知关闭
    handleNotificationClose() {
      this.notificationArticles = [];
      this.showNotificationFlag = false;
    },

    // 处理查看单篇文章
    handleViewArticle(article) {
      window.open(`/expressDetails?id=${article.id}`, "_blank");
    },
  },
};
</script>

<style lang="scss" scoped>
.bigMap {
  background: #081f46;
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0px;
  margin: 0px;
  font-family: PingFang SC, PingFang SC;

  .bigMap-bg {
    // background: url("../../assets/bigScreenThree/bg.png") 0px 0px no-repeat;
    // background-size: 100% 100% !important;
    // background-size: cover;
    font-size: 14px;
    color: #ffffff;
    position: relative;
    width: 100%;
    height: 100%;
    padding: 0px;
    margin: 0px;
    display: flex;
    flex-direction: column;
    cursor: default;

    .top {
      height: 74px;
      display: flex;
      justify-content: center;

      //中间
      .center-content {
        width: 100%;
        height: 74px;
        background: url("../../assets/bigScreenSanhao/Btitle.png") no-repeat 0px
          0px !important;
        background-size: 100% 100% !important;

        .text {
          height: 55px;
          font-weight: 800;
          font-size: 27px;
          color: #ffffff;
          line-height: 54px;
          letter-spacing: 8px;
          text-align: center;
          line-height: 55px;
        }
      }
    }

    .bottom {
      flex: 1;
      height: calc(100% - 74px);
      position: relative;
      padding: 0 10px 0px 10px;
    }
  }
}

::v-deep .el-pagination__sizes {
  .el-input__inner {
    background-color: transparent;
  }
}
::v-deep .el-pagination__jump {
  .el-input__inner {
    background-color: transparent;
  }
}

/* 自定义滚动条样式 */
::v-deep .custom-dialog-body::-webkit-scrollbar {
  width: 8px;
}

::v-deep .custom-dialog-body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::v-deep .custom-dialog-body::-webkit-scrollbar-thumb {
  background: rgba(14, 194, 244, 0.6);
  border-radius: 4px;
}

::v-deep .custom-dialog-body::-webkit-scrollbar-thumb:hover {
  background: rgba(14, 194, 244, 0.8);
}
</style>
