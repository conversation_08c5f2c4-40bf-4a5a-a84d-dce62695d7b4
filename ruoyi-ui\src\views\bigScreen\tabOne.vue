<template>
  <div style="height: 100%; display: flex" class="one">
    <el-col style="width: 600px; height: 100%; margin-left: 62px">
      <el-row class="right-top">
        <el-row style="margin-bottom: 20px">
          <img
            src="@/assets/bigScreen/section-title-info.png"
            alt=""
            style="width: 100%; margin-top: 26px"
          />
          <span class="title-linear">重点推荐</span>
        </el-row>
        <div class="remengwenzhang-box">
          <div
            class="remengwenzhang-box-dynamic"
            style="height: 100%"
            :style="`margin-top: -${listHeight}px;`"
          >
            <div
              class="remengwenzhang-list"
              v-for="(item, index) in remengwenzhangList"
            >
              <div class="title" @click="openNewView(item)">
                {{ item.cnTitle }}
              </div>
              <div class="time">
                {{ parseTime(item.publishTime, "{y}-{m}-{d}") }}
              </div>
            </div>
          </div>
        </div>
      </el-row>
      <el-row class="left-center">
        <div class="wenzhangshu-box">
          <div class="flex">
            <div class="icon"></div>
            <div class="name">隔离文章数</div>
          </div>
          <div class="flex">
            <div class="title">总数</div>
            <div class="number">{{ quarantinesNum.count }}</div>
            <div class="title">占比</div>
            <div class="number">{{ quarantinesNum.percentage }}</div>
          </div>
        </div>
      </el-row>
      <el-row class="left-bottom">
        <el-row style="margin-bottom: 20px">
          <img
            src="@/assets/bigScreen/section-title-info.png"
            alt=""
            style="width: 100%; margin-top: 26px"
          />
          <span class="title-linear">所属归类</span>
        </el-row>
        <div class="xinwendongtai-box">
          <div class="wangzhan xinwendongtai">
            <p>国外信息源</p>
            <div ref="echart-radar-ref1"></div>
          </div>
          <div class="weixing xinwendongtai">
            <p>国内信息源</p>
            <div ref="echart-radar-ref2"></div>
          </div>
        </div>
      </el-row>
    </el-col>
    <el-col style="width: 400px; height: 100%; margin-left: 98px">
      <el-row class="center-top">
        <el-row style="margin: 20px 0 40px 0">
          <span class="title-center">数据采集情况</span>
        </el-row>
        <div class="zhanbiqingkuang-box">
          <div
            class="zhanbiqingkuang-list"
            v-for="(item, index) in zhanbiqingkuangList"
          >
            <div class="zhanbiqingkuang-top">
              <div class="icon">
                <img :src="item.pic" alt="" />
              </div>
            </div>
            <div class="zhanbiqingkuang-bottom">
              <div class="name">{{ item.name }}</div>
              <div class="numer">{{ item.num }}</div>
            </div>
          </div>
        </div>
      </el-row>
      <el-row class="center-center" style="margin-top: 80px">
        <el-row style="margin-bottom: 40px">
          <span class="title-center">区域分析</span>
        </el-row>
        <div class="quyufengxi-box">
          <div class="quyufengxi-list" v-for="(item, index) in quyufenxiList">
            <div class="number">{{ item.num }}</div>
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
      </el-row>
      <!-- <el-row class="center-bottom">
        <el-row style="margin-bottom: 30px">
          <span class="title-center">信源活跃度</span>
        </el-row>
        <div class="huoyuedu-box">
          <div class="huoyuedu-top">
            <div class="huoyuedu-name">活跃信源</div>
            <div class="huoyuedu-name">不活跃信源</div>
          </div>
          <div class="huoyuedu-bottom">
            <div class="huoyuedu-progress">
              <div class="huoyuedu-line" v-for="item in 30"></div>
              <div class="num" :style="`width: ${activity[0].progress};`"></div>
            </div>
            <div class="huoyuedu-progress">
              <div class="huoyuedu-line" v-for="item in 30"></div>
              <div class="num" :style="`width: ${activity[1].progress};`"></div>
            </div>
          </div>
        </div>
      </el-row> -->
    </el-col>

    <el-col style="width: 600px; height: 100%; margin-left: 110px">
      <el-row class="right-bottom">
        <el-row style="margin-bottom: 20px">
          <img
            src="@/assets/bigScreen/section-title-info.png"
            alt=""
            style="width: 100%; margin-top: 26px"
          />
          <span class="title-linear">国内信息源采集趋势</span>
          <el-row type="flex" :class="['info-tags']">
            <el-row
              :class="[{ active: weixinTags === 1 }]"
              @click.native="weixinTagsFun(1)"
              >近1个月</el-row
            >
            <el-row
              :class="[{ active: weixinTags === 2 }]"
              @click.native="weixinTagsFun(2)"
              >近3个月</el-row
            >
          </el-row>
        </el-row>
        <div class="weixin-box">
          <div ref="echart-bar-ref1" style="width: 600px; height: 180px"></div>
        </div>
      </el-row>
      <el-row class="right-center">
        <el-row style="margin-bottom: 20px">
          <img
            src="@/assets/bigScreen/section-title-info.png"
            alt=""
            style="width: 100%; margin-top: 26px"
          />
          <span class="title-linear">国外信息源采集趋势</span>
          <el-row type="flex" :class="['info-tags']">
            <el-row
              :class="[{ active: wangzhanTags === 1 }]"
              @click.native="wangzhanTagsFun(1)"
              >近1个月</el-row
            >
            <el-row
              :class="[{ active: wangzhanTags === 2 }]"
              @click.native="wangzhanTagsFun(2)"
              >近3个月</el-row
            >
          </el-row>
        </el-row>
        <div class="wangzhan-box">
          <div ref="echart-bar-ref2" style="width: 600px; height: 180px"></div>
        </div>
      </el-row>
      <el-row class="left-top">
        <el-row style="margin-bottom: 20px">
          <img
            src="@/assets/bigScreen/section-title-info.png"
            alt=""
            style="width: 100%; margin-top: 26px"
          />
          <span class="title-linear">采集排行</span>
        </el-row>
        <div class="paihangbang-box">
          <div
            v-for="(item, index) in paihangbangList"
            class="paihangbang-list"
          >
            <div class="paihangbang-top">
              <div class="paihangbang-top-left">
                <div class="paihangbang-top-left-index">
                  {{ index + 1 > 9 ? index + 1 : "0" + (index + 1) }}
                </div>
                <div class="paihangbang-top-left-name">
                  {{ item.sourceName }}
                </div>
              </div>
              <div class="paihangbang-top-right">
                <div class="paihangbang-top-right-num">
                  {{ item.count }}
                </div>
              </div>
            </div>
            <div class="paihangbang-bottom">
              <div class="paihangbang-bottom-progress-box">
                <div
                  class="paihangbang-bottom-progress"
                  :style="`width:${(item.count / paihangbangMaxNum) * 100}%`"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </el-row>
    </el-col>
  </div>
</template>
<script>
import * as echarts from "echarts";
import { largeScreenDataMonitoring } from "@/api/bigScreen/index.js";
import api from "@/api/infoEscalation/index";

export default {
  data() {
    return {
      paihangbangList: [],
      paihangbangMaxNum: 0,
      quarantinesNum: {
        count: 0,
        percentage: 0,
      },
      zhanbiqingkuangList: [
        { name: "国外信源占比", num: "22%", pic: "1.png" },
        { name: "国内信源占比", num: "22%", pic: "2.png" },
        { name: "国外信息源文章", num: "22%", pic: "3.png" },
        { name: "国内信息源文章", num: "22%", pic: "4.png" },
      ],
      quyufenxiList: [],
      activity: [
        { count: 0, progress: "33%" },
        { count: 1, progress: "67%" },
      ],
      weixinTags: 1,
      weChatMonthOne: [],
      weChatMonthThree: [],
      wangzhanTags: 1,
      abroadMonthOne: [],
      abroadMonthThree: [],
      remengwenzhangList: [],
      listHeight: 0,
    };
  },
  components: {},
  mounted() {
    this.init();
  },
  beforeDestroy() {},
  methods: {
    init() {
      largeScreenDataMonitoring().then((res) => {
        this.$set(
          this,
          "paihangbangList",
          [...res.data.abroadRankings, ...res.data.weChatRankings]
            .sort((a, b) => {
              return b.count - a.count;
            })
            .splice(0, 10)
        );
        this.$set(this, "paihangbangMaxNum", this.paihangbangList[0].count);
        this.$set(this, "quarantinesNum", res.data.quarantinesNum);
        this.echartRadar(res.data.abroadType);
        this.echartRadar1(res.data.weChatDomain);

        let wx = res.data.collection.filter((item) => item.typeName == "微信");
        let wz = res.data.collection.filter((item) => item.typeName == "国外");
        this.$set(this.zhanbiqingkuangList[0], "num", wz[0].percentage);
        this.$set(this.zhanbiqingkuangList[1], "num", wx[0].percentage);
        this.$set(this.zhanbiqingkuangList[2], "num", wz[0].count);
        this.$set(this.zhanbiqingkuangList[3], "num", wx[0].count);
        this.$set(
          this,
          "quyufenxiList",
          res.data.regional.map((item) => {
            return {
              name: item.regionName,
              num: item.count.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","),
            };
          })
        );
        this.$set(
          this.activity[0],
          "progress",
          res.data.activity.filter((item) => item.stabilityName == "稳定")[0]
            .percentage
        );
        this.$set(
          this.activity[1],
          "progress",
          res.data.activity.filter((item) => item.stabilityName == "不稳定")[0]
            .percentage
        );

        this.$set(this, "weChatMonthOne", res.data.weChatMonthOne);
        this.$set(this, "weChatMonthThree", res.data.weChatMonthThree);
        this.$set(this, "abroadMonthOne", res.data.abroadMonthOne);
        this.$set(this, "abroadMonthThree", res.data.abroadMonthThree);
        this.weixinTagsFun(1);
        this.wangzhanTagsFun(1);
      });
      api.indexPageData().then((res) => {
        this.$set(this, "remengwenzhangList", res.data.hotListVoList);
        let max = res.data.hotListVoList.length * 44 - 300;
        if (max > 0) {
          // 定时器
          this.listHeight = 0;
          let timer = setInterval(() => {
            this.listHeight += 1;
            if (this.listHeight >= max) {
              // clearInterval(timer)
              this.listHeight = 0;
            }
          }, 100);
        }
      });
    },
    echartRadar(data) {
      data.sort((a, b) => {
        return b.count - a.count;
      });
      let max = data[0].count;
      let list = data.map((item) => {
        return {
          name: item.thinkName,
          value: item.count,
          max: max,
        };
      });
      let myChart = echarts.init(this.$refs["echart-radar-ref1"]);
      let option = {
        // title: {
        //   text: 'Basic Radar Chart'
        // },
        // legend: {
        //   data: ['Allocated Budget', 'Actual Spending']
        // },
        tooltip: {
          trigger: "item",
        },
        radar: {
          // shape: 'circle',
          indicator: list,
          textStyle: {
            fontSize: 12, // 调整全局字体大小
          },
          center: ["50%", "50%"],
          radius: "70%",
          axisLine: {
            show: true,
            lineStyle: {
              color: "#b5b4b4",
              opacity: 0.3,
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#b5b4b4",
              opacity: 0.3,
            },
          },
          splitArea: {
            show: true,
            areaStyle: {
              shadowBlur: 0.1,
              shadowColor: "#F2F2F2",
              opacity: 0.3,
            },
          },
          axisName: {
            color: "#fff",
          },
        },
        series: [
          {
            name: "国外信息源",
            type: "radar",
            itemStyle: {
              // 添加itemStyle配置来改变数据项的颜色
              color: "#5FD5EC", // 将颜色设置为蓝色，可以替换为任何有效的颜色值，如 'red', '#ff0000', 'rgb(255, 0, 0)' 等
            },
            data: [
              {
                value: list.map((item) => {
                  return item.value;
                }),
                name: "国外信息源",
              },
            ],
          },
        ],
      };
      myChart.setOption(option);
    },
    echartRadar1(data) {
      data.sort((a, b) => {
        return b.count - a.count;
      });
      let max = data[0].count;
      let list = data.map((item) => {
        return {
          name: item.domainName,
          value: item.count,
          max: max,
        };
      });
      let myChart = echarts.init(this.$refs["echart-radar-ref2"]);
      let option = {
        // title: {
        //   text: 'Basic Radar Chart'
        // },
        // legend: {
        //   data: ['Allocated Budget', 'Actual Spending']
        // },

        tooltip: {
          trigger: "item",
        },
        radar: {
          // shape: 'circle',
          indicator: list,
          textStyle: {
            fontSize: 12, // 调整全局字体大小
          },
          center: ["50%", "50%"],
          radius: "70%",
          axisLine: {
            show: true,
            lineStyle: {
              color: "#b5b4b4",
              opacity: 0.3,
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#b5b4b4",
              opacity: 0.3,
            },
          },
          splitArea: {
            show: true,
            areaStyle: {
              shadowBlur: 0.1,
              shadowColor: "#F2F2F2",
              opacity: 0.3,
            },
          },
          axisName: {
            color: "#fff",
          },
        },
        series: [
          {
            name: "国内信息源",
            type: "radar",
            itemStyle: {
              // 添加itemStyle配置来改变数据项的颜色
              color: "#5FD5EC", // 将颜色设置为蓝色，可以替换为任何有效的颜色值，如 'red', '#ff0000', 'rgb(255, 0, 0)' 等
            },
            data: [
              {
                value: list.map((item) => {
                  return item.value;
                }),
                name: "国内信息源",
              },
            ],
          },
        ],
      };
      myChart.setOption(option);
    },
    weixinTagsFun(item) {
      this.weixinTags = item;
      let data;
      if (this.weixinTags == 1) {
        data = this.weChatMonthOne.map((item) => {
          return {
            week: item.weChatWeek,
            count: item.article_count,
          };
        });
      } else {
        data = this.weChatMonthThree;
      }
      let myChart = echarts.init(this.$refs["echart-bar-ref1"]);
      myChart.clear();
      let option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          top: 30,
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              textStyle: {
                color: "#fff",
              },
            },
            data: data.map((item) => {
              return item.week;
            }),
            axisTick: {
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff",
              },
            },
            splitLine: {
              lineStyle: {
                type: "solid",
                color: "#E6F7FF20",
              },
            },
          },
        ],
        series: [
          {
            name: "国内信息源",
            type: "bar",
            barWidth: "20px",
            color: "#1BDCFF",
            data: data.map((item) => {
              return item.count;
            }),
          },
        ],
      };
      option && myChart.setOption(option);
    },
    wangzhanTagsFun(item) {
      this.wangzhanTags = item;
      let data;
      if (this.wangzhanTags == 1) {
        data = this.abroadMonthOne.map((item) => {
          return {
            week: item.abroadWeek,
            count: item.article_count,
          };
        });
      } else {
        data = this.abroadMonthThree;
      }
      let myChart = echarts.init(this.$refs["echart-bar-ref2"]);
      myChart.clear();
      let option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          top: 30,
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              textStyle: {
                color: "#fff",
              },
            },
            data: data.map((item) => {
              return item.week;
            }),
            axisTick: {
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff",
              },
            },
            splitLine: {
              lineStyle: {
                type: "solid",
                color: "#E6F7FF20",
              },
            },
          },
        ],
        series: [
          {
            name: "国外信息源",
            type: "bar",
            barWidth: "20px",
            color: "#1BDCFF",
            data: data.map((item) => {
              return item.count;
            }),
          },
        ],
      };
      option && myChart.setOption(option);
    },
    openNewView(item) {
      window.open(`/expressDetails?id=${item.id}&docId=${item.docId}`, '_blank')
    },
  },
};
</script>
<style lang="scss" scoped>
.paihangbang-box {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-content: space-between;
  height: 280px;
  padding: 10px 20px 10px 20px;
  align-items: flex-start;
  border: 1px solid rgba(27, 220, 255, 0.4);
  background: linear-gradient(
    180deg,
    rgba(28, 11, 218, 0.6) 0%,
    rgba(27, 91, 255, 0.6) 100%
  );
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);

  .paihangbang-list {
    width: 260px;
    height: 51px;

    .paihangbang-top {
      display: flex;
      height: 24px;
      margin-bottom: 12px;
      justify-content: space-between;
      align-items: baseline;

      .paihangbang-top-left {
        display: flex;

        .paihangbang-top-left-index {
          color: #fff;
          height: 24px;
          width: 24px;
          line-height: 24px;
          margin-right: 10px;
          text-align: center;
          font-family: "Open Sans";
          font-size: 14px;
          font-style: normal;
          font-weight: 700;
          line-height: normal;
          border: 1px solid rgba(255, 255, 255);
        }

        .paihangbang-top-left-name {
          width: 130px;
          color: #fff;
          font-family: "Source Han Sans CN";
          font-size: 14px;
          font-style: normal;
          font-weight: 700;
          line-height: normal;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .paihangbang-top-right {
        .paihangbang-top-right-num {
          width: 80px;
          color: #fff;
          text-align: right;
          font-family: "League Gothic";
          font-size: 24px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .paihangbang-bottom {
      width: 100%;
      height: 6px;

      .paihangbang-bottom-progress-box {
        display: flex;
        width: 260px;
        height: 6px;
        flex-shrink: 0;
        border-radius: 8px;
        background: rgba(27, 220, 255, 0.4);

        .paihangbang-bottom-progress {
          height: 6px;
          border-radius: 8px;
          background: #18fef0;
          box-shadow: 0px 0px 4px 0px rgba(24, 254, 254, 0.6);
        }
      }
    }
  }
}

.wenzhangshu-box {
  display: flex;
  justify-content: space-between;
  width: 600px;
  height: 64px;
  padding: 30px 40px;
  margin-bottom: 20px;
  flex-shrink: 0;
  border: 1px solid rgba(27, 220, 255, 0.4);
  background: linear-gradient(
    180deg,
    rgba(28, 11, 218, 0.6) 0%,
    rgba(27, 91, 255, 0.6) 100%
  );
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);

  .flex {
    display: flex;
    align-items: center;
  }

  .icon {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    background-image: url("../../assets/bigScreen/page-icon.png");
    background-size: 100% 100%;
  }

  .name {
    color: #fff;
    font-family: "Source Han Sans CN";
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .title {
    margin-left: 40px;
    margin-right: 12px;
    color: #1bdcff;
    font-family: "Source Han Sans CN";
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .number {
    color: #fff;
    text-align: center;
    font-family: "League Gothic";
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    letter-spacing: 0.96px;
  }
}

.xinwendongtai-box {
  height: 300px;
  width: 100%;
  display: flex;
  justify-content: space-between;

  .xinwendongtai {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 290px;
    width: 290px;
    border: 1px solid rgba(27, 220, 255, 0.4);
    background: linear-gradient(180deg, #1c0bda 22.38%, #060d5b 121.37%);
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);

    p {
      color: #fff;
      font-family: "Source Han Sans CN";
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }

    div {
      height: 220px;
      width: 280px;
    }
  }
}

.zhanbiqingkuang-box {
  display: flex;
  width: 460px;
  height: 272px;
  margin-left: -25px;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: space-between;

  .zhanbiqingkuang-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 215px;
    height: 116px;
    padding: 20px 0px 40px 0px;
    border: 1px solid rgba(27, 220, 255, 0.4);
    background: linear-gradient(
      180deg,
      rgba(28, 11, 218, 0.6) 0%,
      rgba(27, 91, 255, 0.6) 100%
    );
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  }

  .zhanbiqingkuang-top {
    .icon {
      width: 40px;
      height: 40px;

      img {
        width: 40px;
        height: 40px;
      }
    }
  }

  .zhanbiqingkuang-bottom {
    display: flex;
    align-items: center;
    margin-top: 12px;

    .name {
      margin-right: 12px;
      color: #1bdcff;
      text-align: right;
      font-family: "Source Han Sans CN";
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    .numer {
      color: #fff;
      text-align: center;
      font-family: "Source Han Sans CN";
      font-size: 20px;
      font-style: normal;
      font-weight: 700;
      line-height: 24px;
      /* 120% */
    }
  }
}

.quyufengxi-box {
  height: 246px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: space-between;

  .quyufengxi-list {
    display: flex;
    margin: 0 10px;
    position: relative;
    width: 98px;
    height: 98px;
    background-image: url("../../assets/bigScreen/quyufengxi.png");
    background-size: 74px 74px;
    background-position: 12px 12px;
    background-repeat: no-repeat;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &::after {
      content: "";
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      width: 98px;
      height: 98px;
      background-image: url("../../assets/bigScreen/quyufengxi1.png");
      background-size: 98px 98px;
    }

    &::before {
      content: "";
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      width: 98px;
      height: 98px;
      background-image: url("../../assets/bigScreen/quyufengxi2.png");
      background-size: 98px 80px;
      background-position: 0px 0px;
      background-repeat: no-repeat;
    }

    .number {
      color: #fff;
      text-align: center;
      font-family: "League Gothic";
      font-size: 22px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      letter-spacing: -2px;
    }

    .name {
      color: #fff;
      text-align: center;
      font-family: "League Gothic";
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
    }
  }
}

.huoyuedu-box {
  display: flex;
  height: 96px;
  padding: 12px 20px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1 0 0;
  align-self: stretch;
  border: 1px solid rgba(27, 220, 255, 0.4);
  background: linear-gradient(
    180deg,
    rgba(28, 11, 218, 0.6) 0%,
    rgba(27, 91, 255, 0.6) 100%
  );
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);

  .huoyuedu-top {
    width: 100%;
    display: flex;
    justify-content: space-evenly;

    .huoyuedu-name {
      color: #f76767;

      &:nth-of-type(2) {
        color: #1bdcff;
      }
    }
  }

  .huoyuedu-bottom {
    width: 100%;
    height: 20px;
    margin-top: 10px;
    display: flex;
    justify-content: center;

    .huoyuedu-progress {
      position: relative;
      width: 160px;
      display: flex;
      justify-content: space-between;

      .huoyuedu-line {
        width: 2px;
        height: 20px;
        z-index: 2;
        background-color: #1624e5;
      }

      .num {
        position: absolute;
        right: 0;
        top: 0;
        height: 20px;
        background: #f76767;
        z-index: 1;
      }

      &:nth-of-type(2) {
        .num {
          left: 0;
          background: #1bdcff;
        }
      }
    }
  }
}

.weixin-box,
.wangzhan-box {
  display: flex;
  height: 180px;
  width: 600px;
  margin-bottom: 10px;
  flex-direction: column;
  align-items: flex-start;
  border: 1px solid rgba(27, 220, 255, 0.4);
  background: linear-gradient(
    180deg,
    rgba(28, 11, 218, 0.6) 0%,
    rgba(27, 91, 255, 0.6) 100%
  );
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);

  div {
  }
}

.remengwenzhang-box {
  width: 600px;
  height: 338px;
  padding: 20px;
  border: 1px solid rgba(16, 216, 255, 0.4);
  background: linear-gradient(
    180deg,
    rgba(11, 32, 218, 0.6) 0%,
    rgba(27, 91, 255, 0.6) 100%
  );
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  margin-bottom: 40px;

  .remengwenzhang-box-dynamic {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .remengwenzhang-list {
    position: relative;
    height: 20px;
    margin: 12px 0px 12px 20px;
    display: flex;

    .title {
      width: 420px;
      overflow: hidden;
      color: #fff;
      text-overflow: ellipsis;
      font-family: "Source Han Sans CN";
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .time {
      width: 120px;
      color: #3bb0d5;
      text-align: right;
      font-family: "Source Han Sans CN";
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }

    &::after {
      content: "";
      display: block;
      position: absolute;
      left: -20px;
      top: 6px;
      width: 10px;
      height: 10px;
      border-radius: 1px;
      background: #1bdcff;
    }
  }
}

.title-linear {
  position: absolute;
  top: 30px;
  left: 100px;
  font-size: 24px;
  font-weight: bold;
  color: linear-gradient(to right, #fff, #fee4c4, #fec989);
  background: linear-gradient(to right, #fff, #fee4c4, #fec989);
  /* 使用渐变背景时，通常需要设置这些值以确保文字可读 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-center {
  display: flex;
  margin-top: 29px;
  height: 40px;
  padding: 4px 0px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  align-self: stretch;
  color: #1bdcff;
  text-align: right;
  font-family: "Source Han Sans CN";
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: 40px;
}

.info-tags {
  position: absolute;
  right: 0;
  // top: 22px;
  bottom: 0;

  > div {
    color: #fff;
    font-weight: bold;
    padding: 10px 0;
    margin: 6px 22px 6px 0;
    font-size: 14px;

    cursor: pointer;
  }

  > div:first-child {
    margin-right: 15px;
  }

  .active {
    color: #1bdcff;
  }
}
</style>
