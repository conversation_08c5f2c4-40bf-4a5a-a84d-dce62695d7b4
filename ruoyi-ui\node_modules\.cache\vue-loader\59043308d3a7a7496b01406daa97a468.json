{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\expressDetails\\index2.vue?vue&type=style&index=0&id=3b0dc51a&scoped=true&lang=scss", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\expressDetails\\index2.vue", "mtime": 1754294018383}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLyog5YWo5bGA5qC35byPICovDQo6OnYtZGVlcCBpbWdbc3JjKj0ibW1iaXoiXSB7DQogIG1heC13aWR0aDogMTAwJSAhaW1wb3J0YW50Ow0KICBoZWlnaHQ6IGF1dG8gIWltcG9ydGFudDsNCiAgb2JqZWN0LWZpdDogY29udGFpbiAhaW1wb3J0YW50Ow0KICBtYXJnaW46IDEwcHggYXV0byAhaW1wb3J0YW50Ow0KICBkaXNwbGF5OiBibG9jayAhaW1wb3J0YW50Ow0KICAtd2Via2l0LXJlZmVycmVyOiBuby1yZWZlcnJlciAhaW1wb3J0YW50Ow0KICByZWZlcnJlcnBvbGljeTogbm8tcmVmZXJyZXIgIWltcG9ydGFudDsNCn0NCg0KLmRyYXdlcl9ib3ggew0KICBkaXNwbGF5OiBmbGV4Ow0KICB1c2VyLXNlbGVjdDogdGV4dCAhaW1wb3J0YW50Ow0KICBiYWNrZ3JvdW5kOiAjZjVmN2ZhOw0KICBtaW4taGVpZ2h0OiAxMDB2aDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQoNCiAgLmRyYXdlcl9TdHlsZSB7DQogICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgIHotaW5kZXg6IDI7DQogICAgbWFyZ2luOiAwcHggMjBweCAwcHg7DQogICAgd2lkdGg6IDgwMHB4Ow0KICAgIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogICAgcGFkZGluZzogMTBweCAzMHB4Ow0KDQogICAgLnRpdGxlIHsNCiAgICAgIGZvbnQtc2l6ZTogMjJweDsNCiAgICAgIGZvbnQtd2VpZ2h0OiA1MDBweDsNCiAgICAgIC8vIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICB9DQoNCiAgICAuc291cmNlIHsNCiAgICAgIGNvbG9yOiAjMDc5OGY4Ow0KICAgICAgLy8gdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIH0NCg0KICAgIC50aW1lIHsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIC8vIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICAgICAgY29sb3I6ICM5YjliOWI7DQogICAgfQ0KDQogICAgLmF1dGhvciB7DQogICAgICBjb2xvcjogIzFhMDk5NzsNCiAgICAgIC8vIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIH0NCg0KICAgIC5zdW1tYXJ5IHsNCiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQogICAgICBwYWRkaW5nOiAxMHB4Ow0KICAgICAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICB9DQogICAgLnN1bW1hcnkgLnN1bW1hcnktaXRlbTEgew0KICAgICAgY29sb3I6ICM5NzEyMzE7DQogICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgICB9DQogICAgLnN1bW1hcnkgLnN1bW1hcnktaXRlbTIgew0KICAgICAgbGluZS1oZWlnaHQ6IDMwcHg7DQogICAgICB3b3JkLWJyZWFrOiBicmVhay13b3JkOw0KICAgIH0NCg0KICAgIC8qIOaWh+acrOWxleW8gOaKmOWPoOe7hOS7tuagt+W8j+iwg+aVtCAqLw0KICAgIC5zdW1tYXJ5IDo6di1kZWVwIC50ZXh0LWVsbGlwc2lzIHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgIH0NCg0KICAgIC5zdW1tYXJ5IDo6di1kZWVwIC50ZXh0LWNvbnRlbnQgew0KICAgICAgbGluZS1oZWlnaHQ6IDMwcHg7DQogICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICB3b3JkLWJyZWFrOiBicmVhay13b3JkOw0KICAgIH0NCg0KICAgIC5zdW1tYXJ5IDo6di1kZWVwIC50ZXh0LWxpbWl0ZWQgew0KICAgICAgLXdlYmtpdC1saW5lLWNsYW1wOiAxMCAhaW1wb3J0YW50Ow0KICAgIH0NCg0KICAgIC5zdW1tYXJ5IDo6di1kZWVwIC5lbGxpcHNpcy1hY3Rpb25zIHsNCiAgICAgIHRleHQtYWxpZ246IHJpZ2h0Ow0KICAgICAgLy8gbWFyZ2luLXRvcDogOHB4Ow0KICAgIH0NCg0KICAgIC5zdW1tYXJ5IDo6di1kZWVwIC5zaG93LW1vcmUsDQogICAgLnN1bW1hcnkgOjp2LWRlZXAgLnNob3ctbGVzcyB7DQogICAgICBjb2xvcjogIzA3OThmODsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIHRyYW5zaXRpb246IGNvbG9yIDAuM3MgZWFzZTsNCiAgICB9DQoNCiAgICAuc3VtbWFyeSA6OnYtZGVlcCAuc2hvdy1tb3JlOmhvdmVyLA0KICAgIC5zdW1tYXJ5IDo6di1kZWVwIC5zaG93LWxlc3M6aG92ZXIgew0KICAgICAgY29sb3I6ICMwMDcxY2U7DQogICAgfQ0KDQogICAgLnN1bW1hcnkgOjp2LWRlZXAgLnNob3ctbW9yZSBpLA0KICAgIC5zdW1tYXJ5IDo6di1kZWVwIC5zaG93LWxlc3MgaSB7DQogICAgICBtYXJnaW4tbGVmdDogM3B4Ow0KICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgIH0NCiAgfQ0KfQ0KDQoucmlnaHQgew0KICB3aWR0aDogNTAwcHg7DQoNCiAgLnNpc3RlckRldGFpbHMgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7DQogICAgcGFkZGluZzogMTBweCAyMHB4Ow0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGNvbG9yOiAjMDAwMDAwOw0KDQogICAgLm1lZGlhLWluZm8gew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgIC5tZWRpYS1pbmZvLWl0ZW0gew0KICAgICAgICBmbGV4OiAxOw0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5uYW1lIHsNCiAgICAgIGNvbG9yOiAjOWI5YjliOw0KICAgICAgbGluZS1oZWlnaHQ6IDEuODsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCg0KICAgICAgLnZhbHVlIHsNCiAgICAgICAgbWFyZ2luLWxlZnQ6IDE1cHg7DQogICAgICAgIGNvbG9yOiAjMDAwMDAwOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5tYXJrZG93biB7DQogICAgbWFyZ2luLXRvcDogMjBweDsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjJlM2M1Ow0KICAgIHBhZGRpbmc6IDEwcHggMjBweDsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBjb2xvcjogIzAwMDAwMDsNCiAgICBoZWlnaHQ6IDgxMHB4Ow0KDQogICAgOjp2LWRlZXAgLmVsLWRpdmlkZXIgew0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzgzODQ4NDsNCiAgICAgIG1hcmdpbjogOHB4IDA7DQogICAgfQ0KDQogICAgLm1hcmtkb3duLWNvbnRlbnQgew0KICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgICAgIC5tYXJrZG93bi1jb250ZW50LXRpdGxlIHsNCiAgICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgICAgIH0NCiAgICAgIC5tYXJrZG93bi1jb250ZW50LXRleHQgew0KICAgICAgICBoZWlnaHQ6IDMyNXB4Ow0KICAgICAgICBvdmVyZmxvdy15OiBhdXRvOw0KICAgICAgICBwYWRkaW5nOiAxMHB4Ow0KICAgICAgICBsaW5lLWhlaWdodDogMS42Ow0KICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgICAgICBib3JkZXItcmFkaXVzOiAxMHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5yZWNvbW1lbmRlZEFydGljbGUgew0KICAgIG1hcmdpbi10b3A6IDIwcHg7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjsNCiAgICBwYWRkaW5nOiAxMHB4IDIwcHg7DQogICAgd2lkdGg6IDEwMCU7DQogICAgY29sb3I6ICMwMDAwMDA7DQoNCiAgICAuYXJ0aWNsZUJveCB7DQogICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICBtYXJnaW46IDEwcHggMDsNCg0KICAgICAgLmFydGljbGUgew0KICAgICAgICBtYXJnaW4tYm90dG9tOiA1cHg7DQogICAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgICAgICAvLyB3aWR0aDogY2FsYygxMDAlIC0gNzVweCk7DQogICAgICAgIGxpbmUtaGVpZ2h0OiAxLjg7DQoNCiAgICAgICAgJjpiZWZvcmUgew0KICAgICAgICAgIGNvbnRlbnQ6ICLigKIiOw0KICAgICAgICAgIGNvbG9yOiByZWQ7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLmJvdHRvbSB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGNvbG9yOiAjOWI5YjliOw0KICAgICAgICBhbGlnbi1pdGVtczogYmFzZWxpbmU7DQoNCiAgICAgICAgLnNvdXJjZU5hbWUgew0KICAgICAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICAgICAgICB9DQoNCiAgICAgICAgLmNvdW50IHsNCiAgICAgICAgICBtYXJnaW4tbGVmdDogMTBweDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5rZXl3b3JkIHsNCiAgICBtYXJnaW4tdG9wOiAyMHB4Ow0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7DQogICAgcGFkZGluZzogMTBweCAyMHB4Ow0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGNvbG9yOiAjMDAwMDAwOw0KICAgIGhlaWdodDogMzUwcHg7DQoNCiAgICAuY2hhcnRCb3ggew0KICAgICAgaGVpZ2h0OiAzMDBweDsNCiAgICB9DQogIH0NCg0KICAuYXJ0aWNsZUVudGl0eSB7DQogICAgbWFyZ2luLXRvcDogMjBweDsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmOw0KICAgIHBhZGRpbmc6IDEwcHggMjBweDsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBjb2xvcjogIzAwMDAwMDsNCiAgICBoZWlnaHQ6IDM1MHB4Ow0KDQogICAgLmNoYXJ0Qm94IHsNCiAgICAgIGhlaWdodDogMzAwcHg7DQogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgfQ0KICB9DQp9DQoNCi5yaWdodF90aXRsZSB7DQogIGZvbnQtc2l6ZTogMjBweDsNCn0NCg0KLmVsLWRpdmlkZXItcmlnaHQgew0KICBtYXJnaW46IDhweCAwOw0KfQ0KDQoubGl1eWFuQm94IHsNCiAgcG9zaXRpb246IGZpeGVkOw0KICBib3R0b206IDA7DQogIGxlZnQ6IGNhbGMoNTB2dyAtIDY1M3B4KTsNCiAgd2lkdGg6IDgwMHB4Ow0KICB6LWluZGV4OiA5OTsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2UzZTNlZjsNCiAgcGFkZGluZzogNXB4IDEwcHg7DQogIGJvcmRlci1yYWRpdXM6IDVweDsNCg0KICAubW9yZW56aHVhbmd0YWkgew0KICAgIGhlaWdodDogNDBweDsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICB9DQoNCiAgLnNodXJ1emh1YW5ndGFpIHsNCiAgICBoZWlnaHQ6IDkwcHg7DQoNCiAgICAudG9wIHsNCiAgICAgIGhlaWdodDogNDBweDsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgIH0NCg0KICAgIC5ib3R0b20gew0KICAgICAgaGVpZ2h0OiA1MHB4Ow0KICAgIH0NCiAgfQ0KfQ0KDQoudWVzciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGhlaWdodDogMzBweDsNCg0KICAuYXZhdGFyIHsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgd2lkdGg6IDMwcHg7DQogICAgaGVpZ2h0OiAzMHB4Ow0KICAgIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgfQ0KDQogIC5uYW1lIHsNCiAgICBtYXJnaW4tbGVmdDogMTBweDsNCiAgfQ0KfQ0KDQoudGFicy1hbGwgew0KICBwb3NpdGlvbjogZml4ZWQ7DQogIGxlZnQ6IGNhbGMoNTB2dyAtIDY5M3B4KTsNCiAgdG9wOiAwcHg7DQogIHotaW5kZXg6IDk5Ow0KICBoZWlnaHQ6IDgwMHB4Ow0KICB3aWR0aDogNDBweDsNCg0KICBAbWVkaWEgc2NyZWVuIGFuZCAobWF4LXdpZHRoOiAxNDAwcHgpIHsNCiAgICBsZWZ0OiAwOw0KICB9DQoNCiAgLnRhYnMgew0KICAgIHdyaXRpbmctbW9kZTogdmVydGljYWwtcmw7DQogICAgLyog5paH5a2X5LuO5LiK5Yiw5LiL77yM5LuO5Y+z5Yiw5bemICovDQogICAgaGVpZ2h0OiAxMjBweDsNCiAgICB3aWR0aDogNDBweDsNCiAgICBmb250LXdlaWdodDogODAwOw0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICBsaW5lLWhlaWdodDogMzVweDsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgZm9udC1zdHlsZTogbm9ybWFsOw0KICAgIGJhY2tncm91bmQ6IHVybCgiLi4vLi4vYXNzZXRzL2JpZ1NjcmVlblR3by90YWItYWN0aXZlLnBuZyIpIG5vLXJlcGVhdCAwcHgNCiAgICAgIDBweCAhaW1wb3J0YW50Ow0KICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlICFpbXBvcnRhbnQ7DQogICAgbGV0dGVyLXNwYWNpbmc6IDJweDsNCiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgfQ0KfQ0KDQouY29udGVudC1sb2FkaW5nIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1pbi1oZWlnaHQ6IDEwMHB4Ow0KICB3aWR0aDogMTAwJTsNCiAgbWFyZ2luOiAyMHB4IDA7DQp9DQoNCi5sb2FkaW5nLXByb2dyZXNzIHsNCiAgd2lkdGg6IDgwJTsNCiAgbWF4LXdpZHRoOiA1MDBweDsNCiAgbWFyZ2luLXRvcDogMjBweDsNCn0NCg0KLmxvYWRpbmctdGV4dCB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgY29sb3I6ICM0MDllZmY7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg0KLnByZWxvYWRpbmctY29udGFpbmVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1pbi1oZWlnaHQ6IDMwMHB4Ow0KICB3aWR0aDogMTAwJTsNCn0NCg0KOjp2LWRlZXAgaW1nIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCiAgb2JqZWN0LWZpdDogY29udGFpbjsNCn0NCg0KOjp2LWRlZXAgdmlkZW8gew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLyog5re75Yqg5b6u5L+h5Zu+54mH54m55q6K5qC35byPICovDQo6OnYtZGVlcCAud3gtaW1nIHsNCiAgbWF4LXdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IGF1dG8gIWltcG9ydGFudDsNCiAgZGlzcGxheTogYmxvY2s7DQogIG1hcmdpbjogMTBweCBhdXRvOw0KfQ0KDQovKiDmm7/mjaLlkI7nmoTlm77niYfmoLflvI8gKi8NCjo6di1kZWVwIC53eC1pbWctcmVwbGFjZWQgew0KICBtYXgtd2lkdGg6IDEwMCU7DQogIGhlaWdodDogYXV0byAhaW1wb3J0YW50Ow0KICBkaXNwbGF5OiBibG9jazsNCiAgbWFyZ2luOiAxMHB4IGF1dG87DQogIGJvcmRlcjogMXB4IHNvbGlkIHRyYW5zcGFyZW50Ow0KfQ0KDQovKiBDYW52YXPliJvlu7rnmoTlm77niYfmoLflvI8gKi8NCjo6di1kZWVwIC53eC1pbWctY2FudmFzIHsNCiAgbWF4LXdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IGF1dG8gIWltcG9ydGFudDsNCiAgZGlzcGxheTogYmxvY2s7DQogIG1hcmdpbjogMTBweCBhdXRvOw0KICBib3JkZXI6IDFweCBzb2xpZCB0cmFuc3BhcmVudDsNCn0NCg0KLyog5Y2g5L2N5Zu+5qC35byPICovDQo6OnYtZGVlcCAud3gtaW1nLXBsYWNlaG9sZGVyIHsNCiAgbWF4LXdpZHRoOiAxMDAlOw0KICBtaW4taGVpZ2h0OiAxMDBweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIG1hcmdpbjogMTBweCBhdXRvOw0KICBib3JkZXI6IDFweCBkYXNoZWQgI2NjYzsNCiAgcGFkZGluZzogMjBweDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzk5OTsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQovKiBtYXJrZG93buaYvuekuuagt+W8jyAqLw0KOjp2LWRlZXAgLm1kLWgxLA0KOjp2LWRlZXAgaDEubWQtaDEgew0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBtYXJnaW46IDEycHggMCA4cHggMDsNCiAgY29sb3I6ICMzMzMzMzM7DQp9DQoNCjo6di1kZWVwIC5tZC1oMiwNCjo6di1kZWVwIGgyLm1kLWgyIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCiAgbWFyZ2luOiAxMHB4IDAgNnB4IDA7DQogIGNvbG9yOiAjMzMzMzMzOw0KfQ0KDQo6OnYtZGVlcCAubWQtaDMsDQo6OnYtZGVlcCBoMy5tZC1oMyB7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIG1hcmdpbjogMTJweCAwIDZweCAwOw0KICBjb2xvcjogIzMzMzMzMzsNCn0NCg0KOjp2LWRlZXAgLm1kLWxpIHsNCiAgbWFyZ2luOiA1cHggMDsNCiAgcGFkZGluZy1sZWZ0OiAxMHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCn0NCg0KOjp2LWRlZXAgLm1kLWJ1bGxldCwNCjo6di1kZWVwIC5tZC1udW1iZXIgew0KICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgY29sb3I6ICMzMzMzMzM7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBmbGV4LXNocmluazogMDsNCn0NCg0KOjp2LWRlZXAgLm1kLWJsb2NrcXVvdGUsDQo6OnYtZGVlcCBibG9ja3F1b3RlLm1kLWJsb2NrcXVvdGUgew0KICBib3JkZXItbGVmdDogNHB4IHNvbGlkICNkMGQwZDA7DQogIHBhZGRpbmc6IDVweCAxMHB4Ow0KICBtYXJnaW46IDEwcHggMDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTsNCiAgZm9udC1zdHlsZTogaXRhbGljOw0KICBjb2xvcjogIzU1NTsNCn0NCg0KOjp2LWRlZXAgLm1hcmtkb3duLWNvbnRlbnQtdGV4dCBzdHJvbmcgew0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICMzMzMzMzM7DQp9DQoNCjo6di1kZWVwIC5tYXJrZG93bi1jb250ZW50LXRleHQgZW0gew0KICBmb250LXN0eWxlOiBpdGFsaWM7DQogIGNvbG9yOiAjMzMzMzMzOw0KfQ0KDQo6OnYtZGVlcCAubWQtY29kZS1ibG9jaywNCjo6di1kZWVwIHByZS5tZC1jb2RlLWJsb2NrIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y2ZjhmYTsNCiAgYm9yZGVyLXJhZGl1czogM3B4Ow0KICBwYWRkaW5nOiAxMHB4Ow0KICBtYXJnaW46IDEwcHggMDsNCiAgZm9udC1mYW1pbHk6IG1vbm9zcGFjZTsNCiAgb3ZlcmZsb3cteDogYXV0bzsNCiAgd2hpdGUtc3BhY2U6IHByZTsNCiAgZm9udC1zaXplOiAxM3B4Ow0KfQ0KDQo6OnYtZGVlcCAubWQtY29kZS1pbmxpbmUsDQo6OnYtZGVlcCBjb2RlLm1kLWNvZGUtaW5saW5lIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y2ZjhmYTsNCiAgYm9yZGVyLXJhZGl1czogM3B4Ow0KICBwYWRkaW5nOiAycHggNHB4Ow0KICBmb250LWZhbWlseTogbW9ub3NwYWNlOw0KICBmb250LXNpemU6IDEzcHg7DQp9DQoNCi8qIOa3u+WKoOWIl+ihqOWSjOauteiQveagt+W8jyAqLw0KOjp2LWRlZXAgLm1kLXBhcmFncmFwaCB7DQogIG1hcmdpbjogNXB4IDA7DQogIGxpbmUtaGVpZ2h0OiAxLjY7DQp9DQoNCjo6di1kZWVwIC5tZC1wYXJhZ3JhcGgtc3BhY2Ugew0KICBoZWlnaHQ6IDZweDsgLyog5LuF5Zyo6ZyA6KaB55qE5oOF5Ya15LiL6LCD5pW05Li65pu05bCP55qE5YC8ICovDQp9DQoNCjo6di1kZWVwIC5tZC11bCwNCjo6di1kZWVwIC5tZC1vbCB7DQogIG1hcmdpbjogMTBweCAwOw0KICBwYWRkaW5nLWxlZnQ6IDI1cHg7DQp9DQoNCjo6di1kZWVwIC5tZC11bCB7DQogIGxpc3Qtc3R5bGUtdHlwZTogZGlzYzsNCn0NCg0KOjp2LWRlZXAgLm1kLXVsIGxpIHsNCiAgbWFyZ2luOiA1cHggMDsNCiAgY29sb3I6ICMzMzMzMzM7DQp9DQoNCjo6di1kZWVwIC5tZC11bCBsaTo6bWFya2VyIHsNCiAgY29sb3I6ICMzMzMzMzM7DQp9DQoNCjo6di1kZWVwIC5tZC1vbCB7DQogIGxpc3Qtc3R5bGUtdHlwZTogZGVjaW1hbDsNCn0NCg0KOjp2LWRlZXAgLm1kLW9sIGxpIHsNCiAgbWFyZ2luOiA1cHggMDsNCiAgY29sb3I6ICMzMzMzMzM7DQp9DQoNCjo6di1kZWVwIC5tZC1vbCBsaTo6bWFya2VyIHsNCiAgY29sb3I6ICMzMzMzMzM7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KfQ0KDQovKiDmt7vliqDmu5rliqjmnaHmoLflvI/vvJogKi8NCi8qIOiHquWumuS5iea7muWKqOadoeagt+W8jyAqLw0KOjp2LWRlZXAgLm1hcmtkb3duLWNvbnRlbnQtdGV4dDo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICB3aWR0aDogNnB4Ow0KfQ0KDQo6OnYtZGVlcCAubWFya2Rvd24tY29udGVudC10ZXh0Ojotd2Via2l0LXNjcm9sbGJhci10cmFjayB7DQogIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50OyAvKiDljrvmjonmu5rliqjmnaHog4zmma/oibIgKi8NCn0NCg0KOjp2LWRlZXAgLm1hcmtkb3duLWNvbnRlbnQtdGV4dDo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmOyAvKiDkuK3nrYnngbDoibIgKi8NCiAgYm9yZGVyLXJhZGl1czogM3B4Ow0KfQ0KDQo6OnYtZGVlcCAubWFya2Rvd24tY29udGVudC10ZXh0Ojotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmN2Y3Zjc7IC8qIOaCrOWBnOaXtueahOminOiJsiAqLw0KfQ0KDQovKiDngrnlh7vnnIvlpKflm77mjInpkq7moLflvI8gKi8NCi52aWV3LW1hcmttYXAgew0KICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjMTg5MGZmOw0KICBjdXJzb3I6IHBvaW50ZXI7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjQsIDE0NCwgMjU1LCAwLjEpOw0KICBwYWRkaW5nOiAycHggOHB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4zczsNCn0NCg0KLnZpZXctbWFya21hcDpob3ZlciB7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjQsIDE0NCwgMjU1LCAwLjIpOw0KICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTsNCn0NCg0KLm1hcmtkb3duLWNvbnRlbnQtdGl0bGUgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQo6OnYtZGVlcCBwcmUgew0KICB3aGl0ZS1zcGFjZTogbm9ybWFsOw0KfQ0K"}, {"version": 3, "sources": ["index2.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+q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file": "index2.vue", "sourceRoot": "src/views/expressDetails", "sourcesContent": ["<template>\r\n  <div class=\"drawer_box\" v-loading=\"loading\">\r\n    <div\r\n      class=\"drawer_Style\"\r\n      :style=\"{ marginBottom: type ? '40px' : '100px' }\"\r\n    >\r\n      <div>\r\n        <!--p class=\"title\">\r\n          {{ drawerInfo.cnTitle || drawerInfo.title }}\r\n        </p-->\r\n        <p class=\"title\">\r\n          {{ drawerInfo.cnTitle }}\r\n        </p>\r\n        <p class=\"title\" v-if=\"titleShow\">\r\n          {{ drawerInfo.title }}\r\n        </p>\r\n        <p>\r\n          <span class=\"source\">{{ drawerInfo.sourceName }}</span>\r\n          <span class=\"time\">{{\r\n            formatPublishTime(\r\n              drawerInfo.publishTime,\r\n              drawerInfo.publishLocaltime\r\n            )\r\n          }}</span>\r\n          <span class=\"author\">{{ drawerInfo.author }}</span>\r\n        </p>\r\n        <el-divider></el-divider>\r\n        <div class=\"summary\" v-if=\"drawerInfo.cnSummary || drawerInfo.summary\">\r\n          <div class=\"summary-item1\">\r\n            小信导读：<span style=\"color: #838484\">【内容由大模型生成】</span>\r\n          </div>\r\n          <div class=\"summary-item2\">\r\n            <text-ellipsis\r\n              :text=\"drawerInfo.cnSummary || drawerInfo.summary\"\r\n              :max-lines=\"10\"\r\n              more-text=\"展开\"\r\n              less-text=\"收起\"\r\n            ></text-ellipsis>\r\n          </div>\r\n        </div>\r\n        <!-- 内容区域 -->\r\n        <div v-if=\"!contentReady && isWeixinArticle\" class=\"content-loading\">\r\n          <el-alert\r\n            title=\"正在处理微信图片，请稍等...\"\r\n            type=\"info\"\r\n            center\r\n            :closable=\"false\"\r\n            show-icon\r\n          ></el-alert>\r\n          <div v-if=\"totalImages > 0\" class=\"loading-progress\">\r\n            <div class=\"loading-text\">\r\n              图片加载进度: {{ loadedImages }}/{{ totalImages }}\r\n            </div>\r\n            <el-progress\r\n              :percentage=\"Math.floor((loadedImages / totalImages) * 100)\"\r\n              :show-text=\"false\"\r\n              status=\"success\"\r\n            ></el-progress>\r\n          </div>\r\n        </div>\r\n        <div\r\n          v-else\r\n          style=\"line-height: 30px; white-space: normal; word-break: break-word\"\r\n          v-html=\"htmlJson\"\r\n        ></div>\r\n        <el-empty description=\"当前文章暂无数据\" v-if=\"!htmlJson\"></el-empty>\r\n      </div>\r\n      <div class=\"liuyanBox\" :style=\"{ height: type ? '50px' : '110px' }\">\r\n        <div class=\"morenzhuangtai\" v-if=\"type\">\r\n          <div class=\"uesr\">\r\n            <img :src=\"avatar\" class=\"avatar\" />\r\n            <div class=\"name\">{{ name }}</div>\r\n          </div>\r\n          <div class=\"button\">\r\n            <el-tooltip\r\n              class=\"item\"\r\n              effect=\"dark\"\r\n              :content=\"drawerInfo.recommend ? '取消推荐' : '推荐'\"\r\n              placement=\"top\"\r\n            >\r\n              <el-button\r\n                :icon=\"\r\n                  drawerInfo.recommend\r\n                    ? 'el-icon-message-solid'\r\n                    : 'el-icon-bell'\r\n                \"\r\n                :type=\"drawerInfo.recommend ? 'primary' : ''\"\r\n                circle\r\n                @click=\"handleRecommend(drawerInfo)\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n            <el-tooltip\r\n              class=\"item\"\r\n              effect=\"dark\"\r\n              :content=\"drawerInfo.collection ? '取消收藏' : '收藏'\"\r\n              placement=\"top\"\r\n            >\r\n              <el-button\r\n                :icon=\"\r\n                  drawerInfo.collection ? 'el-icon-star-on' : 'el-icon-star-off'\r\n                \"\r\n                :type=\"drawerInfo.collection ? 'primary' : ''\"\r\n                circle\r\n                @click=\"handleCollections(drawerInfo)\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n            <el-button type=\"text\" icon=\"el-icon-edit\" @click=\"type = false\"\r\n              >写留言</el-button\r\n            >\r\n          </div>\r\n        </div>\r\n        <div class=\"shuruzhuangtai\" v-else>\r\n          <div class=\"top\">\r\n            <div class=\"uesr\">\r\n              <img :src=\"avatar\" class=\"avatar\" />\r\n              <div class=\"name\">{{ name }}</div>\r\n            </div>\r\n            <div class=\"button\">\r\n              <el-button size=\"mini\" type=\"primary\" @click=\"submit\"\r\n                >确定</el-button\r\n              >\r\n              <el-button size=\"mini\" type=\"info\" @click=\"close\">取消</el-button>\r\n            </div>\r\n          </div>\r\n          <div class=\"bottom\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              placeholder=\"请输入内容\"\r\n              v-model=\"textarea\"\r\n              maxlength=\"300\"\r\n              show-word-limit\r\n              resize=\"none\"\r\n            ></el-input>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"right\">\r\n      <div class=\"sisterDetails\">\r\n        <div class=\"right_title\">媒体详情</div>\r\n        <el-divider class=\"el-divider-right\"></el-divider>\r\n        <div class=\"media-info\">\r\n          <div class=\"media-info-item\">\r\n            <div class=\"name\">\r\n              媒体名称:<span class=\"value\">{{ drawerInfo.sourceName }}</span>\r\n            </div>\r\n            <div class=\"name\">媒体等级:<span class=\"value\"></span></div>\r\n          </div>\r\n          <div class=\"media-info-item\">\r\n            <div class=\"name\">媒体地域:<span class=\"value\"></span></div>\r\n            <div class=\"name\">媒体行业:<span class=\"value\"></span></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div\r\n        class=\"markdown\"\r\n        v-if=\"keyPointsFormatted || entitiesFormatted\"\r\n        :style=\"{\r\n          height: keyPointsFormatted && entitiesFormatted ? '810px' : '430px',\r\n        }\"\r\n      >\r\n        <div class=\"right_title\">\r\n          <span style=\"color: #971231\">小信解读</span>\r\n          <span style=\"color: #838484\">【内容由大模型生成】</span>\r\n        </div>\r\n        <el-divider class=\"el-divider-markdown\"></el-divider>\r\n        <div\r\n          class=\"markdown-content\"\r\n          style=\"margin-top: 10px\"\r\n          v-if=\"keyPointsFormatted\"\r\n        >\r\n          <div class=\"markdown-content-title\">\r\n            内容要点\r\n            <span class=\"view-markmap\" @click=\"openMarkmap('keyPoints')\"\r\n              >点击放大查看</span\r\n            >\r\n          </div>\r\n          <div\r\n            class=\"markdown-content-text\"\r\n            v-html=\"keyPointsFormatted\"\r\n            style=\"background-color: #e8d9cc\"\r\n          ></div>\r\n        </div>\r\n        <div\r\n          class=\"markdown-content\"\r\n          :style=\"{ marginTop: keyPointsFormatted ? '20px' : '10px' }\"\r\n          v-if=\"entitiesFormatted\"\r\n        >\r\n          <div class=\"markdown-content-title\">\r\n            人员/机构/技术/产品\r\n            <span class=\"view-markmap\" @click=\"openMarkmap('entities')\"\r\n              >点击放大查看</span\r\n            >\r\n          </div>\r\n          <div\r\n            class=\"markdown-content-text\"\r\n            v-html=\"entitiesFormatted\"\r\n            style=\"background-color: #dce4d4\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"recommendedArticle\">\r\n        <div class=\"right_title\">推荐文章</div>\r\n        <el-divider class=\"el-divider-right\"></el-divider>\r\n        <div class=\"articleBox\" v-for=\"item in articleList\" :key=\"item.id\">\r\n          <div class=\"article\" @click=\"openNewView(item)\">\r\n            {{ item.cnTitle }}\r\n          </div>\r\n          <div class=\"bottom\">\r\n            <div class=\"time\">{{ item.publishTime }}</div>\r\n            <div class=\"sourceName\">{{ item.sourceName }}</div>\r\n            <span class=\"count\">{{ `推荐数量：${item.count}` }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"keyword\">\r\n        <div class=\"right_title\">关键词</div>\r\n        <el-divider class=\"el-divider-right\"></el-divider>\r\n        <div class=\"chartBox\">\r\n          <el-image style=\"width: 100%; height: 290px\" :src=\"url\">\r\n          </el-image>\r\n        </div>\r\n      </div> -->\r\n      <!-- <div class=\"articleEntity\">\r\n        <div class=\"right_title\">文章实体</div>\r\n        <el-divider class=\"el-divider-right\"></el-divider>\r\n        <div class=\"chartBox\">\r\n          <el-radio-group v-model=\"tabPosition\" style=\"margin-bottom: 30px; position: absolute;   z-index: 3;\">\r\n            <el-radio-button label=\"1\">通用</el-radio-button>\r\n            <el-radio-button label=\"2\">人物</el-radio-button>\r\n            <el-radio-button label=\"3\">地域</el-radio-button>\r\n            <el-radio-button label=\"4\">机构</el-radio-button>\r\n          </el-radio-group>\r\n          <el-image style=\"width: 100%; height: 290px\" :src=\"url\">\r\n          </el-image>\r\n        </div>\r\n      </div> -->\r\n    </div>\r\n    <div class=\"tabs-all\">\r\n      <div class=\"tabs\" v-if=\"translationBtnShow\">\r\n        <span @click=\"viewOriginal()\">{{\r\n          this.originalArticleShow ? \"查看原文\" : \"查看中文\"\r\n        }}</span>\r\n      </div>\r\n      <div class=\"tabs\">\r\n        <span @click=\"viewOriginalArticle(drawerInfo)\">原文链接</span>\r\n      </div>\r\n      <div\r\n        class=\"tabs\"\r\n        v-hasPermi=\"['article:articleList:monitoringAndSpecial']\"\r\n        v-if=\"translationBtnShow\"\r\n      >\r\n        <span @click=\"translateEvent(drawerInfo)\">机器翻译</span>\r\n      </div>\r\n      <!-- <div class=\"tabs\" v-if=\"translationBtnShow\">\r\n        <span @click=\"\">人工翻译</span>\r\n      </div> -->\r\n\r\n      <div class=\"tabs\">\r\n        <span @click=\"backToTop()\">返回顶部</span>\r\n      </div>\r\n    </div>\r\n    <markmap-dialog\r\n      :visible.sync=\"markmapVisible\"\r\n      :content=\"markmapContent\"\r\n      :title=\"markmapTitle\"\r\n      :loading=\"markmapLoading\"\r\n      @close=\"handleMarkmapClose\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from \"vuex\";\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport { feedbackAdd } from \"@/api/article/leaveMessage\";\r\nimport {\r\n  containsHtmlTags,\r\n  extractHtmlTags,\r\n  hasValidHtmlStructure,\r\n} from \"@/utils/htmlUtils\";\r\nimport { formatMarkdown } from \"@/utils/markdownUtils\";\r\nimport MarkmapDialog from \"./MarkmapDialog.vue\";\r\nimport TextEllipsis from \"@/components/TextEllipsis/index.vue\";\r\n\r\nexport default {\r\n  dicts: [],\r\n  components: {\r\n    MarkmapDialog,\r\n    TextEllipsis,\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      contentReady: false,\r\n      translationBtnShow: true,\r\n      titleShow: true,\r\n      drawerInfo: {},\r\n      htmlJson: \"\",\r\n      processedHtml: \"\",\r\n      originalArticleShow: true,\r\n      articleList: [],\r\n      type: true,\r\n      textarea: \"\",\r\n      url: \"\",\r\n      tabPosition: 1,\r\n      totalImages: 0,\r\n      loadedImages: 0,\r\n      imageLoadTimeout: null,\r\n      markdownContent: {\r\n        keyPoints: \"\",\r\n        entities: \"\",\r\n      },\r\n      markmapVisible: false,\r\n      markmapContent: \"\",\r\n      markmapTitle: \"\",\r\n      markmapLoading: false,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"roles\", \"name\", \"avatar\"]),\r\n    isWeixinArticle() {\r\n      return (\r\n        this.drawerInfo.originalUrl &&\r\n        this.drawerInfo.originalUrl.includes(\"https://mp.weixin.qq.com\")\r\n      );\r\n    },\r\n    keyPointsFormatted() {\r\n      return formatMarkdown(this.markdownContent.keyPoints);\r\n    },\r\n    entitiesFormatted() {\r\n      return formatMarkdown(this.markdownContent.entities);\r\n    },\r\n  },\r\n  mounted() {\r\n    this.loading = true;\r\n    this.translationBtnShow = true;\r\n\r\n    // 添加全局meta标签禁用referrer\r\n    this.addNoReferrerMeta();\r\n\r\n    this.details();\r\n    this.getIndexData();\r\n    // this.hanldeBrowseAdd();\r\n  },\r\n  watch: {\r\n    tabPosition(newVal, oldVal) {\r\n      console.log(`Tab position changed from ${oldVal} to ${newVal}`);\r\n    },\r\n  },\r\n  methods: {\r\n    // 添加全局meta标签来禁用所有引用头\r\n    addNoReferrerMeta() {\r\n      // 检查是否已经存在meta标签\r\n      if (\r\n        document.querySelector('meta[name=\"referrer\"][content=\"no-referrer\"]')\r\n      ) {\r\n        return;\r\n      }\r\n\r\n      // 创建并添加meta标签\r\n      const meta = document.createElement(\"meta\");\r\n      meta.name = \"referrer\";\r\n      meta.content = \"no-referrer\";\r\n      document.head.appendChild(meta);\r\n      console.log(\"已添加no-referrer meta标签\");\r\n    },\r\n\r\n    // 辅助方法：修复图片的referrer设置\r\n    fixImageReferrer(content) {\r\n      // 确保每个微信图片标签都有referrerpolicy=\"no-referrer\"属性\r\n      if (!content) return content;\r\n\r\n      // 替换所有微信图片标签，确保它们有referrerpolicy属性\r\n      return content.replace(\r\n        /<img([^>]*?src=[\"']https?:\\/\\/mmbiz\\.q(?:logo|pic)\\.cn\\/[^\"']+[\"'][^>]*?)>/gi,\r\n        (match, attrPart) => {\r\n          // 如果已经有referrerpolicy属性，不再添加\r\n          if (attrPart.includes(\"referrerpolicy\")) {\r\n            return match;\r\n          }\r\n          // 添加referrerpolicy属性\r\n          return `<img${attrPart} referrerpolicy=\"no-referrer\">`;\r\n        }\r\n      );\r\n    },\r\n\r\n    // 使用canvas或图片代理增强微信图片的方法（不替换DOM节点）\r\n    replaceAllWechatImages() {\r\n      // 如果不是微信文章，直接返回\r\n      if (!this.isWeixinArticle) {\r\n        return Promise.resolve();\r\n      }\r\n\r\n      return new Promise((resolve) => {\r\n        try {\r\n          // 找到所有微信域名的图片\r\n          const wechatImages = document.querySelectorAll(\r\n            'img[src*=\"mmbiz.qpic.cn\"], img[src*=\"mmbiz.qlogo.cn\"], img[data-src*=\"mmbiz\"], img[src*=\"mmsns.qpic.cn\"]'\r\n          );\r\n\r\n          if (wechatImages.length === 0) {\r\n            resolve();\r\n            return;\r\n          }\r\n\r\n          console.log(`开始增强${wechatImages.length}张微信图片，保留现有图片`);\r\n          let processedCount = 0;\r\n\r\n          // 处理每一张图片\r\n          wechatImages.forEach((img, index) => {\r\n            // 如果图片已经被替换过且非空白，则跳过\r\n            if (\r\n              img.hasAttribute(\"data-wx-replaced\") &&\r\n              img.complete &&\r\n              img.naturalWidth > 0\r\n            ) {\r\n              processedCount++;\r\n              if (processedCount >= wechatImages.length) {\r\n                resolve();\r\n              }\r\n              return;\r\n            }\r\n\r\n            // 记录原始尺寸和样式\r\n            const originalWidth = img.style.width || img.width || \"auto\";\r\n            const originalHeight = img.style.height || img.height || \"auto\";\r\n\r\n            // 只有当图片无法显示时才进行处理\r\n            if (!img.complete || img.naturalWidth === 0) {\r\n              // 获取图片源\r\n              const originalSrc =\r\n                img.getAttribute(\"data-original-src\") ||\r\n                img.getAttribute(\"src\");\r\n              if (!originalSrc) {\r\n                // 无法获取源，跳过\r\n                processedCount++;\r\n                if (processedCount >= wechatImages.length) {\r\n                  resolve();\r\n                }\r\n                return;\r\n              }\r\n\r\n              // 添加必要的属性\r\n              img.setAttribute(\"referrerpolicy\", \"no-referrer\");\r\n              img.classList.add(\"wx-img\");\r\n              img.style.maxWidth = \"100%\";\r\n              img.style.height = \"auto\";\r\n\r\n              // 设置标识\r\n              img.setAttribute(\"data-wx-replaced\", \"true\");\r\n\r\n              // 尝试使用HTTPS加载\r\n              if (originalSrc.startsWith(\"http:\")) {\r\n                const httpsUrl = originalSrc.replace(/^http:/, \"https:\");\r\n                console.log(`尝试使用HTTPS协议: ${httpsUrl}`);\r\n                img.src = httpsUrl;\r\n              }\r\n              // 尝试添加格式参数\r\n              else if (!originalSrc.includes(\"wx_fmt=\")) {\r\n                const separator = originalSrc.includes(\"?\") ? \"&\" : \"?\";\r\n                const srcWithFormat = `${originalSrc}${separator}wx_fmt=jpeg`;\r\n                console.log(`尝试添加格式参数: ${srcWithFormat}`);\r\n                img.src = srcWithFormat;\r\n              }\r\n            }\r\n\r\n            // 无论是否处理，都计数\r\n            processedCount++;\r\n            if (processedCount >= wechatImages.length) {\r\n              resolve();\r\n            }\r\n          });\r\n\r\n          // 设置超时保障\r\n          setTimeout(() => {\r\n            resolve();\r\n          }, 3000);\r\n        } catch (e) {\r\n          console.error(\"增强微信图片出错:\", e);\r\n          resolve();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 强制重新加载内容区域，避免清空导致图片丢失\r\n    forceReloadContent() {\r\n      // 如果没有内容或不是微信文章，直接返回\r\n      if (!this.htmlJson || !this.isWeixinArticle) {\r\n        return;\r\n      }\r\n\r\n      console.log(\"开始增强图片显示处理，保留现有DOM结构\");\r\n\r\n      // 不再清空内容，直接在现有DOM上处理图片\r\n      this.$nextTick(() => {\r\n        // 先进行基础修复\r\n        this.fixWechatImages();\r\n\r\n        // 延迟执行彻底替换\r\n        setTimeout(() => {\r\n          this.replaceAllWechatImages().then(() => {\r\n            console.log(\"完成彻底替换微信图片\");\r\n            // 最后强制更新一次视图\r\n            this.$forceUpdate();\r\n          });\r\n        }, 100);\r\n      });\r\n    },\r\n\r\n    // 预处理微信图片\r\n    preloadWechatImages(content) {\r\n      return new Promise((resolve) => {\r\n        try {\r\n          // 先应用全局referrer策略\r\n          this.addNoReferrerMeta();\r\n\r\n          // 先修复内容中包含转义字符的图片标签\r\n          content = this.fixEscapedImageTags(content);\r\n\r\n          // 修复内容中的图片标签\r\n          content = this.handleWechatImages(content);\r\n\r\n          // 处理相对路径图片\r\n          content = this.handleRelativeImagePaths(content);\r\n\r\n          // 标记内容准备好了\r\n          this.contentReady = true;\r\n          resolve(content);\r\n\r\n          // 在内容加载后，下一个事件循环再应用修复\r\n          setTimeout(() => {\r\n            this.fixWechatImages();\r\n          }, 0);\r\n        } catch (e) {\r\n          console.error(\"预处理微信图片出错:\", e);\r\n          this.contentReady = true;\r\n          resolve(content);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 修复包含转义字符的图片标签 - 更彻底的处理\r\n    fixEscapedImageTags(content) {\r\n      try {\r\n        if (!content) return content;\r\n\r\n        // 先处理全局的转义字符，简化后续处理\r\n        content = content\r\n          .replace(/\\\\\"/g, '\"')\r\n          .replace(/\\\\'/g, \"'\")\r\n          .replace(/\\\\\\\\/g, \"\\\\\")\r\n          .replace(/\\\\&quot;/g, \"&quot;\")\r\n          .replace(/&amp;/g, \"&\");\r\n\r\n        // 扩展匹配模式，捕获所有可能的问题格式\r\n        const escapedTagRegex =\r\n          /<img[^>]*?(?:src|data-src)=[\"']?(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")([^\"']+?)(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")[\"']?[^>]*?>/gi;\r\n        const badStyleRegex =\r\n          /<img[^>]*?style=[\"'][^\"']*?(?:16px|white-space)[^\"']*?[\"'][^>]*?>/gi;\r\n        const brokenPathRegex =\r\n          /<img[^>]*?src=[\"'][^\"']*?mmbiz[^\"']*?[\"'][^>]*?>/gi;\r\n\r\n        // 合并所有匹配结果\r\n        const escapedTags = content.match(escapedTagRegex) || [];\r\n        const badStyleTags = content.match(badStyleRegex) || [];\r\n        const brokenPathTags = content.match(brokenPathRegex) || [];\r\n\r\n        // 去重 - 转换为Set然后再转回数组\r\n        const allTags = [\r\n          ...new Set([...escapedTags, ...badStyleTags, ...brokenPathTags]),\r\n        ];\r\n\r\n        if (allTags.length === 0) {\r\n          return content; // 没有找到需要修复的标签\r\n        }\r\n\r\n        console.log(`找到${allTags.length}个可能有问题的图片标签`);\r\n\r\n        // 处理每个问题标签\r\n        for (const tag of allTags) {\r\n          // 提取图片URL - 尝试多种模式\r\n          let imgUrl = \"\";\r\n\r\n          // 尝试匹配各种可能的src格式\r\n          const patterns = [\r\n            /src=[\"']?(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?([^\"'<>\\s]+?mmbiz[^\"'<>\\s]+)(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?[\"']?/i,\r\n            /data-src=[\"']?(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?([^\"'<>\\s]+?mmbiz[^\"'<>\\s]+)(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?[\"']?/i,\r\n            /original-src=[\"']?(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?([^\"'<>\\s]+?mmbiz[^\"'<>\\s]+)(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?[\"']?/i,\r\n          ];\r\n\r\n          // 尝试所有模式直到找到匹配项\r\n          for (const pattern of patterns) {\r\n            const match = tag.match(pattern);\r\n            if (match && match[1]) {\r\n              imgUrl = match[1];\r\n              break;\r\n            }\r\n          }\r\n\r\n          if (!imgUrl) {\r\n            // 如果仍然无法提取URL，跳过此标签\r\n            continue;\r\n          }\r\n\r\n          // 清理URL并添加必要的参数\r\n          imgUrl = imgUrl.replace(\r\n            /\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\"/g,\r\n            \"\"\r\n          );\r\n          if (!imgUrl.includes(\"wx_fmt=\") && imgUrl.includes(\"mmbiz\")) {\r\n            const separator = imgUrl.includes(\"?\") ? \"&\" : \"?\";\r\n            imgUrl += `${separator}wx_fmt=jpeg`;\r\n          }\r\n\r\n          // 处理可能存在的协议问题\r\n          if (!imgUrl.startsWith(\"http\")) {\r\n            if (imgUrl.startsWith(\"//\")) {\r\n              imgUrl = \"https:\" + imgUrl;\r\n            } else {\r\n              imgUrl = \"https://\" + imgUrl;\r\n            }\r\n          }\r\n\r\n          // 创建干净的替换标签 - 更简洁但完整\r\n          const newTag = `<img referrerpolicy=\"no-referrer\" class=\"wx-img\" src=\"${imgUrl}\" data-src=\"${imgUrl}\" data-original-src=\"${imgUrl}\" style=\"max-width:100%;height:auto!important;\" />`;\r\n\r\n          // 替换原始标签\r\n          content = content.replace(tag, newTag);\r\n        }\r\n\r\n        return content;\r\n      } catch (e) {\r\n        console.error(\"修复图片标签出错:\", e);\r\n        return content; // 出错时返回原始内容\r\n      }\r\n    },\r\n\r\n    // 动态修复图片方法 - 增强版，实现DOM重新渲染\r\n    fixWechatImages() {\r\n      try {\r\n        // 添加全局样式，确保所有微信图片都有no-referrer\r\n        const style = document.createElement(\"style\");\r\n        style.textContent = `\r\n          img[src*=\"mmbiz.qpic.cn\"], img[src*=\"mmbiz.qlogo.cn\"] {\r\n            max-width: 100% !important;\r\n            height: auto !important;\r\n            display: block !important;\r\n            margin: 10px auto !important;\r\n            object-fit: contain !important;\r\n            -webkit-referrer: no-referrer !important;\r\n            referrerpolicy: no-referrer !important;\r\n          }\r\n        `;\r\n        document.head.appendChild(style);\r\n\r\n        // 找到所有微信域名的图片\r\n        const wechatImages = document.querySelectorAll(\r\n          'img[src*=\"mmbiz.qpic.cn\"], img[src*=\"mmbiz.qlogo.cn\"], img[src*=\"mmsns.qpic.cn\"]'\r\n        );\r\n\r\n        if (wechatImages.length > 0) {\r\n          console.log(\r\n            `页面中找到${wechatImages.length}张微信图片，应用全局修复`\r\n          );\r\n\r\n          wechatImages.forEach((img) => {\r\n            // 添加必要的属性\r\n            img.setAttribute(\"referrerpolicy\", \"no-referrer\");\r\n            img.classList.add(\"wx-img\");\r\n\r\n            // 如果图片尚未进行错误处理，添加错误处理\r\n            if (!img.hasAttribute(\"data-error-handled\")) {\r\n              img.setAttribute(\"data-error-handled\", \"true\");\r\n\r\n              // 添加错误处理\r\n              img.onerror = function () {\r\n                console.log(\"图片加载失败，应用占位样式\");\r\n                this.style.border = \"1px dashed #ccc\";\r\n                this.style.padding = \"10px\";\r\n                this.style.width = \"auto\";\r\n                this.style.height = \"auto\";\r\n                this.style.minHeight = \"100px\";\r\n                this.alt = \"图片加载失败\";\r\n              };\r\n            }\r\n          });\r\n        }\r\n      } catch (e) {\r\n        console.error(\"修复微信图片出错:\", e);\r\n      }\r\n\r\n      // 返回一个Promise，方便外部检测完成状态\r\n      return Promise.resolve();\r\n    },\r\n\r\n    // 处理相对路径图片的方法\r\n    handleRelativeImagePaths(content) {\r\n      if (!content || !this.drawerInfo?.originalUrl) {\r\n        return content;\r\n      }\r\n\r\n      // 从原文链接中提取基础URL\r\n      const originalUrl = this.drawerInfo.originalUrl;\r\n      const urlParts = originalUrl.split(\"/\");\r\n      urlParts.pop(); // 移除文件名\r\n      const baseUrl = urlParts.join(\"/\") + \"/\";\r\n\r\n      // 处理所有相对路径图片\r\n      return content.replace(\r\n        /<img([^>]*?)src\\s*=\\s*[\"']([^\"']+)[\"']([^>]*?)>/gi,\r\n        (match, before, src, after) => {\r\n          // 跳过绝对路径\r\n          if (src.startsWith(\"http\") || src.startsWith(\"//\")) {\r\n            return match;\r\n          }\r\n\r\n          // 跳过非图片文件\r\n          if (!/\\.(png|jpg|jpeg|gif|webp|bmp|svg)(\\?.*)?$/i.test(src)) {\r\n            return match;\r\n          }\r\n\r\n          // 转换相对路径\r\n          let newSrc = \"\";\r\n          if (src.startsWith(\"./\")) {\r\n            newSrc = baseUrl + src.substring(2);\r\n          } else if (src.startsWith(\"/\")) {\r\n            const urlObj = new URL(originalUrl);\r\n            newSrc = urlObj.protocol + \"//\" + urlObj.host + src;\r\n          } else {\r\n            newSrc = baseUrl + src;\r\n          }\r\n\r\n          // 构建新的img标签，移除所有old*属性\r\n          let newTag = `<img${before} src=\"${newSrc}\"${after}>`;\r\n          newTag = newTag.replace(\r\n            /\\s*(oldsrc|oldSrc|old-src)\\s*=\\s*[\"'][^\"']*[\"']/gi,\r\n            \"\"\r\n          );\r\n\r\n          return newTag;\r\n        }\r\n      );\r\n    },\r\n\r\n    // 处理微信图片的公共方法\r\n    handleWechatImages(content) {\r\n      if (!content) return content;\r\n\r\n      try {\r\n        // 先处理转义字符问题\r\n        content = content\r\n          .replace(/\\\\\"/g, '\"')\r\n          .replace(/\\\\'/g, \"'\")\r\n          .replace(/\\\\\\\\/g, \"\\\\\");\r\n\r\n        // 将所有微信图片URL进行提取和替换\r\n        const regex =\r\n          /<img[^>]*?src=[\"'](https?:\\/\\/mmbiz\\.q(?:logo|pic)\\.cn\\/[^\"']+)[\"'][^>]*?>/gi;\r\n\r\n        // 简单直接的替换，确保每个微信图片都有正确属性\r\n        let newContent = content.replace(\r\n          regex,\r\n          '<img src=\"$1\" referrerpolicy=\"no-referrer\" class=\"wx-img\" style=\"max-width:100%;height:auto;\" />'\r\n        );\r\n\r\n        // 还需要处理已被转义的图片URL\r\n        const escapedRegex =\r\n          /<img[^>]*?src=[\"']?(\\\\?&quot;|\\\\?\"|&quot;|%22)(https?:\\/\\/mmbiz[^\"'&]+)(\\\\?&quot;|\\\\?\"|&quot;|%22)[\"']?[^>]*?>/gi;\r\n        newContent = newContent.replace(\r\n          escapedRegex,\r\n          '<img src=\"$2\" referrerpolicy=\"no-referrer\" class=\"wx-img\" style=\"max-width:100%;height:auto;\" />'\r\n        );\r\n\r\n        return newContent;\r\n      } catch (e) {\r\n        console.error(\"处理微信图片HTML出错:\", e);\r\n        return content;\r\n      }\r\n    },\r\n\r\n    async getIndexData() {\r\n      await API.recommendHot().then((response) => {\r\n        if (response.code == 200) {\r\n          this.articleList = response.data.slice(0, 5).map((item) => {\r\n            item.cnTitle = item.title;\r\n            item.id = item.articleId;\r\n            return item;\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    async details() {\r\n      let params;\r\n      if (this.$route.query.id) {\r\n        params = {\r\n          id: this.$route.query.id,\r\n        };\r\n      } else {\r\n        params = { articleSn: this.$route.query.articleSn };\r\n      }\r\n      // await API.AreaInfo(this.$route.query.id || this.$route.query.articleSn).then(async (res) => {\r\n      await API.articleDetail(params).then(async (res) => {\r\n        if (res.code == 200) {\r\n          this.hanldeBrowseAdd(res.data.id);\r\n          this.drawerInfo = res.data;\r\n          this.drawerInfo.sourceType != \"1\" && this.drawerInfo.sourceType != \"3\"\r\n            ? (this.translationBtnShow = true)\r\n            : (this.translationBtnShow = false);\r\n\r\n          // 如果是微信文章，先显示加载状态\r\n          if (\r\n            this.drawerInfo.originalUrl &&\r\n            this.drawerInfo.originalUrl.includes(\"https://mp.weixin.qq.com\")\r\n          ) {\r\n            this.contentReady = false;\r\n          } else {\r\n            this.contentReady = true;\r\n          }\r\n\r\n          // 预处理内容\r\n          const rawContent =\r\n            this.drawerInfo.cnContent || this.drawerInfo.content;\r\n          let processedContent = this.formattingJson(rawContent);\r\n\r\n          // 处理swdt数据\r\n          let keyPoints = \"\";\r\n          let entities = \"\";\r\n\r\n          if (this.drawerInfo.swdt && Array.isArray(this.drawerInfo.swdt)) {\r\n            this.drawerInfo.swdt.forEach((item) => {\r\n              if (item.swdtTaskid === \"1\" && item.swdtContent) {\r\n                // 内容要点\r\n                keyPoints = item.swdtContent;\r\n              } else if (item.swdtTaskid === \"2\" && item.swdtContent) {\r\n                // 人员/机构/技术/产品\r\n                entities = item.swdtContent;\r\n              }\r\n            });\r\n          }\r\n\r\n          // 预处理 markdown 内容，确保换行符正确处理\r\n          const preprocessMarkdown = (text) => {\r\n            if (!text) return \"\";\r\n            // 处理各种转义字符和格式问题\r\n            return text\r\n              .replace(/```markdown\\s*|```\\s*/g, \"\") // 移除 markdown 代码块标记\r\n              .replace(/```[a-zA-Z]*\\s*/g, \"\") // 移除带有语言标记的代码块标记\r\n              .replace(/\\\\n/g, \"\\n\") // 处理双反斜杠换行符\r\n              .replace(/\\\\\\n/g, \"\\n\") // 处理三反斜杠换行符\r\n              .replace(/\\r\\n/g, \"\\n\") // 处理 Windows 风格换行符\r\n              .replace(/\\$\\{[^}]+\\}/g, \"\"); // 移除模板字符串占位符\r\n          };\r\n\r\n          // 设置 markdown 内容，使用预处理函数\r\n          this.markdownContent.keyPoints = preprocessMarkdown(keyPoints);\r\n          this.markdownContent.entities = preprocessMarkdown(entities);\r\n\r\n          // 如果是微信公众号文章，进行预加载处理\r\n          if (\r\n            this.drawerInfo.originalUrl &&\r\n            this.drawerInfo.originalUrl.includes(\"https://mp.weixin.qq.com\")\r\n          ) {\r\n            try {\r\n              // 预加载微信图片 - 不会造成闪烁\r\n              processedContent = await this.preloadWechatImages(\r\n                processedContent\r\n              );\r\n              console.log(\"微信图片预处理完成\");\r\n            } catch (e) {\r\n              console.error(\"微信图片预处理失败:\", e);\r\n              // 出错时，显示内容\r\n              this.contentReady = true;\r\n            }\r\n          }\r\n\r\n          // 设置处理后的内容\r\n          this.$set(this, \"htmlJson\", processedContent);\r\n\r\n          if (this.drawerInfo.title == this.drawerInfo.cnTitle) {\r\n            this.titleShow = false;\r\n          }\r\n\r\n          document.title = this.drawerInfo.cnTitle || this.drawerInfo.title;\r\n          this.loading = false;\r\n\r\n          // 内容显示后，应用全局修复\r\n          if (this.isWeixinArticle) {\r\n            this.$nextTick(() => {\r\n              // 应用图片修复\r\n              this.fixWechatImages();\r\n\r\n              // 2秒后再检查一次\r\n              setTimeout(() => {\r\n                this.fixWechatImages();\r\n              }, 2000);\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    formattingJson(content) {\r\n      if (content) {\r\n        if (containsHtmlTags(content)) {\r\n          content = content.replace(/<br>/g, \"\");\r\n          content = content.replace(/\\n/g, \"\");\r\n          content = content.replace(/\\\\n/g, \"\");\r\n          content = content.replace(/\\\\\\n/g, \"\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n\r\n          // 处理微信公众号图片防盗链问题\r\n          if (\r\n            this.drawerInfo.originalUrl &&\r\n            this.drawerInfo.originalUrl.includes(\"https://mp.weixin.qq.com\")\r\n          ) {\r\n            content = this.handleWechatImages(content);\r\n          }\r\n\r\n          // 处理相对路径图片\r\n          content = this.handleRelativeImagePaths(content);\r\n\r\n          console.log(\"包含的HTML标签\", extractHtmlTags(content));\r\n          console.log(\"HTML是否结构正确\", hasValidHtmlStructure(content));\r\n        } else {\r\n          content = content.replace(/\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n\r\n          // 处理微信公众号图片防盗链问题\r\n          if (\r\n            this.drawerInfo.originalUrl &&\r\n            this.drawerInfo.originalUrl.includes(\"https://mp.weixin.qq.com\")\r\n          ) {\r\n            content = this.handleWechatImages(content);\r\n          }\r\n\r\n          // 处理相对路径图片\r\n          content = this.handleRelativeImagePaths(content);\r\n        }\r\n      }\r\n      return content;\r\n    },\r\n    // 翻译文章\r\n    translateEvent(row) {\r\n      const fun = () => {\r\n        const loading = this.$loading({\r\n          lock: true,\r\n          text: \"Loading\",\r\n          spinner: \"el-icon-loading\",\r\n          background: \"rgba(0, 0, 0, 0.7)\",\r\n        });\r\n        API.translationTitle({\r\n          originalText: row.content,\r\n          docId: this.$route.query.id,\r\n          id: row.id,\r\n          translationField: \"content\",\r\n          translationType: 1,\r\n        })\r\n          .then((res) => {\r\n            this.drawerInfo.cnContent = res.data;\r\n            this.$set(\r\n              this,\r\n              \"htmlJson\",\r\n              this.formattingJson(\r\n                this.drawerInfo.cnContent || this.drawerInfo.content\r\n              )\r\n            );\r\n            this.originalArticleShow = true;\r\n            loading.close();\r\n          })\r\n          .catch((err) => {\r\n            loading.close();\r\n          });\r\n      };\r\n\r\n      if (row.cnContent) {\r\n        // 提示是否确认再次翻译\r\n        this.$confirm(\"是否确认再次翻译?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        })\r\n          .then(() => {\r\n            fun();\r\n          })\r\n          .catch(() => {\r\n            this.$set(\r\n              this,\r\n              \"htmlJson\",\r\n              this.formattingJson(\r\n                this.drawerInfo.cnContent || this.drawerInfo.content\r\n              )\r\n            );\r\n            this.originalArticleShow = true;\r\n          });\r\n      } else {\r\n        fun();\r\n      }\r\n    },\r\n    viewOriginal() {\r\n      if (this.originalArticleShow) {\r\n        this.$set(\r\n          this,\r\n          \"htmlJson\",\r\n          this.formattingJson(this.drawerInfo.content)\r\n        );\r\n      } else {\r\n        this.$set(\r\n          this,\r\n          \"htmlJson\",\r\n          this.formattingJson(\r\n            this.drawerInfo.cnContent || this.drawerInfo.content\r\n          )\r\n        );\r\n      }\r\n      this.originalArticleShow = !this.originalArticleShow;\r\n    },\r\n    backToTop() {\r\n      window.scrollTo({ top: 0, behavior: \"smooth\" });\r\n    },\r\n    close() {\r\n      this.type = true;\r\n      this.textarea = \"\";\r\n    },\r\n    submit() {\r\n      feedbackAdd({\r\n        articleId: this.$route.query.id,\r\n        docId: this.$route.query.id,\r\n        cnTitle: this.drawerInfo.cnTitle || this.drawerInfo.title,\r\n        content: this.textarea,\r\n      }).then((res) => {\r\n        if (res.code == 200) {\r\n          this.$message({\r\n            message: \"留言成功\",\r\n            type: \"success\",\r\n          });\r\n          this.close();\r\n        }\r\n      });\r\n    },\r\n    openNewView(item) {\r\n      window.open(`/expressDetails?id=${item.id}&docId=${item.id}`, \"_blank\");\r\n    },\r\n    handleCollections(item) {\r\n      if (!item.collection) {\r\n        API.collectApi([item.id]).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$message({\r\n              message: \"收藏成功,请前往个人中心查看\",\r\n              type: \"success\",\r\n            });\r\n            this.$set(this.drawerInfo, \"collection\", !item.collection);\r\n          } else {\r\n            this.$message({ message: \"收藏失败\", type: \"info\" });\r\n          }\r\n        });\r\n      } else {\r\n        API.cocelCollect([item.id]).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$message({ message: \"已取消收藏\", type: \"success\" });\r\n            this.$set(this.drawerInfo, \"collection\", !item.collection);\r\n          } else {\r\n            this.$message({ message: \"取消收藏失败\", type: \"info\" });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    handleRecommend(item) {\r\n      let query = new FormData();\r\n      query.append(\"articleId\", item.id);\r\n      if (!item.recommend) {\r\n        API.recommendAdd(query).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$message({\r\n              message: \"推荐成功,请前往个人中心查看\",\r\n              type: \"success\",\r\n            });\r\n            this.$set(this.drawerInfo, \"recommend\", !item.recommend);\r\n          } else {\r\n            this.$message({ message: \"推荐失败\", type: \"info\" });\r\n          }\r\n        });\r\n      } else {\r\n        API.recommendCancel(query).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$message({ message: \"已取消推荐\", type: \"success\" });\r\n            this.$set(this.drawerInfo, \"recommend\", !item.recommend);\r\n          } else {\r\n            this.$message({ message: \"取消推荐失败\", type: \"info\" });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    hanldeBrowseAdd(id) {\r\n      let query = new FormData();\r\n      query.append(\"articleId\", id);\r\n      API.browseAdd(query);\r\n    },\r\n    viewOriginalArticle(item) {\r\n      window.open(item.originalUrl);\r\n    },\r\n    // 打开思维导图弹窗\r\n    openMarkmap(type) {\r\n      if (!this.markdownContent[type]) {\r\n        this.$message.warning(\"暂无思维导图数据\");\r\n        return;\r\n      }\r\n\r\n      // 设置标题和内容\r\n      let title = \"\";\r\n      if (type === \"keyPoints\") {\r\n        title = \"内容要点思维导图\";\r\n      } else if (type === \"entities\") {\r\n        title = \"人员/机构/技术/产品思维导图\";\r\n      }\r\n\r\n      // 设置内容\r\n      const content = this.markdownContent[type];\r\n\r\n      // 使用localStorage存储大型内容，而不是通过URL传递\r\n      const storageKey = `markmap_data_${Date.now()}`;\r\n      localStorage.setItem(storageKey, content);\r\n\r\n      // 构建URL参数 - 只传递标题和存储键，不传递大型内容\r\n      const params = new URLSearchParams();\r\n      params.append(\"title\", title);\r\n      params.append(\r\n        \"articleTitle\",\r\n        this.drawerInfo.cnTitle || this.drawerInfo.title\r\n      );\r\n      params.append(\"storageKey\", storageKey);\r\n\r\n      // 获取屏幕尺寸，制定合适的窗口大小\r\n      const screenWidth = window.screen.width;\r\n      const screenHeight = window.screen.height;\r\n\r\n      // 窗口宽高为屏幕的80%，并居中显示\r\n      const width = Math.round(screenWidth * 0.8);\r\n      const height = Math.round(screenHeight * 0.8);\r\n      const left = Math.round((screenWidth - width) / 2);\r\n      const top = Math.round((screenHeight - height) / 2);\r\n\r\n      // 尝试打开新窗口\r\n      const windowFeatures = `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes,status=yes`;\r\n      const newWindow = window.open(\r\n        `/markmap?${params.toString()}`,\r\n        \"_blank\",\r\n        windowFeatures\r\n      );\r\n\r\n      // 检查是否成功打开窗口，如果被浏览器阻止则打开新标签页\r\n      if (\r\n        !newWindow ||\r\n        newWindow.closed ||\r\n        typeof newWindow.closed === \"undefined\"\r\n      ) {\r\n        // 浏览器可能阻止了弹窗，使用新标签页\r\n        window.open(`/markmap?${params.toString()}`, \"_blank\");\r\n        this.$message.info(\"新窗口被浏览器阻止，已在新标签页打开\");\r\n      }\r\n    },\r\n    // 关闭思维导图弹窗 - 保留以防需要\r\n    handleMarkmapClose() {\r\n      this.markmapVisible = false;\r\n      this.markmapContent = \"\";\r\n    },\r\n    // 处理发布时间的显示\r\n    formatPublishTime(publishTime, publishLocaltime) {\r\n      // 格式化publishTime为年月日\r\n      const formattedPublishTime = this.parseTime(publishTime, \"{y}-{m}-{d}\");\r\n\r\n      // 如果webstePublishTime不存在，直接返回publishTime\r\n      if (!publishLocaltime) {\r\n        return \"[北京]\" + formattedPublishTime;\r\n      }\r\n\r\n      let formattedWebsteTime = \"\";\r\n      // 处理不同格式的webstePublishTime\r\n      if (publishLocaltime) {\r\n        formattedWebsteTime = this.parseTime(publishLocaltime, \"{y}-{m}-{d}\");\r\n      }\r\n\r\n      // 比较年月日是否相同\r\n      if (formattedPublishTime === formattedWebsteTime) {\r\n        return \"[北京]\" + formattedPublishTime;\r\n      } else {\r\n        return `[当地]${formattedWebsteTime} / [北京]${formattedPublishTime}`;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n/* 全局样式 */\r\n::v-deep img[src*=\"mmbiz\"] {\r\n  max-width: 100% !important;\r\n  height: auto !important;\r\n  object-fit: contain !important;\r\n  margin: 10px auto !important;\r\n  display: block !important;\r\n  -webkit-referrer: no-referrer !important;\r\n  referrerpolicy: no-referrer !important;\r\n}\r\n\r\n.drawer_box {\r\n  display: flex;\r\n  user-select: text !important;\r\n  background: #f5f7fa;\r\n  min-height: 100vh;\r\n  justify-content: center;\r\n\r\n  .drawer_Style {\r\n    position: relative;\r\n    z-index: 2;\r\n    margin: 0px 20px 0px;\r\n    width: 800px;\r\n    background: #ffffff;\r\n    padding: 10px 30px;\r\n\r\n    .title {\r\n      font-size: 22px;\r\n      font-weight: 500px;\r\n      // text-align: center;\r\n    }\r\n\r\n    .source {\r\n      color: #0798f8;\r\n      // text-align: center;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .time {\r\n      font-size: 14px;\r\n      // text-align: center;\r\n      margin-left: 10px;\r\n      color: #9b9b9b;\r\n    }\r\n\r\n    .author {\r\n      color: #1a0997;\r\n      // text-align: center;\r\n      margin-left: 10px;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .summary {\r\n      background-color: #f5f7fa;\r\n      padding: 10px;\r\n      border-radius: 8px;\r\n      margin-bottom: 20px;\r\n    }\r\n    .summary .summary-item1 {\r\n      color: #971231;\r\n      font-weight: 600;\r\n      margin-bottom: 10px;\r\n    }\r\n    .summary .summary-item2 {\r\n      line-height: 30px;\r\n      word-break: break-word;\r\n    }\r\n\r\n    /* 文本展开折叠组件样式调整 */\r\n    .summary ::v-deep .text-ellipsis {\r\n      width: 100%;\r\n    }\r\n\r\n    .summary ::v-deep .text-content {\r\n      line-height: 30px;\r\n      font-size: 16px;\r\n      word-break: break-word;\r\n    }\r\n\r\n    .summary ::v-deep .text-limited {\r\n      -webkit-line-clamp: 10 !important;\r\n    }\r\n\r\n    .summary ::v-deep .ellipsis-actions {\r\n      text-align: right;\r\n      // margin-top: 8px;\r\n    }\r\n\r\n    .summary ::v-deep .show-more,\r\n    .summary ::v-deep .show-less {\r\n      color: #0798f8;\r\n      font-size: 14px;\r\n      transition: color 0.3s ease;\r\n    }\r\n\r\n    .summary ::v-deep .show-more:hover,\r\n    .summary ::v-deep .show-less:hover {\r\n      color: #0071ce;\r\n    }\r\n\r\n    .summary ::v-deep .show-more i,\r\n    .summary ::v-deep .show-less i {\r\n      margin-left: 3px;\r\n      font-size: 12px;\r\n    }\r\n  }\r\n}\r\n\r\n.right {\r\n  width: 500px;\r\n\r\n  .sisterDetails {\r\n    background-color: #ffffff;\r\n    padding: 10px 20px;\r\n    width: 100%;\r\n    color: #000000;\r\n\r\n    .media-info {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      .media-info-item {\r\n        flex: 1;\r\n      }\r\n    }\r\n\r\n    .name {\r\n      color: #9b9b9b;\r\n      line-height: 1.8;\r\n      font-size: 14px;\r\n\r\n      .value {\r\n        margin-left: 15px;\r\n        color: #000000;\r\n      }\r\n    }\r\n  }\r\n\r\n  .markdown {\r\n    margin-top: 20px;\r\n    background-color: #f2e3c5;\r\n    padding: 10px 20px;\r\n    width: 100%;\r\n    color: #000000;\r\n    height: 810px;\r\n\r\n    ::v-deep .el-divider {\r\n      background-color: #838484;\r\n      margin: 8px 0;\r\n    }\r\n\r\n    .markdown-content {\r\n      margin-bottom: 10px;\r\n      .markdown-content-title {\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        margin-bottom: 10px;\r\n      }\r\n      .markdown-content-text {\r\n        height: 325px;\r\n        overflow-y: auto;\r\n        padding: 10px;\r\n        line-height: 1.6;\r\n        font-size: 16px;\r\n        color: #333333;\r\n        border-radius: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .recommendedArticle {\r\n    margin-top: 20px;\r\n    background-color: #ffffff;\r\n    padding: 10px 20px;\r\n    width: 100%;\r\n    color: #000000;\r\n\r\n    .articleBox {\r\n      font-size: 14px;\r\n      margin: 10px 0;\r\n\r\n      .article {\r\n        margin-bottom: 5px;\r\n        cursor: pointer;\r\n        position: relative;\r\n        // width: calc(100% - 75px);\r\n        line-height: 1.8;\r\n\r\n        &:before {\r\n          content: \"•\";\r\n          color: red;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n\r\n      .bottom {\r\n        display: flex;\r\n        color: #9b9b9b;\r\n        align-items: baseline;\r\n\r\n        .sourceName {\r\n          margin-left: 10px;\r\n        }\r\n\r\n        .count {\r\n          margin-left: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .keyword {\r\n    margin-top: 20px;\r\n    background-color: #ffffff;\r\n    padding: 10px 20px;\r\n    width: 100%;\r\n    color: #000000;\r\n    height: 350px;\r\n\r\n    .chartBox {\r\n      height: 300px;\r\n    }\r\n  }\r\n\r\n  .articleEntity {\r\n    margin-top: 20px;\r\n    background-color: #ffffff;\r\n    padding: 10px 20px;\r\n    width: 100%;\r\n    color: #000000;\r\n    height: 350px;\r\n\r\n    .chartBox {\r\n      height: 300px;\r\n      position: relative;\r\n    }\r\n  }\r\n}\r\n\r\n.right_title {\r\n  font-size: 20px;\r\n}\r\n\r\n.el-divider-right {\r\n  margin: 8px 0;\r\n}\r\n\r\n.liuyanBox {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: calc(50vw - 653px);\r\n  width: 800px;\r\n  z-index: 99;\r\n  background-color: #e3e3ef;\r\n  padding: 5px 10px;\r\n  border-radius: 5px;\r\n\r\n  .morenzhuangtai {\r\n    height: 40px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .shuruzhuangtai {\r\n    height: 90px;\r\n\r\n    .top {\r\n      height: 40px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n    }\r\n\r\n    .bottom {\r\n      height: 50px;\r\n    }\r\n  }\r\n}\r\n\r\n.uesr {\r\n  display: flex;\r\n  align-items: center;\r\n  height: 30px;\r\n\r\n  .avatar {\r\n    cursor: pointer;\r\n    width: 30px;\r\n    height: 30px;\r\n    border-radius: 50%;\r\n  }\r\n\r\n  .name {\r\n    margin-left: 10px;\r\n  }\r\n}\r\n\r\n.tabs-all {\r\n  position: fixed;\r\n  left: calc(50vw - 693px);\r\n  top: 0px;\r\n  z-index: 99;\r\n  height: 800px;\r\n  width: 40px;\r\n\r\n  @media screen and (max-width: 1400px) {\r\n    left: 0;\r\n  }\r\n\r\n  .tabs {\r\n    writing-mode: vertical-rl;\r\n    /* 文字从上到下，从右到左 */\r\n    height: 120px;\r\n    width: 40px;\r\n    font-weight: 800;\r\n    font-size: 16px;\r\n    color: #ffffff;\r\n    line-height: 35px;\r\n    text-align: center;\r\n    font-style: normal;\r\n    background: url(\"../../assets/bigScreenTwo/tab-active.png\") no-repeat 0px\r\n      0px !important;\r\n    background-size: 100% 100% !important;\r\n    letter-spacing: 2px;\r\n    margin-bottom: 10px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.content-loading {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 100px;\r\n  width: 100%;\r\n  margin: 20px 0;\r\n}\r\n\r\n.loading-progress {\r\n  width: 80%;\r\n  max-width: 500px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.loading-text {\r\n  text-align: center;\r\n  margin-bottom: 10px;\r\n  color: #409eff;\r\n  font-size: 14px;\r\n}\r\n\r\n.preloading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 300px;\r\n  width: 100%;\r\n}\r\n\r\n::v-deep img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n::v-deep video {\r\n  width: 100%;\r\n}\r\n\r\n/* 添加微信图片特殊样式 */\r\n::v-deep .wx-img {\r\n  max-width: 100%;\r\n  height: auto !important;\r\n  display: block;\r\n  margin: 10px auto;\r\n}\r\n\r\n/* 替换后的图片样式 */\r\n::v-deep .wx-img-replaced {\r\n  max-width: 100%;\r\n  height: auto !important;\r\n  display: block;\r\n  margin: 10px auto;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n/* Canvas创建的图片样式 */\r\n::v-deep .wx-img-canvas {\r\n  max-width: 100%;\r\n  height: auto !important;\r\n  display: block;\r\n  margin: 10px auto;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n/* 占位图样式 */\r\n::v-deep .wx-img-placeholder {\r\n  max-width: 100%;\r\n  min-height: 100px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 10px auto;\r\n  border: 1px dashed #ccc;\r\n  padding: 20px;\r\n  font-size: 12px;\r\n  color: #999;\r\n  text-align: center;\r\n}\r\n\r\n/* markdown显示样式 */\r\n::v-deep .md-h1,\r\n::v-deep h1.md-h1 {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin: 12px 0 8px 0;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-h2,\r\n::v-deep h2.md-h2 {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin: 10px 0 6px 0;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-h3,\r\n::v-deep h3.md-h3 {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin: 12px 0 6px 0;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-li {\r\n  margin: 5px 0;\r\n  padding-left: 10px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n::v-deep .md-bullet,\r\n::v-deep .md-number {\r\n  margin-right: 8px;\r\n  color: #333333;\r\n  font-weight: bold;\r\n  flex-shrink: 0;\r\n}\r\n\r\n::v-deep .md-blockquote,\r\n::v-deep blockquote.md-blockquote {\r\n  border-left: 4px solid #d0d0d0;\r\n  padding: 5px 10px;\r\n  margin: 10px 0;\r\n  background-color: #f9f9f9;\r\n  font-style: italic;\r\n  color: #555;\r\n}\r\n\r\n::v-deep .markdown-content-text strong {\r\n  font-weight: bold;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .markdown-content-text em {\r\n  font-style: italic;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-code-block,\r\n::v-deep pre.md-code-block {\r\n  background-color: #f6f8fa;\r\n  border-radius: 3px;\r\n  padding: 10px;\r\n  margin: 10px 0;\r\n  font-family: monospace;\r\n  overflow-x: auto;\r\n  white-space: pre;\r\n  font-size: 13px;\r\n}\r\n\r\n::v-deep .md-code-inline,\r\n::v-deep code.md-code-inline {\r\n  background-color: #f6f8fa;\r\n  border-radius: 3px;\r\n  padding: 2px 4px;\r\n  font-family: monospace;\r\n  font-size: 13px;\r\n}\r\n\r\n/* 添加列表和段落样式 */\r\n::v-deep .md-paragraph {\r\n  margin: 5px 0;\r\n  line-height: 1.6;\r\n}\r\n\r\n::v-deep .md-paragraph-space {\r\n  height: 6px; /* 仅在需要的情况下调整为更小的值 */\r\n}\r\n\r\n::v-deep .md-ul,\r\n::v-deep .md-ol {\r\n  margin: 10px 0;\r\n  padding-left: 25px;\r\n}\r\n\r\n::v-deep .md-ul {\r\n  list-style-type: disc;\r\n}\r\n\r\n::v-deep .md-ul li {\r\n  margin: 5px 0;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-ul li::marker {\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-ol {\r\n  list-style-type: decimal;\r\n}\r\n\r\n::v-deep .md-ol li {\r\n  margin: 5px 0;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-ol li::marker {\r\n  color: #333333;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 添加滚动条样式： */\r\n/* 自定义滚动条样式 */\r\n::v-deep .markdown-content-text::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n::v-deep .markdown-content-text::-webkit-scrollbar-track {\r\n  background: transparent; /* 去掉滚动条背景色 */\r\n}\r\n\r\n::v-deep .markdown-content-text::-webkit-scrollbar-thumb {\r\n  background-color: #ffffff; /* 中等灰色 */\r\n  border-radius: 3px;\r\n}\r\n\r\n::v-deep .markdown-content-text::-webkit-scrollbar-thumb:hover {\r\n  background-color: #f7f7f7; /* 悬停时的颜色 */\r\n}\r\n\r\n/* 点击看大图按钮样式 */\r\n.view-markmap {\r\n  display: inline-block;\r\n  margin-left: 10px;\r\n  font-size: 14px;\r\n  color: #1890ff;\r\n  cursor: pointer;\r\n  background-color: rgba(24, 144, 255, 0.1);\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.view-markmap:hover {\r\n  background-color: rgba(24, 144, 255, 0.2);\r\n  text-decoration: underline;\r\n}\r\n\r\n.markdown-content-title {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n::v-deep pre {\r\n  white-space: normal;\r\n}\r\n</style>\r\n"]}]}