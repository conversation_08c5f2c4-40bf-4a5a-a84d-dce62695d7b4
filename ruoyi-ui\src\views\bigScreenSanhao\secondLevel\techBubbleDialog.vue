<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div
      class="custom-dialog"
      :class="{ 'tech-bubble-fullscreen': isFullscreen }"
      :style="isFullscreen ? {} : { width: width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ title }} - 热点技术分布</span>
        <div style="display: flex; align-items: center">
          <div
            @click="handleScreen"
            :title="isFullscreen ? '退出全屏' : '全屏'"
            style="
              margin-right: 20px;
              cursor: pointer;
              color: #ffffff;
              font-size: 20px;
            "
          >
            <i
              :class="isFullscreen ? 'el-icon-rank' : 'el-icon-full-screen'"
              style="width: 20px; height: 20px"
            ></i>
          </div>
          <div class="custom-dialog-close" @click="closeDialog"></div>
        </div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="bg-box-content">
            <div class="chart-container">
              <techBubbleChart
                v-if="visible && screenSn"
                :screenSn="screenSn"
                @openTechnologyDetails="handleOpenTechnologyDetails"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import techBubbleChart from "../components/techBubbleChart";

export default {
  name: "TechBubbleDialog",
  components: {
    techBubbleChart,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    screenSn: {
      type: String,
      default: "",
    },
    width: {
      type: Number,
      default: 1200,
    },
    description: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      // 全屏状态
      isFullscreen: false,
      // ResizeObserver 实例
      resizeObserver: null,
      // 防抖定时器
      resizeTimer: null,
    };
  },

  mounted() {
    // 添加ESC键监听
    document.addEventListener("keydown", this.handleKeydown);
    // 添加窗口大小变化监听
    window.addEventListener("resize", this.handleWindowResize);
  },

  beforeDestroy() {
    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    // 清理定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    }
    // 移除ESC键监听
    document.removeEventListener("keydown", this.handleKeydown);
    // 移除窗口大小变化监听
    window.removeEventListener("resize", this.handleWindowResize);
  },

  watch: {
    visible: {
      handler(newVal) {
        if (newVal) {
          // 重置全屏状态
          this.isFullscreen = false;
          // 延迟设置尺寸监听，确保DOM已渲染
          this.$nextTick(() => {
            this.setupResizeListeners();
          });
        }
      },
    },
  },

  methods: {
    closeDialog() {
      this.$emit("update:visible", false);
    },
    handleMaskClick() {
      this.closeDialog();
    },
    handleOpenTechnologyDetails(data) {
      this.$emit("openTechnologyDetails", data);
    },

    // 设置尺寸变化监听
    setupResizeListeners() {
      // 创建 ResizeObserver 监听容器尺寸变化
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(() => {
          // 使用防抖处理，避免频繁触发
          clearTimeout(this.resizeTimer);
          this.resizeTimer = setTimeout(() => {
            this.handleResize();
          }, 100);
        });

        // 监听弹窗容器的尺寸变化
        const dialogContainer = this.$el?.querySelector(".custom-dialog");
        if (dialogContainer) {
          this.resizeObserver.observe(dialogContainer);
        }

        // 监听图表容器的尺寸变化
        const chartContainer = this.$el?.querySelector(".chart-container");
        if (chartContainer) {
          this.resizeObserver.observe(chartContainer);
        }
      }
    },

    // 处理尺寸变化（只做基本的 resize）
    handleResize() {
      // 通知子组件 techBubbleChart 重新调整大小
      this.$nextTick(() => {
        const techBubbleChart = this.$children.find(
          (child) => child.$options.name === "TechBubbleChart"
        );
        if (techBubbleChart && techBubbleChart.resizeChart) {
          techBubbleChart.resizeChart();
        }
      });
    },

    // 全屏切换
    handleScreen() {
      this.isFullscreen = !this.isFullscreen;

      // 延迟调整图表大小，确保DOM更新完成
      this.$nextTick(() => {
        setTimeout(() => {
          this.handleResize();
        }, 300); // 等待CSS动画完成
      });
    },

    // 处理键盘事件
    handleKeydown(event) {
      // 按ESC键退出全屏
      if (event.key === "Escape" && this.isFullscreen && this.visible) {
        this.isFullscreen = false;
      }
    },

    // 处理窗口大小变化
    handleWindowResize() {
      if (this.isFullscreen) {
        // 重新调整图表大小
        this.handleResize();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;

  // 确保在所有分辨率下都能正确覆盖
  min-width: 100%;
  min-height: 100%;
  overflow: hidden;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 1200px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("../../../assets/bigScreenSanhao/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;
    transition: all 0.3s ease;

    &.tech-bubble-fullscreen {
      width: calc(100vw - 40px) !important;
      height: calc(100vh - 40px) !important;
      max-width: none !important;
      max-height: none !important;
      margin: 0 !important;
      // 确保在所有分辨率下都能正确显示
      min-width: calc(100% - 40px) !important;
      min-height: calc(100% - 40px) !important;

      .custom-dialog-body {
        height: calc(100% - 80px); // 减去header高度和padding
        max-height: calc(100% - 80px);
        overflow-y: auto;
        overflow-x: hidden;

        .bg-box {
          height: 100%;
          margin-bottom: 0;

          .bg-box-content {
            height: 100%;

            .chart-container {
              height: 100%; // 占满整个容器
            }
          }
        }
      }
    }

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("../../../assets/bigScreenSanhao/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("../../../assets/bigScreenSanhao/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      height: 800px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        // margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .bg-box-content {
          font-size: 16px;

          .chart-container {
            width: 100%;
            height: 800px;
          }
        }
      }
    }
  }
}
</style>
