<!-- 科情录入 -->
<template>
  <div style="height: 100%;overflow: hidden;">
    <leftLink :KeList="KeList" :isActive="isActive" :toolShow="false" :type="'新建专题'" :drawer="drawer"
      :ActiveData="ActiveData" :showAdd="false" @activeLink="activeEvent"></leftLink>
    <div>
      <div class="mainKe">
        <div class="parent">
          <div class="headerStyle">科情录入</div>
          <div class="mainStyle">
            <p>通过【科情录入】可以手动添加科情内容。添加的科情内容可以在科情列表中进行查看</p>
            <el-button type="primary" size="mini" style="margin-top: 30px;" @click="dialogTableVisible = true"
              v-hasPermi="['article:articleList:add']">科技情报录入</el-button>
          </div>
        </div>
      <!-- <div class="parent">
          <div class="headerStyle">科情导入</div>
          <div class="mainStyle">
            <p>
              使用【科情导入】，可以批量导入科情内容，导入文件仅支持Excel格式的文件，且不超过10MB 。可以通过下载的模板进行导入。
            </p>
            <el-button type="primary" size="mini" style="margin-top: 30px;"
              @click="uploadDialog = true">Excel文件导入</el-button>
            <el-button icon="el-icon-download" size="mini" type="primary">模板下载</el-button>
          </div>
                                                                        </div> 2023 9-6 暂时注释-->
      </div>
    </div>
    <el-dialog title="添加文章" size="mini" :visible.sync="dialogTableVisible" width="800px" @open="logOpen" :close-on-click-modal="false">
      <el-form :model="gridData" label-position="top" ref="form" :rules="FormRules" size="mini">
        <el-form-item label="原文链接" prop="link">
          <el-input v-model="gridData.link">
            <template slot="append">
              <el-button type="primary">采集内容</el-button>
              <el-divider direction="vertical"></el-divider>
              <el-button type="primary" @click="duplicateChecking">查重</el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="文章标题" prop="articleTitle">
              <el-input v-model="gridData.articleTitle"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="中文标题" prop="ChineseTitle">
              <el-input v-model="gridData.ChineseTitle"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="平台类型" prop="articleSource">
              <el-select v-model="gridData.articleSource" style="width: 100%;" clearable>
                <el-option :key="index" :label="item.name"
                    :value="item.id"
                  v-for="(item, index) in typeOptions"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="媒体来源" prop="mediumSource">
              <!-- <el-select v-model="gridData.mediumSource" style="width: 100%;" clearable></el-select> -->
              <el-input v-model="gridData.mediumSource" placeholder="请输入媒体来源"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="文章作者" prop="author">
              <el-input v-model="gridData.author" style="width: 100%;" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发布时间" prop="releaseTIme">
              <el-date-picker v-model="gridData.releaseTIme" value-format="yyyy-MM-dd HH:mm:ss" type="date"
                style="width: 100%;" clearable placeholder="选择日期"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="所属行业" prop="industry">
              <el-select v-model="gridData.industry" style="width: 100%;" multiple filterable>
                <el-option v-for="item in industry" :key="item.id" :label="item.industryName"
                  :value="item.industryName"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所属领域" prop="Domain">
              <el-select v-model="gridData.Domain" style="width: 100%;" multiple clearable filterable>
                <el-option v-for="item in areaList" :key="item.id" :label="item.fieldName"
                  :value="item.fieldName"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="动态标签" prop="trendsTag">
              <el-select v-model="gridData.trendsTag" style="width: 100%;" multiple filterable allow-create
                default-first-option placeholder="请添加文章标签"></el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="发布地区" prop="region">
          <el-radio-group v-model="gridData.region">
            <el-radio :label="1">国内</el-radio>
            <el-radio :label="2">境外</el-radio>
          </el-radio-group>
          <el-select v-model="gridData.countryOrCrea" style="margin-left: 20px;" placeholder="请选择地区"
            v-if="gridData.region == 1" collapse-tags>
            <el-option v-for="(item, index) in domesticList" :key="index" :label="item.regionName"
              :value="item.regionName"></el-option>
          </el-select>
          <el-select v-model="gridData.countryOrCrea" placeholder="请选择国家" v-if="gridData.region == 2" collapse-tags>
            <el-option v-for="(item, index) in countryList" :key="index" :label="item.regionName"
              :value="item.regionName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文章短链接" prop="shortLink">
          <el-input v-model="gridData.shortLink"></el-input>
        </el-form-item>
        <el-form-item label="摘要" prop="abstract">
          <el-input v-model="gridData.abstract" type="textarea" :autosize="{ minRows: 3, maxRows: 6 }"></el-input>
        </el-form-item>
        <el-form-item label="中文摘要" prop="Englishabstract">
          <el-input v-model="gridData.Englishabstract" type="textarea" :autosize="{ minRows: 3, maxRows: 6 }"></el-input>
        </el-form-item>
        <el-form-item label="文章详情" prop="details">
          <editor v-model="gridData.details" :minHeight="150"></editor>
        </el-form-item>
      <!-- <el-form-item label="英文文章详情" prop="details">
          <editor v-model="gridData.Englishdetails" :minHeight="150"></editor>
                                                  </el-form-item>-->
        <el-form-item label="封面图片" prop="cover">
          <el-upload action="#" ref="upload" :limit="3" :on-exceed="exceed" list-type="picture-card" :auto-upload="false"
            :headers="vertifyUpload.headers" :file-list="fileList" :on-change="handleChange" :http-request="requestLoad">
            <i slot="default" class="el-icon-plus"></i>
            <div slot="file" slot-scope="{file}">
              <img class="el-upload-list__item-thumbnail" :src="file.url" alt="封面图片加载失败" />
              <span class="el-upload-list__item-actions">
                <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="上传附件" prop="fileUrl">
          <el-upload class="upload-demo" :action="fileUrlurl" :before-upload="beforeUploadUrl" multiple :limit="1"
            :http-request="uploadUrlRequest" :on-success="uploadUrlSuccess" :file-list="fileUrlList"
            :on-exceed="uploadUrlExceed" :on-remove="uploadUrlRemove">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogTableVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="Excel导入" :visible.sync="uploadDialog" width="25%" :close-on-click-modal="false">
      <el-upload class="upload-demo" :limit="1" style="width: 360px;margin: 0 auto;" drag :action="url" multiple
        :file-list="fileListOne" :on-change="fileChange" :before-upload="beforeUpload" :on-exceed="exceed_Excel">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">只能上传.xls/.xlsx,文件大小不超过500kb</div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="uploadDialog = false">取 消</el-button>
        <el-button type="primary" @click="upLoadSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import leftLink from '../components/leftLink.vue'
import editor from '@/components/Editor/index.vue'
import { getToken } from '@/utils/auth'
import api from '@/api/ScienceApi/KeEnter.js'
import Article from '@/api/ScienceApi/index.js'
import { getListClassify } from '@/api/article/classify'

export default {
  components: {
    leftLink,
    editor
  },
  data() {
    return {
      areaList: [] /* 领域列表 */,
      industry: [] /* 行业列表 */,
      options1: [],
      options: [],
      KeList: [{ title: '开源科技情报数据' }, { title: '境外智库报告' }] /* 科情列表 */,
      isActive: 0,
      ActiveData: {},
      drawer: false,
      dialogTableVisible: false,
      gridData: {
        articleTitle: '' /* 文章标题 */,
        ChineseTitle: '' /* 中文标题 */,
        articleSource: '' /* 平台类型 */,
        details: '' /* 文章详情 */,
        Englishdetails: '' /* 英文详情 */,
        link: '' /* 原文链接 */,
        shortLink: '' /* 文章短连接 */,
        webName: '',
        author: '',
        mediumSource: '' /*  媒体来源 */,
        author: '' /* 作者 */,
        releaseTIme: '' /* 发布时间 */,
        industry: '' /* 所属行业 */,
        Domain: '' /* 所属领域 */,
        trendsTag: '' /* 动态标签 */,
        abstract: '' /* 摘要 */,
        Englishabstract: '' /* 英文摘要 */,
        region: 1 /* 类型 */,
        countryOrCrea: '' /* 地区 */
      },
      creaList: ['北京市', '天津市', '河北省'],
      country: ['俄罗斯', '英国', '德国'],
      FormRules: {
        articleTitle: [{ required: true, message: '文章标题为必填项' }],
        details: [{ required: true, message: '文章详情为必填项' }],
        releaseTIme: [{ required: true, message: '发布时间为必填项' }],
        ChineseTitle: [{ required: true, message: '中文名称为必填项' }],
        articleSource: [{ required: true, message: '平台类型为必填项' }],
        link: [{ required: true, message: '原文为必填项' }],
        abstract: [{ required: true, message: '请填写摘要' }],
        Englishabstract: [{ required: true, message: '请填写中文摘要' }]
      },
      /* 文件上传 */
      dialogVisible: false,
      disabled: false,
      fileList: [],
      url: process.env.VUE_APP_BASE_API + '',
      headers: {},
      uploadDialog: false /* excel文件导入Dialog */,
      fileListOne: [],
      vertifyUpload: {
        isUploading: false,
        // 设置上传的请求头部
        headers: {
          Authorization: 'Bearer ' + getToken(),
          ContentType: 'application/json;charset=utf-8'
        },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/article/articleList/cover'
      },
      typeOptions: [
        { name: '微信公众号', value: 1 },
        { name: '网站', value: 2 }
      ],
      domesticList: [] /* 国内地区 */,
      countryList: [] /* 国外地区 */,
      snRepeat: false,
      fileUrlList: [],
      fileUrlurl: process.env.VUE_APP_BASE_API + '/article/articleList/upload/file',
      sourceTypeList:[],
    }
  },
  watch: {
    isActive(newVaslue, old) {
      /* 防止出现未选中的情况 */
      if (newVaslue == null) {
        this.isActive = old
      }
    },
    'gridData.link': {
      handler(newVal, oldVal) {
        this.snRepeat = false
      },
      deep: true
    },
    'gridData.articleSource': {
      handler(newVal, oldVal) {
        if (newVal == 1) {
          this.FormRules.shortLink = [{ required: true, message: '请填写文章短链接' }]
        } else {
          this.FormRules.shortLink = [{ required: false, message: '请填写文章短链接' }]
        }
      },
      deep: true
    },
    uploadDialog: function (newVal, oldVal) {
      if (!newVal) {
        this.fileListOne = []
      }
    },
    dialogTableVisible: function (newVal, oldVal) {
      if (!newVal) {
        this.fileList = []
      }
    }
  },
  created() {
    getListClassify().then(res => {
      this.sourceTypeList = res.data
      this.typeOptions = res.data
    })
    this.getList()
  },
  methods: {
    async logOpen() {
      let Type = []
      this.KeList[this.isActive].type.split(',').map(item => {
        this.sourceTypeList.forEach(element => {
          if (element.id == item) {
            Type.push(element)
          }
        })
      })
      this.typeOptions = Type
      /* 获取领域和分类 */
      await Article.areaList()
        .then(data => {
          if (data.code == 200) {
            this.areaList = data.data
          }
        })
        .then(() => {
          Article.industry().then(data => {
            if (data.code == 200) {
              this.industry = data.data
            }
          })
        })
    },
    async getList() {
      let res = await api.monitoringAndSpecial({ pageSize: 20, pageNum: 1 })
      if (res.code == 200) {
        this.KeList = res.data
        this.ActiveData = this.KeList[this.isActive]
        this.isEmpty = false
      } else {
        this.isEmpty = true
        this.$message({ message: '科情列表获取失败，请联系管理员', type: 'error' })
      }
      this.getArea()
    },
    getArea() {
      Article.getAreaList()
        .then(Response => {
          if (Response.code == 200) {
            this.domesticList = Response.data[0]
            this.countryList = Response.data[1]
          }
        })
        .catch(err => {
          this.$message({ message: '地区数据获取失败', type: 'error' })
        })
    },
    /* 筛选领域 */
    remoteEvent(query) {
      this.options = this.areaList.filter(item => {
        return item.fieldName.toLowerCase().indexOf(query.toLowerCase()) > -1
      })
    },
    /* 筛选行业 */
    remoteIndustry(query) {
      this.options1 = this.industry.filter(item => {
        return item.industryName.toLowerCase().indexOf(query.toLowerCase()) > -1
      })
    },
    activeEvent(num) {
      this.isActive = num
      if (!this.isActive) return
      this.ActiveData = this.KeList[this.isActive]
    },
    /* 
    上传图片模块
    */
    async handleRemove(file) {
      /* 移除图片 */
      await api.removeImages({ filePath: file.path }).then(response => {
        if (response.code == 200) {
          let index = this.fileList.lastIndexOf(file)
          this.fileList = this.fileList.slice(0, index)
          this.$message({ message: '已删除文件', type: 'success' })
        }
      })
    },
    handleChange(file, fileList) {
      this.fileList = fileList
      this.$refs.upload.submit()
    },
    /* 文件超出限制 */
    exceed() {
      this.$message({ message: '文件上传超出限制,最多可以上传三个文件', type: 'info' })
    },
    /* 表单上传 */
    async onSubmit() {
      if (!this.snRepeat) {
        await this.duplicateChecking(false)
        if (!this.snRepeat) {
          return
        }
      }
      let cover = String(this.fileList.map(item => item.path))
      let params = {
        title: this.gridData.articleTitle /* 标题 */,
        cnTitle: this.gridData.ChineseTitle /* 中文标题 */,
        sourceType: this.gridData.articleSource /* 平台类型：1.微信公众号；2.网站；3.手动录入 */,
        sourceName: this.gridData.mediumSource /* 来源名称 */,
        author: this.gridData.author /* 作者 */,
        publishTime: this.gridData.releaseTIme /* 发布时间yyyy-MM-dd HH:mm:ss */,
        industry: String(this.gridData.industry) /* 行业ID */,
        domain: String(this.gridData.Domain) /* 领域 */,
        tags: String(this.gridData.trendsTag) /* 标签名称 */,
        originalUrl: this.gridData.link /* 文章原文链接 */,
        shortUrl: this.gridData.shortLink /* 文章短连接 */,
        summary: this.gridData.abstract /* 摘要 */,
        cnSummary: this.gridData.Englishabstract /* 中文摘要 */,
        cover: cover /* 封面图片路径 */,
        publish_type: this.gridData.region,
        content: this.gridData.details,
        publish_area: String(this.gridData.countryOrCrea),
        fileUrl: this.gridData.fileUrl,
      }
      this.$refs.form.validate(vold => {
        if (vold) {
          api.articleList(params).then(response => {
            if (response.code == 200) {
              this.$message({ message: '录入成功', type: 'success' })
            } else {
              this.$message({ message: '录入失败', type: 'error' })
            }
          }).catch(err => {
            this.dialogTableVisible = false
            this.$refs.form.resetFields()
            this.$message('录入出错了,请及时向管理员反馈')
          })
          this.snRepeat = false
          this.dialogTableVisible = false
          this.$refs.form.resetFields()
        }
      })
    },
    /* 自定义上传 */
    async requestLoad(file) {
      let data = new FormData()
      data.append('cover', file.file)

      await api.uploadImage(data).then(response => {
        if (response.code == 200) {
          this.fileList.map(item => {
            if (item.uid == file.file.uid) {
              item.path = response.imgUrl
            }
          })
          this.$message({ message: '上传成功', type: 'success' })
        } else {
          this.$message({ message: '上传失败,请稍候重试', type: 'error' })
        }
      })
    },
    /* 
    文件上传之前   excel模块
     */
    beforeUpload(file) {
      let fileName = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase(),
        condition = fileName == 'xlsx' || fileName == 'xls'
      let fileSize = file.size / 1024 / 1024 < 10
      if (!condition) {
        this.$notify({
          title: '警告',
          message: '上传文件必须是XLSX、XLS格式',
          type: 'warning'
        })
      }
      /* 文件大小限制 */
      if (!fileSize) {
        this.$notify({
          title: '警告',
          message: '上传文件的大小不能超过 10MB!',
          type: 'warning'
        })
      }
      return condition && fileSize
    },
    /* 文件数量超出限制 */
    exceed_Excel() {
      this.$message({ message: '文件上传超出限制,最多可以上传1个文件', type: 'info' })
    },
    /* 提交 */
    upLoadSubmit() {
      this.uploadDialog = false
    },
    /* 文件状态变化 */
    fileChange(file, fileList) {
      this.fileListOne = fileList
    },
    // 查重
    async duplicateChecking(blo) {
      if (this.gridData.link != null && this.gridData.link != '') {
        if (this.gridData.link.match(/(http|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/)) {
          await api.checkSn({ originalUrl: this.gridData.link }).then(response => {
            this.snRepeat = response.data
            if (response.data) {
              blo && this.$message({ message: '该链接不存在', type: 'success' })
            } else {
              this.$message({ message: '该链接已存在', type: 'warning' })
            }
          })
        } else {
          this.$message({ message: '请填写正确的原文链接', type: 'warning' })
        }
      } else {
        this.$message({ message: '请填写原文链接', type: 'warning' })
      }
    },
    // 上传附件校验
    beforeUploadUrl(file) {
      // 判断文件是否为excel
      let fileName = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase(),
        condition = fileName == 'pdf' || fileName == 'doc' || fileName == 'xls' || fileName == 'ppt' || fileName == 'xlsx' || fileName == 'pptx' || fileName == 'docx'
      let fileSize = file.size / 1024 / 1024 < 10
      if (!condition) {
        this.$notify({
          title: '警告',
          message: '上传文件必须是pdf,doc,xls,ppt,xlsx,pptx,docx格式',
          type: 'warning'
        })
      }
      /* 文件大小限制 */
      if (!fileSize) {
        this.$notify({
          title: '警告',
          message: '上传文件的大小不能超过 10MB!',
          type: 'warning'
        })
      }
      return condition && fileSize
    },
    // 文件上传成功
    uploadUrlSuccess(res, file) {
      this.$message({ message: '上传成功', type: 'success' })
    },
    // 文件上传超出限制
    uploadUrlExceed() {
      this.$message({ message: '文件上传超出限制,最多可以上传1个文件', type: 'info' })
    },
    // 文件上传方法
    uploadUrlRequest(file) {
      if (this.gridData.link != null && this.gridData.link != '') {
        if (this.gridData.link.match(/(http|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/)) {
          let data = new FormData()
          data.append('file', file.file)
          data.append('originalUrl', this.gridData.link)

          api.uploadFile(data).then(response => {
            if (response.code == 200) {
              this.$message({ message: '上传成功', type: 'success' })
              this.gridData.fileUrl = response.data
            } else {
              this.$message({ message: '上传失败,请稍候重试', type: 'error' })
              this.gridData.fileUrl = ''
            }
          })
        } else {
          this.$message({ message: '请填写正确的原文链接', type: 'warning' })
          this.fileUrlList = []
        }
      } else {
        this.$message({ message: '请填写原文链接', type: 'warning' })
        this.fileUrlList = []
      }
    },
    // 删除附件
    uploadUrlRemove() {
      api.removeFile({ filePath: this.gridData.fileUrl }).then(response => {
        if (response.code == 200) {
          this.$message({ message: '删除成功', type: 'success' })
          this.fileUrlList = []
          this.gridData.fileUrl = ''
        } else {
          this.$message({ message: '删除失败,请稍候重试', type: 'error' })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mainKe {
  margin-left: 10%;
  min-height: 76vh;
  display: flex;
  justify-content: center;
  gap: 30px;
  align-items: center;

  .parent {
    width: 600px;

    box-shadow: 0px -1px 12px 5px #e8e8e8;

    .headerStyle {
      width: 100%;
      height: 50px;
      line-height: 50px;
      padding-left: 20px;
      background-color: #0094f7;
      color: #ffff;
    }

    .mainStyle {
      width: 100%;
      height: 125px;
      text-align: center;

      p {
        width: 90%;
        text-align: left;
        margin: 30px auto 0 auto;
      }
    }
  }
}

::v-deep.el-form--label-top .el-form-item__label {
  padding: 0;
}

::v-deep.el-form-item {
  margin-bottom: 10px;
}

::v-deep.el-dialog__body {
  padding: 0 20px;
}
</style>