{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\MainArticle.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\MainArticle.vue", "mtime": 1754299178105}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "_request", "_auth", "_elementUi", "_axios", "_classify", "_fileSaver", "_ruoyi", "_list", "_DeepseekReportDialog", "_ai", "_marked", "_config", "_articleHistory", "props", "downLoadShow", "required", "type", "Boolean", "default", "editShow", "copyShow", "<PERSON>u<PERSON><PERSON>", "height", "Number", "currentPage", "pageSize", "total", "ArticleList", "flag", "SeachData", "keywords", "String", "sourceType", "components", "DeepseekReportDialog", "data", "loading", "regExpImg", "reportId", "reportOptions", "dialogVisible", "checkedCities", "checked", "html", "text", "that", "tagShow", "isIndeterminate", "count", "separate", "tagDialog", "formLabelAlign", "tag", "industry", "domain", "options", "options1", "tagItem", "areaList", "num", "timer", "drawer", "drawerInfo", "AreaId", "translationBtnShow", "open", "sourceTypeList", "sourceLists", "sourceTypeLists", "form", "rules", "title", "message", "content", "publishTime", "cnTitle", "originalUrl", "summary", "sn", "vertifyUpload", "isUploading", "headers", "Authorization", "getToken", "ContentType", "url", "process", "env", "VUE_APP_BASE_API", "fileList", "fileUrlList", "fileUrlurl", "showSummary", "batchImportVisible", "batchImportFiles", "showDeepseekDialog", "currentArticle", "aiDialogVisible", "chatMessages", "isThinking", "userAvatar", "streamingMessage", "markdownOptions", "gfm", "breaks", "headerIds", "mangle", "headerPrefix", "pedantic", "sanitize", "smartLists", "smartypants", "xhtml", "isRequesting", "isAborted", "currentReader", "aiPlatform", "articleAiPrompt", "computed", "watch", "newVal", "oldVal", "_this", "API", "getNewBuilt", "then", "code", "$message", "handler", "Refresh", "deep", "filter", "item", "mounted", "created", "_this2", "openDialog", "getListClassify", "res", "getSourceList", "$route", "query", "getConfigKey", "msg", "$store", "getters", "avatar", "updated", "filters", "methods", "getTechnologyLabel", "value", "mapping", "getSafeSummary", "cnSummary", "processedCnSummary", "replace", "trim", "processedSummary", "formatPublishTime", "webstePublishTime", "formattedPublishTime", "parseTime", "formattedWebsteTime", "includes", "dateMatch", "match", "year", "month", "day", "concat", "padStart", "has<PERSON><PERSON>ual<PERSON><PERSON>nt", "contentWithoutTags", "test", "changeColor", "str", "result", "split", "keyword", "length", "for<PERSON>ach", "keyitem", "trimmedKeyword", "escapedKeyword", "replaceString", "htmlTagRegex", "parts", "lastIndex", "exec", "index", "push", "substring", "part", "regex", "RegExp", "map", "join", "downLoadExcel", "_this3", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "abrupt", "response", "a", "document", "createElement", "href", "window", "URL", "createObjectURL", "download", "Date", "getTime", "click", "downLoadExportExcel", "stop", "batchDelete", "_this4", "$confirm", "batchRemove", "$emit", "catch", "publishHot", "_this5", "publishEverydayHot", "mainScorll", "_this6", "scrollStep", "$refs", "scroll", "scrollTop", "scrollInterval", "setInterval", "scrollBy", "clearInterval", "scrollChange", "_this7", "clearTimeout", "setTimeout", "stopScroll", "downLoad", "handleSizeChange", "handleCurrentChange", "current", "collect", "_this8", "_callee2", "_res", "_callee2$", "_context2", "id", "favorites", "collectApi", "sent", "cocelCollect", "copyText", "_this9", "navigator", "clipboard", "writeText", "alert", "typeHandle", "handleCheckedCitiesChange", "handleCheckAllChange", "val", "reportSubmit", "_this10", "_callee3", "keyWordList", "_callee3$", "_context3", "listId", "listSn", "article", "AddReport", "separateAdd", "openNewView", "isLink", "docId", "<PERSON><PERSON><PERSON><PERSON>", "tags", "_this11", "_callee4", "_callee4$", "_context4", "remoteEvent", "fieldName", "remoteIndustry", "industryName", "SubmitTag", "_this12", "_callee5", "params", "_callee5$", "_context5", "articleId", "tagAdd", "closeTag", "resetFields", "hotIncrease", "_this13", "_callee6", "is<PERSON><PERSON>ther", "_callee6$", "_context6", "JSON", "parse", "openDrawer", "_this14", "_callee7", "_callee7$", "_context7", "AreaInfo", "cnC<PERSON>nt", "b", "c", "industryHandle", "_this15", "ids", "ele", "undefined", "domainHandle", "_this16", "resultEvent", "_this17", "warning", "zhuangtai", "snapshotUrl", "downLoadExportKe", "$msgbox", "_defineProperty2", "showCancelButton", "confirmButtonText", "cancelButtonText", "beforeClose", "action", "instance", "done", "err", "downLoadExportZhuan", "location", "origin", "documentDownload", "_this18", "_callee9", "urls", "_iterator", "_step", "_loop", "_callee9$", "_context10", "fileUrl", "_createForOfIteratorHelper2", "entries", "_step$value", "_loop$", "_context9", "_slicedToArray2", "indexOf", "_callee8", "_callee8$", "_context8", "downLoadFun", "error", "s", "n", "<PERSON><PERSON><PERSON>", "t1", "e", "f", "finish", "_this19", "_callee10", "formData", "isBlob", "blob", "list", "fileName", "resText", "rspObj", "errMsg", "_callee10$", "_context11", "FormData", "append", "downloadFile", "blobValidate", "Blob", "saveAs", "errorCode", "t0", "translateTitle", "row", "_this20", "$loading", "lock", "spinner", "background", "translationTitle", "originalText", "translationField", "translationType", "findIndex", "isTranslated", "close", "translateEvent", "_this21", "handleUpdate", "_this22", "reset", "handleDelete", "_this23", "$modal", "confirm", "monitoringEsRemove", "msgSuccess", "submitForm", "_this24", "validate", "valid", "queryForm", "stringify", "articleListEdit", "requestLoad", "file", "_this25", "_callee11", "_callee11$", "_context12", "uploadCover", "uid", "path", "imgUrl", "exceed", "handleRemove", "handleChange", "upload", "submit", "beforeUploadUrl", "name", "lastIndexOf", "toLowerCase", "condition", "fileSize", "size", "$notify", "uploadUrlSuccess", "uploadUrlExceed", "uploadUrlRequest", "_this26", "uploadFile", "uploadUrlRemove", "_this27", "removeFile", "filePath", "cancel", "articleSn", "sourceName", "sourceSn", "shortUrl", "author", "description", "cover", "publishType", "publishCode", "publishArea", "numberLikes", "numberReads", "numberCollects", "numberShares", "numberComments", "emotion", "status", "remark", "createBy", "createTime", "updateBy", "updateTime", "userId", "deptId", "tmpUrl", "is<PERSON><PERSON><PERSON>", "groupId", "appId", "resetForm", "openBatchImportDialog", "handleFileSelect", "newFiles", "raw", "splice", "cancelBatchImport", "batchUpload", "clearFiles", "confirmBatchImport", "_this28", "_callee12", "emptySourceNames", "sourceNames", "_callee12$", "_context13", "batchImportReports", "console", "reportAiChat", "selectedArticleId", "selectedArticle", "find", "difyAiChat", "_this29", "_callee13", "_articlesResponse$dat", "selectedArticles", "titles", "articlesResponse", "articlesContent", "aiMessage", "prompt", "reader", "decoder", "buffer", "<PERSON><PERSON><PERSON><PERSON>", "isInThinkTag", "decodeUnicode", "updateContent", "_yield$reader$read", "lastData", "decodedAnswer", "chunk", "newlineIndex", "line", "jsonData", "answer", "_callee13$", "_context14", "log", "Promise", "resolve", "getListByIds", "Error", "_selectedArticles$ind", "_selectedArticles$ind2", "role", "difyAiQa", "ok", "body", "<PERSON><PERSON><PERSON><PERSON>", "TextDecoder", "fromCharCode", "parseInt", "newContent", "renderedContent", "marked", "$nextTick", "scrollHeight", "read", "warn", "decode", "slice", "startsWith", "t2", "ollamaAiChat", "_this30", "_callee15", "_articlesResponse$dat2", "_aiMessage", "lastUpdateTime", "isThinkContent", "temp<PERSON><PERSON><PERSON>", "processStream", "_callee15$", "_context16", "_selectedArticles$ind3", "_selectedArticles$ind4", "ollamaAiQa", "now", "currentTime", "_ref2", "_callee14", "_yield$reader$read2", "lines", "_iterator2", "_step2", "_response", "thinkStartIndex", "thinkEndIndex", "_callee14$", "_context15", "apply", "arguments", "deepseekAiChat", "_this31", "_callee16", "_aiMessage2", "_lastUpdateTime", "_yield$reader$read3", "_iterator3", "_step3", "_jsonData$choices", "_callee16$", "_context17", "_selectedArticles$ind5", "_selectedArticles$ind6", "deepseekAiQa", "choices", "delta", "t3", "t4", "closeAiDialog", "articleAiChat"], "sources": ["src/views/components/MainArticle.vue"], "sourcesContent": ["<template>\r\n  <div class=\"main\" v-loading=\"loading\">\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Monitor'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(100vh - 405px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    >\r\n                    </span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div\r\n                  class=\"ArticlBottom\"\r\n                  v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <div>\r\n                    原文标题:\r\n                    <span class=\"infomation\">\r\n                      {{ item.title }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"changeColor(getSafeSummary(item))\"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div\r\n      class=\"MainArticle\"\r\n      v-if=\"flag == 'MonitorUse' || flag == 'specialSubjectUse'\"\r\n    >\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载文章\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除文章\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i title=\"批量生成快照\" class=\"icon-pingmukuaizhao\" style=\"color:green\" @click=\"resultEvent('BatchGeneration')\"></i>\r\n          </p> -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-document-add\"\r\n              style=\"font-size: 24px\"\r\n              title=\"发布到每日最新热点\"\r\n              @click=\"publishHot\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\" v-if=\"$route.query.domain\">\r\n            <i\r\n              class=\"el-icon-chat-dot-round\"\r\n              style=\"font-size: 24px\"\r\n              title=\"Deepseek深度解读\"\r\n              @click=\"articleAiChat\"\r\n            ></i>\r\n            <span class=\"deepseek-text\" @click=\"articleAiChat\"\r\n              >Deepseek深度解读</span\r\n            >\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          :style=\"\r\n            $route.query.domain\r\n              ? 'height: calc(100vh - 170px)'\r\n              : 'height: calc(100vh - 389px)'\r\n          \"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\">{{\r\n                      (currentPage - 1) * pageSize + key + 1\r\n                    }}</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span>\r\n                </p>\r\n                <!-- v-if=\"item.fileUrl\" -->\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题：</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{\r\n                        $route.query.domain\r\n                          ? \"是\"\r\n                          : getTechnologyLabel(item.isTechnology)\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"changeColor(getSafeSummary(item))\"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\">\r\n              <!-- <p>\r\n                  <el-button type=\"primary\" plain size=\"mini\" @click=\"resultEvent(item)\">\r\n                    {{ item.snapshotUrl\r\n                      ? '下载快照' : '生成快照' }}\r\n                  </el-button>\r\n                </p> -->\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  plain\r\n                  @click=\"handleUpdate(item)\"\r\n                  v-hasPermi=\"['article:articleList:edit']\"\r\n                  >{{ ` 修改 ` }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"danger\"\r\n                  plain\r\n                  @click=\"handleDelete(item)\"\r\n                  v-hasPermi=\"['article:list:remove']\"\r\n                  >{{ `删除` }}</el-button\r\n                >\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Special'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(100vh - 405px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    ></span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div\r\n                  class=\"ArticlBottom\"\r\n                  v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <div>\r\n                    原文标题:\r\n                    <span class=\"infomation\">\r\n                      {{ item.title }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"changeColor(getSafeSummary(item))\"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'infoInter'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div class=\"scollBox\" @mousewheel=\"scrollChange\" ref=\"scroll\">\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    ></span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"changeColor(getSafeSummary(item))\"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n            </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Wechat'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p> -->\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(50vh - 200px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_kqcb\">\r\n              <div>\r\n                <div class=\"ArticlTop\" style=\"width: 55%\">\r\n                  <p style=\"line-height: 20px\">\r\n                    <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                      {{ null }} </el-checkbox\r\n                    >&nbsp;&nbsp;\r\n                    <a href=\"#\"\r\n                      ><span style=\"padding: 0 10px 0 0\"\r\n                        >{{\r\n                          (currentPage - 1) * pageSize + key + 1\r\n                        }}&nbsp;</span\r\n                      ><span\r\n                        class=\"title_Article\"\r\n                        @click=\"openNewView(item)\"\r\n                        v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                      ></span\r\n                      >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                    >\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3; cursor: pointer\"\r\n                    >\r\n                      原文链接\r\n                    </span>\r\n                  </p>\r\n                </div>\r\n                <div class=\"info_flex\" style=\"width: auto\">\r\n                  <div class=\"ArticlBottom\">\r\n                    <div>\r\n                      类型:\r\n                      <span class=\"infomation\">\r\n                        {{ typeHandle(item.sourceType) }}\r\n                      </span>\r\n                    </div>\r\n                    <div>\r\n                      媒体来源:\r\n                      <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                    </div>\r\n                    <div v-if=\"item.author\">\r\n                      作者:\r\n                      <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                    </div>\r\n                    <div>\r\n                      发布时间:\r\n                      <span class=\"infomation\">{{\r\n                        formatPublishTime(\r\n                          item.publishTime,\r\n                          item.webstePublishTime\r\n                        )\r\n                      }}</span>\r\n                    </div>\r\n                    <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                    <!-- <p>标签A</p> -->\r\n                  </div>\r\n                  <div class=\"ArticlBottom\">\r\n                    <div v-if=\"item.industryName\">\r\n                      所属行业:\r\n                      <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                    </div>\r\n                    <div v-if=\"item.domain\">\r\n                      所属领域:\r\n                      <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"changeColor(getSafeSummary(item))\"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p> -->\r\n            <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            <!-- </div> -->\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div\r\n      class=\"MainArticle\"\r\n      v-if=\"flag == 'artificialIntelligence' || flag == 'networkSecurity'\"\r\n    >\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载文章\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除文章\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div class=\"scollBox\" @mousewheel=\"scrollChange\" ref=\"scroll\">\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div\r\n              class=\"Articl_left\"\r\n              :style=\"item.fileUrl ? 'width: 85%' : 'width: 100%'\"\r\n            >\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span>\r\n                </p>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题:</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"changeColor(getSafeSummary(item))\"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\" v-if=\"item.fileUrl\">\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <!-- <p style=\"margin-top: -5px;\">\r\n                  <el-button style=\"width: 80px;\" size=\"mini\" type=\"primary\" plain @click=\"handleUpdate(item)\"\r\n                    v-hasPermi=\"['article:articleList:edit']\">{{ ` 修改 ` }}</el-button>\r\n                </p>\r\n                <p style=\"margin-top: -5px;\">\r\n                  <el-button style=\"width: 80px;\" size=\"mini\" type=\"danger\" plain @click=\"handleDelete(item)\"\r\n                    v-hasPermi=\"['article:list:remove']\">{{ `删除` }}</el-button>\r\n                </p> -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'zhikubaogao'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-upload2\"\r\n              style=\"font-size: 22px; color: #1296db\"\r\n              title=\"批量导入报告\"\r\n              @click=\"openBatchImportDialog\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载报告\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除报告\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-chat-dot-round\"\r\n              style=\"font-size: 24px\"\r\n              title=\"Deepseek报告解读\"\r\n              @click=\"reportAiChat\"\r\n            ></i>\r\n            <span class=\"deepseek-text\" @click=\"reportAiChat\"\r\n              >Deepseek报告解读</span\r\n            >\r\n          </p> -->\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          style=\"height: calc(100vh - 310px)\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\">{{\r\n                      (currentPage - 1) * pageSize + key + 1\r\n                    }}</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <!-- <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span> -->\r\n                </p>\r\n                <!-- v-if=\"item.fileUrl\" -->\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题：</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div> -->\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"changeColor(getSafeSummary(item))\"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\">\r\n              <!-- <p>\r\n                  <el-button type=\"primary\" plain size=\"mini\" @click=\"resultEvent(item)\">\r\n                    {{ item.snapshotUrl\r\n                      ? '下载快照' : '生成快照' }}\r\n                  </el-button>\r\n                </p> -->\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"danger\"\r\n                  plain\r\n                  @click=\"handleDelete(item)\"\r\n                  >删除</el-button\r\n                >\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"pagination\" ref=\"pagination\">\r\n      <el-pagination\r\n        :small=\"false\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"currentPage\"\r\n        :page-sizes=\"[10, 20, 30, 40, 50]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\"->, total, sizes, prev, pager, next, jumper\"\r\n        :total=\"total\"\r\n      ></el-pagination>\r\n    </div>\r\n    <el-dialog\r\n      title=\"添加到报告\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-row style=\"line-height: 50px\">\r\n        <el-col :span=\"18\">\r\n          <el-select\r\n            v-model=\"reportId\"\r\n            placeholder=\"请选择报告\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, key) in reportOptions\"\r\n              :key=\"key\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-col>\r\n      </el-row>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"reportSubmit\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <el-dialog\r\n      title\r\n      :visible.sync=\"tagDialog\"\r\n      width=\"20%\"\r\n      :before-close=\"closeTag\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-form\r\n        label-position=\"left\"\r\n        label-width=\"40px\"\r\n        :model=\"formLabelAlign\"\r\n        ref=\"ruleForm\"\r\n      >\r\n        <el-form-item label=\"行业\" prop=\"industry\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.industry\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n            multiple\r\n            placeholder=\"请选择行业\"\r\n            :filterable=\"true\"\r\n            default-first-option\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.industryName\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"领域\" prop=\"domain\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.domain\"\r\n            filterable\r\n            style=\"width: 100%\"\r\n            multiple\r\n            clearable\r\n            placeholder=\"请选择领域\"\r\n            default-first-option\r\n          >\r\n            <el-option\r\n              v-for=\"item in options\"\r\n              :key=\"item.id\"\r\n              :label=\"item.fieldName\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"标签\" prop=\"tag\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.tag\"\r\n            style=\"width: 100%\"\r\n            multiple\r\n            filterable\r\n            allow-create\r\n            default-first-option\r\n            placeholder=\"请添加文章标签\"\r\n          >\r\n            <!-- <el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n            </el-option>-->\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"closeTag\" size=\"mini\">取 消</el-button>\r\n        <el-button @click=\"SubmitTag\" type=\"primary\" size=\"mini\"\r\n          >确 定</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n    <el-drawer\r\n      @open=\"openDrawer\"\r\n      :title=\"drawerInfo.cnTitle || drawerInfo.title\"\r\n      :visible.sync=\"drawer\"\r\n      custom-class=\"drawer_box\"\r\n      direction=\"rtl\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <div\r\n        v-if=\"flag == 'MonitorUse' || flag == 'specialSubjectUse'\"\r\n        style=\"width: 100%; text-align: right; padding-right: 10px\"\r\n      >\r\n        <el-button\r\n          @click=\"documentDownload('drawer')\"\r\n          :type=\"drawerInfo.fileUrl ? 'primary' : 'info'\"\r\n          size=\"mini\"\r\n          plain\r\n          :disabled=\"!drawerInfo.fileUrl\"\r\n          >{{ \"附件下载\" }}</el-button\r\n        >\r\n        <!-- <el-button @click=\"resultEvent('drawer')\" type=\"primary\" size=\"mini\" plain>\r\n          {{ drawerInfo.snapshotUrl\r\n            ? '下载快照' : '生成快照' }}\r\n        </el-button> -->\r\n        <!-- v-if=\"translationBtnShow\" -->\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"mini\"\r\n          plain\r\n          @click=\"translateEvent(drawerInfo)\"\r\n          >翻译内容</el-button\r\n        >\r\n      </div>\r\n      <template slot=\"title\">\r\n        <span class=\"drawer_Title\">{{\r\n          drawerInfo.cnTitle || drawerInfo.title\r\n        }}</span>\r\n      </template>\r\n      <div class=\"drawer_Style\">\r\n        <p class=\"title\">\r\n          {{ drawerInfo.cnTitle || drawerInfo.title }}\r\n          <!-- v-if=\"!drawerInfo.cnTitle\" -->\r\n          <i\r\n            class=\"icon-taizhangtranslate\"\r\n            @click=\"translateTitle(drawerInfo)\"\r\n          ></i>\r\n        </p>\r\n        <p style=\"text-align: center\">\r\n          <span class=\"source\">{{ drawerInfo.sourceName }}</span>\r\n          <span class=\"time\">{{ drawerInfo.publishTime }}</span>\r\n        </p>\r\n        <div\r\n          style=\"user-select: text !important; line-height: 30px\"\r\n          v-html=\"\r\n            drawerInfo.cnContent &&\r\n            drawerInfo.cnContent.replace(/<img\\b[^>]*>/gi, '')\r\n          \"\r\n        ></div>\r\n        <el-empty\r\n          description=\"当前文章暂无数据\"\r\n          v-if=\"!drawerInfo.cnContent\"\r\n        ></el-empty>\r\n      </div>\r\n    </el-drawer>\r\n    <el-dialog\r\n      :title=\"'修改文章'\"\r\n      :visible.sync=\"open\"\r\n      width=\"1000px\"\r\n      append-to-body\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"dialog_Box\">\r\n        <el-form\r\n          :model=\"form\"\r\n          class=\"form_Style\"\r\n          label-position=\"top\"\r\n          ref=\"form\"\r\n          :rules=\"rules\"\r\n          size=\"mini\"\r\n        >\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"文章标题\" prop=\"title\">\r\n                <el-input v-model=\"form.title\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"中文标题\" prop=\"cnTitle\">\r\n                <el-input v-model=\"form.cnTitle\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"平台类型\" prop=\"sourceType\">\r\n                <el-select\r\n                  v-model=\"form.sourceType\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in sourceTypeList\"\r\n                    :key=\"index\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.id\"\r\n                  ></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"媒体来源\" prop=\"sourceName\">\r\n                <el-select\r\n                  v-model=\"form.sourceName\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  filterable\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in sourceTypeLists\"\r\n                    :key=\"index\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.name\"\r\n                  ></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发布时间\" prop=\"publishTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.publishTime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  type=\"datetime\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  placeholder=\"选择日期\"\r\n                ></el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"文章作者\" prop=\"author\">\r\n                <el-input\r\n                  v-model=\"form.author\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                ></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-form-item label=\"原文链接\" prop=\"originalUrl\">\r\n            <el-input v-model=\"form.originalUrl\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"摘要\" prop=\"summary\">\r\n            <el-input\r\n              v-model=\"form.summary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文摘要\" prop=\"cnSummary\">\r\n            <el-input\r\n              v-model=\"form.cnSummary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文内容\" prop=\"cnContent\">\r\n            <editor v-model=\"form.cnContent\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n          <el-form-item label=\"文章内容\" prop=\"content\">\r\n            <editor v-model=\"form.content\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n          <!-- <el-form-item label=\"封面图片\" prop=\"cover\">\r\n            <el-upload action=\"#\" ref=\"upload\" :limit=\"3\" :on-exceed=\"exceed\" list-type=\"picture-card\"\r\n              :auto-upload=\"false\" :headers=\"vertifyUpload.headers\" :file-list=\"fileList\" :on-change=\"handleChange\"\r\n              :http-request=\"requestLoad\">\r\n              <i slot=\"default\" class=\"el-icon-plus\"></i>\r\n              <div slot=\"file\" slot-scope=\"{ file }\">\r\n                <img class=\"el-upload-list__item-thumbnail\" :src=\"file.url\" alt=\"文件缩略图加载失败\" />\r\n                <span class=\"el-upload-list__item-actions\">\r\n                  <span v-if=\"!disabled\" class=\"el-upload-list__item-delete\" @click=\"handleRemove(file)\">\r\n                    <i class=\"el-icon-delete\"></i>\r\n                  </span>\r\n                </span>\r\n              </div>\r\n            </el-upload>\r\n          </el-form-item>\r\n          <el-form-item label=\"上传附件\" prop=\"fileUrl\">\r\n            <el-upload class=\"upload-demo\" :action=\"fileUrlurl\" :before-upload=\"beforeUploadUrl\" multiple :limit=\"1\"\r\n              :http-request=\"uploadUrlRequest\" :on-success=\"uploadUrlSuccess\" :file-list=\"fileUrlList\"\r\n              :on-exceed=\"uploadUrlExceed\" :on-remove=\"uploadUrlRemove\">\r\n              <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n              <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过500kb</div>\r\n            </el-upload>\r\n          </el-form-item> -->\r\n        </el-form>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量导入弹框 -->\r\n    <el-dialog\r\n      title=\"批量导入报告\"\r\n      :visible.sync=\"batchImportVisible\"\r\n      width=\"800px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"batch-import-container\">\r\n        <!-- 文件选择区域 -->\r\n        <div class=\"file-select-area\">\r\n          <el-upload\r\n            ref=\"batchUpload\"\r\n            action=\"#\"\r\n            :auto-upload=\"false\"\r\n            :show-file-list=\"false\"\r\n            multiple\r\n            :on-change=\"handleFileSelect\"\r\n            accept=\".pdf,.doc,.docx,.txt\"\r\n          >\r\n            <el-button type=\"primary\" icon=\"el-icon-upload\">选择文件</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">\r\n              支持选择多个文件，格式：PDF、DOC、DOCX、TXT\r\n            </div>\r\n          </el-upload>\r\n        </div>\r\n\r\n        <!-- 文件列表表格 -->\r\n        <div class=\"file-table-area\" v-if=\"batchImportFiles.length > 0\">\r\n          <el-table\r\n            :data=\"batchImportFiles\"\r\n            border\r\n            style=\"width: 100%; margin-top: 20px\"\r\n            max-height=\"300\"\r\n          >\r\n            <el-table-column prop=\"fileName\" label=\"文件名称\" width=\"300\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.fileName }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"sourceName\" label=\"数据源名称\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input\r\n                  v-model=\"scope.row.sourceName\"\r\n                  placeholder=\"请输入数据源名称\"\r\n                  size=\"small\"\r\n                ></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"removeFile(scope.$index)\"\r\n                  style=\"color: #f56c6c\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelBatchImport\">取 消</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"confirmBatchImport\"\r\n          :disabled=\"batchImportFiles.length === 0\"\r\n        >\r\n          确 认\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek深度解读\"\r\n      :visible.sync=\"aiDialogVisible\"\r\n      width=\"1000px\"\r\n      :before-close=\"closeAiDialog\"\r\n      custom-class=\"ai-dialog\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"ai-chat-container\">\r\n        <div class=\"chat-messages\" ref=\"chatMessages\">\r\n          <div\r\n            class=\"message\"\r\n            v-for=\"(message, index) in chatMessages\"\r\n            :key=\"index\"\r\n            :class=\"[\r\n              'message',\r\n              message.role === 'user' ? 'user-message' : 'ai-message',\r\n            ]\"\r\n          >\r\n            <div class=\"avatar\">\r\n              <img\r\n                style=\"width: 30px; height: 30px\"\r\n                v-if=\"message.role === 'user'\"\r\n                :src=\"userAvatar || require('@/assets/images/home/<USER>')\"\r\n                alt=\"用户头像\"\r\n              />\r\n              <img v-else src=\"@/assets/images/logo2.png\" alt=\"AI头像\" />\r\n            </div>\r\n            <div class=\"message-wrapper\">\r\n              <div\r\n                v-if=\"\r\n                  message.role === 'assistant' && isThinking && !message.content\r\n                \"\r\n                class=\"thinking-animation\"\r\n              >\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"message-content\"\r\n                v-html=\"\r\n                  message.role === 'assistant'\r\n                    ? message.content\r\n                    : message.content\r\n                \"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <template slot=\"footer\">\r\n        <el-button @click=\"closeAiDialog\">取 消</el-button>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- Deepseek报告解读弹窗 -->\r\n    <deepseek-report-dialog\r\n      :visible.sync=\"showDeepseekDialog\"\r\n      :article-data=\"currentArticle\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport request from \"@/utils/request\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { MessageBox } from \"element-ui\";\r\nimport axios from \"axios\";\r\nimport { getListClassify } from \"@/api/article/classify\";\r\nimport { saveAs } from \"file-saver\";\r\nimport { blobValidate, tansParams } from \"@/utils/ruoyi\";\r\nimport { articleListEdit, uploadCover } from \"@/api/articleCrawler/list\";\r\nimport DeepseekReportDialog from \"./DeepseekReportDialog.vue\";\r\nimport { deepseekAiQa, difyAiQa, ollamaAiQa } from \"@/api/infoEscalation/ai\";\r\nimport { marked } from \"marked\";\r\nimport { getConfigKey } from \"@/api/system/config\";\r\nimport { getListByIds } from \"@/api/article/articleHistory\";\r\n\r\nexport default {\r\n  props: {\r\n    downLoadShow: {\r\n      /* 下载按钮 */ required: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    editShow: {\r\n      /* 编辑按钮 */ required: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    copyShow: {\r\n      /* 复制按钮 */ reuqired: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    height: {\r\n      type: Number,\r\n      default: 655,\r\n    },\r\n    currentPage: {\r\n      reuqired: true,\r\n      default: 1,\r\n    },\r\n    pageSize: {\r\n      reuqired: true,\r\n      default: 50,\r\n    },\r\n    total: {\r\n      reuqired: true,\r\n      default: 0,\r\n    },\r\n    ArticleList: {\r\n      required: true,\r\n      default: [],\r\n    },\r\n    flag: {\r\n      required: true,\r\n    },\r\n    SeachData: {\r\n      required: true,\r\n    },\r\n    keywords: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    // 报告类型字段\r\n    sourceType: {\r\n      default: \"\",\r\n    },\r\n  },\r\n  components: {\r\n    DeepseekReportDialog,\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      regExpImg: /^\\n$/,\r\n      reportId: \"\",\r\n      reportOptions: [],\r\n      dialogVisible: false,\r\n      checkedCities: [] /* 多选 */,\r\n      checked: false /* 全选 */,\r\n      html: \"\",\r\n      text: \"\",\r\n      that: this,\r\n      tagShow: false,\r\n      isIndeterminate: true,\r\n      count: 0,\r\n      separate: {},\r\n      /* 标签功能 */\r\n      tagDialog: false,\r\n      formLabelAlign: {\r\n        tag: \"\",\r\n        industry: \"\",\r\n        domain: \"\",\r\n      },\r\n      options: [],\r\n      options1: [],\r\n      tagItem: {} /* 标签对象 */,\r\n      areaList: [] /* 领域 */,\r\n      industry: [] /* 行业 */,\r\n      num: 0,\r\n      timer: null,\r\n      drawer: false,\r\n      drawerInfo: {},\r\n      AreaId: null,\r\n      translationBtnShow: null,\r\n      open: false,\r\n      sourceTypeList: [], // 数据源分类\r\n      sourceLists: [], // 数据源列表\r\n      sourceTypeLists: [],\r\n      form: {}, // 表单参数\r\n      rules: {\r\n        // 表单校验\r\n        title: [{ required: true, message: \"文章标题为必填项\" }],\r\n        content: [{ required: true, message: \"文章详情为必填项\" }],\r\n        publishTime: [{ required: true, message: \"发布时间为必填项\" }],\r\n        cnTitle: [{ required: true, message: \"中文名称为必填项\" }],\r\n        sourceType: [{ required: true, message: \"平台类型为必填项\" }],\r\n        originalUrl: [{ required: true, message: \"原文为必填项\" }],\r\n        summary: [{ required: true, message: \"请填写摘要\" }],\r\n        // cnSummary: [{ required: true, message: '请填写中文摘要' }],\r\n        sn: [{ required: true, message: \"请填写文章地址唯一识别号\" }],\r\n      },\r\n      vertifyUpload: {\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: {\r\n          Authorization: \"Bearer \" + getToken(),\r\n          ContentType: \"application/json;charset=utf-8\",\r\n        },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/article/articleList/cover\",\r\n      },\r\n      fileList: [],\r\n      fileUrlList: [],\r\n      fileUrlurl:\r\n        process.env.VUE_APP_BASE_API + \"/article/articleList/upload/file\",\r\n      showSummary: true,\r\n      // 批量导入相关数据\r\n      batchImportVisible: false,\r\n      batchImportFiles: [],\r\n      // Deepseek报告解读弹窗\r\n      showDeepseekDialog: false,\r\n      currentArticle: {},\r\n      // ai相关\r\n      aiDialogVisible: false,\r\n      chatMessages: [],\r\n      isThinking: false,\r\n      userAvatar: \"\", // 用户头像\r\n      streamingMessage: \"\", // 添加用于存储正在流式输出的消息\r\n      markdownOptions: {\r\n        gfm: true,\r\n        breaks: true,\r\n        headerIds: true,\r\n        mangle: false,\r\n        headerPrefix: \"\",\r\n        pedantic: false,\r\n        sanitize: false,\r\n        smartLists: true,\r\n        smartypants: true,\r\n        xhtml: true,\r\n      },\r\n      isRequesting: false, // 标记是否正在请求中\r\n      isAborted: false, // 标记是否已中断\r\n      currentReader: null, // 当前的 reader\r\n      aiPlatform: \"\",\r\n      articleAiPrompt: \"\",\r\n    };\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    dialogVisible: function (newVal, oldVal) {\r\n      if (newVal) {\r\n        API.getNewBuilt({ sourceType: this.sourceType }).then((data) => {\r\n          if (data.code == 200) {\r\n            this.reportOptions = data.data;\r\n          } else {\r\n            this.$message({ message: \"报告列表获取失败了\", type: \"error\" });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 'formLabelAlign.industry': {\r\n    //   handler(newVal) {\r\n    //     if (newVal == '') {\r\n    //       this.options1 = this.industry\r\n    //     }\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    // 'formLabelAlign.domain': {\r\n    //   handler(newVal) {\r\n    //     if (newVal == '') {\r\n    //       this.options = this.areaList\r\n    //     }\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    \"SeachData.sortMode\": {\r\n      handler(newVal, oldVal) {\r\n        this.Refresh();\r\n      },\r\n      deep: true,\r\n    },\r\n    \"form.sourceType\": {\r\n      handler(newVal, oldVal) {\r\n        this.sourceTypeLists = this.sourceLists.filter((item) => {\r\n          return item.type == newVal;\r\n        });\r\n      },\r\n      deep: true,\r\n    },\r\n  },\r\n  mounted() {},\r\n  created() {\r\n    if (\r\n      this.flag !== \"MonitorUse\" &&\r\n      this.flag !== \"specialSubjectUse\" &&\r\n      this.flag !== \"Wechat\"\r\n    ) {\r\n      this.openDialog();\r\n    }\r\n    if (this.flag !== \"Wechat\") {\r\n      getListClassify().then((res) => {\r\n        this.sourceTypeList = res.data;\r\n      });\r\n      API.getSourceList().then((data) => {\r\n        if (data.code == 200) {\r\n          this.sourceLists = data.data;\r\n        }\r\n      });\r\n    }\r\n    if (this.$route.query.domain) {\r\n      getConfigKey(\"sys.ai.platform\").then((res) => {\r\n        if (res.code == 200) {\r\n          this.aiPlatform = res.msg;\r\n        }\r\n      });\r\n      getConfigKey(\"wechat.ai.articlePrompt\").then((res) => {\r\n        if (res.code == 200) {\r\n          this.articleAiPrompt = res.msg;\r\n        }\r\n      });\r\n      // 获取用户头像\r\n      this.userAvatar = this.$store.getters.avatar;\r\n    }\r\n\r\n    this.showSummary = true;\r\n  },\r\n  updated() {},\r\n  filters: {},\r\n  methods: {\r\n    // 处理科技相关字段的显示映射\r\n    getTechnologyLabel(value) {\r\n      const mapping = {\r\n        0: \"排除\",\r\n        1: \"选中\",\r\n        2: \"待定\",\r\n        3: \"排队中\",\r\n      };\r\n      return mapping[value];\r\n    },\r\n    // 安全处理摘要内容，避免null调用replace报错\r\n    getSafeSummary(item) {\r\n      const cnSummary = item.cnSummary || \"\";\r\n      const summary = item.summary || \"\";\r\n\r\n      const processedCnSummary = cnSummary\r\n        ? cnSummary\r\n            .replace(/p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6|ul|li|a/g, \"span\")\r\n            .replace(/<img[^>]*>/g, \"\") // 移除img标签\r\n            .replace(/\\\\n|\\\\\\\\n|\\\\\\\\\\\\n|\\n/g, \" \") // 移除各种换行符\r\n            .replace(/\\s+/g, \" \") // 合并多个空格为一个\r\n            .trim()\r\n        : \"\";\r\n\r\n      const processedSummary = summary\r\n        ? summary\r\n            .replace(/p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6|ul|li|a/g, \"span\")\r\n            .replace(/<img[^>]*>/g, \"\") // 移除img标签\r\n            .replace(/\\\\n|\\\\\\\\n|\\\\\\\\\\\\n|\\n/g, \" \") // 移除各种换行符\r\n            .replace(/\\s+/g, \" \") // 合并多个空格为一个\r\n            .trim()\r\n        : \"\";\r\n\r\n      return processedCnSummary || processedSummary;\r\n    },\r\n    // 处理发布时间的显示\r\n    formatPublishTime(publishTime, webstePublishTime) {\r\n      // 格式化publishTime为年月日\r\n      const formattedPublishTime = this.parseTime(publishTime, \"{y}-{m}-{d}\");\r\n\r\n      // 如果webstePublishTime不存在，直接返回publishTime\r\n      if (!webstePublishTime) {\r\n        return \"[北京]\" + formattedPublishTime;\r\n      }\r\n\r\n      let formattedWebsteTime = \"\";\r\n      // 处理不同格式的webstePublishTime\r\n      if (webstePublishTime) {\r\n        // 处理2025-04-12 10:09:21.971191格式（包含连字符的标准格式）\r\n        if (webstePublishTime.includes(\"-\")) {\r\n          const dateMatch = webstePublishTime.match(/(\\d{4})-(\\d{2})-(\\d{2})/);\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2];\r\n            const day = dateMatch[3];\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025年04月14日 11:29:22格式（中文年月日格式，带\"日\"字）\r\n        else if (\r\n          webstePublishTime.includes(\"年\") &&\r\n          webstePublishTime.includes(\"月\") &&\r\n          webstePublishTime.includes(\"日\")\r\n        ) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})年(\\d{1,2})月(\\d{1,2})日/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025年4月15格式（中文年月格式，不带\"日\"字）\r\n        else if (\r\n          webstePublishTime.includes(\"年\") &&\r\n          webstePublishTime.includes(\"月\")\r\n        ) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})年(\\d{1,2})月(\\d{1,2})/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025/04/14 11:29:22格式（斜杠分隔的格式）\r\n        else if (webstePublishTime.includes(\"/\")) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})\\/(\\d{1,2})\\/(\\d{1,2})/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        } else {\r\n          // 其他格式直接使用原值\r\n          formattedWebsteTime = webstePublishTime;\r\n        }\r\n      }\r\n\r\n      // 比较年月日是否相同\r\n      if (formattedPublishTime === formattedWebsteTime) {\r\n        return \"[北京]\" + formattedPublishTime;\r\n      } else {\r\n        return `[北京]${formattedPublishTime} / [当地]${webstePublishTime}`;\r\n      }\r\n    },\r\n\r\n    // 检查文本是否有实际内容（去除HTML标签后）\r\n    hasActualContent(text) {\r\n      if (!text) {\r\n        return false;\r\n      }\r\n      // 去除HTML标签\r\n      const contentWithoutTags = text.replace(/<[^>]*>/g, \"\");\r\n      // 检查是否有中文、英文、数字等实际内容\r\n      return /[\\u4e00-\\u9fa5a-zA-Z0-9]/.test(contentWithoutTags);\r\n    },\r\n    // 关键字替换\r\n    changeColor(str) {\r\n      if (!str || !this.keywords) {\r\n        return str;\r\n      }\r\n\r\n      let result = str;\r\n      let keywords;\r\n\r\n      // 解析关键词\r\n      if (this.keywords.includes(\",\")) {\r\n        keywords = this.keywords.split(\",\");\r\n      } else if (this.keywords.includes(\"|\")) {\r\n        keywords = this.keywords.split(\"|\");\r\n      } else {\r\n        keywords = [this.keywords];\r\n      }\r\n\r\n      // 过滤空关键词\r\n      keywords = keywords.filter(\r\n        (keyword) => keyword && keyword.trim().length > 0\r\n      );\r\n\r\n      keywords.forEach((keyitem) => {\r\n        const trimmedKeyword = keyitem.trim();\r\n        if (trimmedKeyword.length > 0) {\r\n          // 转义特殊正则字符\r\n          const escapedKeyword = trimmedKeyword.replace(\r\n            /[.*+?^${}()|[\\]\\\\]/g,\r\n            \"\\\\$&\"\r\n          );\r\n\r\n          // 高亮替换字符串\r\n          const replaceString = `<span class=\"highlight\" style=\"color: red;\">${trimmedKeyword}</span>`;\r\n\r\n          // 使用更安全的方法：先分离HTML标签和文本内容\r\n          const htmlTagRegex = /<[^>]*>/g;\r\n          const parts = [];\r\n          let lastIndex = 0;\r\n          let match;\r\n\r\n          // 分离HTML标签和文本\r\n          while ((match = htmlTagRegex.exec(result)) !== null) {\r\n            // 添加标签前的文本\r\n            if (match.index > lastIndex) {\r\n              parts.push({\r\n                type: \"text\",\r\n                content: result.substring(lastIndex, match.index),\r\n              });\r\n            }\r\n            // 添加HTML标签\r\n            parts.push({\r\n              type: \"html\",\r\n              content: match[0],\r\n            });\r\n            lastIndex = match.index + match[0].length;\r\n          }\r\n\r\n          // 添加最后剩余的文本\r\n          if (lastIndex < result.length) {\r\n            parts.push({\r\n              type: \"text\",\r\n              content: result.substring(lastIndex),\r\n            });\r\n          }\r\n\r\n          // 如果没有HTML标签，直接处理整个字符串\r\n          if (parts.length === 0) {\r\n            parts.push({\r\n              type: \"text\",\r\n              content: result,\r\n            });\r\n          }\r\n\r\n          // 只在文本部分进行关键词替换\r\n          parts.forEach((part) => {\r\n            if (part.type === \"text\") {\r\n              // 使用全局替换，但要考虑词边界\r\n              const regex = new RegExp(escapedKeyword, \"gi\");\r\n              part.content = part.content.replace(regex, replaceString);\r\n            }\r\n          });\r\n\r\n          // 重新组合结果\r\n          result = parts.map((part) => part.content).join(\"\");\r\n        }\r\n      });\r\n\r\n      return result;\r\n    },\r\n    /* 下载Excel */\r\n    async downLoadExcel() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要导出的数据\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      if (this.flag == \"specialSubjectUse\") {\r\n        API.downLoadExcel(this.checkedCities).then((response) => {\r\n          let a = document.createElement(\"a\");\r\n          a.href = window.URL.createObjectURL(response);\r\n          a.download = `source_${new Date().getTime()}.xlsx`;\r\n          a.click();\r\n        });\r\n      } else {\r\n        await API.downLoadExportExcel(this.checkedCities).then((response) => {\r\n          let a = document.createElement(\"a\");\r\n          a.href = window.URL.createObjectURL(response);\r\n          a.download = `source_${new Date().getTime()}.xlsx`;\r\n          a.click();\r\n\r\n          // saveAs(blob, `source_${new Date().getTime()}.xlsx`)\r\n        });\r\n      }\r\n    },\r\n    batchDelete() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要删除的数据\", type: \"warning\" });\r\n        return;\r\n      }\r\n      this.$confirm(\"是否确认删除已勾选的数据项?\")\r\n        .then(() => {\r\n          API.batchRemove(this.checkedCities.join(\",\")).then((response) => {\r\n            this.$message({ message: \"删除成功\", type: \"success\" });\r\n            this.$emit(\"Refresh\");\r\n            this.checkedCities = [];\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /* 发布到每日最新热点 */\r\n    publishHot() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({\r\n          message: \"请选择要发布到每日最新热点的数据\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      this.$confirm(\"是否确认发布已勾选的数据项到每日最新热点?\")\r\n        .then(() => {\r\n          API.publishEverydayHot(this.checkedCities.join(\",\")).then(() => {\r\n            this.$message({ type: \"success\", message: \"发布成功!\" });\r\n            this.$emit(\"Refresh\");\r\n            this.checkedCities = [];\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /* 返回顶部动画 */\r\n    mainScorll() {\r\n      var scrollStep = -this.$refs.scroll.scrollTop / (800 / 15); // 计算每一步滚动的距离\r\n      var scrollInterval = setInterval(() => {\r\n        if (this.$refs.scroll.scrollTop !== 0) {\r\n          this.$refs.scroll.scrollBy(0, scrollStep); // 按照给定步长滚动窗口\r\n        } else {\r\n          clearInterval(scrollInterval); // 到达顶部时清除定时器\r\n        }\r\n      }, 15);\r\n    },\r\n    scrollChange() {\r\n      clearTimeout(this.timer);\r\n      this.timer = setTimeout(() => {\r\n        this.stopScroll();\r\n      }, 500);\r\n      // this.$refs.pagination.style.opacity = 0\r\n      // this.$refs.pagination.style.transition = '0'\r\n    } /* 滚动事件 */,\r\n    stopScroll() {\r\n      // this.$refs.pagination.style.transition = '1s'\r\n      // this.$refs.pagination.style.opacity = 1\r\n    },\r\n    /* 下载 */\r\n    downLoad() {\r\n      this.dialogVisible = true;\r\n    },\r\n    /* 每页条数变化 */\r\n    handleSizeChange(num) {\r\n      this.$emit(\"handleSizeChange\", num);\r\n      this.mainScorll();\r\n      this.checked = false;\r\n    },\r\n    /* 页码变化 */\r\n    handleCurrentChange(current) {\r\n      this.$emit(\"handleCurrentChange\", current);\r\n      this.mainScorll();\r\n      this.checked = false;\r\n    },\r\n    /* 收藏 */\r\n    async collect(item) {\r\n      /* 点击列表收藏 */\r\n      if (item.id) {\r\n        this.checkedCities = [item.id];\r\n      }\r\n      /* 未选择提示 */\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要收藏的文章\", type: \"info\" });\r\n        return;\r\n      }\r\n      /* 收藏 */\r\n      if (!item.favorites) {\r\n        let res = await API.collectApi([item.id]);\r\n        if (res.code) {\r\n          this.$message({\r\n            message: \"收藏成功,请前往个人中心查看\",\r\n            type: \"success\",\r\n          });\r\n          this.$emit(\"Refresh\");\r\n          this.checkedCities = [];\r\n          return;\r\n        }\r\n        this.$message({ message: \"收藏失败\", type: \"info\" });\r\n      } else {\r\n        let res = await API.cocelCollect([item.id]);\r\n        if (res.code) {\r\n          this.$message({ message: \"已取消收藏\", type: \"success\" });\r\n          this.$emit(\"Refresh\");\r\n          this.checkedCities = [];\r\n          return;\r\n        }\r\n        this.$message({ message: \"取消收藏失败\", type: \"info\" });\r\n      }\r\n    },\r\n    /* 一键复制 */\r\n    copyText(item) {\r\n      navigator.clipboard\r\n        .writeText(item.cnTitle)\r\n        .then(() => {\r\n          this.$message({ message: \"已成功复制到剪贴板\", type: \"success\" });\r\n        })\r\n        .catch(function () {\r\n          alert(\"复制失败\");\r\n        });\r\n    },\r\n    typeHandle(data) {\r\n      if (data == 1) {\r\n        return \"微信公众号\";\r\n      } else if (data == 2) {\r\n        return \"网站\";\r\n      } else if (data == 3) {\r\n        return \"手动录入\";\r\n      }\r\n    },\r\n    /* 选择事件 */\r\n    handleCheckedCitiesChange(value) {\r\n      this.checkedCities = value;\r\n    },\r\n    /* 全选 */\r\n    handleCheckAllChange(val) {\r\n      this.checkedCities = val ? this.ArticleList.map((item) => item.id) : [];\r\n      this.isIndeterminate = false;\r\n    },\r\n    /* 刷新 */\r\n    Refresh() {\r\n      this.$emit(\"Refresh\");\r\n    },\r\n    /*确定添加到报告 */\r\n    async reportSubmit() {\r\n      this.dialogVisible = false;\r\n      let keyWordList = [];\r\n      if (!this.reportId)\r\n        return this.$message({\r\n          message: \"请选择要添加到的报告\",\r\n          type: \"warning\",\r\n        });\r\n      /* 单独添加 */\r\n      if (this.separate.id) {\r\n        // let keyword = Object.keys(this.separate.keywordCount)\r\n        keyWordList.push({\r\n          reportId: this.reportId,\r\n          listId: this.separate.id,\r\n          listSn: this.separate.sn,\r\n        });\r\n      } else {\r\n        /* 批量添加 */\r\n        if (this.checkedCities == \"\")\r\n          return this.$message({\r\n            message: \"请选择要添加的数据\",\r\n            type: \"warning\",\r\n          });\r\n        this.checkedCities.forEach((item) => {\r\n          let article = this.ArticleList.filter((value) => value.id == item);\r\n          keyWordList.push({\r\n            reportId: this.reportId,\r\n            listId: item,\r\n            listSn: article[0].sn,\r\n          });\r\n        });\r\n      }\r\n      let res = await API.AddReport(keyWordList);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"已添加到报告\", type: \"success\" });\r\n        this.$emit(\"Refresh\");\r\n      } else {\r\n        this.$message({\r\n          message: \"添加到报告失败,请联系管理员\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n      this.separate = {};\r\n      this.reportId = \"\";\r\n      this.checkedCities = [];\r\n      this.checked = false;\r\n    },\r\n    /* 单独添加报告 */\r\n    separateAdd(item) {\r\n      this.dialogVisible = true;\r\n      this.separate = item;\r\n    },\r\n    /* 跳转新页面 */\r\n    openNewView(item, isLink) {\r\n      if (isLink) {\r\n        if (item.originalUrl) {\r\n          window.open(item.originalUrl);\r\n          return;\r\n        }\r\n        this.$message({ message: \"该文章没有原文链接\" });\r\n        return;\r\n      }\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n      // this.drawerInfo = item\r\n      // this.drawer = true\r\n    },\r\n    /* 文章打标签 */\r\n    tagHandler(item) {\r\n      this.tagDialog = true;\r\n      this.tagItem = item;\r\n      if (item.industry) {\r\n        this.formLabelAlign.industry = item.industry\r\n          .split(\",\")\r\n          .map((data) => Number(data));\r\n      }\r\n      if (item.domain) {\r\n        this.formLabelAlign.domain = item.domain\r\n          .split(\",\")\r\n          .map((data) => Number(data));\r\n      }\r\n      this.formLabelAlign.tag = item.tags ? item.tags.split(\",\") : \"\";\r\n    },\r\n    /* 获取领域和分类 */\r\n    async openDialog() {\r\n      await API.areaList().then((data) => {\r\n        if (data.code == 200) {\r\n          this.areaList = data.data;\r\n          this.options = data.data;\r\n          API.industry().then((value) => {\r\n            this.industry = value.data;\r\n            this.options1 = value.data;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /* 筛选领域 */\r\n    remoteEvent(query) {\r\n      this.options = this.areaList.filter((item) => item.fieldName == query);\r\n    },\r\n    /* 筛选行业 */\r\n    remoteIndustry(query) {\r\n      this.options1 = this.industry.filter(\r\n        (item) => item.industryName == query\r\n      );\r\n    },\r\n    async SubmitTag() {\r\n      let params = {\r\n        domain: String(this.formLabelAlign.domain),\r\n        industry: String(this.formLabelAlign.industry),\r\n        tags: String(this.formLabelAlign.tag),\r\n        articleId: String(this.tagItem.id),\r\n        docId: this.tagItem.docId ? String(this.tagItem.docId) : \"\",\r\n      };\r\n      let res = await API.tagAdd(params);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"保存成功\", type: \"success\" });\r\n        setTimeout(() => {\r\n          this.Refresh();\r\n        }, 1000);\r\n      } else {\r\n        this.$message({ message: \"保存失败\", type: \"error\" });\r\n      }\r\n      this.closeTag();\r\n    },\r\n    closeTag() {\r\n      this.$refs[\"ruleForm\"].resetFields();\r\n      this.formLabelAlign = {\r\n        tag: \"\",\r\n        industry: \"\",\r\n        domain: \"\",\r\n      };\r\n      this.tagDialog = false;\r\n    },\r\n    async hotIncrease(item) {\r\n      let isWhether = JSON.parse(item.isWhether);\r\n      let res = await API.tagAdd({\r\n        articleId: item.id,\r\n        isWhether: +!Boolean(isWhether),\r\n      });\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"操作成功\", type: \"success\" });\r\n        this.Refresh();\r\n      } else {\r\n        this.$message({ message: \"操作失败\", type: \"error\" });\r\n      }\r\n    },\r\n    async openDrawer() {\r\n      let docId = this.drawerInfo.docId;\r\n      await API.AreaInfo(this.drawerInfo.id).then((res) => {\r\n        if (res.code == 200) {\r\n          this.drawerInfo = res.data;\r\n          this.drawerInfo.docId = docId;\r\n          /* 将字符串中的\\n替换为<br> */\r\n          this.translationBtnShow = !this.drawerInfo.cnContent;\r\n          if (this.drawerInfo.cnContent || this.drawerInfo.content) {\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(/\\\\n/g, (a, b, c) => {\r\n              return \"<br>\";\r\n            });\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(/\\${[^}]+}/g, \"<br>\");\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(\"|xa0\", \"\");\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /* 所属行业处理 */\r\n    industryHandle(item) {\r\n      let ids = [],\r\n        str = \"\";\r\n      if (item.industry) {\r\n        ids = item.industry.split(\",\");\r\n      }\r\n      ids.forEach((data) => {\r\n        this.industry.map((ele) => {\r\n          if (ele.id == data) {\r\n            if (str == undefined) {\r\n              str = \"\";\r\n            }\r\n            str += ele.industryName + \" \";\r\n          }\r\n        });\r\n      });\r\n      return str;\r\n    },\r\n    domainHandle(item) {\r\n      let ids = [],\r\n        str = \"\";\r\n      if (item.domain) {\r\n        ids = item.domain.split(\",\");\r\n      }\r\n      ids.forEach((data) => {\r\n        this.areaList.map((ele) => {\r\n          if (ele.id == data) {\r\n            if (str == undefined) {\r\n              str = \"\";\r\n            }\r\n            str += ele.fieldName + \" \";\r\n          }\r\n        });\r\n      });\r\n      return str;\r\n    },\r\n    /* 快照生成 */\r\n    resultEvent(item) {\r\n      if (item == \"BatchGeneration\" && this.checkedCities.length == 0) {\r\n        this.$message.warning(\"请先选择文章\");\r\n        return;\r\n      }\r\n      let ids = null;\r\n      let zhuangtai = \"生成\";\r\n      let url = \"\";\r\n      if (item == \"drawer\") {\r\n        ids = [this.drawerInfo.id];\r\n        if (this.drawerInfo.snapshotUrl) zhuangtai = \"查看\";\r\n        url = this.drawerInfo.snapshotUrl;\r\n      } else if (item == \"BatchGeneration\") {\r\n        ids = this.checkedCities;\r\n      } else {\r\n        ids = [item.id];\r\n        if (item.snapshotUrl) zhuangtai = \"查看\";\r\n        url = item.snapshotUrl;\r\n      }\r\n      if (zhuangtai == \"生成\") {\r\n        if (this.flag == \"MonitorUse\") {\r\n          API.downLoadExportKe(ids)\r\n            .then((response) => {\r\n              if (response.code == 200) {\r\n                this.$msgbox({\r\n                  title: \"提示\",\r\n                  message: \"快照正在生成中，请稍后查看\",\r\n                  showCancelButton: true,\r\n                  confirmButtonText: \"关闭\",\r\n                  cancelButtonText: \"取消\",\r\n                  showCancelButton: false,\r\n                  beforeClose: (action, instance, done) => {\r\n                    done();\r\n                  },\r\n                });\r\n              } else {\r\n                this.$message({\r\n                  message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                  type: \"error\",\r\n                });\r\n              }\r\n            })\r\n            .catch((err) => {});\r\n        } else {\r\n          API.downLoadExportZhuan(ids)\r\n            .then((response) => {\r\n              if (response.code == 200) {\r\n                this.$msgbox({\r\n                  title: \"提示\",\r\n                  message: \"快照正在生成中，请稍后查看\",\r\n                  showCancelButton: true,\r\n                  confirmButtonText: \"关闭\",\r\n                  cancelButtonText: \"取消\",\r\n                  showCancelButton: false,\r\n                  beforeClose: (action, instance, done) => {\r\n                    done();\r\n                  },\r\n                });\r\n              } else {\r\n                this.$message({\r\n                  message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                  type: \"error\",\r\n                });\r\n              }\r\n            })\r\n            .catch((err) => {});\r\n        }\r\n      } else {\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/server-api/\", \"g\"), \"/\");\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/\", \"g\"), \"/\");\r\n        window.open(window.location.origin + url, \"_blank\");\r\n      }\r\n    },\r\n    /* 附件下载 */\r\n    async documentDownload(item) {\r\n      this.loading = true;\r\n      if (item.fileUrl) {\r\n        const urls = item.fileUrl.split(\",\");\r\n        for (const [index, url] of urls.entries()) {\r\n          if (url.indexOf(\"https://\") === -1) {\r\n            setTimeout(async () => {\r\n              await this.downLoadFun(url, index, item.cnTitle || item.title);\r\n            }, index * 500);\r\n          } else {\r\n            this.$message.error(\"附件还没同步到当前系统，暂时无法下载\");\r\n          }\r\n        }\r\n      }\r\n      this.loading = false;\r\n    },\r\n\r\n    async downLoadFun(url, index, title) {\r\n      let formData = new FormData();\r\n      formData.append(\"fileUrl\", url);\r\n\r\n      try {\r\n        const response = await API.downloadFile(formData);\r\n        const isBlob = blobValidate(response);\r\n\r\n        if (isBlob) {\r\n          const blob = new Blob([response]);\r\n          let list = url.split(\"/\");\r\n          let fileName = list[list.length - 1];\r\n          saveAs(blob, fileName);\r\n        } else {\r\n          const resText = await response.text();\r\n          const rspObj = JSON.parse(resText);\r\n          const errMsg =\r\n            errorCode[rspObj.code] || rspObj.msg || errorCode[\"default\"];\r\n          this.$message.error(errMsg);\r\n        }\r\n      } catch (err) {\r\n        // this.$message.error(`Error downloading file: ${err}`);\r\n      } finally {\r\n        // 确保 loading 在每次下载后都设置为 false\r\n        this.loading = false;\r\n      }\r\n\r\n      // 之前的附件下载\r\n      // if (item.annexUrl) {\r\n      //   /* 有文件地址 直接下载 */\r\n      //   let formData = new FormData()\r\n      //   formData.append('id', item.id)\r\n      //   this.loading = true\r\n      //   API.documentDownloadKe(formData).then(res => {\r\n      //     let a = document.createElement('a')\r\n      //     a.href = URL.createObjectURL(res.data)\r\n      //     a.download = res.headers['content-disposition'].split('filename=')[1]\r\n      //     a.click()\r\n      //     this.loading = false\r\n      //   })\r\n      // } else {\r\n      //   /* 没有文件格式 申请下载 */\r\n      //   API.documentDownload(id).then(response => {\r\n      //     if (response.code == 200) {\r\n      //       this.$msgbox({\r\n      //         title: '提示',\r\n      //         message: '附件正在同步中，请稍后下载',\r\n      //         showCancelButton: true,\r\n      //         confirmButtonText: '关闭',\r\n      //         cancelButtonText: '取消',\r\n      //         showCancelButton: false,\r\n      //         beforeClose: (action, instance, done) => {\r\n      //           done()\r\n      //         }\r\n      //       })\r\n      //     } else {\r\n      //       this.$message({ message: '附件下载失败', type: 'error' })\r\n      //     }\r\n      //   }).catch(err => { })\r\n      // }\r\n    },\r\n    // 翻译标题\r\n    translateTitle(row) {\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"Loading\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      API.translationTitle({\r\n        originalText: row.title,\r\n        docId: row.docId,\r\n        id: row.id,\r\n        translationField: \"title\",\r\n        translationType: 1,\r\n      })\r\n        .then((res) => {\r\n          this.drawerInfo.cnTitle = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].cnTitle = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].isTranslated = 1;\r\n          loading.close();\r\n        })\r\n        .catch((err) => {\r\n          loading.close();\r\n        });\r\n    },\r\n    // 翻译文章\r\n    translateEvent(row) {\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"Loading\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      API.translationTitle({\r\n        originalText: row.content,\r\n        docId: row.docId,\r\n        id: row.id,\r\n        translationField: \"content\",\r\n        translationType: 1,\r\n      })\r\n        .then((res) => {\r\n          this.drawerInfo.cnContent = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].cnContent = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].isTranslated = 1;\r\n          this.translationBtnShow = false;\r\n          loading.close();\r\n        })\r\n        .catch((err) => {\r\n          loading.close();\r\n        });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      API.AreaInfo(row.id).then((response) => {\r\n        this.form = response.data;\r\n        this.form.sourceType = Number(this.form.sourceType);\r\n        this.form.docId = row.docId;\r\n        // this.fileUrlList = this.form.fileUrl ? this.form.fileUrl.split(\",\").map(item => {\r\n        //   return {\r\n        //     name: item,\r\n        //     url: item\r\n        //   }\r\n        // }) : []\r\n        this.open = true;\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal\r\n        .confirm('是否确认删除该条文章？\"')\r\n        .then(() => {\r\n          return API.monitoringEsRemove({ id: row.id, docId: row.docId });\r\n        })\r\n        .then(() => {\r\n          this.Refresh();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          let queryForm = JSON.parse(JSON.stringify(this.form));\r\n          // let cover = String(this.fileList.map(item => item.path))\r\n          // queryForm.cover = cover\r\n          articleListEdit(queryForm).then((response) => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.Refresh();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /* 自定义上传 */\r\n    async requestLoad(file) {\r\n      let data = new FormData();\r\n      data.append(\"cover\", file.file);\r\n      await uploadCover(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.fileList.map((item) => {\r\n            if (item.uid == file.file.uid) {\r\n              item.path = response.imgUrl;\r\n            }\r\n          });\r\n          this.$message({ message: \"上传成功\", type: \"success\" });\r\n        } else {\r\n          this.$message({ message: \"上传失败,请稍候重试\", type: \"error\" });\r\n        }\r\n      });\r\n    },\r\n    /* 文件超出限制 */\r\n    exceed() {\r\n      this.$message({\r\n        message: \"文件上传超出限制,最多可以上传三个文件\",\r\n        type: \"info\",\r\n      });\r\n    },\r\n    /* 移除文件 */\r\n    handleRemove(file) {\r\n      this.fileList = this.fileList.filter((item) => item !== file);\r\n    },\r\n    // 文件更改\r\n    handleChange(file, fileList) {\r\n      this.fileList = fileList;\r\n      this.$refs.upload.submit();\r\n    },\r\n    // 上传附件校验\r\n    beforeUploadUrl(file) {\r\n      // 判断文件是否为excel\r\n      let fileName = file.name\r\n          .substring(file.name.lastIndexOf(\".\") + 1)\r\n          .toLowerCase(),\r\n        condition =\r\n          fileName == \"pdf\" ||\r\n          fileName == \"doc\" ||\r\n          fileName == \"xls\" ||\r\n          fileName == \"ppt\" ||\r\n          fileName == \"xlsx\" ||\r\n          fileName == \"pptx\" ||\r\n          fileName == \"docx\";\r\n      let fileSize = file.size / 1024 / 1024 < 10;\r\n      if (!condition) {\r\n        this.$notify({\r\n          title: \"警告\",\r\n          message: \"上传文件必须是pdf,doc,xls,ppt,xlsx,pptx,docx格式\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      /* 文件大小限制 */\r\n      if (!fileSize) {\r\n        this.$notify({\r\n          title: \"警告\",\r\n          message: \"上传文件的大小不能超过 10MB!\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      return condition && fileSize;\r\n    },\r\n    // 文件上传成功\r\n    uploadUrlSuccess(res, file) {\r\n      this.$message({ message: \"上传成功\", type: \"success\" });\r\n    },\r\n    // 文件上传超出限制\r\n    uploadUrlExceed() {\r\n      this.$message({\r\n        message: \"文件上传超出限制,最多可以上传1个文件\",\r\n        type: \"info\",\r\n      });\r\n    },\r\n    // 文件上传方法\r\n    uploadUrlRequest(file) {\r\n      if (this.form.originalUrl != null && this.form.originalUrl != \"\") {\r\n        if (\r\n          this.form.originalUrl.match(\r\n            /(http|https):\\/\\/[\\w\\-_]+(\\.[\\w\\-_]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?/\r\n          )\r\n        ) {\r\n          let data = new FormData();\r\n          data.append(\"file\", file.file);\r\n          data.append(\"originalUrl\", this.form.originalUrl);\r\n\r\n          API.uploadFile(data).then((response) => {\r\n            if (response.code == 200) {\r\n              this.$message({ message: \"上传成功\", type: \"success\" });\r\n              this.form.fileUrl = response.data;\r\n            } else {\r\n              this.$message({ message: \"上传失败,请稍候重试\", type: \"error\" });\r\n              this.form.fileUrl = \"\";\r\n            }\r\n          });\r\n        } else {\r\n          this.$message({ message: \"请填写正确的原文链接\", type: \"warning\" });\r\n          this.fileUrlList = [];\r\n        }\r\n      } else {\r\n        this.$message({ message: \"请填写原文链接\", type: \"warning\" });\r\n        this.fileUrlList = [];\r\n      }\r\n    },\r\n    // 删除附件\r\n    uploadUrlRemove() {\r\n      API.removeFile({ filePath: this.form.fileUrl }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.$message({ message: \"删除成功\", type: \"success\" });\r\n          this.fileUrlList = [];\r\n          this.form.fileUrl = \"\";\r\n        } else {\r\n          this.$message({ message: \"删除失败,请稍候重试\", type: \"error\" });\r\n        }\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        articleSn: null,\r\n        title: null,\r\n        cnTitle: null,\r\n        sourceType: null,\r\n        sourceName: null,\r\n        sourceSn: null,\r\n        originalUrl: null,\r\n        shortUrl: null,\r\n        author: null,\r\n        description: null,\r\n        summary: null,\r\n        cnSummary: null,\r\n        cover: null,\r\n        publishType: null,\r\n        publishCode: null,\r\n        publishArea: null,\r\n        publishTime: null,\r\n        numberLikes: null,\r\n        numberReads: null,\r\n        numberCollects: null,\r\n        numberShares: null,\r\n        numberComments: null,\r\n        emotion: null,\r\n        status: null,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        userId: null,\r\n        deptId: null,\r\n        content: null,\r\n        cnContent: null,\r\n        fileUrl: null,\r\n        industry: null,\r\n        domain: null,\r\n        tmpUrl: null,\r\n        isFinish: null,\r\n        groupId: null,\r\n        appId: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    // 批量导入相关方法\r\n    // 打开批量导入弹框\r\n    openBatchImportDialog() {\r\n      this.batchImportVisible = true;\r\n      this.batchImportFiles = [];\r\n    },\r\n    // 文件选择处理\r\n    handleFileSelect(file, fileList) {\r\n      // 将新选择的文件添加到列表中\r\n      const newFiles = fileList.map((item) => ({\r\n        fileName: item.name,\r\n        file: item.raw,\r\n        sourceName: \"\",\r\n      }));\r\n      this.batchImportFiles = newFiles;\r\n    },\r\n    // 删除文件\r\n    removeFile(index) {\r\n      this.batchImportFiles.splice(index, 1);\r\n    },\r\n    // 取消批量导入\r\n    cancelBatchImport() {\r\n      this.batchImportVisible = false;\r\n      this.batchImportFiles = [];\r\n      // 清空文件选择器\r\n      this.$refs.batchUpload.clearFiles();\r\n    },\r\n    // 确认批量导入\r\n    async confirmBatchImport() {\r\n      // 验证数据源名称是否都已填写\r\n      const emptySourceNames = this.batchImportFiles.filter(\r\n        (item) => !item.sourceName.trim()\r\n      );\r\n      if (emptySourceNames.length > 0) {\r\n        this.$message({\r\n          message: \"请为所有文件填写数据源名称\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      try {\r\n        this.loading = true;\r\n\r\n        // 创建FormData对象\r\n        const formData = new FormData();\r\n\r\n        // 添加文件到FormData\r\n        this.batchImportFiles.forEach((item) => {\r\n          formData.append(\"files\", item.file);\r\n        });\r\n\r\n        // 获取数据源名称数组\r\n        const sourceNames = this.batchImportFiles\r\n          .map((item) => item.sourceName)\r\n          .join(\",\");\r\n\r\n        formData.append(\"sourceNames\", sourceNames);\r\n\r\n        // 调用批量导入API，传递FormData和sourceNames参数\r\n        const response = await API.batchImportReports(formData);\r\n\r\n        if (response.code === 200) {\r\n          this.$message({\r\n            message: \"批量导入成功\",\r\n            type: \"success\",\r\n          });\r\n          this.batchImportVisible = false;\r\n          this.batchImportFiles = [];\r\n          this.$refs.batchUpload.clearFiles();\r\n          // 刷新列表\r\n          this.Refresh();\r\n        } else {\r\n          this.$message({\r\n            message: response.msg || \"批量导入失败\",\r\n            type: \"error\",\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"批量导入错误:\", error);\r\n        this.$message({\r\n          message: \"批量导入失败，请稍后重试\",\r\n          type: \"error\",\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    reportAiChat() {\r\n      this.showDeepseekDialog = true;\r\n      if (this.checkedCities.length === 0) {\r\n        this.$message({ message: \"请选择要解读的文章\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      if (this.checkedCities.length > 1) {\r\n        this.$message({ message: \"请只选择一篇文章进行解读\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      // 获取选中的文章\r\n      const selectedArticleId = this.checkedCities[0];\r\n      const selectedArticle = this.ArticleList.find(\r\n        (item) => item.id === selectedArticleId\r\n      );\r\n\r\n      if (selectedArticle) {\r\n        this.currentArticle = selectedArticle;\r\n        this.showDeepseekDialog = true;\r\n      } else {\r\n        this.$message({ message: \"未找到选中的文章\", type: \"error\" });\r\n      }\r\n    },\r\n    // ai相关\r\n    // dify\r\n    async difyAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.checkedCities.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await difyAiQa(\r\n          articlesContent,\r\n          \"streaming\",\r\n          \"dify.article.apikey\"\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader;\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let pendingBuffer = \"\"; // 用于存储待处理的不完整数据\r\n        let isInThinkTag = false; // 新增：标记是否在think标签内\r\n\r\n        // 将Unicode转义字符(\\uXXXX)转换为实际字符\r\n        const decodeUnicode = (str) => {\r\n          return str.replace(/\\\\u[\\dA-Fa-f]{4}/g, (match) => {\r\n            return String.fromCharCode(parseInt(match.replace(/\\\\u/g, \"\"), 16));\r\n          });\r\n        };\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          try {\r\n            const renderedContent = marked(newContent, this.markdownOptions);\r\n            aiMessage.content = renderedContent;\r\n\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          } catch (error) {\r\n            console.error(\"渲染内容时出错:\", error);\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        while (true) {\r\n          // 检查是否已中断\r\n          if (this.isAborted) {\r\n            throw new Error(\"AbortError\");\r\n          }\r\n\r\n          const { done, value } = await reader.read();\r\n\r\n          if (done) {\r\n            // 处理最后可能剩余的数据\r\n            if (pendingBuffer) {\r\n              try {\r\n                const lastData = JSON.parse(pendingBuffer);\r\n                if (lastData.answer) {\r\n                  // 解码Unicode转义字符\r\n                  const decodedAnswer = decodeUnicode(lastData.answer);\r\n                  buffer += decodedAnswer;\r\n                  updateContent(buffer);\r\n                }\r\n              } catch (e) {\r\n                console.warn(\"处理最后的数据时出错:\", e);\r\n              }\r\n            }\r\n            break;\r\n          }\r\n\r\n          const chunk = decoder.decode(value);\r\n          pendingBuffer += chunk;\r\n\r\n          // 处理完整的数据行\r\n          while (pendingBuffer.includes(\"\\n\")) {\r\n            const newlineIndex = pendingBuffer.indexOf(\"\\n\");\r\n            const line = pendingBuffer.slice(0, newlineIndex).trim();\r\n            pendingBuffer = pendingBuffer.slice(newlineIndex + 1);\r\n\r\n            if (!line || line === \"data:\" || !line.startsWith(\"data:\")) {\r\n              continue;\r\n            }\r\n\r\n            try {\r\n              const data = line.slice(5).trim();\r\n              if (data === \"[DONE]\") {\r\n                continue;\r\n              }\r\n\r\n              const jsonData = JSON.parse(data);\r\n              if (!jsonData.answer) {\r\n                continue;\r\n              }\r\n\r\n              // 跳过特殊字符\r\n              if (jsonData.answer === \"```\" || jsonData.answer === \"markdown\") {\r\n                continue;\r\n              }\r\n\r\n              // 解码Unicode转义字符\r\n              let answer = decodeUnicode(jsonData.answer);\r\n\r\n              // 检查是否包含<think>开始标签\r\n              if (answer.includes(\"<think>\")) {\r\n                isInThinkTag = true;\r\n                continue; // 跳过包含<think>的部分\r\n              }\r\n\r\n              // 检查是否包含</think>结束标签\r\n              if (answer.includes(\"</think>\")) {\r\n                isInThinkTag = false;\r\n                continue; // 跳过包含</think>的部分\r\n              }\r\n\r\n              // 只有不在think标签内的内容才会被添加到buffer中\r\n              if (!isInThinkTag && answer) {\r\n                buffer += answer;\r\n                updateContent(buffer);\r\n              }\r\n            } catch (parseError) {\r\n              console.warn(\"解析数据行时出错:\", {\r\n                line,\r\n                error: parseError.message,\r\n                pendingBuffer,\r\n              });\r\n              continue;\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null;\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // Ollama\r\n    async ollamaAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.checkedCities.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await ollamaAiQa(prompt, true);\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader; // 保存当前的 reader\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let lastUpdateTime = Date.now();\r\n        let isThinkContent = false;\r\n        let tempBuffer = \"\";\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          const currentTime = Date.now();\r\n          // 控制更新频率，避免过于频繁的DOM更新\r\n          if (currentTime - lastUpdateTime >= 50) {\r\n            aiMessage.content = newContent;\r\n            lastUpdateTime = currentTime;\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        const processStream = async () => {\r\n          try {\r\n            while (true) {\r\n              // 检查是否已中断\r\n              if (this.isAborted) {\r\n                throw new Error(\"AbortError\");\r\n              }\r\n\r\n              const { done, value } = await reader.read();\r\n              if (done) {\r\n                if (buffer.length > 0) {\r\n                  updateContent(buffer);\r\n                }\r\n                break;\r\n              }\r\n\r\n              const chunk = decoder.decode(value);\r\n              const lines = chunk.split(\"\\n\").filter((line) => line.trim());\r\n\r\n              for (const line of lines) {\r\n                try {\r\n                  const jsonData = JSON.parse(line);\r\n                  if (!jsonData.response) continue;\r\n\r\n                  const response = jsonData.response;\r\n\r\n                  // 跳过特殊字符\r\n                  if (response === \"```\" || response === \"markdown\") {\r\n                    continue;\r\n                  }\r\n\r\n                  tempBuffer += response;\r\n\r\n                  // 检查是否包含完整的think标签对\r\n                  while (true) {\r\n                    const thinkStartIndex = tempBuffer.indexOf(\"<think>\");\r\n                    const thinkEndIndex = tempBuffer.indexOf(\"</think>\");\r\n\r\n                    if (thinkStartIndex === -1 && thinkEndIndex === -1) {\r\n                      // 没有think标签，直接添加到buffer\r\n                      if (!isThinkContent) {\r\n                        buffer += tempBuffer;\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = \"\";\r\n                      break;\r\n                    } else if (thinkStartIndex !== -1 && thinkEndIndex === -1) {\r\n                      // 只有开始标签，等待结束标签\r\n                      isThinkContent = true;\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkStartIndex);\r\n                      break;\r\n                    } else if (thinkStartIndex === -1 && thinkEndIndex !== -1) {\r\n                      // 只有结束标签，移除之前的内容\r\n                      isThinkContent = false;\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      continue;\r\n                    } else {\r\n                      // 有完整的think标签对\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      isThinkContent = false;\r\n                      continue;\r\n                    }\r\n                  }\r\n                } catch (parseError) {\r\n                  console.warn(\"无效的JSON行，已跳过\", {\r\n                    line,\r\n                    error: parseError.message,\r\n                  });\r\n                }\r\n              }\r\n            }\r\n          } catch (streamError) {\r\n            if (streamError.message === \"AbortError\") {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n            console.error(\"处理流式响应时出错:\", streamError);\r\n            throw streamError;\r\n          }\r\n        };\r\n\r\n        await processStream();\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // deepseek\r\n    async deepseekAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      const selectedArticles = this.ArticleList.filter((article) =>\r\n        this.checkedCities.includes(article.id)\r\n      );\r\n      const titles = selectedArticles\r\n        .map((article) => `《${article.cnTitle || article.title}》`)\r\n        .join(\"\\n\");\r\n\r\n      try {\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data || !articlesResponse.data.length) {\r\n          throw new Error(\"Failed to get article contents\");\r\n        }\r\n\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息并添加到对话中\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n        this.isThinking = true;\r\n\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `\\n\\n**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        const response = await deepseekAiQa(prompt, true);\r\n\r\n        if (response.ok) {\r\n          const reader = response.body.getReader();\r\n          this.currentReader = reader; // 保存当前的 reader\r\n          const decoder = new TextDecoder();\r\n          let buffer = \"\";\r\n          let lastUpdateTime = Date.now();\r\n\r\n          const updateContent = (newContent) => {\r\n            const currentTime = Date.now();\r\n            if (currentTime - lastUpdateTime >= 50) {\r\n              aiMessage.content = newContent;\r\n              lastUpdateTime = currentTime;\r\n              this.$nextTick(() => {\r\n                const chatMessages = this.$refs.chatMessages;\r\n                if (chatMessages) {\r\n                  chatMessages.scrollTop = chatMessages.scrollHeight;\r\n                }\r\n              });\r\n            }\r\n          };\r\n\r\n          while (true) {\r\n            // 检查是否已中断\r\n            if (this.isAborted) {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n\r\n            const { done, value } = await reader.read();\r\n            if (done) {\r\n              if (buffer.length > 0) {\r\n                updateContent(buffer);\r\n              }\r\n              break;\r\n            }\r\n\r\n            const chunk = decoder.decode(value);\r\n            try {\r\n              const lines = chunk.split(\"\\n\");\r\n\r\n              for (const line of lines) {\r\n                if (!line.trim() || !line.startsWith(\"data: \")) continue;\r\n\r\n                const data = line.slice(5);\r\n                if (data === \"[DONE]\") break;\r\n\r\n                try {\r\n                  const jsonData = JSON.parse(data);\r\n                  if (jsonData.choices?.[0]?.delta?.content) {\r\n                    let content = jsonData.choices[0].delta.content;\r\n\r\n                    // 跳过特殊字符\r\n                    if (content === \"```\" || content === \"markdown\") {\r\n                      continue;\r\n                    }\r\n\r\n                    buffer += content;\r\n                    updateContent(buffer);\r\n                  }\r\n                } catch (parseError) {\r\n                  console.error(\"Error parsing JSON:\", parseError);\r\n                }\r\n              }\r\n            } catch (e) {\r\n              console.error(\"Error processing chunk:\", e);\r\n            }\r\n          }\r\n        } else {\r\n          throw new Error(\"Request failed\");\r\n        }\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI Chat Error:\", error);\r\n        this.$message.error(\"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // 关闭AI对话\r\n    closeAiDialog() {\r\n      this.isAborted = true; // 设置中断标志\r\n      if (this.currentReader) {\r\n        this.currentReader.cancel(); // 中断当前的读取\r\n      }\r\n      this.aiDialogVisible = false;\r\n      this.chatMessages = [];\r\n      this.isThinking = false;\r\n      this.isRequesting = false;\r\n      this.currentReader = null;\r\n    },\r\n    articleAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyAiChat();\r\n      } else if (this.aiPlatform === \"ollama\") {\r\n        this.ollamaAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekAiChat();\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* drawer样式修改 */\r\n.main ::v-deep .el-drawer.drawer_box {\r\n  width: 700px !important;\r\n}\r\n\r\n.MainArticle {\r\n  width: 100%;\r\n  box-shadow: 0 0px 10px 0px rgb(206, 205, 205);\r\n  // margin-top: 10px;\r\n  user-select: text;\r\n\r\n  .TopBtnGroup {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n    align-items: center;\r\n    border-bottom: solid 1px #e2e2e2;\r\n  }\r\n\r\n  .leftBtnGroup {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    gap: 15px;\r\n\r\n    .toolTitle {\r\n      color: #606266;\r\n      font-size: 14px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    & > p {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n  }\r\n\r\n  .leftBtnGroup2 {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    align-items: center;\r\n    gap: 15px;\r\n    flex: 1;\r\n    padding-left: 20px;\r\n\r\n    .toolTitle {\r\n      color: #606266;\r\n      font-size: 14px;\r\n      line-height: 36px;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .scollBox {\r\n    overflow-y: auto;\r\n    height: calc(100vh - 389px);\r\n    transition: transform 5s ease;\r\n\r\n    .Articl {\r\n      display: flex;\r\n      width: 100%;\r\n      border-bottom: solid 1px #d4d4d4;\r\n\r\n      .Articl_left {\r\n        width: 85%;\r\n        padding-bottom: 16px;\r\n      }\r\n\r\n      .Articl_kqcb {\r\n        width: 100%;\r\n        padding-bottom: 16px;\r\n        & > div:first-child {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n        }\r\n      }\r\n\r\n      .ArticlTop {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        padding: 0 0 0 20px;\r\n        font-size: 15px;\r\n      }\r\n\r\n      .ArticlMain {\r\n        padding: 0 0 0 30px;\r\n        color: #3f3f3f;\r\n        font-size: 14px;\r\n        line-height: 24px;\r\n      }\r\n\r\n      .ArticlMain > span:hover {\r\n        color: #1889f3;\r\n        border-bottom: solid 1px #0798f8;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .info_flex {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        width: 100%;\r\n      }\r\n\r\n      .ArticlBottom {\r\n        padding: 0 0 0 30px;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 0 25px;\r\n        line-height: 24px;\r\n        color: #9b9b9b;\r\n        font-size: 14px;\r\n\r\n        div {\r\n          text-overflow: ellipsis;\r\n          overflow: hidden;\r\n          white-space: nowrap;\r\n        }\r\n\r\n        .linkStyle:hover {\r\n          border-bottom: solid 1px #1889f3;\r\n        }\r\n\r\n        .infomation {\r\n          color: #464749;\r\n          font-size: 14px;\r\n          margin-left: 8px;\r\n        }\r\n\r\n        p {\r\n          border: solid 1px #f0a147;\r\n          width: 45px;\r\n          height: 25px;\r\n          line-height: 25px;\r\n          font-size: 14px;\r\n          color: #f0a147;\r\n          text-align: center;\r\n        }\r\n      }\r\n\r\n      .imgBox {\r\n        padding: 0 20px 0 30px;\r\n        display: flex;\r\n        gap: 15px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.pagination {\r\n  z-index: 2;\r\n  background-color: rgba(255, 255, 255, 1);\r\n  width: 100%;\r\n  box-shadow: 0 0px 10px 0px rgb(206, 205, 205);\r\n  padding: 10px 0;\r\n}\r\n\r\n.title_Article {\r\n  color: rgb(8, 8, 8);\r\n  font-size: 15px;\r\n  line-height: 16px;\r\n}\r\n\r\n.title_Article:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n}\r\n\r\n.drawer_Style {\r\n  z-index: 2;\r\n  margin: 0 15px 0 15px;\r\n  width: 661px;\r\n  height: 80vh;\r\n\r\n  .title {\r\n    font-size: 16px;\r\n    font-weight: 500px;\r\n    text-align: center;\r\n  }\r\n\r\n  .source {\r\n    color: #0798f8;\r\n    text-align: center;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .time {\r\n    font-size: 14px;\r\n    text-align: center;\r\n    margin-left: 10px;\r\n    color: #9b9b9b;\r\n  }\r\n}\r\n\r\n.drawer_Title {\r\n  text-overflow: ellipsis;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n}\r\n\r\n.btnBox {\r\n  z-index: 2;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex: 1;\r\n\r\n  & > p {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n// 批量导入弹框样式\r\n.batch-import-container {\r\n  .file-select-area {\r\n    text-align: center;\r\n    padding: 20px 0;\r\n    border: 2px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    background-color: #fafafa;\r\n\r\n    &:hover {\r\n      border-color: #409eff;\r\n    }\r\n  }\r\n\r\n  .file-table-area {\r\n    margin-top: 20px;\r\n\r\n    .el-table {\r\n      border-radius: 4px;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-icon-chat-dot-round:before {\r\n  color: #1296db;\r\n}\r\n\r\n::v-deep .el-icon-document-add:before {\r\n  color: #1296db;\r\n}\r\n\r\n.deepseek-text {\r\n  color: #1296db; // 使用与图标相同的颜色\r\n  margin-left: 4px;\r\n  font-size: 14px;\r\n  line-height: 24px;\r\n}\r\n\r\n// ai相关\r\n.ai-chat-container {\r\n  height: 550px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f7fa;\r\n\r\n  .chat-messages {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 24px;\r\n\r\n    .message {\r\n      margin-bottom: 28px;\r\n      display: flex;\r\n      align-items: flex-start;\r\n\r\n      .avatar {\r\n        width: 42px;\r\n        height: 42px;\r\n        border-radius: 50%;\r\n        overflow: hidden;\r\n        flex-shrink: 0;\r\n        border: 2px solid #fff;\r\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n        background-color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: contain;\r\n          background-color: #fff;\r\n        }\r\n      }\r\n\r\n      .message-wrapper {\r\n        margin: 0 16px;\r\n        max-width: calc(100% - 100px);\r\n      }\r\n\r\n      .message-content {\r\n        padding: 12px 16px;\r\n        border-radius: 12px;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n        position: relative;\r\n\r\n        &::before {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 14px;\r\n          width: 0;\r\n          height: 0;\r\n          border: 6px solid transparent;\r\n        }\r\n      }\r\n    }\r\n\r\n    .user-message {\r\n      flex-direction: row-reverse;\r\n\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #e6f3ff;\r\n        color: #2d2d2d;\r\n        line-height: 1.8em;\r\n        font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n          Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n          Arial, sans-serif;\r\n\r\n        &::before {\r\n          right: -12px;\r\n          border-left-color: #e6f3ff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .ai-message {\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #fff;\r\n        color: #2d2d2d;\r\n\r\n        &::before {\r\n          left: -12px;\r\n          border-right-color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .thinking-animation {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    padding: 12px 16px;\r\n    min-height: 45px;\r\n    background: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n    position: relative;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 14px;\r\n      left: -12px;\r\n      width: 0;\r\n      height: 0;\r\n      border: 6px solid transparent;\r\n      border-right-color: #fff;\r\n    }\r\n\r\n    span {\r\n      display: inline-block;\r\n      width: 6px;\r\n      height: 6px;\r\n      margin: 0 3px;\r\n      background-color: #409eff;\r\n      border-radius: 50%;\r\n      opacity: 0.7;\r\n      animation: thinking 1.4s infinite ease-in-out both;\r\n\r\n      &:nth-child(1) {\r\n        animation-delay: -0.32s;\r\n      }\r\n\r\n      &:nth-child(2) {\r\n        animation-delay: -0.16s;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message-content {\r\n    min-height: 45px;\r\n    white-space: pre-wrap;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n\r\n    ::v-deep {\r\n      h1,\r\n      h2,\r\n      h3,\r\n      h4,\r\n      h5,\r\n      h6 {\r\n        margin: 0.05em 0 0.02em 0;\r\n        font-weight: 600;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      h1 {\r\n        font-size: 1.6em;\r\n        margin-top: 0;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h2 {\r\n        font-size: 1.4em;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 1.2em;\r\n      }\r\n\r\n      p {\r\n        margin: 0;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      strong {\r\n        font-weight: 600;\r\n        color: #1a1a1a;\r\n      }\r\n\r\n      em {\r\n        font-style: italic;\r\n        color: #2c5282;\r\n      }\r\n\r\n      ul,\r\n      ol {\r\n        margin: 0;\r\n        padding-left: 1em;\r\n        display: flex !important;\r\n        flex-direction: column !important;\r\n        // row-gap: 20px !important;\r\n\r\n        li {\r\n          margin: 0;\r\n          line-height: 1.8em;\r\n          color: #2d3748;\r\n\r\n          // 如果li中包含p标签，则设置行高为1\r\n          &:has(p) {\r\n            line-height: 1;\r\n          }\r\n\r\n          // 如果li中没有p标签，保持默认行高1.8em（已在上面设置）\r\n\r\n          p {\r\n            margin: 0;\r\n            line-height: 1.8em;\r\n          }\r\n        }\r\n      }\r\n\r\n      blockquote {\r\n        margin: 0.05em 0;\r\n        padding: 0.05em 0.4em;\r\n        color: #2c5282;\r\n        background: #ebf8ff;\r\n        border-left: 4px solid #4299e1;\r\n\r\n        p {\r\n          margin: 0.02em 0;\r\n          line-height: 1.8em;\r\n        }\r\n\r\n        > :first-child {\r\n          margin-top: 0;\r\n        }\r\n\r\n        > :last-child {\r\n          margin-bottom: 0;\r\n        }\r\n      }\r\n\r\n      code {\r\n        padding: 0.05em 0.1em;\r\n        margin: 0;\r\n        font-size: 0.9em;\r\n        background: #edf2f7;\r\n        border-radius: 3px;\r\n        color: #2d3748;\r\n      }\r\n\r\n      hr {\r\n        height: 1px;\r\n        margin: 0.1em 0;\r\n        border: none;\r\n        background-color: #e2e8f0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.chat-messages {\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background-color: rgba(192, 196, 204, 0.5);\r\n    border-radius: 3px;\r\n\r\n    &:hover {\r\n      background-color: rgba(192, 196, 204, 0.8);\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes thinking {\r\n  0%,\r\n  80%,\r\n  100% {\r\n    transform: scale(0);\r\n  }\r\n  40% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n// 修改弹窗样式\r\n::v-deep .ai-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 15px 20px;\r\n    background: #fff;\r\n    border-top: 1px solid #e4e7ed;\r\n\r\n    .el-button {\r\n      padding: 9px 20px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style>\r\n.title_Article {\r\n  color: rgb(8, 8, 8);\r\n  font-size: 15px;\r\n}\r\n\r\n.title_Article:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA05DA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AACA,IAAAM,UAAA,GAAAN,OAAA;AACA,IAAAO,MAAA,GAAAP,OAAA;AACA,IAAAQ,KAAA,GAAAR,OAAA;AACA,IAAAS,qBAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,GAAA,GAAAV,OAAA;AACA,IAAAW,OAAA,GAAAX,OAAA;AACA,IAAAY,OAAA,GAAAZ,OAAA;AACA,IAAAa,eAAA,GAAAb,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAc,KAAA;IACAC,YAAA;MACA,UAAAC,QAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACA,UAAAJ,QAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAE,QAAA;MACA,UAAAC,QAAA;MACAL,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAI,MAAA;MACAN,IAAA,EAAAO,MAAA;MACAL,OAAA;IACA;IACAM,WAAA;MACAH,QAAA;MACAH,OAAA;IACA;IACAO,QAAA;MACAJ,QAAA;MACAH,OAAA;IACA;IACAQ,KAAA;MACAL,QAAA;MACAH,OAAA;IACA;IACAS,WAAA;MACAZ,QAAA;MACAG,OAAA;IACA;IACAU,IAAA;MACAb,QAAA;IACA;IACAc,SAAA;MACAd,QAAA;IACA;IACAe,QAAA;MACAd,IAAA,EAAAe,MAAA;MACAb,OAAA;IACA;IACA;IACAc,UAAA;MACAd,OAAA;IACA;EACA;EACAe,UAAA;IACAC,oBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,QAAA;MACAC,aAAA;MACAC,aAAA;MACAC,aAAA;MACAC,OAAA;MACAC,IAAA;MACAC,IAAA;MACAC,IAAA;MACAC,OAAA;MACAC,eAAA;MACAC,KAAA;MACAC,QAAA;MACA;MACAC,SAAA;MACAC,cAAA;QACAC,GAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACAC,OAAA;MACAC,QAAA;MACAC,OAAA;MACAC,QAAA;MACAL,QAAA;MACAM,GAAA;MACAC,KAAA;MACAC,MAAA;MACAC,UAAA;MACAC,MAAA;MACAC,kBAAA;MACAC,IAAA;MACAC,cAAA;MAAA;MACAC,WAAA;MAAA;MACAC,eAAA;MACAC,IAAA;MAAA;MACAC,KAAA;QACA;QACAC,KAAA;UAAAxD,QAAA;UAAAyD,OAAA;QAAA;QACAC,OAAA;UAAA1D,QAAA;UAAAyD,OAAA;QAAA;QACAE,WAAA;UAAA3D,QAAA;UAAAyD,OAAA;QAAA;QACAG,OAAA;UAAA5D,QAAA;UAAAyD,OAAA;QAAA;QACAxC,UAAA;UAAAjB,QAAA;UAAAyD,OAAA;QAAA;QACAI,WAAA;UAAA7D,QAAA;UAAAyD,OAAA;QAAA;QACAK,OAAA;UAAA9D,QAAA;UAAAyD,OAAA;QAAA;QACA;QACAM,EAAA;UAAA/D,QAAA;UAAAyD,OAAA;QAAA;MACA;MACAO,aAAA;QACAC,WAAA;QACA;QACAC,OAAA;UACAC,aAAA,kBAAAC,cAAA;UACAC,WAAA;QACA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACAC,QAAA;MACAC,WAAA;MACAC,UAAA,EACAL,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAI,WAAA;MACA;MACAC,kBAAA;MACAC,gBAAA;MACA;MACAC,kBAAA;MACAC,cAAA;MACA;MACAC,eAAA;MACAC,YAAA;MACAC,UAAA;MACAC,UAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,eAAA;QACAC,GAAA;QACAC,MAAA;QACAC,SAAA;QACAC,MAAA;QACAC,YAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,KAAA;MACA;MACAC,YAAA;MAAA;MACAC,SAAA;MAAA;MACAC,aAAA;MAAA;MACAC,UAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA;EACAC,KAAA;IACA/E,aAAA,WAAAA,cAAAgF,MAAA,EAAAC,MAAA;MAAA,IAAAC,KAAA;MACA,IAAAF,MAAA;QACAG,cAAA,CAAAC,WAAA;UAAA5F,UAAA,OAAAA;QAAA,GAAA6F,IAAA,WAAA1F,IAAA;UACA,IAAAA,IAAA,CAAA2F,IAAA;YACAJ,KAAA,CAAAnF,aAAA,GAAAJ,IAAA,CAAAA,IAAA;UACA;YACAuF,KAAA,CAAAK,QAAA;cAAAvD,OAAA;cAAAxD,IAAA;YAAA;UACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACAgH,OAAA,WAAAA,QAAAR,MAAA,EAAAC,MAAA;QACA,KAAAQ,OAAA;MACA;MACAC,IAAA;IACA;IACA;MACAF,OAAA,WAAAA,QAAAR,MAAA,EAAAC,MAAA;QACA,KAAArD,eAAA,QAAAD,WAAA,CAAAgE,MAAA,WAAAC,IAAA;UACA,OAAAA,IAAA,CAAApH,IAAA,IAAAwG,MAAA;QACA;MACA;MACAU,IAAA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,IACA,KAAA3G,IAAA,qBACA,KAAAA,IAAA,4BACA,KAAAA,IAAA,eACA;MACA,KAAA4G,UAAA;IACA;IACA,SAAA5G,IAAA;MACA,IAAA6G,yBAAA,IAAAZ,IAAA,WAAAa,GAAA;QACAH,MAAA,CAAArE,cAAA,GAAAwE,GAAA,CAAAvG,IAAA;MACA;MACAwF,cAAA,CAAAgB,aAAA,GAAAd,IAAA,WAAA1F,IAAA;QACA,IAAAA,IAAA,CAAA2F,IAAA;UACAS,MAAA,CAAApE,WAAA,GAAAhC,IAAA,CAAAA,IAAA;QACA;MACA;IACA;IACA,SAAAyG,MAAA,CAAAC,KAAA,CAAAvF,MAAA;MACA,IAAAwF,oBAAA,qBAAAjB,IAAA,WAAAa,GAAA;QACA,IAAAA,GAAA,CAAAZ,IAAA;UACAS,MAAA,CAAAnB,UAAA,GAAAsB,GAAA,CAAAK,GAAA;QACA;MACA;MACA,IAAAD,oBAAA,6BAAAjB,IAAA,WAAAa,GAAA;QACA,IAAAA,GAAA,CAAAZ,IAAA;UACAS,MAAA,CAAAlB,eAAA,GAAAqB,GAAA,CAAAK,GAAA;QACA;MACA;MACA;MACA,KAAA3C,UAAA,QAAA4C,MAAA,CAAAC,OAAA,CAAAC,MAAA;IACA;IAEA,KAAAtD,WAAA;EACA;EACAuD,OAAA,WAAAA,QAAA;EACAC,OAAA;EACAC,OAAA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,KAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,KAAA;IACA;IACA;IACAE,cAAA,WAAAA,eAAArB,IAAA;MACA,IAAAsB,SAAA,GAAAtB,IAAA,CAAAsB,SAAA;MACA,IAAA7E,OAAA,GAAAuD,IAAA,CAAAvD,OAAA;MAEA,IAAA8E,kBAAA,GAAAD,SAAA,GACAA,SAAA,CACAE,OAAA,6DACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAC,IAAA,KACA;MAEA,IAAAC,gBAAA,GAAAjF,OAAA,GACAA,OAAA,CACA+E,OAAA,6DACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAC,IAAA,KACA;MAEA,OAAAF,kBAAA,IAAAG,gBAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAArF,WAAA,EAAAsF,iBAAA;MACA;MACA,IAAAC,oBAAA,QAAAC,SAAA,CAAAxF,WAAA;;MAEA;MACA,KAAAsF,iBAAA;QACA,gBAAAC,oBAAA;MACA;MAEA,IAAAE,mBAAA;MACA;MACA,IAAAH,iBAAA;QACA;QACA,IAAAA,iBAAA,CAAAI,QAAA;UACA,IAAAC,SAAA,GAAAL,iBAAA,CAAAM,KAAA;UACA,IAAAD,SAAA;YACA,IAAAE,IAAA,GAAAF,SAAA;YACA,IAAAG,KAAA,GAAAH,SAAA;YACA,IAAAI,GAAA,GAAAJ,SAAA;YACAF,mBAAA,MAAAO,MAAA,CAAAH,IAAA,OAAAG,MAAA,CAAAF,KAAA,OAAAE,MAAA,CAAAD,GAAA;UACA;YACAN,mBAAA,GAAAH,iBAAA;UACA;QACA;QACA;QAAA,KACA,IACAA,iBAAA,CAAAI,QAAA,SACAJ,iBAAA,CAAAI,QAAA,SACAJ,iBAAA,CAAAI,QAAA,OACA;UACA,IAAAC,UAAA,GAAAL,iBAAA,CAAAM,KAAA,CACA,8BACA;UACA,IAAAD,UAAA;YACA,IAAAE,KAAA,GAAAF,UAAA;YACA,IAAAG,MAAA,GAAAH,UAAA,IAAAM,QAAA;YACA,IAAAF,IAAA,GAAAJ,UAAA,IAAAM,QAAA;YACAR,mBAAA,MAAAO,MAAA,CAAAH,KAAA,OAAAG,MAAA,CAAAF,MAAA,OAAAE,MAAA,CAAAD,IAAA;UACA;YACAN,mBAAA,GAAAH,iBAAA;UACA;QACA;QACA;QAAA,KACA,IACAA,iBAAA,CAAAI,QAAA,SACAJ,iBAAA,CAAAI,QAAA,OACA;UACA,IAAAC,WAAA,GAAAL,iBAAA,CAAAM,KAAA,CACA,6BACA;UACA,IAAAD,WAAA;YACA,IAAAE,MAAA,GAAAF,WAAA;YACA,IAAAG,OAAA,GAAAH,WAAA,IAAAM,QAAA;YACA,IAAAF,KAAA,GAAAJ,WAAA,IAAAM,QAAA;YACAR,mBAAA,MAAAO,MAAA,CAAAH,MAAA,OAAAG,MAAA,CAAAF,OAAA,OAAAE,MAAA,CAAAD,KAAA;UACA;YACAN,mBAAA,GAAAH,iBAAA;UACA;QACA;QACA;QAAA,KACA,IAAAA,iBAAA,CAAAI,QAAA;UACA,IAAAC,WAAA,GAAAL,iBAAA,CAAAM,KAAA,CACA,+BACA;UACA,IAAAD,WAAA;YACA,IAAAE,MAAA,GAAAF,WAAA;YACA,IAAAG,OAAA,GAAAH,WAAA,IAAAM,QAAA;YACA,IAAAF,KAAA,GAAAJ,WAAA,IAAAM,QAAA;YACAR,mBAAA,MAAAO,MAAA,CAAAH,MAAA,OAAAG,MAAA,CAAAF,OAAA,OAAAE,MAAA,CAAAD,KAAA;UACA;YACAN,mBAAA,GAAAH,iBAAA;UACA;QACA;UACA;UACAG,mBAAA,GAAAH,iBAAA;QACA;MACA;;MAEA;MACA,IAAAC,oBAAA,KAAAE,mBAAA;QACA,gBAAAF,oBAAA;MACA;QACA,wBAAAS,MAAA,CAAAT,oBAAA,uBAAAS,MAAA,CAAAV,iBAAA;MACA;IACA;IAEA;IACAY,gBAAA,WAAAA,iBAAAhI,IAAA;MACA,KAAAA,IAAA;QACA;MACA;MACA;MACA,IAAAiI,kBAAA,GAAAjI,IAAA,CAAAgH,OAAA;MACA;MACA,kCAAAkB,IAAA,CAAAD,kBAAA;IACA;IACA;IACAE,WAAA,WAAAA,YAAAC,GAAA;MACA,KAAAA,GAAA,UAAAlJ,QAAA;QACA,OAAAkJ,GAAA;MACA;MAEA,IAAAC,MAAA,GAAAD,GAAA;MACA,IAAAlJ,QAAA;;MAEA;MACA,SAAAA,QAAA,CAAAsI,QAAA;QACAtI,QAAA,QAAAA,QAAA,CAAAoJ,KAAA;MACA,gBAAApJ,QAAA,CAAAsI,QAAA;QACAtI,QAAA,QAAAA,QAAA,CAAAoJ,KAAA;MACA;QACApJ,QAAA,SAAAA,QAAA;MACA;;MAEA;MACAA,QAAA,GAAAA,QAAA,CAAAqG,MAAA,CACA,UAAAgD,OAAA;QAAA,OAAAA,OAAA,IAAAA,OAAA,CAAAtB,IAAA,GAAAuB,MAAA;MAAA,CACA;MAEAtJ,QAAA,CAAAuJ,OAAA,WAAAC,OAAA;QACA,IAAAC,cAAA,GAAAD,OAAA,CAAAzB,IAAA;QACA,IAAA0B,cAAA,CAAAH,MAAA;UACA;UACA,IAAAI,cAAA,GAAAD,cAAA,CAAA3B,OAAA,CACA,uBACA,MACA;;UAEA;UACA,IAAA6B,aAAA,sDAAAf,MAAA,CAAAa,cAAA;;UAEA;UACA,IAAAG,YAAA;UACA,IAAAC,KAAA;UACA,IAAAC,SAAA;UACA,IAAAtB,KAAA;;UAEA;UACA,QAAAA,KAAA,GAAAoB,YAAA,CAAAG,IAAA,CAAAZ,MAAA;YACA;YACA,IAAAX,KAAA,CAAAwB,KAAA,GAAAF,SAAA;cACAD,KAAA,CAAAI,IAAA;gBACA/K,IAAA;gBACAyD,OAAA,EAAAwG,MAAA,CAAAe,SAAA,CAAAJ,SAAA,EAAAtB,KAAA,CAAAwB,KAAA;cACA;YACA;YACA;YACAH,KAAA,CAAAI,IAAA;cACA/K,IAAA;cACAyD,OAAA,EAAA6F,KAAA;YACA;YACAsB,SAAA,GAAAtB,KAAA,CAAAwB,KAAA,GAAAxB,KAAA,IAAAc,MAAA;UACA;;UAEA;UACA,IAAAQ,SAAA,GAAAX,MAAA,CAAAG,MAAA;YACAO,KAAA,CAAAI,IAAA;cACA/K,IAAA;cACAyD,OAAA,EAAAwG,MAAA,CAAAe,SAAA,CAAAJ,SAAA;YACA;UACA;;UAEA;UACA,IAAAD,KAAA,CAAAP,MAAA;YACAO,KAAA,CAAAI,IAAA;cACA/K,IAAA;cACAyD,OAAA,EAAAwG;YACA;UACA;;UAEA;UACAU,KAAA,CAAAN,OAAA,WAAAY,IAAA;YACA,IAAAA,IAAA,CAAAjL,IAAA;cACA;cACA,IAAAkL,KAAA,OAAAC,MAAA,CAAAX,cAAA;cACAS,IAAA,CAAAxH,OAAA,GAAAwH,IAAA,CAAAxH,OAAA,CAAAmF,OAAA,CAAAsC,KAAA,EAAAT,aAAA;YACA;UACA;;UAEA;UACAR,MAAA,GAAAU,KAAA,CAAAS,GAAA,WAAAH,IAAA;YAAA,OAAAA,IAAA,CAAAxH,OAAA;UAAA,GAAA4H,IAAA;QACA;MACA;MAEA,OAAApB,MAAA;IACA;IACA,aACAqB,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAA,MACAT,MAAA,CAAA9J,aAAA,CAAA2I,MAAA;gBAAA0B,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAT,MAAA,CAAAxE,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cAAA,OAAA8L,QAAA,CAAAG,MAAA;YAAA;cAAA,MAIAV,MAAA,CAAA3K,IAAA;gBAAAkL,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACArF,cAAA,CAAA2E,aAAA,CAAAC,MAAA,CAAA9J,aAAA,EAAAoF,IAAA,WAAAqF,QAAA;gBACA,IAAAC,CAAA,GAAAC,QAAA,CAAAC,aAAA;gBACAF,CAAA,CAAAG,IAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAP,QAAA;gBACAC,CAAA,CAAAO,QAAA,aAAAhD,MAAA,KAAAiD,IAAA,GAAAC,OAAA;gBACAT,CAAA,CAAAU,KAAA;cACA;cAAAf,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OAEArF,cAAA,CAAAmG,mBAAA,CAAAvB,MAAA,CAAA9J,aAAA,EAAAoF,IAAA,WAAAqF,QAAA;gBACA,IAAAC,CAAA,GAAAC,QAAA,CAAAC,aAAA;gBACAF,CAAA,CAAAG,IAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAP,QAAA;gBACAC,CAAA,CAAAO,QAAA,aAAAhD,MAAA,KAAAiD,IAAA,GAAAC,OAAA;gBACAT,CAAA,CAAAU,KAAA;;gBAEA;cACA;YAAA;YAAA;cAAA,OAAAf,QAAA,CAAAiB,IAAA;UAAA;QAAA,GAAApB,OAAA;MAAA;IAEA;IACAqB,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,SAAAxL,aAAA,CAAA2I,MAAA;QACA,KAAArD,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;QACA;MACA;MACA,KAAAkN,QAAA,mBACArG,IAAA;QACAF,cAAA,CAAAwG,WAAA,CAAAF,MAAA,CAAAxL,aAAA,CAAA4J,IAAA,OAAAxE,IAAA,WAAAqF,QAAA;UACAe,MAAA,CAAAlG,QAAA;YAAAvD,OAAA;YAAAxD,IAAA;UAAA;UACAiN,MAAA,CAAAG,KAAA;UACAH,MAAA,CAAAxL,aAAA;QACA;MACA,GACA4L,KAAA;IACA;IACA,eACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,SAAA9L,aAAA,CAAA2I,MAAA;QACA,KAAArD,QAAA;UACAvD,OAAA;UACAxD,IAAA;QACA;QACA;MACA;MACA,KAAAkN,QAAA,0BACArG,IAAA;QACAF,cAAA,CAAA6G,kBAAA,CAAAD,MAAA,CAAA9L,aAAA,CAAA4J,IAAA,OAAAxE,IAAA;UACA0G,MAAA,CAAAxG,QAAA;YAAA/G,IAAA;YAAAwD,OAAA;UAAA;UACA+J,MAAA,CAAAH,KAAA;UACAG,MAAA,CAAA9L,aAAA;QACA;MACA,GACA4L,KAAA;IACA;IACA,YACAI,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,UAAA,SAAAC,KAAA,CAAAC,MAAA,CAAAC,SAAA;MACA,IAAAC,cAAA,GAAAC,WAAA;QACA,IAAAN,MAAA,CAAAE,KAAA,CAAAC,MAAA,CAAAC,SAAA;UACAJ,MAAA,CAAAE,KAAA,CAAAC,MAAA,CAAAI,QAAA,IAAAN,UAAA;QACA;UACAO,aAAA,CAAAH,cAAA;QACA;MACA;IACA;IACAI,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACAC,YAAA,MAAAzL,KAAA;MACA,KAAAA,KAAA,GAAA0L,UAAA;QACAF,MAAA,CAAAG,UAAA;MACA;MACA;MACA;IACA;IACAA,UAAA,WAAAA,WAAA;MACA;MACA;IAAA,CACA;IACA,QACAC,QAAA,WAAAA,SAAA;MACA,KAAAhN,aAAA;IACA;IACA,YACAiN,gBAAA,WAAAA,iBAAA9L,GAAA;MACA,KAAAyK,KAAA,qBAAAzK,GAAA;MACA,KAAA8K,UAAA;MACA,KAAA/L,OAAA;IACA;IACA,UACAgN,mBAAA,WAAAA,oBAAAC,OAAA;MACA,KAAAvB,KAAA,wBAAAuB,OAAA;MACA,KAAAlB,UAAA;MACA,KAAA/L,OAAA;IACA;IACA,QACAkN,OAAA,WAAAA,QAAAxH,IAAA;MAAA,IAAAyH,MAAA;MAAA,WAAArD,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAAoD,SAAA;QAAA,IAAApH,GAAA,EAAAqH,IAAA;QAAA,WAAAtD,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAAoD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlD,IAAA,GAAAkD,SAAA,CAAAjD,IAAA;YAAA;cACA;cACA,IAAA5E,IAAA,CAAA8H,EAAA;gBACAL,MAAA,CAAApN,aAAA,IAAA2F,IAAA,CAAA8H,EAAA;cACA;cACA;cAAA,MACAL,MAAA,CAAApN,aAAA,CAAA2I,MAAA;gBAAA6E,SAAA,CAAAjD,IAAA;gBAAA;cAAA;cACA6C,MAAA,CAAA9H,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cAAA,OAAAiP,SAAA,CAAAhD,MAAA;YAAA;cAAA,IAIA7E,IAAA,CAAA+H,SAAA;gBAAAF,SAAA,CAAAjD,IAAA;gBAAA;cAAA;cAAAiD,SAAA,CAAAjD,IAAA;cAAA,OACArF,cAAA,CAAAyI,UAAA,EAAAhI,IAAA,CAAA8H,EAAA;YAAA;cAAAxH,GAAA,GAAAuH,SAAA,CAAAI,IAAA;cAAA,KACA3H,GAAA,CAAAZ,IAAA;gBAAAmI,SAAA,CAAAjD,IAAA;gBAAA;cAAA;cACA6C,MAAA,CAAA9H,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;cACA6O,MAAA,CAAAzB,KAAA;cACAyB,MAAA,CAAApN,aAAA;cAAA,OAAAwN,SAAA,CAAAhD,MAAA;YAAA;cAGA4C,MAAA,CAAA9H,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cAAAiP,SAAA,CAAAjD,IAAA;cAAA;YAAA;cAAAiD,SAAA,CAAAjD,IAAA;cAAA,OAEArF,cAAA,CAAA2I,YAAA,EAAAlI,IAAA,CAAA8H,EAAA;YAAA;cAAAxH,IAAA,GAAAuH,SAAA,CAAAI,IAAA;cAAA,KACA3H,IAAA,CAAAZ,IAAA;gBAAAmI,SAAA,CAAAjD,IAAA;gBAAA;cAAA;cACA6C,MAAA,CAAA9H,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cACA6O,MAAA,CAAAzB,KAAA;cACAyB,MAAA,CAAApN,aAAA;cAAA,OAAAwN,SAAA,CAAAhD,MAAA;YAAA;cAGA4C,MAAA,CAAA9H,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;YAAA;YAAA;cAAA,OAAAiP,SAAA,CAAAlC,IAAA;UAAA;QAAA,GAAA+B,QAAA;MAAA;IAEA;IACA,UACAS,QAAA,WAAAA,SAAAnI,IAAA;MAAA,IAAAoI,MAAA;MACAC,SAAA,CAAAC,SAAA,CACAC,SAAA,CAAAvI,IAAA,CAAAzD,OAAA,EACAkD,IAAA;QACA2I,MAAA,CAAAzI,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;MACA,GACAqN,KAAA;QACAuC,KAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA1O,IAAA;MACA,IAAAA,IAAA;QACA;MACA,WAAAA,IAAA;QACA;MACA,WAAAA,IAAA;QACA;MACA;IACA;IACA,UACA2O,yBAAA,WAAAA,0BAAAvH,KAAA;MACA,KAAA9G,aAAA,GAAA8G,KAAA;IACA;IACA,QACAwH,oBAAA,WAAAA,qBAAAC,GAAA;MACA,KAAAvO,aAAA,GAAAuO,GAAA,QAAArP,WAAA,CAAAyK,GAAA,WAAAhE,IAAA;QAAA,OAAAA,IAAA,CAAA8H,EAAA;MAAA;MACA,KAAAnN,eAAA;IACA;IACA,QACAkF,OAAA,WAAAA,QAAA;MACA,KAAAmG,KAAA;IACA;IACA,YACA6C,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAA1E,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAAyE,SAAA;QAAA,IAAAC,WAAA,EAAA1I,GAAA;QAAA,WAAA+D,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAAyE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvE,IAAA,GAAAuE,SAAA,CAAAtE,IAAA;YAAA;cACAkE,OAAA,CAAA1O,aAAA;cACA4O,WAAA;cAAA,IACAF,OAAA,CAAA5O,QAAA;gBAAAgP,SAAA,CAAAtE,IAAA;gBAAA;cAAA;cAAA,OAAAsE,SAAA,CAAArE,MAAA,WACAiE,OAAA,CAAAnJ,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cAAA,KAEAkQ,OAAA,CAAAjO,QAAA,CAAAiN,EAAA;gBAAAoB,SAAA,CAAAtE,IAAA;gBAAA;cAAA;cACA;cACAoE,WAAA,CAAArF,IAAA;gBACAzJ,QAAA,EAAA4O,OAAA,CAAA5O,QAAA;gBACAiP,MAAA,EAAAL,OAAA,CAAAjO,QAAA,CAAAiN,EAAA;gBACAsB,MAAA,EAAAN,OAAA,CAAAjO,QAAA,CAAA6B;cACA;cAAAwM,SAAA,CAAAtE,IAAA;cAAA;YAAA;cAAA,MAGAkE,OAAA,CAAAzO,aAAA;gBAAA6O,SAAA,CAAAtE,IAAA;gBAAA;cAAA;cAAA,OAAAsE,SAAA,CAAArE,MAAA,WACAiE,OAAA,CAAAnJ,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cACAkQ,OAAA,CAAAzO,aAAA,CAAA4I,OAAA,WAAAjD,IAAA;gBACA,IAAAqJ,OAAA,GAAAP,OAAA,CAAAvP,WAAA,CAAAwG,MAAA,WAAAoB,KAAA;kBAAA,OAAAA,KAAA,CAAA2G,EAAA,IAAA9H,IAAA;gBAAA;gBACAgJ,WAAA,CAAArF,IAAA;kBACAzJ,QAAA,EAAA4O,OAAA,CAAA5O,QAAA;kBACAiP,MAAA,EAAAnJ,IAAA;kBACAoJ,MAAA,EAAAC,OAAA,IAAA3M;gBACA;cACA;YAAA;cAAAwM,SAAA,CAAAtE,IAAA;cAAA,OAEArF,cAAA,CAAA+J,SAAA,CAAAN,WAAA;YAAA;cAAA1I,GAAA,GAAA4I,SAAA,CAAAjB,IAAA;cACA,IAAA3H,GAAA,CAAAZ,IAAA;gBACAoJ,OAAA,CAAAnJ,QAAA;kBAAAvD,OAAA;kBAAAxD,IAAA;gBAAA;gBACAkQ,OAAA,CAAA9C,KAAA;cACA;gBACA8C,OAAA,CAAAnJ,QAAA;kBACAvD,OAAA;kBACAxD,IAAA;gBACA;cACA;cACAkQ,OAAA,CAAAjO,QAAA;cACAiO,OAAA,CAAA5O,QAAA;cACA4O,OAAA,CAAAzO,aAAA;cACAyO,OAAA,CAAAxO,OAAA;YAAA;YAAA;cAAA,OAAA4O,SAAA,CAAAvD,IAAA;UAAA;QAAA,GAAAoD,QAAA;MAAA;IACA;IACA,YACAQ,WAAA,WAAAA,YAAAvJ,IAAA;MACA,KAAA5F,aAAA;MACA,KAAAS,QAAA,GAAAmF,IAAA;IACA;IACA,WACAwJ,WAAA,WAAAA,YAAAxJ,IAAA,EAAAyJ,MAAA;MACA,IAAAA,MAAA;QACA,IAAAzJ,IAAA,CAAAxD,WAAA;UACA2I,MAAA,CAAAtJ,IAAA,CAAAmE,IAAA,CAAAxD,WAAA;UACA;QACA;QACA,KAAAmD,QAAA;UAAAvD,OAAA;QAAA;QACA;MACA;MACA+I,MAAA,CAAAtJ,IAAA,uBAAAyG,MAAA,CACAtC,IAAA,CAAA8H,EAAA,aAAAxF,MAAA,CAAAtC,IAAA,CAAA0J,KAAA,kBAAApH,MAAA,CAAAtC,IAAA,CAAApG,UAAA,GACA,QACA;MACA;MACA;IACA;IACA,WACA+P,UAAA,WAAAA,WAAA3J,IAAA;MACA,KAAAlF,SAAA;MACA,KAAAO,OAAA,GAAA2E,IAAA;MACA,IAAAA,IAAA,CAAA/E,QAAA;QACA,KAAAF,cAAA,CAAAE,QAAA,GAAA+E,IAAA,CAAA/E,QAAA,CACA6H,KAAA,MACAkB,GAAA,WAAAjK,IAAA;UAAA,OAAAZ,MAAA,CAAAY,IAAA;QAAA;MACA;MACA,IAAAiG,IAAA,CAAA9E,MAAA;QACA,KAAAH,cAAA,CAAAG,MAAA,GAAA8E,IAAA,CAAA9E,MAAA,CACA4H,KAAA,MACAkB,GAAA,WAAAjK,IAAA;UAAA,OAAAZ,MAAA,CAAAY,IAAA;QAAA;MACA;MACA,KAAAgB,cAAA,CAAAC,GAAA,GAAAgF,IAAA,CAAA4J,IAAA,GAAA5J,IAAA,CAAA4J,IAAA,CAAA9G,KAAA;IACA;IACA,aACA1C,UAAA,WAAAA,WAAA;MAAA,IAAAyJ,OAAA;MAAA,WAAAzF,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAAwF,SAAA;QAAA,WAAAzF,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAAuF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArF,IAAA,GAAAqF,SAAA,CAAApF,IAAA;YAAA;cAAAoF,SAAA,CAAApF,IAAA;cAAA,OACArF,cAAA,CAAAjE,QAAA,GAAAmE,IAAA,WAAA1F,IAAA;gBACA,IAAAA,IAAA,CAAA2F,IAAA;kBACAmK,OAAA,CAAAvO,QAAA,GAAAvB,IAAA,CAAAA,IAAA;kBACA8P,OAAA,CAAA1O,OAAA,GAAApB,IAAA,CAAAA,IAAA;kBACAwF,cAAA,CAAAtE,QAAA,GAAAwE,IAAA,WAAA0B,KAAA;oBACA0I,OAAA,CAAA5O,QAAA,GAAAkG,KAAA,CAAApH,IAAA;oBACA8P,OAAA,CAAAzO,QAAA,GAAA+F,KAAA,CAAApH,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAiQ,SAAA,CAAArE,IAAA;UAAA;QAAA,GAAAmE,QAAA;MAAA;IACA;IACA,UACAG,WAAA,WAAAA,YAAAxJ,KAAA;MACA,KAAAtF,OAAA,QAAAG,QAAA,CAAAyE,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAkK,SAAA,IAAAzJ,KAAA;MAAA;IACA;IACA,UACA0J,cAAA,WAAAA,eAAA1J,KAAA;MACA,KAAArF,QAAA,QAAAH,QAAA,CAAA8E,MAAA,CACA,UAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAoK,YAAA,IAAA3J,KAAA;MAAA,CACA;IACA;IACA4J,SAAA,WAAAA,UAAA;MAAA,IAAAC,OAAA;MAAA,WAAAlG,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAAiG,SAAA;QAAA,IAAAC,MAAA,EAAAlK,GAAA;QAAA,WAAA+D,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAAiG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/F,IAAA,GAAA+F,SAAA,CAAA9F,IAAA;YAAA;cACA4F,MAAA;gBACAtP,MAAA,EAAAvB,MAAA,CAAA2Q,OAAA,CAAAvP,cAAA,CAAAG,MAAA;gBACAD,QAAA,EAAAtB,MAAA,CAAA2Q,OAAA,CAAAvP,cAAA,CAAAE,QAAA;gBACA2O,IAAA,EAAAjQ,MAAA,CAAA2Q,OAAA,CAAAvP,cAAA,CAAAC,GAAA;gBACA2P,SAAA,EAAAhR,MAAA,CAAA2Q,OAAA,CAAAjP,OAAA,CAAAyM,EAAA;gBACA4B,KAAA,EAAAY,OAAA,CAAAjP,OAAA,CAAAqO,KAAA,GAAA/P,MAAA,CAAA2Q,OAAA,CAAAjP,OAAA,CAAAqO,KAAA;cACA;cAAAgB,SAAA,CAAA9F,IAAA;cAAA,OACArF,cAAA,CAAAqL,MAAA,CAAAJ,MAAA;YAAA;cAAAlK,GAAA,GAAAoK,SAAA,CAAAzC,IAAA;cACA,IAAA3H,GAAA,CAAAZ,IAAA;gBACA4K,OAAA,CAAA3K,QAAA;kBAAAvD,OAAA;kBAAAxD,IAAA;gBAAA;gBACAsO,UAAA;kBACAoD,OAAA,CAAAzK,OAAA;gBACA;cACA;gBACAyK,OAAA,CAAA3K,QAAA;kBAAAvD,OAAA;kBAAAxD,IAAA;gBAAA;cACA;cACA0R,OAAA,CAAAO,QAAA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAA/E,IAAA;UAAA;QAAA,GAAA4E,QAAA;MAAA;IACA;IACAM,QAAA,WAAAA,SAAA;MACA,KAAArE,KAAA,aAAAsE,WAAA;MACA,KAAA/P,cAAA;QACAC,GAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACA,KAAAJ,SAAA;IACA;IACAiQ,WAAA,WAAAA,YAAA/K,IAAA;MAAA,IAAAgL,OAAA;MAAA,WAAA5G,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAA2G,SAAA;QAAA,IAAAC,SAAA,EAAA5K,GAAA;QAAA,WAAA+D,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAA2G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzG,IAAA,GAAAyG,SAAA,CAAAxG,IAAA;YAAA;cACAsG,SAAA,GAAAG,IAAA,CAAAC,KAAA,CAAAtL,IAAA,CAAAkL,SAAA;cAAAE,SAAA,CAAAxG,IAAA;cAAA,OACArF,cAAA,CAAAqL,MAAA;gBACAD,SAAA,EAAA3K,IAAA,CAAA8H,EAAA;gBACAoD,SAAA,IAAArS,OAAA,CAAAqS,SAAA;cACA;YAAA;cAHA5K,GAAA,GAAA8K,SAAA,CAAAnD,IAAA;cAIA,IAAA3H,GAAA,CAAAZ,IAAA;gBACAsL,OAAA,CAAArL,QAAA;kBAAAvD,OAAA;kBAAAxD,IAAA;gBAAA;gBACAoS,OAAA,CAAAnL,OAAA;cACA;gBACAmL,OAAA,CAAArL,QAAA;kBAAAvD,OAAA;kBAAAxD,IAAA;gBAAA;cACA;YAAA;YAAA;cAAA,OAAAwS,SAAA,CAAAzF,IAAA;UAAA;QAAA,GAAAsF,QAAA;MAAA;IACA;IACAM,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,WAAApH,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAAmH,SAAA;QAAA,IAAA/B,KAAA;QAAA,WAAArF,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAAkH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhH,IAAA,GAAAgH,SAAA,CAAA/G,IAAA;YAAA;cACA8E,KAAA,GAAA8B,OAAA,CAAA9P,UAAA,CAAAgO,KAAA;cAAAiC,SAAA,CAAA/G,IAAA;cAAA,OACArF,cAAA,CAAAqM,QAAA,CAAAJ,OAAA,CAAA9P,UAAA,CAAAoM,EAAA,EAAArI,IAAA,WAAAa,GAAA;gBACA,IAAAA,GAAA,CAAAZ,IAAA;kBACA8L,OAAA,CAAA9P,UAAA,GAAA4E,GAAA,CAAAvG,IAAA;kBACAyR,OAAA,CAAA9P,UAAA,CAAAgO,KAAA,GAAAA,KAAA;kBACA;kBACA8B,OAAA,CAAA5P,kBAAA,IAAA4P,OAAA,CAAA9P,UAAA,CAAAmQ,SAAA;kBACA,IAAAL,OAAA,CAAA9P,UAAA,CAAAmQ,SAAA,IAAAL,OAAA,CAAA9P,UAAA,CAAAW,OAAA;oBACAmP,OAAA,CAAA9P,UAAA,CAAAmQ,SAAA,IACAL,OAAA,CAAA9P,UAAA,CAAAmQ,SAAA,IAAAL,OAAA,CAAA9P,UAAA,CAAAW,OAAA,EACAmF,OAAA,mBAAAuD,CAAA,EAAA+G,CAAA,EAAAC,CAAA;sBACA;oBACA;oBACAP,OAAA,CAAA9P,UAAA,CAAAmQ,SAAA,IACAL,OAAA,CAAA9P,UAAA,CAAAmQ,SAAA,IAAAL,OAAA,CAAA9P,UAAA,CAAAW,OAAA,EACAmF,OAAA;oBACAgK,OAAA,CAAA9P,UAAA,CAAAmQ,SAAA,IACAL,OAAA,CAAA9P,UAAA,CAAAmQ,SAAA,IAAAL,OAAA,CAAA9P,UAAA,CAAAW,OAAA,EACAmF,OAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAmK,SAAA,CAAAhG,IAAA;UAAA;QAAA,GAAA8F,QAAA;MAAA;IACA;IACA,YACAO,cAAA,WAAAA,eAAAhM,IAAA;MAAA,IAAAiM,OAAA;MACA,IAAAC,GAAA;QACAtJ,GAAA;MACA,IAAA5C,IAAA,CAAA/E,QAAA;QACAiR,GAAA,GAAAlM,IAAA,CAAA/E,QAAA,CAAA6H,KAAA;MACA;MACAoJ,GAAA,CAAAjJ,OAAA,WAAAlJ,IAAA;QACAkS,OAAA,CAAAhR,QAAA,CAAA+I,GAAA,WAAAmI,GAAA;UACA,IAAAA,GAAA,CAAArE,EAAA,IAAA/N,IAAA;YACA,IAAA6I,GAAA,IAAAwJ,SAAA;cACAxJ,GAAA;YACA;YACAA,GAAA,IAAAuJ,GAAA,CAAA/B,YAAA;UACA;QACA;MACA;MACA,OAAAxH,GAAA;IACA;IACAyJ,YAAA,WAAAA,aAAArM,IAAA;MAAA,IAAAsM,OAAA;MACA,IAAAJ,GAAA;QACAtJ,GAAA;MACA,IAAA5C,IAAA,CAAA9E,MAAA;QACAgR,GAAA,GAAAlM,IAAA,CAAA9E,MAAA,CAAA4H,KAAA;MACA;MACAoJ,GAAA,CAAAjJ,OAAA,WAAAlJ,IAAA;QACAuS,OAAA,CAAAhR,QAAA,CAAA0I,GAAA,WAAAmI,GAAA;UACA,IAAAA,GAAA,CAAArE,EAAA,IAAA/N,IAAA;YACA,IAAA6I,GAAA,IAAAwJ,SAAA;cACAxJ,GAAA;YACA;YACAA,GAAA,IAAAuJ,GAAA,CAAAjC,SAAA;UACA;QACA;MACA;MACA,OAAAtH,GAAA;IACA;IACA,UACA2J,WAAA,WAAAA,YAAAvM,IAAA;MAAA,IAAAwM,OAAA;MACA,IAAAxM,IAAA,8BAAA3F,aAAA,CAAA2I,MAAA;QACA,KAAArD,QAAA,CAAA8M,OAAA;QACA;MACA;MACA,IAAAP,GAAA;MACA,IAAAQ,SAAA;MACA,IAAAzP,GAAA;MACA,IAAA+C,IAAA;QACAkM,GAAA,SAAAxQ,UAAA,CAAAoM,EAAA;QACA,SAAApM,UAAA,CAAAiR,WAAA,EAAAD,SAAA;QACAzP,GAAA,QAAAvB,UAAA,CAAAiR,WAAA;MACA,WAAA3M,IAAA;QACAkM,GAAA,QAAA7R,aAAA;MACA;QACA6R,GAAA,IAAAlM,IAAA,CAAA8H,EAAA;QACA,IAAA9H,IAAA,CAAA2M,WAAA,EAAAD,SAAA;QACAzP,GAAA,GAAA+C,IAAA,CAAA2M,WAAA;MACA;MACA,IAAAD,SAAA;QACA,SAAAlT,IAAA;UACA+F,cAAA,CAAAqN,gBAAA,CAAAV,GAAA,EACAzM,IAAA,WAAAqF,QAAA;YACA,IAAAA,QAAA,CAAApF,IAAA;cACA8M,OAAA,CAAAK,OAAA,KAAAC,gBAAA,CAAAhU,OAAA,MAAAgU,gBAAA,CAAAhU,OAAA;gBACAqD,KAAA;gBACAC,OAAA;gBACA2Q,gBAAA;gBACAC,iBAAA;gBACAC,gBAAA;cAAA,uBACA,uBACA,SAAAC,YAAAC,MAAA,EAAAC,QAAA,EAAAC,IAAA;gBACAA,IAAA;cACA,EACA;YACA;cACAb,OAAA,CAAA7M,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YACA;UACA,GACAqN,KAAA,WAAAqH,GAAA;QACA;UACA/N,cAAA,CAAAgO,mBAAA,CAAArB,GAAA,EACAzM,IAAA,WAAAqF,QAAA;YACA,IAAAA,QAAA,CAAApF,IAAA;cACA8M,OAAA,CAAAK,OAAA,KAAAC,gBAAA,CAAAhU,OAAA,MAAAgU,gBAAA,CAAAhU,OAAA;gBACAqD,KAAA;gBACAC,OAAA;gBACA2Q,gBAAA;gBACAC,iBAAA;gBACAC,gBAAA;cAAA,uBACA,uBACA,SAAAC,YAAAC,MAAA,EAAAC,QAAA,EAAAC,IAAA;gBACAA,IAAA;cACA,EACA;YACA;cACAb,OAAA,CAAA7M,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YACA;UACA,GACAqN,KAAA,WAAAqH,GAAA;QACA;MACA;QACArQ,GAAA,GAAAA,GAAA,CAAAuE,OAAA,KAAAuC,MAAA;QACA9G,GAAA,GAAAA,GAAA,CAAAuE,OAAA,KAAAuC,MAAA;QACAoB,MAAA,CAAAtJ,IAAA,CAAAsJ,MAAA,CAAAqI,QAAA,CAAAC,MAAA,GAAAxQ,GAAA;MACA;IACA;IACA,UACAyQ,gBAAA,WAAAA,iBAAA1N,IAAA;MAAA,IAAA2N,OAAA;MAAA,WAAAvJ,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAAsJ,SAAA;QAAA,IAAAC,IAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,KAAA;QAAA,WAAA3J,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAAyJ,UAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvJ,IAAA,GAAAuJ,UAAA,CAAAtJ,IAAA;YAAA;cACA+I,OAAA,CAAA3T,OAAA;cAAA,KACAgG,IAAA,CAAAmO,OAAA;gBAAAD,UAAA,CAAAtJ,IAAA;gBAAA;cAAA;cACAiJ,IAAA,GAAA7N,IAAA,CAAAmO,OAAA,CAAArL,KAAA;cAAAgL,SAAA,OAAAM,2BAAA,CAAAtV,OAAA,EACA+U,IAAA,CAAAQ,OAAA;cAAAH,UAAA,CAAAvJ,IAAA;cAAAqJ,KAAA,oBAAA3J,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAA0J,MAAA;gBAAA,IAAAM,WAAA,EAAA5K,KAAA,EAAAzG,GAAA;gBAAA,WAAAoH,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAA+J,OAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAA7J,IAAA,GAAA6J,SAAA,CAAA5J,IAAA;oBAAA;sBAAA0J,WAAA,OAAAG,eAAA,CAAA3V,OAAA,EAAAiV,KAAA,CAAA5M,KAAA,MAAAuC,KAAA,GAAA4K,WAAA,KAAArR,GAAA,GAAAqR,WAAA;sBACA,IAAArR,GAAA,CAAAyR,OAAA;wBACAxH,UAAA,kBAAA9C,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAAqK,SAAA;0BAAA,WAAAtK,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAAoK,UAAAC,SAAA;4BAAA,kBAAAA,SAAA,CAAAlK,IAAA,GAAAkK,SAAA,CAAAjK,IAAA;8BAAA;gCAAAiK,SAAA,CAAAjK,IAAA;gCAAA,OACA+I,OAAA,CAAAmB,WAAA,CAAA7R,GAAA,EAAAyG,KAAA,EAAA1D,IAAA,CAAAzD,OAAA,IAAAyD,IAAA,CAAA7D,KAAA;8BAAA;8BAAA;gCAAA,OAAA0S,SAAA,CAAAlJ,IAAA;4BAAA;0BAAA,GAAAgJ,QAAA;wBAAA,CACA,IAAAjL,KAAA;sBACA;wBACAiK,OAAA,CAAAhO,QAAA,CAAAoP,KAAA;sBACA;oBAAA;oBAAA;sBAAA,OAAAP,SAAA,CAAA7I,IAAA;kBAAA;gBAAA,GAAAqI,KAAA;cAAA;cAAAF,SAAA,CAAAkB,CAAA;YAAA;cAAA,KAAAjB,KAAA,GAAAD,SAAA,CAAAmB,CAAA,IAAA5B,IAAA;gBAAAa,UAAA,CAAAtJ,IAAA;gBAAA;cAAA;cAAA,OAAAsJ,UAAA,CAAAgB,aAAA,CAAAlB,KAAA;YAAA;cAAAE,UAAA,CAAAtJ,IAAA;cAAA;YAAA;cAAAsJ,UAAA,CAAAtJ,IAAA;cAAA;YAAA;cAAAsJ,UAAA,CAAAvJ,IAAA;cAAAuJ,UAAA,CAAAiB,EAAA,GAAAjB,UAAA;cAAAJ,SAAA,CAAAsB,CAAA,CAAAlB,UAAA,CAAAiB,EAAA;YAAA;cAAAjB,UAAA,CAAAvJ,IAAA;cAAAmJ,SAAA,CAAAuB,CAAA;cAAA,OAAAnB,UAAA,CAAAoB,MAAA;YAAA;cAGA3B,OAAA,CAAA3T,OAAA;YAAA;YAAA;cAAA,OAAAkU,UAAA,CAAAvI,IAAA;UAAA;QAAA,GAAAiI,QAAA;MAAA;IACA;IAEAkB,WAAA,WAAAA,YAAA7R,GAAA,EAAAyG,KAAA,EAAAvH,KAAA;MAAA,IAAAoT,OAAA;MAAA,WAAAnL,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAAkL,UAAA;QAAA,IAAAC,QAAA,EAAA3K,QAAA,EAAA4K,MAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,QAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,MAAA;QAAA,WAAA3L,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAAyL,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvL,IAAA,GAAAuL,UAAA,CAAAtL,IAAA;YAAA;cACA6K,QAAA,OAAAU,QAAA;cACAV,QAAA,CAAAW,MAAA,YAAAnT,GAAA;cAAAiT,UAAA,CAAAvL,IAAA;cAAAuL,UAAA,CAAAtL,IAAA;cAAA,OAGArF,cAAA,CAAA8Q,YAAA,CAAAZ,QAAA;YAAA;cAAA3K,QAAA,GAAAoL,UAAA,CAAAjI,IAAA;cACAyH,MAAA,OAAAY,mBAAA,EAAAxL,QAAA;cAAA,KAEA4K,MAAA;gBAAAQ,UAAA,CAAAtL,IAAA;gBAAA;cAAA;cACA+K,IAAA,OAAAY,IAAA,EAAAzL,QAAA;cACA8K,IAAA,GAAA3S,GAAA,CAAA6F,KAAA;cACA+M,QAAA,GAAAD,IAAA,CAAAA,IAAA,CAAA5M,MAAA;cACA,IAAAwN,iBAAA,EAAAb,IAAA,EAAAE,QAAA;cAAAK,UAAA,CAAAtL,IAAA;cAAA;YAAA;cAAAsL,UAAA,CAAAtL,IAAA;cAAA,OAEAE,QAAA,CAAAtK,IAAA;YAAA;cAAAsV,OAAA,GAAAI,UAAA,CAAAjI,IAAA;cACA8H,MAAA,GAAA1E,IAAA,CAAAC,KAAA,CAAAwE,OAAA;cACAE,MAAA,GACAS,SAAA,CAAAV,MAAA,CAAArQ,IAAA,KAAAqQ,MAAA,CAAApP,GAAA,IAAA8P,SAAA;cACAlB,OAAA,CAAA5P,QAAA,CAAAoP,KAAA,CAAAiB,MAAA;YAAA;cAAAE,UAAA,CAAAtL,IAAA;cAAA;YAAA;cAAAsL,UAAA,CAAAvL,IAAA;cAAAuL,UAAA,CAAAQ,EAAA,GAAAR,UAAA;YAAA;cAAAA,UAAA,CAAAvL,IAAA;cAKA;cACA4K,OAAA,CAAAvV,OAAA;cAAA,OAAAkW,UAAA,CAAAZ,MAAA;YAAA;YAAA;cAAA,OAAAY,UAAA,CAAAvK,IAAA;UAAA;QAAA,GAAA6J,SAAA;MAAA;IAoCA;IACA;IACAmB,cAAA,WAAAA,eAAAC,GAAA;MAAA,IAAAC,OAAA;MACA,IAAA7W,OAAA,QAAA8W,QAAA;QACAC,IAAA;QACAvW,IAAA;QACAwW,OAAA;QACAC,UAAA;MACA;MACA1R,cAAA,CAAA2R,gBAAA;QACAC,YAAA,EAAAP,GAAA,CAAAzU,KAAA;QACAuN,KAAA,EAAAkH,GAAA,CAAAlH,KAAA;QACA5B,EAAA,EAAA8I,GAAA,CAAA9I,EAAA;QACAsJ,gBAAA;QACAC,eAAA;MACA,GACA5R,IAAA,WAAAa,GAAA;QACAuQ,OAAA,CAAAnV,UAAA,CAAAa,OAAA,GAAA+D,GAAA,CAAAvG,IAAA;QACA8W,OAAA,CAAAtX,WAAA,CACAsX,OAAA,CAAAtX,WAAA,CAAA+X,SAAA,WAAAnQ,KAAA;UAAA,OAAAA,KAAA,CAAA2G,EAAA,IAAA8I,GAAA,CAAA9I,EAAA;QAAA,GACA,CAAAvL,OAAA,GAAA+D,GAAA,CAAAvG,IAAA;QACA8W,OAAA,CAAAtX,WAAA,CACAsX,OAAA,CAAAtX,WAAA,CAAA+X,SAAA,WAAAnQ,KAAA;UAAA,OAAAA,KAAA,CAAA2G,EAAA,IAAA8I,GAAA,CAAA9I,EAAA;QAAA,GACA,CAAAyJ,YAAA;QACAvX,OAAA,CAAAwX,KAAA;MACA,GACAvL,KAAA,WAAAqH,GAAA;QACAtT,OAAA,CAAAwX,KAAA;MACA;IACA;IACA;IACAC,cAAA,WAAAA,eAAAb,GAAA;MAAA,IAAAc,OAAA;MACA,IAAA1X,OAAA,QAAA8W,QAAA;QACAC,IAAA;QACAvW,IAAA;QACAwW,OAAA;QACAC,UAAA;MACA;MACA1R,cAAA,CAAA2R,gBAAA;QACAC,YAAA,EAAAP,GAAA,CAAAvU,OAAA;QACAqN,KAAA,EAAAkH,GAAA,CAAAlH,KAAA;QACA5B,EAAA,EAAA8I,GAAA,CAAA9I,EAAA;QACAsJ,gBAAA;QACAC,eAAA;MACA,GACA5R,IAAA,WAAAa,GAAA;QACAoR,OAAA,CAAAhW,UAAA,CAAAmQ,SAAA,GAAAvL,GAAA,CAAAvG,IAAA;QACA2X,OAAA,CAAAnY,WAAA,CACAmY,OAAA,CAAAnY,WAAA,CAAA+X,SAAA,WAAAnQ,KAAA;UAAA,OAAAA,KAAA,CAAA2G,EAAA,IAAA8I,GAAA,CAAA9I,EAAA;QAAA,GACA,CAAA+D,SAAA,GAAAvL,GAAA,CAAAvG,IAAA;QACA2X,OAAA,CAAAnY,WAAA,CACAmY,OAAA,CAAAnY,WAAA,CAAA+X,SAAA,WAAAnQ,KAAA;UAAA,OAAAA,KAAA,CAAA2G,EAAA,IAAA8I,GAAA,CAAA9I,EAAA;QAAA,GACA,CAAAyJ,YAAA;QACAG,OAAA,CAAA9V,kBAAA;QACA5B,OAAA,CAAAwX,KAAA;MACA,GACAvL,KAAA,WAAAqH,GAAA;QACAtT,OAAA,CAAAwX,KAAA;MACA;IACA;IACA,aACAG,YAAA,WAAAA,aAAAf,GAAA;MAAA,IAAAgB,OAAA;MACA,KAAAC,KAAA;MACAtS,cAAA,CAAAqM,QAAA,CAAAgF,GAAA,CAAA9I,EAAA,EAAArI,IAAA,WAAAqF,QAAA;QACA8M,OAAA,CAAA3V,IAAA,GAAA6I,QAAA,CAAA/K,IAAA;QACA6X,OAAA,CAAA3V,IAAA,CAAArC,UAAA,GAAAT,MAAA,CAAAyY,OAAA,CAAA3V,IAAA,CAAArC,UAAA;QACAgY,OAAA,CAAA3V,IAAA,CAAAyN,KAAA,GAAAkH,GAAA,CAAAlH,KAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACAkI,OAAA,CAAA/V,IAAA;MACA;IACA;IACA,aACAiW,YAAA,WAAAA,aAAAlB,GAAA;MAAA,IAAAmB,OAAA;MACA,KAAAC,MAAA,CACAC,OAAA,iBACAxS,IAAA;QACA,OAAAF,cAAA,CAAA2S,kBAAA;UAAApK,EAAA,EAAA8I,GAAA,CAAA9I,EAAA;UAAA4B,KAAA,EAAAkH,GAAA,CAAAlH;QAAA;MACA,GACAjK,IAAA;QACAsS,OAAA,CAAAlS,OAAA;QACAkS,OAAA,CAAAC,MAAA,CAAAG,UAAA;MACA,GACAlM,KAAA;IACA;IACA,WACAmM,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAA7L,KAAA,SAAA8L,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,SAAA,GAAAnH,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAoH,SAAA,CAAAJ,OAAA,CAAApW,IAAA;UACA;UACA;UACA,IAAAyW,qBAAA,EAAAF,SAAA,EAAA/S,IAAA,WAAAqF,QAAA;YACAuN,OAAA,CAAAL,MAAA,CAAAG,UAAA;YACAE,OAAA,CAAAxW,IAAA;YACAwW,OAAA,CAAAxS,OAAA;UACA;QACA;MACA;IACA;IACA,WACA8S,WAAA,WAAAA,YAAAC,IAAA;MAAA,IAAAC,OAAA;MAAA,WAAAzO,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAAwO,UAAA;QAAA,IAAA/Y,IAAA;QAAA,WAAAsK,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAAuO,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAArO,IAAA,GAAAqO,UAAA,CAAApO,IAAA;YAAA;cACA7K,IAAA,OAAAoW,QAAA;cACApW,IAAA,CAAAqW,MAAA,UAAAwC,IAAA,CAAAA,IAAA;cAAAI,UAAA,CAAApO,IAAA;cAAA,OACA,IAAAqO,iBAAA,EAAAlZ,IAAA,EAAA0F,IAAA,WAAAqF,QAAA;gBACA,IAAAA,QAAA,CAAApF,IAAA;kBACAmT,OAAA,CAAAxV,QAAA,CAAA2G,GAAA,WAAAhE,IAAA;oBACA,IAAAA,IAAA,CAAAkT,GAAA,IAAAN,IAAA,CAAAA,IAAA,CAAAM,GAAA;sBACAlT,IAAA,CAAAmT,IAAA,GAAArO,QAAA,CAAAsO,MAAA;oBACA;kBACA;kBACAP,OAAA,CAAAlT,QAAA;oBAAAvD,OAAA;oBAAAxD,IAAA;kBAAA;gBACA;kBACAia,OAAA,CAAAlT,QAAA;oBAAAvD,OAAA;oBAAAxD,IAAA;kBAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAoa,UAAA,CAAArN,IAAA;UAAA;QAAA,GAAAmN,SAAA;MAAA;IACA;IACA,YACAO,MAAA,WAAAA,OAAA;MACA,KAAA1T,QAAA;QACAvD,OAAA;QACAxD,IAAA;MACA;IACA;IACA,UACA0a,YAAA,WAAAA,aAAAV,IAAA;MACA,KAAAvV,QAAA,QAAAA,QAAA,CAAA0C,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,KAAA4S,IAAA;MAAA;IACA;IACA;IACAW,YAAA,WAAAA,aAAAX,IAAA,EAAAvV,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAAmJ,KAAA,CAAAgN,MAAA,CAAAC,MAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAAd,IAAA;MACA;MACA,IAAA/C,QAAA,GAAA+C,IAAA,CAAAe,IAAA,CACA/P,SAAA,CAAAgP,IAAA,CAAAe,IAAA,CAAAC,WAAA,WACAC,WAAA;QACAC,SAAA,GACAjE,QAAA,aACAA,QAAA,aACAA,QAAA,aACAA,QAAA,aACAA,QAAA,cACAA,QAAA,cACAA,QAAA;MACA,IAAAkE,QAAA,GAAAnB,IAAA,CAAAoB,IAAA;MACA,KAAAF,SAAA;QACA,KAAAG,OAAA;UACA9X,KAAA;UACAC,OAAA;UACAxD,IAAA;QACA;MACA;MACA;MACA,KAAAmb,QAAA;QACA,KAAAE,OAAA;UACA9X,KAAA;UACAC,OAAA;UACAxD,IAAA;QACA;MACA;MACA,OAAAkb,SAAA,IAAAC,QAAA;IACA;IACA;IACAG,gBAAA,WAAAA,iBAAA5T,GAAA,EAAAsS,IAAA;MACA,KAAAjT,QAAA;QAAAvD,OAAA;QAAAxD,IAAA;MAAA;IACA;IACA;IACAub,eAAA,WAAAA,gBAAA;MACA,KAAAxU,QAAA;QACAvD,OAAA;QACAxD,IAAA;MACA;IACA;IACA;IACAwb,gBAAA,WAAAA,iBAAAxB,IAAA;MAAA,IAAAyB,OAAA;MACA,SAAApY,IAAA,CAAAO,WAAA,iBAAAP,IAAA,CAAAO,WAAA;QACA,IACA,KAAAP,IAAA,CAAAO,WAAA,CAAA0F,KAAA,CACA,mFACA,GACA;UACA,IAAAnI,IAAA,OAAAoW,QAAA;UACApW,IAAA,CAAAqW,MAAA,SAAAwC,IAAA,CAAAA,IAAA;UACA7Y,IAAA,CAAAqW,MAAA,qBAAAnU,IAAA,CAAAO,WAAA;UAEA+C,cAAA,CAAA+U,UAAA,CAAAva,IAAA,EAAA0F,IAAA,WAAAqF,QAAA;YACA,IAAAA,QAAA,CAAApF,IAAA;cACA2U,OAAA,CAAA1U,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cACAyb,OAAA,CAAApY,IAAA,CAAAkS,OAAA,GAAArJ,QAAA,CAAA/K,IAAA;YACA;cACAsa,OAAA,CAAA1U,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cACAyb,OAAA,CAAApY,IAAA,CAAAkS,OAAA;YACA;UACA;QACA;UACA,KAAAxO,QAAA;YAAAvD,OAAA;YAAAxD,IAAA;UAAA;UACA,KAAA0E,WAAA;QACA;MACA;QACA,KAAAqC,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;QACA,KAAA0E,WAAA;MACA;IACA;IACA;IACAiX,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACAjV,cAAA,CAAAkV,UAAA;QAAAC,QAAA,OAAAzY,IAAA,CAAAkS;MAAA,GAAA1O,IAAA,WAAAqF,QAAA;QACA,IAAAA,QAAA,CAAApF,IAAA;UACA8U,OAAA,CAAA7U,QAAA;YAAAvD,OAAA;YAAAxD,IAAA;UAAA;UACA4b,OAAA,CAAAlX,WAAA;UACAkX,OAAA,CAAAvY,IAAA,CAAAkS,OAAA;QACA;UACAqG,OAAA,CAAA7U,QAAA;YAAAvD,OAAA;YAAAxD,IAAA;UAAA;QACA;MACA;IACA;IACA;IACA+b,MAAA,WAAAA,OAAA;MACA,KAAA9Y,IAAA;MACA,KAAAgW,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA5V,IAAA;QACA6L,EAAA;QACA8M,SAAA;QACAzY,KAAA;QACAI,OAAA;QACA3C,UAAA;QACAib,UAAA;QACAC,QAAA;QACAtY,WAAA;QACAuY,QAAA;QACAC,MAAA;QACAC,WAAA;QACAxY,OAAA;QACA6E,SAAA;QACA4T,KAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACA/Y,WAAA;QACAgZ,WAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,cAAA;QACAC,OAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAC,MAAA;QACA9Z,OAAA;QACAwP,SAAA;QACAsC,OAAA;QACAlT,QAAA;QACAC,MAAA;QACAkb,MAAA;QACAC,QAAA;QACAC,OAAA;QACAC,KAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAA;MACA,KAAAhZ,kBAAA;MACA,KAAAC,gBAAA;IACA;IACA;IACAgZ,gBAAA,WAAAA,iBAAA9D,IAAA,EAAAvV,QAAA;MACA;MACA,IAAAsZ,QAAA,GAAAtZ,QAAA,CAAA2G,GAAA,WAAAhE,IAAA;QAAA;UACA6P,QAAA,EAAA7P,IAAA,CAAA2T,IAAA;UACAf,IAAA,EAAA5S,IAAA,CAAA4W,GAAA;UACA/B,UAAA;QACA;MAAA;MACA,KAAAnX,gBAAA,GAAAiZ,QAAA;IACA;IACA;IACAlC,UAAA,WAAAA,WAAA/Q,KAAA;MACA,KAAAhG,gBAAA,CAAAmZ,MAAA,CAAAnT,KAAA;IACA;IACA;IACAoT,iBAAA,WAAAA,kBAAA;MACA,KAAArZ,kBAAA;MACA,KAAAC,gBAAA;MACA;MACA,KAAA8I,KAAA,CAAAuQ,WAAA,CAAAC,UAAA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA9S,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAA6S,UAAA;QAAA,IAAAC,gBAAA,EAAA3H,QAAA,EAAA4H,WAAA,EAAAvS,QAAA;QAAA,WAAAT,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAA8S,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA5S,IAAA,GAAA4S,UAAA,CAAA3S,IAAA;YAAA;cACA;cACAwS,gBAAA,GAAAF,OAAA,CAAAxZ,gBAAA,CAAAqC,MAAA,CACA,UAAAC,IAAA;gBAAA,QAAAA,IAAA,CAAA6U,UAAA,CAAApT,IAAA;cAAA,CACA;cAAA,MACA2V,gBAAA,CAAApU,MAAA;gBAAAuU,UAAA,CAAA3S,IAAA;gBAAA;cAAA;cACAsS,OAAA,CAAAvX,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;cAAA,OAAA2e,UAAA,CAAA1S,MAAA;YAAA;cAAA0S,UAAA,CAAA5S,IAAA;cAKAuS,OAAA,CAAAld,OAAA;;cAEA;cACAyV,QAAA,OAAAU,QAAA,IAEA;cACA+G,OAAA,CAAAxZ,gBAAA,CAAAuF,OAAA,WAAAjD,IAAA;gBACAyP,QAAA,CAAAW,MAAA,UAAApQ,IAAA,CAAA4S,IAAA;cACA;;cAEA;cACAyE,WAAA,GAAAH,OAAA,CAAAxZ,gBAAA,CACAsG,GAAA,WAAAhE,IAAA;gBAAA,OAAAA,IAAA,CAAA6U,UAAA;cAAA,GACA5Q,IAAA;cAEAwL,QAAA,CAAAW,MAAA,gBAAAiH,WAAA;;cAEA;cAAAE,UAAA,CAAA3S,IAAA;cAAA,OACArF,cAAA,CAAAiY,kBAAA,CAAA/H,QAAA;YAAA;cAAA3K,QAAA,GAAAyS,UAAA,CAAAtP,IAAA;cAEA,IAAAnD,QAAA,CAAApF,IAAA;gBACAwX,OAAA,CAAAvX,QAAA;kBACAvD,OAAA;kBACAxD,IAAA;gBACA;gBACAse,OAAA,CAAAzZ,kBAAA;gBACAyZ,OAAA,CAAAxZ,gBAAA;gBACAwZ,OAAA,CAAA1Q,KAAA,CAAAuQ,WAAA,CAAAC,UAAA;gBACA;gBACAE,OAAA,CAAArX,OAAA;cACA;gBACAqX,OAAA,CAAAvX,QAAA;kBACAvD,OAAA,EAAA0I,QAAA,CAAAnE,GAAA;kBACA/H,IAAA;gBACA;cACA;cAAA2e,UAAA,CAAA3S,IAAA;cAAA;YAAA;cAAA2S,UAAA,CAAA5S,IAAA;cAAA4S,UAAA,CAAA7G,EAAA,GAAA6G,UAAA;cAEAE,OAAA,CAAA1I,KAAA,YAAAwI,UAAA,CAAA7G,EAAA;cACAwG,OAAA,CAAAvX,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cAAA2e,UAAA,CAAA5S,IAAA;cAEAuS,OAAA,CAAAld,OAAA;cAAA,OAAAud,UAAA,CAAAjI,MAAA;YAAA;YAAA;cAAA,OAAAiI,UAAA,CAAA5R,IAAA;UAAA;QAAA,GAAAwR,SAAA;MAAA;IAEA;IACAO,YAAA,WAAAA,aAAA;MACA,KAAA/Z,kBAAA;MACA,SAAAtD,aAAA,CAAA2I,MAAA;QACA,KAAArD,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;QACA;MACA;MAEA,SAAAyB,aAAA,CAAA2I,MAAA;QACA,KAAArD,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;QACA;MACA;;MAEA;MACA,IAAA+e,iBAAA,QAAAtd,aAAA;MACA,IAAAud,eAAA,QAAAre,WAAA,CAAAse,IAAA,CACA,UAAA7X,IAAA;QAAA,OAAAA,IAAA,CAAA8H,EAAA,KAAA6P,iBAAA;MAAA,CACA;MAEA,IAAAC,eAAA;QACA,KAAAha,cAAA,GAAAga,eAAA;QACA,KAAAja,kBAAA;MACA;QACA,KAAAgC,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;MACA;IACA;IACA;IACA;IACAkf,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,WAAA3T,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAA0T,UAAA;QAAA,IAAAC,qBAAA,EAAAC,gBAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,SAAA,EAAAC,MAAA,EAAAzT,QAAA,EAAA0T,MAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,aAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,kBAAA,EAAA1L,IAAA,EAAAlM,KAAA,EAAA6X,QAAA,EAAAC,aAAA,EAAAC,KAAA,EAAAC,YAAA,EAAAC,IAAA,EAAArf,IAAA,EAAAsf,QAAA,EAAAC,MAAA;QAAA,WAAAjV,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAA+U,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA7U,IAAA,GAAA6U,UAAA,CAAA5U,IAAA;YAAA;cAAA,MACAmT,OAAA,CAAA1d,aAAA,CAAA2I,MAAA;gBAAAwW,UAAA,CAAA5U,IAAA;gBAAA;cAAA;cAAA,OAAA4U,UAAA,CAAA3U,MAAA,WACAkT,OAAA,CAAApY,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cAAA,KAIAmf,OAAA,CAAAlZ,YAAA;gBAAA2a,UAAA,CAAA5U,IAAA;gBAAA;cAAA;cACAmT,OAAA,CAAAjZ,SAAA;cAAA,KACAiZ,OAAA,CAAAhZ,aAAA;gBAAAya,UAAA,CAAA5U,IAAA;gBAAA;cAAA;cAAA4U,UAAA,CAAA7U,IAAA;cAAA6U,UAAA,CAAA5U,IAAA;cAAA,OAEAmT,OAAA,CAAAhZ,aAAA,CAAA4V,MAAA;YAAA;cAAA6E,UAAA,CAAA5U,IAAA;cAAA;YAAA;cAAA4U,UAAA,CAAA7U,IAAA;cAAA6U,UAAA,CAAA9I,EAAA,GAAA8I,UAAA;cAEA/B,OAAA,CAAAgC,GAAA,sOAAAD,UAAA,CAAA9I,EAAA;YAAA;cAAA8I,UAAA,CAAA5U,IAAA;cAAA,OAGA,IAAA8U,OAAA,WAAAC,OAAA;gBAAA,OAAAzS,UAAA,CAAAyS,OAAA;cAAA;YAAA;cAGA5B,OAAA,CAAAlZ,YAAA;cACAkZ,OAAA,CAAAjZ,SAAA;cACAiZ,OAAA,CAAAla,eAAA;cACAka,OAAA,CAAAja,YAAA;cACAia,OAAA,CAAAha,UAAA;cAAAyb,UAAA,CAAA7U,IAAA;cAGA;cACAuT,gBAAA,GAAAH,OAAA,CAAAxe,WAAA,CAAAwG,MAAA,WAAAsJ,OAAA;gBAAA,OACA0O,OAAA,CAAA1d,aAAA,CAAA2H,QAAA,CAAAqH,OAAA,CAAAvB,EAAA;cAAA,CACA;cACAqQ,MAAA,GAAAD,gBAAA,CACAlU,GAAA,WAAAqF,OAAA;gBAAA,gBAAA/G,MAAA,CAAA+G,OAAA,CAAA9M,OAAA,IAAA8M,OAAA,CAAAlN,KAAA;cAAA,GACA8H,IAAA,QAEA;cAAAuV,UAAA,CAAA5U,IAAA;cAAA,OACA,IAAAgV,4BAAA,EACA7B,OAAA,CAAA1d,aAAA,CAAA4J,IAAA,KACA;YAAA;cAFAmU,gBAAA,GAAAoB,UAAA,CAAAvR,IAAA;cAAA,KAAAgQ,qBAAA,GAGAG,gBAAA,CAAAre,IAAA,cAAAke,qBAAA,eAAAA,qBAAA,CAAAjV,MAAA;gBAAAwW,UAAA,CAAA5U,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAiV,KAAA;YAAA;cAGA;cACAxB,eAAA,GAAAD,gBAAA,CAAAre,IAAA,CACAiK,GAAA,WAAAqF,OAAA,EAAA3F,KAAA;gBAAA,IAAAoW,qBAAA,EAAAC,sBAAA;gBACA,IAAA5d,KAAA,GACA,EAAA2d,qBAAA,GAAA5B,gBAAA,CAAAxU,KAAA,eAAAoW,qBAAA,uBAAAA,qBAAA,CAAAvd,OAAA,OAAAwd,sBAAA,GACA7B,gBAAA,CAAAxU,KAAA,eAAAqW,sBAAA,uBAAAA,sBAAA,CAAA5d,KAAA,KACA;gBACA,IAAAE,OAAA,GAAAgN,OAAA,CAAAhN,OAAA;gBACA,uBAAAiG,MAAA,CAAAoB,KAAA,yCAAApB,MAAA,CAAAnG,KAAA,gBAAAmG,MAAA,CAAAjG,OAAA;cACA,GACA4H,IAAA,yDAEA;cACA8T,OAAA,CAAAja,YAAA,CAAA6F,IAAA;gBACAqW,IAAA;gBACA3d,OAAA,qDAAAiG,MAAA,CAAAyV,OAAA,CAAA1d,aAAA,CAAA2I,MAAA,gCAAAV,MAAA,CAAA6V,MAAA;cACA;;cAEA;cACAG,SAAA;gBACA0B,IAAA;gBACA3d,OAAA;cACA;cACA0b,OAAA,CAAAja,YAAA,CAAA6F,IAAA,CAAA2U,SAAA;;cAEA;cACAC,MAAA,GACAR,OAAA,CAAA9Y,eAAA,CACAuC,OAAA,kBAAAuW,OAAA,CAAA1d,aAAA,CAAA2I,MAAA,EACAxB,OAAA,yFAAAc,MAAA,CACA+V,eAAA,GAEA;cAAAmB,UAAA,CAAA5U,IAAA;cAAA,OACA,IAAAqV,YAAA,EACA5B,eAAA,EACA,aACA,qBACA;YAAA;cAJAvT,QAAA,GAAA0U,UAAA,CAAAvR,IAAA;cAAA,IAKAnD,QAAA,CAAAoV,EAAA;gBAAAV,UAAA,CAAA5U,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAiV,KAAA;YAAA;cAGA;cACArB,MAAA,GAAA1T,QAAA,CAAAqV,IAAA,CAAAC,SAAA;cACArC,OAAA,CAAAhZ,aAAA,GAAAyZ,MAAA;cACAC,OAAA,OAAA4B,WAAA;cACA3B,MAAA;cACAC,aAAA;cACAC,YAAA;cAEA;cACAC,aAAA,YAAAA,cAAAjW,GAAA;gBACA,OAAAA,GAAA,CAAApB,OAAA,gCAAAU,KAAA;kBACA,OAAAvI,MAAA,CAAA2gB,YAAA,CAAAC,QAAA,CAAArY,KAAA,CAAAV,OAAA;gBACA;cACA,GAEA;cACAsX,aAAA,YAAAA,cAAA0B,UAAA;gBACA;kBACA,IAAAC,eAAA,OAAAC,cAAA,EAAAF,UAAA,EAAAzC,OAAA,CAAA7Z,eAAA;kBACAoa,SAAA,CAAAjc,OAAA,GAAAoe,eAAA;;kBAEA;kBACA1C,OAAA,CAAA4C,SAAA;oBACA,IAAA7c,YAAA,GAAAia,OAAA,CAAAvR,KAAA,CAAA1I,YAAA;oBACA,IAAAA,YAAA;sBACAA,YAAA,CAAA4I,SAAA,GAAA5I,YAAA,CAAA8c,YAAA;oBACA;kBACA;gBACA,SAAA7L,KAAA;kBACA0I,OAAA,CAAA1I,KAAA,aAAAA,KAAA;gBACA;cACA,GAEA;YAAA;cAAA,KACA;gBAAAyK,UAAA,CAAA5U,IAAA;gBAAA;cAAA;cAAA,KAEAmT,OAAA,CAAAjZ,SAAA;gBAAA0a,UAAA,CAAA5U,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAiV,KAAA;YAAA;cAAAL,UAAA,CAAA5U,IAAA;cAAA,OAGA4T,MAAA,CAAAqC,IAAA;YAAA;cAAA9B,kBAAA,GAAAS,UAAA,CAAAvR,IAAA;cAAAoF,IAAA,GAAA0L,kBAAA,CAAA1L,IAAA;cAAAlM,KAAA,GAAA4X,kBAAA,CAAA5X,KAAA;cAAA,KAEAkM,IAAA;gBAAAmM,UAAA,CAAA5U,IAAA;gBAAA;cAAA;cACA;cACA,IAAA+T,aAAA;gBACA;kBACAK,QAAA,GAAA3N,IAAA,CAAAC,KAAA,CAAAqN,aAAA;kBACA,IAAAK,QAAA,CAAAM,MAAA;oBACA;oBACAL,aAAA,GAAAJ,aAAA,CAAAG,QAAA,CAAAM,MAAA;oBACAZ,MAAA,IAAAO,aAAA;oBACAH,aAAA,CAAAJ,MAAA;kBACA;gBACA,SAAAtJ,CAAA;kBACAqI,OAAA,CAAAqD,IAAA,gBAAA1L,CAAA;gBACA;cACA;cAAA,OAAAoK,UAAA,CAAA3U,MAAA;YAAA;cAIAqU,KAAA,GAAAT,OAAA,CAAAsC,MAAA,CAAA5Z,KAAA;cACAwX,aAAA,IAAAO,KAAA;;cAEA;YAAA;cAAA,KACAP,aAAA,CAAA3W,QAAA;gBAAAwX,UAAA,CAAA5U,IAAA;gBAAA;cAAA;cACAuU,YAAA,GAAAR,aAAA,CAAAjK,OAAA;cACA0K,IAAA,GAAAT,aAAA,CAAAqC,KAAA,IAAA7B,YAAA,EAAA1X,IAAA;cACAkX,aAAA,GAAAA,aAAA,CAAAqC,KAAA,CAAA7B,YAAA;cAAA,MAEA,CAAAC,IAAA,IAAAA,IAAA,iBAAAA,IAAA,CAAA6B,UAAA;gBAAAzB,UAAA,CAAA5U,IAAA;gBAAA;cAAA;cAAA,OAAA4U,UAAA,CAAA3U,MAAA;YAAA;cAAA2U,UAAA,CAAA7U,IAAA;cAKA5K,IAAA,GAAAqf,IAAA,CAAA4B,KAAA,IAAAvZ,IAAA;cAAA,MACA1H,IAAA;gBAAAyf,UAAA,CAAA5U,IAAA;gBAAA;cAAA;cAAA,OAAA4U,UAAA,CAAA3U,MAAA;YAAA;cAIAwU,QAAA,GAAAhO,IAAA,CAAAC,KAAA,CAAAvR,IAAA;cAAA,IACAsf,QAAA,CAAAC,MAAA;gBAAAE,UAAA,CAAA5U,IAAA;gBAAA;cAAA;cAAA,OAAA4U,UAAA,CAAA3U,MAAA;YAAA;cAAA,MAKAwU,QAAA,CAAAC,MAAA,cAAAD,QAAA,CAAAC,MAAA;gBAAAE,UAAA,CAAA5U,IAAA;gBAAA;cAAA;cAAA,OAAA4U,UAAA,CAAA3U,MAAA;YAAA;cAIA;cACAyU,MAAA,GAAAT,aAAA,CAAAQ,QAAA,CAAAC,MAAA,GAEA;cAAA,KACAA,MAAA,CAAAtX,QAAA;gBAAAwX,UAAA,CAAA5U,IAAA;gBAAA;cAAA;cACAgU,YAAA;cAAA,OAAAY,UAAA,CAAA3U,MAAA;YAAA;cAAA,KAKAyU,MAAA,CAAAtX,QAAA;gBAAAwX,UAAA,CAAA5U,IAAA;gBAAA;cAAA;cACAgU,YAAA;cAAA,OAAAY,UAAA,CAAA3U,MAAA;YAAA;cAIA;cACA,KAAA+T,YAAA,IAAAU,MAAA;gBACAZ,MAAA,IAAAY,MAAA;gBACAR,aAAA,CAAAJ,MAAA;cACA;cAAAc,UAAA,CAAA5U,IAAA;cAAA;YAAA;cAAA4U,UAAA,CAAA7U,IAAA;cAAA6U,UAAA,CAAArK,EAAA,GAAAqK,UAAA;cAEA/B,OAAA,CAAAqD,IAAA;gBACA1B,IAAA,EAAAA,IAAA;gBACArK,KAAA,EAAAyK,UAAA,CAAArK,EAAA,CAAA/S,OAAA;gBACAuc,aAAA,EAAAA;cACA;cAAA,OAAAa,UAAA,CAAA3U,MAAA;YAAA;cAAA2U,UAAA,CAAA5U,IAAA;cAAA;YAAA;cAAA4U,UAAA,CAAA5U,IAAA;cAAA;YAAA;cAAA4U,UAAA,CAAA5U,IAAA;cAAA;YAAA;cAAA4U,UAAA,CAAA7U,IAAA;cAAA6U,UAAA,CAAA0B,EAAA,GAAA1B,UAAA;cAMA/B,OAAA,CAAA1I,KAAA,YAAAyK,UAAA,CAAA0B,EAAA;cACAnD,OAAA,CAAApY,QAAA,CAAAoP,KAAA,CAAAyK,UAAA,CAAA0B,EAAA,CAAA9e,OAAA;cACA,IAAA2b,OAAA,CAAAja,YAAA;gBACAia,OAAA,CAAAja,YAAA,IAAAzB,OAAA;cACA;YAAA;cAAAmd,UAAA,CAAA7U,IAAA;cAEAoT,OAAA,CAAAhZ,aAAA;cACA,IAAAgZ,OAAA,CAAAla,eAAA;gBACAka,OAAA,CAAAha,UAAA;gBACAga,OAAA,CAAAlZ,YAAA;cACA;cAAA,OAAA2a,UAAA,CAAAlK,MAAA;YAAA;YAAA;cAAA,OAAAkK,UAAA,CAAA7T,IAAA;UAAA;QAAA,GAAAqS,SAAA;MAAA;IAEA;IACA;IACAmD,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAAhX,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAA+W,UAAA;QAAA,IAAAC,sBAAA,EAAApD,gBAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAkD,UAAA,EAAAhD,MAAA,EAAAzT,QAAA,EAAA0T,MAAA,EAAAC,OAAA,EAAAC,MAAA,EAAA8C,cAAA,EAAAC,cAAA,EAAAC,UAAA,EAAA5C,aAAA,EAAA6C,aAAA;QAAA,WAAAtX,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAAoX,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAlX,IAAA,GAAAkX,UAAA,CAAAjX,IAAA;YAAA;cAAA,MACAwW,OAAA,CAAA/gB,aAAA,CAAA2I,MAAA;gBAAA6Y,UAAA,CAAAjX,IAAA;gBAAA;cAAA;cAAA,OAAAiX,UAAA,CAAAhX,MAAA,WACAuW,OAAA,CAAAzb,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cAAA,KAIAwiB,OAAA,CAAAvc,YAAA;gBAAAgd,UAAA,CAAAjX,IAAA;gBAAA;cAAA;cACAwW,OAAA,CAAAtc,SAAA;cAAA,KACAsc,OAAA,CAAArc,aAAA;gBAAA8c,UAAA,CAAAjX,IAAA;gBAAA;cAAA;cAAAiX,UAAA,CAAAlX,IAAA;cAAAkX,UAAA,CAAAjX,IAAA;cAAA,OAEAwW,OAAA,CAAArc,aAAA,CAAA4V,MAAA;YAAA;cAAAkH,UAAA,CAAAjX,IAAA;cAAA;YAAA;cAAAiX,UAAA,CAAAlX,IAAA;cAAAkX,UAAA,CAAAnL,EAAA,GAAAmL,UAAA;cAEApE,OAAA,CAAAgC,GAAA,sOAAAoC,UAAA,CAAAnL,EAAA;YAAA;cAAAmL,UAAA,CAAAjX,IAAA;cAAA,OAIA,IAAA8U,OAAA,WAAAC,OAAA;gBAAA,OAAAzS,UAAA,CAAAyS,OAAA;cAAA;YAAA;cAGAyB,OAAA,CAAAvc,YAAA;cACAuc,OAAA,CAAAtc,SAAA;cACAsc,OAAA,CAAAvd,eAAA;cACAud,OAAA,CAAAtd,YAAA;cACAsd,OAAA,CAAArd,UAAA;cAAA8d,UAAA,CAAAlX,IAAA;cAGA;cACAuT,gBAAA,GAAAkD,OAAA,CAAA7hB,WAAA,CAAAwG,MAAA,WAAAsJ,OAAA;gBAAA,OACA+R,OAAA,CAAA/gB,aAAA,CAAA2H,QAAA,CAAAqH,OAAA,CAAAvB,EAAA;cAAA,CACA;cACAqQ,MAAA,GAAAD,gBAAA,CACAlU,GAAA,WAAAqF,OAAA;gBAAA,gBAAA/G,MAAA,CAAA+G,OAAA,CAAA9M,OAAA,IAAA8M,OAAA,CAAAlN,KAAA;cAAA,GACA8H,IAAA,QAEA;cAAA4X,UAAA,CAAAjX,IAAA;cAAA,OACA,IAAAgV,4BAAA,EACAwB,OAAA,CAAA/gB,aAAA,CAAA4J,IAAA,KACA;YAAA;cAFAmU,gBAAA,GAAAyD,UAAA,CAAA5T,IAAA;cAAA,KAAAqT,sBAAA,GAGAlD,gBAAA,CAAAre,IAAA,cAAAuhB,sBAAA,eAAAA,sBAAA,CAAAtY,MAAA;gBAAA6Y,UAAA,CAAAjX,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAiV,KAAA;YAAA;cAGA;cACAxB,eAAA,GAAAD,gBAAA,CAAAre,IAAA,CACAiK,GAAA,WAAAqF,OAAA,EAAA3F,KAAA;gBAAA,IAAAoY,sBAAA,EAAAC,sBAAA;gBACA,IAAA5f,KAAA,GACA,EAAA2f,sBAAA,GAAA5D,gBAAA,CAAAxU,KAAA,eAAAoY,sBAAA,uBAAAA,sBAAA,CAAAvf,OAAA,OAAAwf,sBAAA,GACA7D,gBAAA,CAAAxU,KAAA,eAAAqY,sBAAA,uBAAAA,sBAAA,CAAA5f,KAAA,KACA;gBACA,IAAAE,OAAA,GAAAgN,OAAA,CAAAhN,OAAA;gBACA,uBAAAiG,MAAA,CAAAoB,KAAA,yCAAApB,MAAA,CAAAnG,KAAA,gBAAAmG,MAAA,CAAAjG,OAAA;cACA,GACA4H,IAAA,yDAEA;cACAmX,OAAA,CAAAtd,YAAA,CAAA6F,IAAA;gBACAqW,IAAA;gBACA3d,OAAA,qDAAAiG,MAAA,CAAA8Y,OAAA,CAAA/gB,aAAA,CAAA2I,MAAA,gCAAAV,MAAA,CAAA6V,MAAA;cACA;;cAEA;cACAG,UAAA;gBACA0B,IAAA;gBACA3d,OAAA;cACA;cACA+e,OAAA,CAAAtd,YAAA,CAAA6F,IAAA,CAAA2U,UAAA;;cAEA;cACAC,MAAA,GACA6C,OAAA,CAAAnc,eAAA,CACAuC,OAAA,kBAAA4Z,OAAA,CAAA/gB,aAAA,CAAA2I,MAAA,EACAxB,OAAA,yFAAAc,MAAA,CACA+V,eAAA,GAEA;cAAAwD,UAAA,CAAAjX,IAAA;cAAA,OACA,IAAAoX,cAAA,EAAAzD,MAAA;YAAA;cAAAzT,QAAA,GAAA+W,UAAA,CAAA5T,IAAA;cAAA,IACAnD,QAAA,CAAAoV,EAAA;gBAAA2B,UAAA,CAAAjX,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAiV,KAAA;YAAA;cAGA;cACArB,MAAA,GAAA1T,QAAA,CAAAqV,IAAA,CAAAC,SAAA;cACAgB,OAAA,CAAArc,aAAA,GAAAyZ,MAAA;cACAC,OAAA,OAAA4B,WAAA;cACA3B,MAAA;cACA8C,cAAA,GAAAjW,IAAA,CAAA0W,GAAA;cACAR,cAAA;cACAC,UAAA,OAEA;cACA5C,aAAA,YAAAA,cAAA0B,UAAA;gBACA,IAAA0B,WAAA,GAAA3W,IAAA,CAAA0W,GAAA;gBACA;gBACA,IAAAC,WAAA,GAAAV,cAAA;kBACAlD,UAAA,CAAAjc,OAAA,GAAAme,UAAA;kBACAgB,cAAA,GAAAU,WAAA;kBACA;kBACAd,OAAA,CAAAT,SAAA;oBACA,IAAA7c,YAAA,GAAAsd,OAAA,CAAA5U,KAAA,CAAA1I,YAAA;oBACA,IAAAA,YAAA;sBACAA,YAAA,CAAA4I,SAAA,GAAA5I,YAAA,CAAA8c,YAAA;oBACA;kBACA;gBACA;cACA,GAEA;cACAe,aAAA;gBAAA,IAAAQ,KAAA,OAAA/X,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAA8X,UAAA;kBAAA,IAAAC,mBAAA,EAAAhP,IAAA,EAAAlM,KAAA,EAAA+X,KAAA,EAAAoD,KAAA,EAAAC,UAAA,EAAAC,MAAA,EAAApD,IAAA,EAAAC,QAAA,EAAAoD,SAAA,EAAAC,eAAA,EAAAC,aAAA;kBAAA,WAAAtY,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAAoY,WAAAC,UAAA;oBAAA,kBAAAA,UAAA,CAAAlY,IAAA,GAAAkY,UAAA,CAAAjY,IAAA;sBAAA;wBAAAiY,UAAA,CAAAlY,IAAA;sBAAA;wBAAA,KAEA;0BAAAkY,UAAA,CAAAjY,IAAA;0BAAA;wBAAA;wBAAA,KAEAwW,OAAA,CAAAtc,SAAA;0BAAA+d,UAAA,CAAAjY,IAAA;0BAAA;wBAAA;wBAAA,MACA,IAAAiV,KAAA;sBAAA;wBAAAgD,UAAA,CAAAjY,IAAA;wBAAA,OAGA4T,MAAA,CAAAqC,IAAA;sBAAA;wBAAAwB,mBAAA,GAAAQ,UAAA,CAAA5U,IAAA;wBAAAoF,IAAA,GAAAgP,mBAAA,CAAAhP,IAAA;wBAAAlM,KAAA,GAAAkb,mBAAA,CAAAlb,KAAA;wBAAA,KACAkM,IAAA;0BAAAwP,UAAA,CAAAjY,IAAA;0BAAA;wBAAA;wBACA,IAAA8T,MAAA,CAAA1V,MAAA;0BACA8V,aAAA,CAAAJ,MAAA;wBACA;wBAAA,OAAAmE,UAAA,CAAAhY,MAAA;sBAAA;wBAIAqU,KAAA,GAAAT,OAAA,CAAAsC,MAAA,CAAA5Z,KAAA;wBACAmb,KAAA,GAAApD,KAAA,CAAApW,KAAA,OAAA/C,MAAA,WAAAqZ,IAAA;0BAAA,OAAAA,IAAA,CAAA3X,IAAA;wBAAA;wBAAA8a,UAAA,OAAAnO,2BAAA,CAAAtV,OAAA,EAEAwjB,KAAA;wBAAAO,UAAA,CAAAlY,IAAA;wBAAA4X,UAAA,CAAAvN,CAAA;sBAAA;wBAAA,KAAAwN,MAAA,GAAAD,UAAA,CAAAtN,CAAA,IAAA5B,IAAA;0BAAAwP,UAAA,CAAAjY,IAAA;0BAAA;wBAAA;wBAAAwU,IAAA,GAAAoD,MAAA,CAAArb,KAAA;wBAAA0b,UAAA,CAAAlY,IAAA;wBAEA0U,QAAA,GAAAhO,IAAA,CAAAC,KAAA,CAAA8N,IAAA;wBAAA,IACAC,QAAA,CAAAvU,QAAA;0BAAA+X,UAAA,CAAAjY,IAAA;0BAAA;wBAAA;wBAAA,OAAAiY,UAAA,CAAAhY,MAAA;sBAAA;wBAEAC,SAAA,GAAAuU,QAAA,CAAAvU,QAAA,EAEA;wBAAA,MACAA,SAAA,cAAAA,SAAA;0BAAA+X,UAAA,CAAAjY,IAAA;0BAAA;wBAAA;wBAAA,OAAAiY,UAAA,CAAAhY,MAAA;sBAAA;wBAIA6W,UAAA,IAAA5W,SAAA;;wBAEA;sBAAA;wBAAA,KACA;0BAAA+X,UAAA,CAAAjY,IAAA;0BAAA;wBAAA;wBACA8X,eAAA,GAAAhB,UAAA,CAAAhN,OAAA;wBACAiO,aAAA,GAAAjB,UAAA,CAAAhN,OAAA;wBAAA,MAEAgO,eAAA,WAAAC,aAAA;0BAAAE,UAAA,CAAAjY,IAAA;0BAAA;wBAAA;wBACA;wBACA,KAAA6W,cAAA;0BACA/C,MAAA,IAAAgD,UAAA;0BACA;0BACA5C,aAAA,KAAA4B,cAAA,EAAAhC,MAAA,EAAA0C,OAAA,CAAAld,eAAA;wBACA;wBACAwd,UAAA;wBAAA,OAAAmB,UAAA,CAAAhY,MAAA;sBAAA;wBAAA,MAEA6X,eAAA,WAAAC,aAAA;0BAAAE,UAAA,CAAAjY,IAAA;0BAAA;wBAAA;wBACA;wBACA6W,cAAA;wBACA,IAAAiB,eAAA;0BACAhE,MAAA,IAAAgD,UAAA,CAAA9X,SAAA,IAAA8Y,eAAA;0BACA;0BACA5D,aAAA,KAAA4B,cAAA,EAAAhC,MAAA,EAAA0C,OAAA,CAAAld,eAAA;wBACA;wBACAwd,UAAA,GAAAA,UAAA,CAAA9X,SAAA,CAAA8Y,eAAA;wBAAA,OAAAG,UAAA,CAAAhY,MAAA;sBAAA;wBAAA,MAEA6X,eAAA,WAAAC,aAAA;0BAAAE,UAAA,CAAAjY,IAAA;0BAAA;wBAAA;wBACA;wBACA6W,cAAA;wBACAC,UAAA,GAAAA,UAAA,CAAA9X,SAAA,CAAA+Y,aAAA;wBAAA,OAAAE,UAAA,CAAAhY,MAAA;sBAAA;wBAGA;wBACA,IAAA6X,eAAA;0BACAhE,MAAA,IAAAgD,UAAA,CAAA9X,SAAA,IAAA8Y,eAAA;0BACA;0BACA5D,aAAA,KAAA4B,cAAA,EAAAhC,MAAA,EAAA0C,OAAA,CAAAld,eAAA;wBACA;wBACAwd,UAAA,GAAAA,UAAA,CAAA9X,SAAA,CAAA+Y,aAAA;wBACAlB,cAAA;wBAAA,OAAAoB,UAAA,CAAAhY,MAAA;sBAAA;wBAAAgY,UAAA,CAAAjY,IAAA;wBAAA;sBAAA;wBAAAiY,UAAA,CAAAjY,IAAA;wBAAA;sBAAA;wBAAAiY,UAAA,CAAAlY,IAAA;wBAAAkY,UAAA,CAAAnM,EAAA,GAAAmM,UAAA;wBAKApF,OAAA,CAAAqD,IAAA;0BACA1B,IAAA,EAAAA,IAAA;0BACArK,KAAA,EAAA8N,UAAA,CAAAnM,EAAA,CAAAtU;wBACA;sBAAA;wBAAAygB,UAAA,CAAAjY,IAAA;wBAAA;sBAAA;wBAAAiY,UAAA,CAAAjY,IAAA;wBAAA;sBAAA;wBAAAiY,UAAA,CAAAlY,IAAA;wBAAAkY,UAAA,CAAA1N,EAAA,GAAA0N,UAAA;wBAAAN,UAAA,CAAAnN,CAAA,CAAAyN,UAAA,CAAA1N,EAAA;sBAAA;wBAAA0N,UAAA,CAAAlY,IAAA;wBAAA4X,UAAA,CAAAlN,CAAA;wBAAA,OAAAwN,UAAA,CAAAvN,MAAA;sBAAA;wBAAAuN,UAAA,CAAAjY,IAAA;wBAAA;sBAAA;wBAAAiY,UAAA,CAAAjY,IAAA;wBAAA;sBAAA;wBAAAiY,UAAA,CAAAlY,IAAA;wBAAAkY,UAAA,CAAA3B,EAAA,GAAA2B,UAAA;wBAAA,MAKAA,UAAA,CAAA3B,EAAA,CAAA9e,OAAA;0BAAAygB,UAAA,CAAAjY,IAAA;0BAAA;wBAAA;wBAAA,MACA,IAAAiV,KAAA;sBAAA;wBAEApC,OAAA,CAAA1I,KAAA,eAAA8N,UAAA,CAAA3B,EAAA;wBAAA,MAAA2B,UAAA,CAAA3B,EAAA;sBAAA;sBAAA;wBAAA,OAAA2B,UAAA,CAAAlX,IAAA;oBAAA;kBAAA,GAAAyW,SAAA;gBAAA,CAGA;gBAAA,gBAzFAT,cAAA;kBAAA,OAAAQ,KAAA,CAAAW,KAAA,OAAAC,SAAA;gBAAA;cAAA;cAAAlB,UAAA,CAAAjX,IAAA;cAAA,OA2FA+W,aAAA;YAAA;cAAAE,UAAA,CAAAjX,IAAA;cAAA;YAAA;cAAAiX,UAAA,CAAAlX,IAAA;cAAAkX,UAAA,CAAA1M,EAAA,GAAA0M,UAAA;cAAA,MAGAA,UAAA,CAAA1M,EAAA,CAAA/S,OAAA;gBAAAyf,UAAA,CAAAjX,IAAA;gBAAA;cAAA;cACA6S,OAAA,CAAAgC,GAAA;cAAA,OAAAoC,UAAA,CAAAhX,MAAA;YAAA;cAGA4S,OAAA,CAAA1I,KAAA,YAAA8M,UAAA,CAAA1M,EAAA;cACAiM,OAAA,CAAAzb,QAAA,CAAAoP,KAAA,CAAA8M,UAAA,CAAA1M,EAAA,CAAA/S,OAAA;cACA,IAAAgf,OAAA,CAAAtd,YAAA;gBACAsd,OAAA,CAAAtd,YAAA,IAAAzB,OAAA;cACA;YAAA;cAAAwf,UAAA,CAAAlX,IAAA;cAEAyW,OAAA,CAAArc,aAAA;cACA;cACA,IAAAqc,OAAA,CAAAvd,eAAA;gBACAud,OAAA,CAAArd,UAAA;gBACAqd,OAAA,CAAAvc,YAAA;cACA;cAAA,OAAAgd,UAAA,CAAAvM,MAAA;YAAA;YAAA;cAAA,OAAAuM,UAAA,CAAAlW,IAAA;UAAA;QAAA,GAAA0V,SAAA;MAAA;IAEA;IACA;IACA2B,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MAAA,WAAA7Y,kBAAA,CAAAtL,OAAA,mBAAAuL,oBAAA,CAAAvL,OAAA,IAAAwL,IAAA,UAAA4Y,UAAA;QAAA,IAAAhF,gBAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAA8E,WAAA,EAAA5E,MAAA,EAAAzT,QAAA,EAAA0T,MAAA,EAAAC,OAAA,EAAAC,MAAA,EAAA0E,eAAA,EAAAtE,aAAA,EAAAuE,mBAAA,EAAAhQ,IAAA,EAAAlM,KAAA,EAAA+X,KAAA,EAAAoD,KAAA,EAAAgB,UAAA,EAAAC,MAAA,EAAAnE,IAAA,EAAArf,IAAA,EAAAyjB,iBAAA,EAAAnE,QAAA,EAAAhd,OAAA;QAAA,WAAAgI,oBAAA,CAAAvL,OAAA,IAAA0L,IAAA,UAAAiZ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA/Y,IAAA,GAAA+Y,UAAA,CAAA9Y,IAAA;YAAA;cAAA,MACAqY,OAAA,CAAA5iB,aAAA,CAAA2I,MAAA;gBAAA0a,UAAA,CAAA9Y,IAAA;gBAAA;cAAA;cAAA,OAAA8Y,UAAA,CAAA7Y,MAAA,WACAoY,OAAA,CAAAtd,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cAAA,KAIAqkB,OAAA,CAAApe,YAAA;gBAAA6e,UAAA,CAAA9Y,IAAA;gBAAA;cAAA;cACAqY,OAAA,CAAAne,SAAA;cAAA,KACAme,OAAA,CAAAle,aAAA;gBAAA2e,UAAA,CAAA9Y,IAAA;gBAAA;cAAA;cAAA8Y,UAAA,CAAA/Y,IAAA;cAAA+Y,UAAA,CAAA9Y,IAAA;cAAA,OAEAqY,OAAA,CAAAle,aAAA,CAAA4V,MAAA;YAAA;cAAA+I,UAAA,CAAA9Y,IAAA;cAAA;YAAA;cAAA8Y,UAAA,CAAA/Y,IAAA;cAAA+Y,UAAA,CAAAhN,EAAA,GAAAgN,UAAA;cAEAjG,OAAA,CAAAgC,GAAA,sOAAAiE,UAAA,CAAAhN,EAAA;YAAA;cAAAgN,UAAA,CAAA9Y,IAAA;cAAA,OAIA,IAAA8U,OAAA,WAAAC,OAAA;gBAAA,OAAAzS,UAAA,CAAAyS,OAAA;cAAA;YAAA;cAGAsD,OAAA,CAAApe,YAAA;cACAoe,OAAA,CAAAne,SAAA;cACAme,OAAA,CAAApf,eAAA;cACAof,OAAA,CAAAnf,YAAA;cACAmf,OAAA,CAAAlf,UAAA;cAEAma,gBAAA,GAAA+E,OAAA,CAAA1jB,WAAA,CAAAwG,MAAA,WAAAsJ,OAAA;gBAAA,OACA4T,OAAA,CAAA5iB,aAAA,CAAA2H,QAAA,CAAAqH,OAAA,CAAAvB,EAAA;cAAA,CACA;cACAqQ,MAAA,GAAAD,gBAAA,CACAlU,GAAA,WAAAqF,OAAA;gBAAA,gBAAA/G,MAAA,CAAA+G,OAAA,CAAA9M,OAAA,IAAA8M,OAAA,CAAAlN,KAAA;cAAA,GACA8H,IAAA;cAAAyZ,UAAA,CAAA/Y,IAAA;cAAA+Y,UAAA,CAAA9Y,IAAA;cAAA,OAGA,IAAAgV,4BAAA,EACAqD,OAAA,CAAA5iB,aAAA,CAAA4J,IAAA,KACA;YAAA;cAFAmU,gBAAA,GAAAsF,UAAA,CAAAzV,IAAA;cAAA,MAGA,CAAAmQ,gBAAA,CAAAre,IAAA,KAAAqe,gBAAA,CAAAre,IAAA,CAAAiJ,MAAA;gBAAA0a,UAAA,CAAA9Y,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAiV,KAAA;YAAA;cAGAxB,eAAA,GAAAD,gBAAA,CAAAre,IAAA,CACAiK,GAAA,WAAAqF,OAAA,EAAA3F,KAAA;gBAAA,IAAAia,sBAAA,EAAAC,sBAAA;gBACA,IAAAzhB,KAAA,GACA,EAAAwhB,sBAAA,GAAAzF,gBAAA,CAAAxU,KAAA,eAAAia,sBAAA,uBAAAA,sBAAA,CAAAphB,OAAA,OAAAqhB,sBAAA,GACA1F,gBAAA,CAAAxU,KAAA,eAAAka,sBAAA,uBAAAA,sBAAA,CAAAzhB,KAAA,KACA;gBACA,IAAAE,OAAA,GAAAgN,OAAA,CAAAhN,OAAA;gBACA,uBAAAiG,MAAA,CAAAoB,KAAA,yCAAApB,MAAA,CAAAnG,KAAA,gBAAAmG,MAAA,CAAAjG,OAAA;cACA,GACA4H,IAAA,yDAEA;cACAgZ,OAAA,CAAAnf,YAAA,CAAA6F,IAAA;gBACAqW,IAAA;gBACA3d,OAAA,qDAAAiG,MAAA,CAAA2a,OAAA,CAAA5iB,aAAA,CAAA2I,MAAA,gCAAAV,MAAA,CAAA6V,MAAA;cACA;;cAEA;cACAG,WAAA;gBACA0B,IAAA;gBACA3d,OAAA;cACA;cACA4gB,OAAA,CAAAnf,YAAA,CAAA6F,IAAA,CAAA2U,WAAA;cACA2E,OAAA,CAAAlf,UAAA;cAEAwa,MAAA,GACA0E,OAAA,CAAAhe,eAAA,CACAuC,OAAA,kBAAAyb,OAAA,CAAA5iB,aAAA,CAAA2I,MAAA,EACAxB,OAAA,6FAAAc,MAAA,CACA+V,eAAA;cAAAqF,UAAA,CAAA9Y,IAAA;cAAA,OAEA,IAAAiZ,gBAAA,EAAAtF,MAAA;YAAA;cAAAzT,QAAA,GAAA4Y,UAAA,CAAAzV,IAAA;cAAA,KAEAnD,QAAA,CAAAoV,EAAA;gBAAAwD,UAAA,CAAA9Y,IAAA;gBAAA;cAAA;cACA4T,MAAA,GAAA1T,QAAA,CAAAqV,IAAA,CAAAC,SAAA;cACA6C,OAAA,CAAAle,aAAA,GAAAyZ,MAAA;cACAC,OAAA,OAAA4B,WAAA;cACA3B,MAAA;cACA8C,eAAA,GAAAjW,IAAA,CAAA0W,GAAA;cAEAnD,aAAA,YAAAA,cAAA0B,UAAA;gBACA,IAAA0B,WAAA,GAAA3W,IAAA,CAAA0W,GAAA;gBACA,IAAAC,WAAA,GAAAV,eAAA;kBACAlD,WAAA,CAAAjc,OAAA,GAAAme,UAAA;kBACAgB,eAAA,GAAAU,WAAA;kBACAe,OAAA,CAAAtC,SAAA;oBACA,IAAA7c,YAAA,GAAAmf,OAAA,CAAAzW,KAAA,CAAA1I,YAAA;oBACA,IAAAA,YAAA;sBACAA,YAAA,CAAA4I,SAAA,GAAA5I,YAAA,CAAA8c,YAAA;oBACA;kBACA;gBACA;cACA;YAAA;cAAA,KAEA;gBAAA8C,UAAA,CAAA9Y,IAAA;gBAAA;cAAA;cAAA,KAEAqY,OAAA,CAAAne,SAAA;gBAAA4e,UAAA,CAAA9Y,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAiV,KAAA;YAAA;cAAA6D,UAAA,CAAA9Y,IAAA;cAAA,OAGA4T,MAAA,CAAAqC,IAAA;YAAA;cAAAwC,mBAAA,GAAAK,UAAA,CAAAzV,IAAA;cAAAoF,IAAA,GAAAgQ,mBAAA,CAAAhQ,IAAA;cAAAlM,KAAA,GAAAkc,mBAAA,CAAAlc,KAAA;cAAA,KACAkM,IAAA;gBAAAqQ,UAAA,CAAA9Y,IAAA;gBAAA;cAAA;cACA,IAAA8T,MAAA,CAAA1V,MAAA;gBACA8V,aAAA,CAAAJ,MAAA;cACA;cAAA,OAAAgF,UAAA,CAAA7Y,MAAA;YAAA;cAIAqU,KAAA,GAAAT,OAAA,CAAAsC,MAAA,CAAA5Z,KAAA;cAAAuc,UAAA,CAAA/Y,IAAA;cAEA2X,KAAA,GAAApD,KAAA,CAAApW,KAAA;cAAAwa,UAAA,OAAAlP,2BAAA,CAAAtV,OAAA,EAEAwjB,KAAA;cAAAoB,UAAA,CAAA/Y,IAAA;cAAA2Y,UAAA,CAAAtO,CAAA;YAAA;cAAA,KAAAuO,MAAA,GAAAD,UAAA,CAAArO,CAAA,IAAA5B,IAAA;gBAAAqQ,UAAA,CAAA9Y,IAAA;gBAAA;cAAA;cAAAwU,IAAA,GAAAmE,MAAA,CAAApc,KAAA;cAAA,MACA,CAAAiY,IAAA,CAAA3X,IAAA,OAAA2X,IAAA,CAAA6B,UAAA;gBAAAyC,UAAA,CAAA9Y,IAAA;gBAAA;cAAA;cAAA,OAAA8Y,UAAA,CAAA7Y,MAAA;YAAA;cAEA9K,IAAA,GAAAqf,IAAA,CAAA4B,KAAA;cAAA,MACAjhB,IAAA;gBAAA2jB,UAAA,CAAA9Y,IAAA;gBAAA;cAAA;cAAA,OAAA8Y,UAAA,CAAA7Y,MAAA;YAAA;cAAA6Y,UAAA,CAAA/Y,IAAA;cAGA0U,QAAA,GAAAhO,IAAA,CAAAC,KAAA,CAAAvR,IAAA;cAAA,OAAAyjB,iBAAA,GACAnE,QAAA,CAAAyE,OAAA,cAAAN,iBAAA,gBAAAA,iBAAA,GAAAA,iBAAA,iBAAAA,iBAAA,gBAAAA,iBAAA,GAAAA,iBAAA,CAAAO,KAAA,cAAAP,iBAAA,eAAAA,iBAAA,CAAAnhB,OAAA;gBAAAqhB,UAAA,CAAA9Y,IAAA;gBAAA;cAAA;cACAvI,OAAA,GAAAgd,QAAA,CAAAyE,OAAA,IAAAC,KAAA,CAAA1hB,OAAA,EAEA;cAAA,MACAA,OAAA,cAAAA,OAAA;gBAAAqhB,UAAA,CAAA9Y,IAAA;gBAAA;cAAA;cAAA,OAAA8Y,UAAA,CAAA7Y,MAAA;YAAA;cAIA6T,MAAA,IAAArc,OAAA;cACAyc,aAAA,CAAAJ,MAAA;YAAA;cAAAgF,UAAA,CAAA9Y,IAAA;cAAA;YAAA;cAAA8Y,UAAA,CAAA/Y,IAAA;cAAA+Y,UAAA,CAAAvO,EAAA,GAAAuO,UAAA;cAGAjG,OAAA,CAAA1I,KAAA,wBAAA2O,UAAA,CAAAvO,EAAA;YAAA;cAAAuO,UAAA,CAAA9Y,IAAA;cAAA;YAAA;cAAA8Y,UAAA,CAAA9Y,IAAA;cAAA;YAAA;cAAA8Y,UAAA,CAAA/Y,IAAA;cAAA+Y,UAAA,CAAAxC,EAAA,GAAAwC,UAAA;cAAAJ,UAAA,CAAAlO,CAAA,CAAAsO,UAAA,CAAAxC,EAAA;YAAA;cAAAwC,UAAA,CAAA/Y,IAAA;cAAA2Y,UAAA,CAAAjO,CAAA;cAAA,OAAAqO,UAAA,CAAApO,MAAA;YAAA;cAAAoO,UAAA,CAAA9Y,IAAA;cAAA;YAAA;cAAA8Y,UAAA,CAAA/Y,IAAA;cAAA+Y,UAAA,CAAAM,EAAA,GAAAN,UAAA;cAIAjG,OAAA,CAAA1I,KAAA,4BAAA2O,UAAA,CAAAM,EAAA;YAAA;cAAAN,UAAA,CAAA9Y,IAAA;cAAA;YAAA;cAAA8Y,UAAA,CAAA9Y,IAAA;cAAA;YAAA;cAAA,MAIA,IAAAiV,KAAA;YAAA;cAAA6D,UAAA,CAAA9Y,IAAA;cAAA;YAAA;cAAA8Y,UAAA,CAAA/Y,IAAA;cAAA+Y,UAAA,CAAAO,EAAA,GAAAP,UAAA;cAAA,MAIAA,UAAA,CAAAO,EAAA,CAAA7hB,OAAA;gBAAAshB,UAAA,CAAA9Y,IAAA;gBAAA;cAAA;cACA6S,OAAA,CAAAgC,GAAA;cAAA,OAAAiE,UAAA,CAAA7Y,MAAA;YAAA;cAGA4S,OAAA,CAAA1I,KAAA,mBAAA2O,UAAA,CAAAO,EAAA;cACAhB,OAAA,CAAAtd,QAAA,CAAAoP,KAAA;cACA,IAAAkO,OAAA,CAAAnf,YAAA;gBACAmf,OAAA,CAAAnf,YAAA,IAAAzB,OAAA;cACA;YAAA;cAAAqhB,UAAA,CAAA/Y,IAAA;cAEAsY,OAAA,CAAAle,aAAA;cACA;cACA,IAAAke,OAAA,CAAApf,eAAA;gBACAof,OAAA,CAAAlf,UAAA;gBACAkf,OAAA,CAAApe,YAAA;cACA;cAAA,OAAA6e,UAAA,CAAApO,MAAA;YAAA;YAAA;cAAA,OAAAoO,UAAA,CAAA/X,IAAA;UAAA;QAAA,GAAAuX,SAAA;MAAA;IAEA;IACA;IACAgB,aAAA,WAAAA,cAAA;MACA,KAAApf,SAAA;MACA,SAAAC,aAAA;QACA,KAAAA,aAAA,CAAA4V,MAAA;MACA;MACA,KAAA9W,eAAA;MACA,KAAAC,YAAA;MACA,KAAAC,UAAA;MACA,KAAAc,YAAA;MACA,KAAAE,aAAA;IACA;IACAof,aAAA,WAAAA,cAAA;MACA,SAAAnf,UAAA;QACA,KAAA8Y,UAAA;MACA,gBAAA9Y,UAAA;QACA,KAAAmc,YAAA;MACA,gBAAAnc,UAAA;QACA,KAAAge,cAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}