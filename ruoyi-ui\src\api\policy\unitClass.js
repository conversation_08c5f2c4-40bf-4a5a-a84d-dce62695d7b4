import request from '@/utils/request'

// 查询政策库发布单位分类列表
export function listUnitClass(query) {
  return request({
    url: '/policy/unitClass/list',
    method: 'get',
    params: query
  })
}

// 查询政策库发布单位分类详细
export function getUnitClass(id) {
  return request({
    url: '/policy/unitClass/' + id,
    method: 'get'
  })
}

// 新增政策库发布单位分类
export function addUnitClass(data) {
  return request({
    url: '/policy/unitClass',
    method: 'post',
    data: data
  })
}

// 修改政策库发布单位分类
export function updateUnitClass(data) {
  return request({
    url: '/policy/unitClass/edit',
    method: 'post',
    data: data
  })
}

// 删除政策库发布单位分类
export function delUnitClass(data) {
  return request({
    url: '/policy/unitClass/remove',
    method: 'post',
    data: data
  })
}
