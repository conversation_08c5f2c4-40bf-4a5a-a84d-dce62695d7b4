<template>
  <div id="main2" style="width: 100%; height: 100%"></div>
</template>
 
<script>
import { demo2 } from "../demo";
import request from "@/utils/request";
import * as echarts from "echarts";

export default {
  props: {
    graphList: {
      type: Object,
      default: () => {
        return {
          nodeList: [],
          relationList: [],
        };
      },
    },
  },
  watch: {
    graphList(value) {
      if (value) {
        this.initChart(value);
      }
    },
  },
  data() {
    return {
      myChart: null,
      option: {},
    };
  },
  mounted() {},

  components: {},
  methods: {
    /**
     * 初始化
     */
    initChart(data) {
      let chartDom = document.getElementById("main2");
      let myChart = echarts.init(chartDom);
      let option = {
        title: {
          show: false,
        },
        tooltip: {},
        legend: [
          {
            show: false,
            textStyle: {
              color: "#fff",
              fontWeight: 600,
            },
            // selectedMode: 'single',
            data: data.categories.map(function (a) {
              return a;
            }),
            formatter: function (name) {
              return "  ";
            },
            top: "3%",
          },
        ],
        animationDuration: 1500,
        animationEasingUpdate: "quinticInOut",
        color: data.color,
        series: [
          {
            name: "",
            type: "graph",
            legendHoverLink: false,
            layout: "none",
            data: data.nodes,
            links: data.links,
            categories: data.categories,
            roam: true,
            label: {
              position: "right",
              formatter: "{b}",
            },
            lineStyle: {
              color: "source",
              curveness: 0.4,
              lineStyle: {
                width: 1,
              },
            },
            emphasis: {
              focus: "node",
              lineStyle: {
                width: 30,
                type: "solid",
              },
            },
            emphasis: {
              focus: "adjacency",
              lineStyle: {
                width: 6,
              },
            },
            focusNodeAdjacency: true,
          },
        ],
      };
      //随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        myChart.resize();
      });
      setTimeout(() => {
        myChart.resize();
      }, 1);
      myChart.setOption(option);

      // 监听鼠标移入事件
      myChart.on("mouseover", function (params) {
        if (params.dataType === "node") {
          // 获取所有的edges
          var links = option.series[0].links;
          // 遍历edges，如果节点名称匹配，则标记为加粗

          for (var i = 0; i < links.length; i++) {
            if (
              links[i].source === String(params.data.id) ||
              links[i].target === String(params.data.id)
            ) {
              links[i].lineStyle = {
                width: 6,
                opacity: 1,
              };
            }
          }
          console.log(links);
          // 更新图表配置
          myChart.setOption({
            series: [
              {
                links: links,
              },
            ],
          });
        }
      });

      // 监听鼠标移出事件，可选操作，恢复线的原始粗细
      myChart.on("mouseout", function (params) {
        if (params.dataType === "node") {
          // 重置edges的lineStyle
          var links = option.series[0].links;
          for (var i = 0; i < links.length; i++) {
            if (
              links[i].source === String(params.data.id) ||
              links[i].target === String(params.data.id)
            ) {
              links[i].lineStyle = {
                width: 1.5,
                opacity: 1,
              };
            }
          }
          // 更新图表配置
          myChart.setOption({
            series: [
              {
                links: links,
              },
            ],
          });
        }
      });
    },
  },
  beforeDestroy() {
    /* 页面组件销毁的时候，别忘了移除绑定的监听resize事件，否则的话，多渲染几次
      容易导致内存泄漏和额外CPU或GPU占用哦*/
    window.removeEventListener("resize", () => {
      this.myChart.resize();
    });
  },
};
</script>

<style lang='scss'>
</style>