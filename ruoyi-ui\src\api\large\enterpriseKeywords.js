import request from '@/utils/request'

// 查询信通院大屏 企业列 检索词库列表
export function listEnterpriseKeywords(query) {
  return request({
    url: '/large/enterpriseKeywords/list',
    method: 'get',
    params: query
  })
}

// 检索词库列表（排除节点）
export function listEnterpriseKeywordsExcludeChild(deptId) {
  return request({
    url: '/large/enterpriseKeywords/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询信通院大屏 企业列 检索词库详细
export function getEnterpriseKeywords(id) {
  return request({
    url: '/large/enterpriseKeywords/' + id,
    method: 'get'
  })
}

// 新增信通院大屏 企业列 检索词库
export function addEnterpriseKeywords(data) {
  return request({
    url: '/large/enterpriseKeywords',
    method: 'post',
    data: data
  })
}

// 修改信通院大屏 企业列 检索词库
export function updateEnterpriseKeywords(data) {
  return request({
    url: '/large/enterpriseKeywords/edit',
    method: 'post',
    data: data
  })
}

// 删除信通院大屏 企业列 检索词库
export function delEnterpriseKeywords(id) {
  let data = new FormData()
  data.append('id', id)
  return request({
    url: '/large/enterpriseKeywords/remove',
    method: 'post',
    data: data
  })
}
