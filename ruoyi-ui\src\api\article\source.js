import request from "@/utils/request";

// 查询文章数据源列表
export function listSource(query) {
  return request({
    url: "/article/source/pageList",
    method: "get",
    params: query,
  });
}

// 查询文章数据源详细
export function getSource(id) {
  return request({
    url: "/article/source/" + id,
    method: "get",
  });
}

// 新增文章数据源
export function addSource(data) {
  return request({
    url: "/article/source",
    method: "post",
    data: data,
  });
}

// 修改文章数据源
export function updateSource(data) {
  return request({
    url: "/article/source/edit",
    method: "post",
    data: data,
  });
}

// 删除文章数据源
export function delSource(id) {
  return request({
    url: "/article/source/remove",
    method: "post",
    data: id,
  });
}
/* 获取分类下拉框 */
export function getSourceSelect() {
  return request({
    url: "/article/classify/classifyAll",
    method: "get",
  });
}
