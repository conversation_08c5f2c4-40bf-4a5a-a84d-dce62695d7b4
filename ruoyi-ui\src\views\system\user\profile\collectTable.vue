<template>
  <div :class="type == '' ? 'app-container' : ''">
    <div v-if="type == ''">
      <h1>收藏列表</h1>
    </div>
    <div>
      <el-table :data="tableData" style="width: 100%" border :header-cell-style="{ textAlign: 'center' }" height="calc(100vh - 220px)" ref="tableRef">
        <el-table-column type="index" label="序号" width="80" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="cnTitle" label="文章标题" align="left" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="title" @click="openNewView(scope.row)">{{ scope.row.cnTitle || scope.row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="publishTime" label="发布时间" width="160" align="center"
          show-overflow-tooltip></el-table-column>
        <el-table-column prop="address" label="操作" width="80" align="center">
          <template slot-scope="scope">
            <span class="title" @click="removeCollect(scope.row)">删除</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination class="pagination" @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="pageNum" :page-sizes="[10, 20, 30, 40]" :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </div>
  </div>
</template>

<script>
import { collectList, removeCollect } from "@/api/system/user";

export default {
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableData: [],
      pageNum: 1,
      pageSize: 50,
      total: 0,
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    async getList() {
      let res = await collectList({ pageNum: this.pageNum, pageSize: this.pageSize })
      if (res.code == 200) {
        this.tableData = res.rows
        this.total = res.total
        this.$nextTick(() => {
          this.scrollToTop();
        });
      }
    },
    // 表格滚动到顶部
    scrollToTop() {
      if (this.$refs.tableRef) {
        const tableEl = this.$refs.tableRef.$el.querySelector(
          ".el-table__body-wrapper"
        );
        if (tableEl) {
          tableEl.scrollTop = 0;
        }
      }
    },
    /* 跳转新页面 */
    openNewView(item) {
      if (item.sourceType == 1) {
        window.open(item.shortUrl)
      } else if (item.sourceType == 2) {
        window.open(item.originalUrl)
      }
    },
    handleSizeChange(size) {
      this.pageSize = size
      this.getList()
    },
    handleCurrentChange(current) {
      this.pageNum = current
      this.getList()
    },
    removeCollect(colData) {
      this.$confirm('此操作将取消收藏该条文章, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeCollect([colData.id]).then(response => {
          if (response.code == 200) {
            this.$message({ message: '删除成功', type: 'success' })
            this.getList()
          }
        }).catch(err => {
          this.$message({ message: '删除出现错误', type: 'error' })
        })
      }).catch(() => { })
    },
  }
}
</script>

<style lang="scss" scoped>
.title:hover {
  color: #1d8af0;
  border-bottom: solid 1px #1d8af0;
  cursor: pointer;
}

.pagination {
  text-align: right;
  margin-top: 20px;
}
</style>