<template>
  <div style="height: 100%; display: flex" class="three">
    <div class="left">
      <TitleComponent :title="'网络安全技术信息'" />
      <div style="height: 559px;width: 100%;margin-bottom: 26px;background: rgba(0, 0, 0, 0.15);">
        <internetSecurity @expertFun="expertFun" />
      </div>
      <TitleComponent :title="'网络安全行业全景'" />
      <div style="height: 250px;width: 100%;background: rgba(0, 0, 0, 0.15);margin-top: 15px;">
        <div class="info-tag">
          <div v-for="(item, index) in arrList" :key="index" :style="{
            color: item.bgColor,
            background: `${item.bgColor}30`,
            border: `1px solid ${item.bgColor}`,
            margin: `${item.x}px 0 -${item.y}px`,
          }" class="random-div" @click="hangyequanjing(item)">
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
    <div class="center">
      <TitleComponent :title="'风险态势'" :width="'820px'" :type="'2'" />
      <div style="height: 570px; width: 100%; margin-bottom: 15px">
        <linEchart :valueObj="lineList" @openList="openList" />
      </div>
      <!-- <TitleComponent :title="'关键企业'" :width="'820px'" :type="'2'" /> -->
      <div :class="['title', 'title2']" :style="{ width: '820px', height: '32px' }">
        <span @click="guanjianType = 1" :class="{ 'titleColor': guanjianType == 1 }"> 关键企业 </span>
        /
        <span @click="guanjianType = 2" :class="{ 'titleColor': guanjianType == 2 }"> 关键人 </span>
      </div>
      <div style="height: 266px; width: 100%; background: rgba(0, 0, 0, 0.15)">
        <div class="img-all" v-if="guanjianType == 1">
          <div class="img-info" v-for="(item, index) in allImgList" :key="index" @click="guanjianqiye(item)">
            <div class="img">
              <img :src="baseUrl + item.cover" alt="" />
            </div>
          </div>
        </div>
        <div class="img-all" v-else>
          <div class="img-info" v-for="(item, index) in allImgList1" :key="index" @click="guanjianren(item)">
            <div class="img1">
              <div>{{ item.name }}</div>
              <img :src="baseUrl + item.cover" alt="" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <TitleComponent :title="'安全风险指数'" />
      <div style="height: 255px;width: 100%;margin-bottom: 24px;background: rgba(0, 0, 0, 0.15);">
        <pieEchart @openList="openList" />
      </div>
      <TitleComponent :title="'前沿技术分析'" />
      <div style="height: 255px;width: 100%;margin-bottom: 24px;background: rgba(0, 0, 0, 0.15); ">
        <lineEchartY @openList="openList" />
      </div>
      <TitleComponent :title="'突发事件风险指标'" />
      <div style="height: 255px; width: 100%; background: rgba(0, 0, 0, 0.15)">
        <keyWordVue :wordValueList="wordValueList" />
      </div>
    </div>

    <el-dialog :title="title" :visible.sync="articleDialogVisible" width="800px" append-to-body
      :before-close="handleClose" :close-on-click-modal="false">
      <div
        style="line-height: 30px;display: -webkit-box;-webkit-line-clamp: 3;-webkit-box-orient: vertical;overflow: hidden;min-height: 90px;text-indent:2em;">
        {{ cnContent ? cnContent : '暂无介绍' }}
      </div>
      <div style="line-height: 20px;margin: 5px 0;font-size: 18px;">相关内容</div>
      <el-table v-loading="guanjianqiyeLoading" :data="list" style="width: 100%" :show-header="false" ref="table"
        class="table-all" height="400" @cell-click="openNewView">
        <el-table-column prop="title" label="标题" width="510" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-html="changeColor(scope.row.cnTitle || scope.row.title)"></span>
          </template>
        </el-table-column>
        <el-table-column prop="sourceName" label="数据源" width="140" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="publishTime" label="发布时间" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>
              {{ parseTime(scope.row.publishTime || scope.row.gatherTime, "{y}-{m}-{d}") }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        :background="false" @pagination="guanjianqiyeList" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="drawerInfo.cnTitle || drawerInfo.title" :visible.sync="articleDialogVisible1" width="800px"
      append-to-body :before-close="handleClose1" :close-on-click-modal="false">
      <div style="line-height: 30px" v-html="drawerInfo.cnContent"></div>
      <el-empty description="当前文章暂无数据" v-if="!drawerInfo.cnContent"></el-empty>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose1">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="title" :visible.sync="articleDialogVisible2" width="800px" append-to-body
      :before-close="handleClose2" :close-on-click-modal="false">
      <el-table v-loading="hangyequanjingLoading" :data="list" style="width: 100%" :show-header="false" ref="table"
        class="table-all1" height="510" @cell-click="openNewView1">
        <el-table-column prop="title" label="标题" width="510" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-html="changeColor(scope.row.cnTitle || scope.row.title)"></span>
          </template>
        </el-table-column>
        <el-table-column prop="sourceName" label="数据源" width="140" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="publishTime" label="发布时间" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>
              {{ parseTime(scope.row.publishTime || scope.row.gatherTime, "{y}-{m}-{d}") }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        :background="false" @pagination="hangyequanjingList" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose2">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 抽屉 -->
    <drawerComponent :open.sync="open" />
  </div>
</template>
<script>
import drawerComponent from "./components/drawerComponent.vue";
import TitleComponent from "./components/titleVue.vue";
import internetSecurity from "./components/internetSecurity.vue";
import expertOpinions from "./components/expertOpinions.vue";
import linEchart from "./components/linEchart.vue";
import pieGlEchart from "./components/pieGlEchart.vue";
import sankeyEchart from "./components/sankeyEchart.vue";
import keyWordVue from "./components/keyWord.vue";
import pieEchart from "./components/pieEchart.vue";
import lineEchartY from "./components/lineEchartY.vue";
import { demo5 } from "./demo";
import { listEnterprise } from "@/api/large/enterprise";
import { listCharacter } from "@/api/large/character";
import { largeEnterpriseDataList, largeNseKeywordsNames, largeEnterpriseData, nsStatisticsAllData, largeNsioDataList, largeNsioKeywordsNames, largeNsioData, largeCharactersData, largeNscKeywordsNames, largeCharactersDataList } from "@/api/bigScreen/index1";
import { listNsioKeywords } from "@/api/large/nsioKeywords";

export default {
  data() {
    return {
      lineList: {
        xData: [],
        yData: [],
      },
      wordValueList: [],
      allImgList: [],
      allImgList1: [],
      guanjianType: 1,
      baseUrl: process.env.VUE_APP_BASE_API,
      arrList: [],
      articleDialogVisible: false,
      title: '',
      cnContent: "",
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      list: [],
      guanjianqiyeLoading: false,
      keywords: [],
      drawerInfo: {},
      articleDialogVisible1: false,
      hangyequanjingLoading: false,
      articleDialogVisible2: false,
    };
  },
  components: {
    drawerComponent,
    TitleComponent,
    internetSecurity,
    expertOpinions,
    linEchart,
    pieGlEchart,
    sankeyEchart,
    keyWordVue,
    pieEchart,
    lineEchartY,
  },
  mounted() {
    this.init();
    this.getGuanjianqiyeList()
  },
  beforeDestroy() { },
  methods: {
    init() {
      nsStatisticsAllData().then(res => {
        const data = res.data
        const months = Object.keys(data).sort().map(date => date);
        const uniqueAiTrendNames = [...new Set(Object.values(data).flat().map(item => item.nsTrendName))];

        let yData = []
        let color = ["#5B8FF9", "#5AD8A6", "#5D7092", "#F6BD16", "#E8684A", "#6DC8EC", "#9270CA", "#FF9D4D", "#5B8FF9", "#5AD8A6", "#5D7092", "#F6BD16", "#E8684A", "#6DC8EC", "#9270CA", "#FF9D4D",];
        uniqueAiTrendNames.map((rows, key) => {
          yData.push({
            name: rows,
            type: "line",
            data: months.map(month => {
              const item = data[month].find(item => item.nsTrendName === rows);
              if (item) {
                return item.nsTrendTotal;
              } else {
                return 0;
              }
            }),
            connectNulls: true,
            smooth: true,
            itemStyle: {
              color: color[key - 1],
            },
            symbolSize: 8,
            lineStyle: {
              width: 2, // 设置线宽为5
            },
          });
        })
        let lineList = {
          xData: months,
          yData: yData,
        }
        this.$set(this, 'lineList', lineList)
      })
      this.wordValueList = demo5;
      this.NsioLoading = true
      listNsioKeywords().then(response => {
        this.arrList = response.data.filter((item) => item.parentId === 0).map(item => {
          item.x = Math.floor(Math.random() * 10)
          item.y = Math.floor(Math.random() * 10)
          return item
        })
        this.NsioLoading = false;
      });
    },
    getGuanjianqiyeList() {
      listEnterprise({ pageNum: 1, pageSize: 10, type: 2 }).then(response => {
        this.allImgList = response.rows;
        this.total = response.total;
      });
      listCharacter({ pageNum: 1, pageSize: 10, type: 2 }).then(response => {
        this.allImgList1 = response.rows
      });
    },
    getRandomInt(min, max) {
      min = Math.ceil(min); // 将min向上取整
      max = Math.floor(max); // 将max向下取整
      return Math.floor(Math.random() * (max - min + 1)) + min; // 返回min到max之间的随机整数
    },
    handleClose() {
      this.articleDialogVisible = false;
    },
    handleClose2() {
      this.articleDialogVisible2 = false;
    },
    expertFun(item) {
      this.articleDialogVisible = true;
    },
    openList() {
      this.open = true;
    },
    async guanjianqiye(item) {
      await largeNseKeywordsNames(item.name).then((res) => {
        this.keywords = res.data;
        this.keywords.push(item.name)
      });
      this.cnContent = item.summary
      this.title = item.name
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
      this.guanjianqiyeList()
      this.articleDialogVisible = true;
    },
    async guanjianqiyeList() {
      this.guanjianqiyeLoading = true
      largeEnterpriseDataList({ enterpriseName: this.title, ...this.queryParams, type: 2 }).then((res) => {
        this.list = res.rows;
        this.total = res.total;
        this.guanjianqiyeLoading = false
      });
    },
    async hangyequanjing(item) {
      await largeNsioKeywordsNames(item.name).then((res) => {
        this.keywords = res.data;
        this.keywords.push(item.name)
      });
      this.title = item.name
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
      this.hangyequanjingList()
      this.articleDialogVisible2 = true;
    },
    async hangyequanjingList() {
      this.hangyequanjingLoading = true
      largeNsioDataList({ nsName: this.title, ...this.queryParams }).then((res) => {
        this.list = res.rows;
        this.total = res.total;
        this.hangyequanjingLoading = false
      });
    },
    // 关键字替换
    changeColor(str) {
      let Str = str;
      if (Str) {
        let keywords = this.keywords;
        keywords.map((keyitem, keyindex) => {
          if (keyitem && keyitem.length > 0) {
            // 匹配关键字正则
            let replaceReg = new RegExp(keyitem, "g");
            // 高亮替换v-html值
            let replaceString =
              '<span class="highlight"' +
              ' style="color: #ff7500;">' +
              keyitem +
              "</span>";
            Str = Str.replace(replaceReg, replaceString);
          }
        });
      }
      return Str;
    },
    async openNewView(item) {
      if (this.guanjianType == 1) {
        await largeEnterpriseData(item.id).then((res) => {
          this.drawerInfo = res.data;
        });
      } else {
        await largeCharactersData(item.id).then((res) => {
          this.drawerInfo = res.data;
        });
      }
      let content = this.drawerInfo.article || this.drawerInfo.content;
      if (content) {
        content = content.replace(/\n/g, "<br>");
        content = content.replace(/\${[^}]+}/g, "<br>");
        content = content.replace("|xa0", "");
        content = content.replace("opacity: 0", "");
        content = content.replace(/<img\b[^>]*>/gi, "");
        content = content.replace(/ style="[^"]*"/g, "");
      }
      this.drawerInfo.cnContent = content;
      this.articleDialogVisible1 = true;
    },
    async openNewView1(item) {
      await largeNsioData(item.id).then((res) => {
        this.drawerInfo = res.data;
      });
      let content = this.drawerInfo.article || this.drawerInfo.content;
      if (content) {
        content = content.replace(/\n/g, "<br>");
        content = content.replace(/\${[^}]+}/g, "<br>");
        content = content.replace("|xa0", "");
        content = content.replace("opacity: 0", "");
        content = content.replace(/<img\b[^>]*>/gi, "");
        content = content.replace(/ style="[^"]*"/g, "");
      }
      this.drawerInfo.cnContent = content;
      this.articleDialogVisible1 = true;
    },
    handleClose1() {
      this.articleDialogVisible1 = false;
    },
    async guanjianren(item) {
      await largeNscKeywordsNames(item.name).then((res) => {
        this.keywords = res.data;
        this.keywords.push(item.name)
      });
      this.cnContent = item.summary
      this.title = item.name
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
      this.guanjianrenList()
      this.articleDialogVisible = true;
    },
    async guanjianrenList() {
      this.guanjianqiyeLoading = true
      largeCharactersDataList({ enterpriseName: this.title, ...this.queryParams, type: 2 }).then((res) => {
        this.list = res.rows;
        this.total = res.total;
        this.guanjianqiyeLoading = false
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.three {
  padding-top: 20px;
  height: 100%;
  width: 100%;

  .left {
    width: 517px;

    .info-tag {
      width: 100%;
      height: 100%;
      position: relative;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-evenly;

      >div {
        min-width: 100px;
        text-align: center;
        border-radius: 20px;
        padding: 0 15px;
        font-size: 14px;
        font-weight: bold;
        height: 32px;
        line-height: 30px;
      }
    }
  }

  .center {
    margin-left: 20px;
    width: 820px;
  }

  .right {
    margin-left: 13px;
    width: 520px;
  }
}

.img-all {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 0 10px 5px;

  .img-info {
    position: relative;
    display: inline-block;
    cursor: pointer;

    .img {
      width: 150px;
      height: 90px;
      background: #fff;
      vertical-align: middle;
      line-height: 160px;
      margin: 0px 5px;

      img {
        width: 150px;
        height: 90px;
        display: inline-block;
      }
    }

    .img1 {
      width: 120px;
      height: 120px;
      background: #fff;
      vertical-align: middle;
      position: relative;
      margin: 0px 20px;

      div {
        position: absolute;
        bottom: 0;
        height: 20px;
        width: 100%;
        background-color: #31313187;
        color: #fff;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      img {
        width: 120px;
        height: 120px;
        display: inline-block;
      }
    }
  }
}

.img-all-dialog {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;

  .img-info {
    margin: auto;
    position: relative;
    display: inline-block;

    width: 25%;

    .img {
      width: 150px;
      height: 90px;
      background: #fff;
      vertical-align: middle;
      line-height: 160px;
      margin: auto;

      img {
        width: 150px;
        margin: auto;
        display: inline-block;
      }
    }
  }
}

::v-deep .el-dialog {
  background: url("../../assets/bigScreenFour/dialog.png") no-repeat;
  background-size: 100% 100% !important;
  background-size: cover;
  height: 800px;

  .el-dialog__header {
    background-color: #1d233400;
    font-size: 30px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);
    height: 100px;
    padding-left: 31px;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    .el-dialog__title {
      color: #fff;
      overflow: hidden;
      width: calc(100% - 40px);
      height: 100%;
      display: flow;
      line-height: 100px;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .el-dialog__body {
    background-color: #2a304000;
    color: #f2f2f2;
    height: calc(100% - 160px);
    overflow: auto;
  }

  .el-dialog__footer {
    background-color: #1d233400;
    padding: 0 27px 28px 0;
  }

  .el-button {
    background-color: #002766;
    color: #fff;
    border: 0px;
  }

  .el-dialog__headerbtn .el-dialog__close {
    background: url("../../assets/bigScreenFour/close.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
    width: 31px;
    height: 31px;
    top: 16px;

    &::before {
      content: none;
    }
  }
}

::v-deep .el-table {
  background-color: #2a304000;

  tr {
    color: #f2f2f2;
    background: url("../../assets/bigScreenTwo/弹窗列表.png") no-repeat;
    background-size: 100% 100% !important;
    height: 68px;
    padding: 0 0 0 65px;
    margin-bottom: 10px;
    display: block;
    width: 790px;
  }

  td.el-table__cell {
    border-bottom: 1px solid #1d233400;
    height: 68px;
    line-height: 68px;
    font-size: 16px;
    text-shadow: 0px 0px 9px rgba(30, 198, 255, 0.8);
  }

  &::before {
    height: 0;
  }
}

.table-all {
  ::v-deep .el-table {
    background-color: #2a304000;
  }

  ::v-deep tr {
    color: #f2f2f2;
    background: #2a304000;
    height: 30px;
    padding: 0 0 0 0px;
    margin-bottom: 10px;
    display: block;
    width: 790px;
  }

  ::v-deep td.el-table__cell {
    border-bottom: 1px solid #1d233400;
    height: 30px;
    line-height: 30px;
    font-size: 16px;
    text-shadow: 0px 0px 9px rgba(30, 198, 255, 0.8);
  }
}

.table-all1 {
  ::v-deep .el-table {
    background-color: #2a304000;
  }

  ::v-deep tr {
    color: #f2f2f2;
    background: #2a304000;
    height: 41px;
    padding: 0 0 0 0px;
    margin-bottom: 10px;
    display: block;
    width: 790px;
  }

  ::v-deep td.el-table__cell {
    border-bottom: 1px solid #1d233400;
    height: 41px;
    line-height: 41px;
    font-size: 18px;
    text-shadow: 0px 0px 9px rgba(30, 198, 255, 0.8);
  }
}

::v-deep .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell,
::v-deep .el-table__empty-block {
  background-color: #2a304000;
  color: #f2f2f2;
  cursor: pointer;
}

::v-deep .pagination-container {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 0;

  .el-select .el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }

  .el-pagination__editor.el-input .el-input__inner {
    background: #2a304000;
    border-color: #ffffff;
    color: #fff;
  }
}

::v-deep .el-pagination__total,
::v-deep .el-pagination__jump {
  color: #f2f2f2;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination button:disabled {
  background-color: #ffffff00;
  color: #fff;
}

::v-deep .el-pager li {
  background: #ffffff00;
  color: #fff;

  &.active {
    color: #1890ff;
  }
}
</style>
<style lang="scss">
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}

/* 保持滚动效果 */
.scrollable {
  overflow-y: scroll;
}

/* 隐藏滚动条 */
.scrollable {
  scrollbar-width: none;
  /* Firefox */
}

/* 隐藏滚动条 */
.scrollable {
  -ms-scrollbar-face-color: transparent;
  /* IE and Edge */
  -ms-scrollbar-3dlight-color: transparent;
  /* IE and Edge */
}

/* 保持滚动效果 */
</style>
<style lang="scss" scoped>
.title {
  background: url("../../assets/bigScreenFour/title.png") -3px 0px no-repeat;
  background-size: 100% 100% !important;
  background-size: cover;
  font-family: "pingFangMedium";
  font-size: 18px;
  color: #ffffff;
  line-height: 32px;
  letter-spacing: 2px;
  text-align: left;
  font-style: normal;
  padding-left: 35px;

  span {
    cursor: pointer;
  }
}

.title2 {
  background: url("../../assets/bigScreenFour/title-long.png") -3px 0px no-repeat;
}

.titleColor {
  color: #EE9900;
}
</style>
