<template>
  <div id="main" style="width: 100%; height: 100%"></div>
</template>
 
<script>
import mapdata from "./mapData.json";
import request from "@/utils/request";
import * as echarts from "echarts";
import { nameMap } from "../demo";

export default {
  data() {
    return {
      myChart: null,
      option: {},
      mapDataArr: [],
      tip: require("@/assets/bigScreen/maptip.png"),
      tipActive: require("@/assets/bigScreen/maptip-active.png"),
      //动的点 { value: [116.405285, 39.904989] }
      dataTmp: [],
      //柱状图
      barList: [],
      loopTimer: null,
    };
  },
  props: {
    mapList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  watch: {
    mapList(value) {
      if (value) {
        if (value.length > 0) {
          this.dataTmp = [];
          this.barList = [];
          value.forEach((item) => {
            this.dataTmp.push({
              name: item.name,
              value: [Number(item.longitude), Number(item.latitude)],
            });
            this.barList.push({
              name: item.name,
              value: item.tally,
              coords: [
                [Number(item.longitude), Number(item.latitude)],
                [
                  Number(item.longitude),
                  Number(item.latitude) + item.tally * this.lineMaxHeight(),
                ], //*
              ],
            });
          });
          this.initChart();
        }
      }
    },
  },
  mounted() {},

  components: {},
  methods: {
    /**
     * 初始化渲染中国地图
     */
    initChart() {
      let chartDom = document.getElementById("main");
      const worldMap = this.formatWorldMapToZH(mapdata);
      echarts.registerMap("world", { geoJSON: worldMap });

      let myChart = echarts.init(chartDom);

      let option = {
        geo: {
          map: "world",
          zoom: 1.2,

          show: true,
          roam: false, //禁止缩放
          label: {
            normal: {
              show: false,
            },
            emphasis: {
              show: false,
            },
          },
          itemStyle: {
            normal: {
              areaColor: "#0756F9",
              borderColor: "#1BDCFF30", //线
              borderWidth: 1,
              borderJoin: "round",
              shadowColor: "#ffffff", //外发光
              shadowOffsetX: 1,
              shadowOffsetY: 2,
              shadowBlur: 1, //图形阴影的模糊大小
              // opacity: 0.8,
            },
            emphasis: {
              areaColor: "#2f9eff", //悬浮区背景
            },
          },
          select: {
            itemStyle: {
              areaColor: "#2f9eff", //悬浮区背景
            },
          },
        },

        series: [
          {
            type: "map",
            geoIndex: 0,
            data: this.barList,
          },

          // 柱状体的主干
          {
            type: "lines",
            zlevel: 5,
            lineStyle: {
              width: 10, //尾迹线条宽度
              opacity: 1, //尾迹线条透明度'
              curveness: 0, //尾迹线条曲直度
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#BA86FC", // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: "#6AB1FE", // 0% 处的颜色
                  },

                  {
                    offset: 1,
                    color: "#1BDCFF", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
              //鼠标移入高光亮样式
              emphasis: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#00FF75", // 0% 处的颜色
                    },
                    {
                      offset: 0.5,
                      color: "#0EEDBA", // 0% 处的颜色
                    },

                    {
                      offset: 1,
                      color: "#1BDCFF", // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
            },
            selectedMode: false, //只有配置了这个选项才能有选中状态
            // select: {
            //   lineStyle: {
            //     color: {
            //       type: "linear",
            //       x: 0,
            //       y: 0,
            //       x2: 0,
            //       y2: 1,
            //       colorStops: [
            //         {
            //           offset: 0,
            //           color: "#FFC368", // 0% 处的颜色
            //         },
            //         {
            //           offset: 0.5,
            //           color: "#8DCFB495", // 0% 处的颜色
            //         },

            //         {
            //           offset: 1,
            //           color: "#FFC36890", // 100% 处的颜色
            //         },
            //       ],
            //       global: false, // 缺省为 false
            //     },
            //   },
            //   label: {
            //     position: "top",
            //     show: true,
            //     backgroundColor: {
            //       image: this.tip,
            //     },
            //     width: 75,
            //     height: 50,
            //     lineHeight: 18,
            //     padding: [20, 20],
            //     formatter: function (params) {
            //       var name = params.name;
            //       var value = params.value;
            //       var data = 600;
            //       var text = `{fname|${name}}\n{img|${value}}\n{fdata|${data}}`;
            //       return text;
            //     },
            //     rich: {
            //       fname: {
            //         color: "#E59B0A",
            //         fontSize: 12,
            //       },
            //       fdata: {
            //         color: "#FFE82A",
            //         fontSize: 12,
            //       },
            //       img: {
            //         color: "#0EB1FE",
            //         fontSize: 14,
            //         fontWeight: 600,

            //         align: "center",
            //       },
            //     },
            //   },
            // },
            // 鼠标移入高光亮样式

            label: {
              position: "top",
              normal: {
                backgroundColor: {
                  image: this.tip,
                },
                padding: [2, 10, 0, 10],
                lineHeight: 34,
                show: true,
                formatter: function (params) {
                  return ["{img|" + params.value + "}"];
                },
                rich: {
                  img: {
                    color: "#fff",
                    fontSize: 24,
                    fontWeight: 600,
                    fontFamily: "League-Gothic-Regular",
                  },
                },
              },
            },
            emphasis: {
              label: {
                position: "top",
                offset: [0, 10],
                show: true,
                backgroundColor: {
                  image: this.tipActive,
                },
                padding: [2, 10, 0, 10],
                lineHeight: 34,
                show: true,
                formatter: function (params) {
                  return ["{img|" + params.value + "}"];
                },
                rich: {
                  img: {
                    color: "#fff",
                    fontSize: 24,
                    fontWeight: 600,
                    fontFamily: "League-Gothic-Regular",
                  },
                },
              },
            },
            data: this.barList,
          },
          // 波浪
          {
            type: "effectScatter",
            coordinateSystem: "geo",
            // geoIndex: 0,
            zlevel: 4,
            symbolSize: [10, 5],
            showEffectOn: "render",
            rippleEffect: {
              brushType: "stroke",
              period: 4,
              scale: 6,
              number: 3,
            },
            itemStyle: {
              normal: {
                color: "#1BDCFF",
              },
            },
            label: {
              normal: {
                position: "center",
                offset: [-2, 2],
                lineHeight: 34,
                show: true,
                formatter: function (params) {
                  return ["{img|" + params.name + "}"];
                },
                rich: {
                  img: {
                    color: "#fff",
                    fontSize: 14,
                    fontWeight: 600,
                    fontFamily: "League-Gothic-Regular",
                  },
                },
              },
            },
            data: this.dataTmp,
          },
        ],
      };
      //随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        myChart.resize();
      });
      setTimeout(() => {
        myChart.resize();
      }, 1);
      myChart.setOption(option);

      //定时器
      this.loopTimer = null;
      let loopIndex = 0;
      let oldIndex = 0;
      let overIndex = 0;
      let that = this;
      that.loopTimer = setInterval(() => {
        myChart.dispatchAction({
          type: "downplay",
          seriesIndex: 1,
          dataIndex: loopIndex - 1,
        });
        if (loopIndex >= that.barList.length) {
          loopIndex = 0;
        }
        myChart.dispatchAction({
          type: "highlight",
          seriesIndex: 1,
          dataIndex: loopIndex,
        });
        oldIndex = loopIndex;
        loopIndex += 1;
      }, 2600);
      //鼠标移出事件
      myChart.on("mouseover", function (params) {
        clearInterval(that.loopTimer);
        myChart.dispatchAction({
          type: "downplay",
          seriesIndex: 1,
        });
        myChart.dispatchAction({
          type: "highlight",
          seriesIndex: 1,
          dataIndex: params.dataIndex,
        });

        loopIndex = params.dataIndex;
      });
      myChart.on("mouseout", function (params) {
        clearInterval(that.loopTimer);
        that.loopTimer = setInterval(() => {
          myChart.dispatchAction({
            type: "downplay",
            seriesIndex: 1,
            dataIndex: loopIndex - 1,
          });
          if (loopIndex >= that.barList.length) {
            loopIndex = 0;
          }

          myChart.dispatchAction({
            type: "highlight",
            seriesIndex: 1,
            dataIndex: loopIndex,
          });
          loopIndex += 1;
        }, 2600);
      });
    },
    lineMaxHeight() {
      const maxValue = Math.max(...this.mapList.map((item) => item.tally));
      return 20 / maxValue;
    },
    formatWorldMapToZH(data) {
      let zhFeatures = [];
      if (data.features) {
        zhFeatures = data.features.map((item) => {
          if (nameMap[item.properties.name]) {
            item.properties.name = nameMap[item.properties.name];
          }
          return item;
        });
      }
      data.features = zhFeatures;
      return data;
    },
  },
  beforeDestroy() {
    /* 页面组件销毁的时候，别忘了移除绑定的监听resize事件，否则的话，多渲染几次
      容易导致内存泄漏和额外CPU或GPU占用哦*/
    window.removeEventListener("resize", () => {
      this.myChart.resize();
    });
    clearInterval(this.loopTimer);
  },
};
</script>

<style lang='scss'>
.el-table .warning-row {
  background-color: #13436d;
}
.el-table .success-row {
  background-color: #113a65;
}
</style>