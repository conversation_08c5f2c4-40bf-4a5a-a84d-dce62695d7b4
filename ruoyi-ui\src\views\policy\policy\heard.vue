<template>
  <div class="zcHeard">
    <div class="name">政策文件库</div>
    <div class="keyword">
      <el-input ref="keywordRef" placeholder="请输入搜索词,使用逗号分割" v-model="keyword" @focus="showHistoryList()"
        @blur="hideHistoryList()">
        <template #append>
          <el-button @click="search()" icon="el-icon-search">搜索</el-button>
        </template>
      </el-input>
      <div class="history" v-show="showHistory">
        <div class="historyItem" v-for="(history, index) in historyList" :key="index" v-loading="historyLoading">
          <div @click="keywordsChange(history)" class="historyText">{{ history.keyword }}</div>
          <el-button type="text" @click="removeHistory(history, 1)">删除</el-button>
        </div>
        <div class="historyItem">
          <el-button type="text" @click="moreHistory()">更多</el-button>
          <el-button type="text" @click="clearHistory()">清空</el-button>
        </div>
      </div>
    </div>

    <el-dialog title="关键词历史" :visible.sync="dialogVisible1" width="470px" append-to-body :close-on-click-modal="false">
      <div class="history" v-loading="historyLoading">
        <div class="historyItem" v-for="(history, index) in historyList1" :key="index">
          <div @click="keywordsChange(history)" class="historyText">{{ history.keyword }}</div>
          <el-button type="text" @click="removeHistory(history, 2)">删除</el-button>
        </div>
      </div>
      <pagination v-show="total1 > 0" :total="total1" :page.sync="queryParams1.pageNum"
        :limit.sync="queryParams1.pageSize" :background="false" @pagination="getArticleHistory1" />
    </el-dialog>
  </div>
</template>

<script>
import { listArticleHistory, delArticleHistory, addArticleHistory, cleanArticleHistory } from "@/api/article/articleHistory";

export default {
  name: 'MyComponent',
  data() {
    return {
      keyword: '',
      showHistory: false,
      historyList: [],
      historyTimeout: null,
      dialogVisible1: false,
      historyLoading: false,
      queryParams1: {
        pageNum: 1,
        pageSize: 50,
      },
      total1: 0,
      historyList1: [],
    };
  },
  created() {
    this.getArticleHistory()
  },
  methods: {
    search() {
      this.showHistory = false;
      this.$nextTick(() => {
        this.$refs['keywordRef'].blur()
      });
      if (this.keyword) {
        addArticleHistory({ keyword: this.keyword, type: 3 }).then(response => {
          this.getArticleHistory()
        });
      }
    },
    async removeHistory(item, type) {
      clearTimeout(this.historyTimeout);
      await delArticleHistory([item.id])
      if (type == 1) {
        this.$refs['keywordRef'].focus();
        await this.getArticleHistory()
      } else {
        await this.getArticleHistory1()
      }
    },
    showHistoryList() {
      this.showHistory = true;
    },
    hideHistoryList() {
      this.historyTimeout = setTimeout(() => {
        this.showHistory = false;
      }, 100);
    },
    keywordsChange(item) {
      this.keyword = item.keyword
      this.dialogVisible1 = false
    },
    getArticleHistory() {
      this.historyLoading = true
      listArticleHistory({ pageNum: 1, pageSize: 5, type: 3 }).then(response => {
        this.historyList = response.rows;
        this.historyLoading = false
      });
    },
    async clearHistory() {
      clearTimeout(this.historyTimeout)
      this.$refs['keywordRef'].focus();
      await cleanArticleHistory(3)
      this.getArticleHistory()
    },
    moreHistory() {
      this.getArticleHistory1()
      this.dialogVisible1 = true
    },
    getArticleHistory1() {
      this.historyLoading = true
      listArticleHistory({ ...this.queryParams1, type: 3 }).then(response => {
        this.historyList1 = response.rows;
        this.total1 = response.total;
        this.historyLoading = false
      });
    },
  }
};
</script>

<style lang="scss" scoped>
.zcHeard {
  // position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  background-color: rgba(255, 255, 255, 1);
  width: 100%;
  height: 82px;
  display: flex;
  align-items: center;
  justify-content: center;

  .name {
    color: rgb(189, 26, 45);
    font-size: 32px;
    font-weight: 700;
    margin-right: 40px;
  }

  .keyword {
    width: 800px;
    position: relative;

    .history {
      width: 800px;
      position: absolute;
      background: #fff;
      z-index: 99;
      left: 0;
      border: 1px solid rgb(221, 219, 219);
    }
  }
}

.history {
  width: 430px;

  .historyItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    padding: 0 10px;
    overflow: hidden;

    .historyText {
      width: 350px;
      height: 34px;
      line-height: 34px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
  }
}
</style>